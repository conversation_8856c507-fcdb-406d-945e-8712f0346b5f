<template>
  <div class="mainAnalysis">
    <div class="left_card">
      <el-card class="box-card">
        <IconTitle class="selfTitle" title="数据采集/设备列表" imgUrl="yunwei"/>
        <div class="tabs">
          <el-tabs v-model="activeName" @tab-click="handleClick" stretch>
            <el-tab-pane label="类别" name="first">类别</el-tab-pane>
            <el-tab-pane label="位置" name="second">

              <div class="tree">
                <el-tree
                    style="width: 130px;"
                    :data="data"
                    :props="defaultProps"
                    :default-expand-all="true"
                    @node-click="handleNodeClick">
                </el-tree>
                <!-- 分割线 -->
                <el-divider style="margin-left: 20px" direction="vertical"/>
                <div class="right_data">
                  <div class="data_top">
                    <span style="color: #E29836">温馨提示：</span>
                    <div>
                      <div style="padding-top: 10px; display: flex">
                        <div>
                          <img src="@/assets/svg/yxtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">运行中</span>

                        <div>
                          <img src="@/assets/svg/gjtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">关机</span>

                        <div>
                          <img :src="require('@/assets/imagesAssets/jgtp.png')"
                               style="margin: 1px 5px 0 0; color: #8c939d; width: 18px">
                        </div>
                        <span>告警</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="data_body">
                      <div>
                        <!-- 遍历出来的数据 -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <div class="right_card">
      <div class="box-card">
        <div class="gk">
          <div class="tp">
            <img src="@/assets/svg/gktb.svg" alt="">
          </div>
          <span style="font-weight: 700; text-align: center; margin-top: 5px">概况</span>
        </div>
        <div class="right_top">
          <div class="div-card-one">
            <div style="font-weight: 700;">温馨提示</div>
            <el-divider></el-divider>
            <div class="divider_bottom">
              <div class="sxtp">
                <img src="@/assets/svg/sxtb.svg" alt="">
              </div>
              <div class="sxtp_center">
                <span style="font-weight: 700; margin-top: -10px">当前数据</span>
                <span style="margin-top: 15px; color: #898989">2022-2-16 9:47:36</span>
              </div>
              <div class="sxtp_right">
                <span style="font-weight: 700; margin-top: -10px">统计算法采用的是每30分钟统计一次（颗粒度为10分钟）</span>
                <span style="margin-top: 15px; color: #898989">可在数据采集平台里修改相应配置</span>
              </div>
            </div>
          </div>
        </div>

        <div class="right_top">
          <div class="div-card-tow">
            <IconTitle class="selfTitle" title="开机统计" imgUrl="yunwei" style="font-size: 12px"/>
            <div class="statistical_dev">

              <statistical style="margin: 0px 27px 0 20px;"/>

              <div class="statistical_bar">
                <div class="statistical_bar_item">
                  <img :src="require('@/assets/imagesAssets/kj.png')" alt="">
                  <div class="div_span">
                    <span style="color: #959699;font-size: 10px">03</span>
                    <span style="color: #b3b4b6;font-size: 10px">开机数</span>
                  </div>
                </div>
                <div class="statistical_bar_item" style="margin-top: 15px">
                  <img :src="require('@/assets/imagesAssets/gj.png')" alt="">
                  <div class="div_span">
                    <span style="color: #959699;font-size: 10px">00</span>
                    <span style="color: #b3b4b6;font-size: 10px">关机数</span>
                  </div>
                </div>
              </div>
              <div class="count_div">
                <span style="margin-bottom: 10px">40</span>
                <span>设备总数</span>
              </div>

              <el-divider direction="vertical"></el-divider>

              <div class="divider_right">
                <div class="divider_right_left">
                  <img :src="require('@/assets/imagesAssets/dkj.png')" alt="" class="img">
                  <span class="img_span">
                    开机 <span style="font-size: 14px">24</span> 台
                  </span>
                </div>
                <div class="divider_right_left" style="margin-left: 30px">
                  <img :src="require('@/assets/imagesAssets/dgj.png')" alt="" class="img">
                  <span class="img_span">
                    关机 <span style="font-size: 14px">16</span> 台
                  </span>
                </div>
                <div class="divider_right_right">
                  <div>
                    <span>其中未知台数：</span>
                    <span style="color: #ffac50">0</span>
                  </div>
                  <span style="color: #a199a3">
                    设备数据无法采集的按照关机处理
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="div-card-tow">
            <IconTitle class="selfTitle" title="运行统计" imgUrl="yunwei" style="font-size: 12px"/>
            <div class="statistical_dev">

              <run-statistical class="run_statistical"/>

              <div class="count_div">
                <span style="margin-bottom: 10px">24</span>
                <span>开机总数</span>
              </div>

              <el-divider direction="vertical"></el-divider>

              <div class="divider_right">
                <div class="divider_right_one">
                  <img :src="require('@/assets/imagesAssets/yxtp.png')" alt="" class="img">
                  <span class="img_span">
                    <span style="color: #101010; margin-top: 2px;">13</span>
                    <span style="color: #606266; margin-top: 5px">运行</span>
                  </span>
                </div>
                <div class="divider_right_one" style="margin-left: 30px">
                  <img :src="require('@/assets/imagesAssets/djtp.png')" alt="" class="img">
                  <span class="img_span">
                    <span style="color: #101010; margin-top: 2px;">7</span>
                    <span style="color: #606266; margin-top: 5px">待机</span>
                  </span>
                </div>
                <div class="divider_right_one" style="margin-left: 30px">
                  <img :src="require('@/assets/imagesAssets/gztp.png')" alt="" class="img">
                  <span class="img_span">
                    <span style="color: #101010; margin-top: 2px;">4</span>
                    <span style="color: #606266; margin-top: 5px">故障</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="right_status">
          <div class="div-card">
            <IconTitle class="selfTitle" title="24小时开机状态" imgUrl="yunwei" style="font-size: 12px"/>
            <div class="statistical_dev">
              <book-status class="book_status"/>
            </div>
          </div>
        </div>

        <div class="right_status">
          <div class="div-card">
            <IconTitle class="selfTitle" title="24小时运行状态" imgUrl="yunwei" style="font-size: 12px"/>
            <div class="statistical_dev">
              <run-status class="run_status"/>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import statistical from "@/echarts/statistical";
import runStatistical from "@/echarts/runStatistical";
import bookStatus from "@/echarts/bookStatus";
import runStatus from "@/echarts/runStatus";

export default {
  name: "index",
  components: {
    IconTitle,
    statistical,
    runStatistical,
    bookStatus,
    runStatus
  },
  data() {
    return {
      activeName: 'first',
      data: [{
        label: '一级 1',
        children: [{
          label: '二级 1-1',
          children: [{
            label: '三级 1-1-1'
          }]
        }]
      }, {
        label: '一级 2',
        children: [{
          label: '二级 2-1',
          children: [{
            label: '三级 2-1-1'
          }]
        }, {
          label: '二级 2-2',
          children: [{
            label: '三级 2-2-1'
          }]
        }]
      }, {
        label: '一级 3',
        children: [{
          label: '二级 3-1',
          children: [{
            label: '三级 3-1-1'
          }]
        }, {
          label: '二级 3-2',
          children: [{
            label: '三级 3-2-1'
          }]
        }]
      }],
      defaultProps: {
        children: 'children',
        label: 'label'
      }

    };
  },
  methods: {
    handleClick(tab, event) {
    },
    handleNodeClick(data) {
    }
  }

}
</script>

<style lang="less">

.mainAnalysis {
  font-size: 12px;
  display: flex;

  .right_card {
    width: 900px;
    height: 650px;
    margin-left: 15px;

    .box-card {
      height: 1000px;
      border-radius: 10px;
      background-color: #ffffff;

      .gk {
        display: flex;
        padding: 10px 0 0 10px;

        .tp {
          background-color: #009DFF;
          width: 25px;
          height: 25px;
          border-radius: 50%;
          padding: 3px 0 0 3px;
          margin-right: 5px;
        }
      }

      .right_status{
        .div-card {
          padding: 10px 10px 0 10px;
          margin: 15px;
          height: 220px;
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
          border-radius: 10px;

          .book_status{
            margin: -35px 0 0 -50px
          }

          .run_status{
            margin: -35px 0 0 -10px;
          }
        }
      }

      .right_top {
        .div-card-one {
          padding: 10px 10px 0 10px;
          margin: 15px;
          height: 130px;
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
          border-radius: 10px;

          .divider_bottom {
            display: flex;

            .sxtp {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              padding: 3px 0 0 3px;
              background-color: #c6f0e7;
            }

            .sxtp_center {
              display: flex;
              flex-direction: column;
              margin-left: 10px;
            }

            .sxtp_right {
              display: flex;
              flex-direction: column;
              margin-left: 80px;
            }
          }
        }

        .div-card-tow {
          padding: 10px 10px 0 10px;
          margin: 15px;
          height: 130px;
          box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
          border-radius: 10px;

          .statistical_dev {
            display: flex;


            .statistical {
              //margin: 26px 0 0 20px;
              margin-top: 25px;
            }

            .statistical_bar {
              display: flex;
              flex-direction: column;

              .statistical_bar_item {
                display: flex;

                .div_span {
                  display: flex;
                  flex-direction: column;
                  margin: 8px 0 0 15px;

                }
              }
            }

            .count_div {
              color: #000c17;
              font-size: 12px;
              display: flex;
              flex-direction: column;
              margin: 30px 0 0 30px;
            }

            .el-divider--vertical {
              display: inline-block;
              width: 1px;
              height: 7em;
              margin: 0 8px;
              vertical-align: middle;
              position: relative;
              margin: 10px 20px 0 20px;
            }

            .divider_right {
              display: flex;

              .divider_right_one{
                display: flex;

                .img {
                  width: 20px;
                  height: 20px;
                  margin-top: 40px;
                }

                .img_span {
                  display: flex;
                  flex-direction: column;
                  margin: 40px 0 0 15px;
                }
              }

              .divider_right_left {
                display: flex;

                .img {
                  width: 50px;
                  height: 50px;
                  margin-top: 25px;
                }

                .img_span {
                  margin: 40px 0 0 15px;
                }
              }

              .divider_right_right {
                margin: 30px 0 0 30px;
              }
            }


          }
        }
      }

    }
  }

  .left_card {
    width: 411px;
    height: 650px;

    .box-card {
      height: 1000px;
      border-radius: 10px;


      .tabs {
        .el-tabs {
          height: 100%;
          .tree {
            display: flex;

            .right_data {
              width: 176px;
              height: 100%;

              .data_top {
                width: 204px;
                height: 60px;
                background-color: #fafbfd;
                padding: 5px 0 0 5px;
              }
            }
          }

        }
      }

      .text {
        font-size: 12px;
      }

      .item {
        margin-bottom: 18px;
      }

      .clearfix:before,
      .clearfix:after {
        display: table;
        content: "";
      }

      .clearfix:after {
        clear: both
      }

      .box-card {
        width: 480px;
      }
    }
  }
}

.el-tabs__nav-wrap::after {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #E4E7ED;
  z-index: 1;
}

.el-tabs__item {
  font-size: 12px;
}

.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 71em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}

</style>
