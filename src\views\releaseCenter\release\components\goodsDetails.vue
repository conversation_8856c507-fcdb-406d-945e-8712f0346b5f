<template>
  <div class="app-container">
    <el-form
      ref="dateFormRefs"
      :model="dataForm"
      :disabled="detailFlag"
      :rules="dataRules"
      v-loading="formLoading"
      label-width="120px"
    >
      <el-row :gutter="24">
        <el-col>
          <span class="form_title">基本信息</span>
        </el-col>
        <el-col :span="24">
          <el-form-item label="商品名称" prop="proName">
            <el-input
              v-model="dataForm.proName"
              maxlength="60"
              show-word-limit
              placeholder="请输入商品名称"
            ></el-input>
            <p>
              <!-- 商品标题名称长度至少3个字符，最长60个汉字，且不包含英文双引号（""）、英文单引号（''）和反斜线（\） -->
            </p>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="销项税率" prop="taxRate">
            <el-input v-model="dataForm.taxRate" placeholder="请输入销项税率"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="产地" prop="citys">
            <el-cascader
              v-model="dataForm.citys"
              style="width: 100%"
              :options="cityOptions"
              clearable
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品类" prop="grade">
            <el-checkbox-group v-model="dataForm.grade">
              <el-checkbox
                v-for="(item, index) in dict.type.goods_grade"
                :label="item.value"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="种类" prop="category">
            <el-checkbox-group v-model="dataForm.category">
              <el-checkbox
                v-for="(item, index) in dict.type.goods_category"
                :label="item.value"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <span class="form_title">销售信息</span>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规格明细" prop="speDetails">
            <el-input
              v-model="dataForm.speDetails"
              placeholder="请输入规格明细"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品价格" prop="price">
            <el-input v-model="dataForm.price" placeholder="请输入商品价格"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品库存" prop="inventory">
            <el-input
              v-model="dataForm.inventory"
              placeholder="请输入商品库存"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品重量（g）" prop="weight">
            <el-input
              v-model="dataForm.weight"
              placeholder="请输入商品重量（g）"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品货号" prop="artno">
            <el-input v-model="dataForm.artno" placeholder="请输入商品货号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <span class="form_title">图文描述</span>
        </el-col>
        <el-col :span="24">
          <el-form-item label="商品图片" prop="images">
            <NewImageUpload
              isShowTip
              :value="dataForm.images"
              :limit="9"
              @input="handleUpload"
            />
          </el-form-item>
          <!-- <el-form-item label="视频描述" prop="images">
            <ImageUpload
              :isShowTip="false"
              :showTipText="showTipText"
              :value="dataForm.images"
              :limit="1"
              @input="handleUpload"
            />
          </el-form-item> -->
          <el-form-item label="电脑端描述" prop="description">
            <Editor v-model="dataForm.description" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getCityData } from "@/api/goods/list";
import { getInfo, uploadApi } from "@/api/release/index.js";

export default {
  dicts: ["goods_grade", "goods_category"],
  data() {
    return {
      dataForm: {
        grade: [],
        category: [],
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
      showTipText:
        "商品图片支持JPG、JPEG、GIF、PNG格式，建议上传尺寸800*800、大小1.00M内的图片，图片可以通过两侧转角调整顺序",
      formDisabled: false,
      formLoading: false,
      btnLoading: false,
      dataRules: {
        proName: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
        taxRate: [{ required: true, message: "请输入销项税率", trigger: "blur" }],
        citys: [{ required: true, message: "请选择产地", trigger: "change" }],
        grade: [{ required: true, message: "请选择品类", trigger: "blur" }],
        category: [{ required: true, message: "请选择种类", trigger: "blur" }],
        speDetails: [{ required: true, message: "请输入规格明细", trigger: "blur" }],
        price: [{ required: true, message: "请输入商品价格", trigger: "blur" }],
        inventory: [{ required: true, message: "请输入商品库存", trigger: "blur" }],
        weight: [{ required: true, message: "请输入商品重量（g）", trigger: "blur" }],
        artno: [{ required: true, message: "请输入商品货号", trigger: "blur" }],
        images: [{ required: true, message: "请上传商品图片", trigger: "blur" }],
        description: [{ required: true, message: "请输入电脑端描述", trigger: "blur" }],
      },
      cityOptions: [],
    };
  },
  created() {
    getCityData().then((res) => {
      this.cityOptions = res.data.map((first) => {
        return {
          value: first.id,
          label: first.name,
          children: first.children.map((second) => {
            return {
              value: second.id,
              label: second.name,
              children: second.children.map((third) => {
                return {
                  value: third.id,
                  label: third.name,
                };
              }),
            };
          }),
        };
      });
      const { flowInstanceId } = this.$route.query;
      flowInstanceId && this.getFormDataFtn(flowInstanceId);
    });
  },
  methods: {
    handleUpload(val) {
      console.log(val, "val555");
      this.dataForm.images = val;
    },
    //详情接口回显
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.dataForm = {
          ...params,
          grade: params.grade.split(","),
          category: params.category.split(","),
          citys: [Number(params.province), Number(params.city), Number(params.district)],
        };
        console.log(this.dataForm, "this.dataForm");

        this.submitDing = false;
      });
    },
    submitForm() {
      const params = {
        ...this.dataForm,
        grade: this.dataForm.grade.join(","),
        category: this.dataForm.category.join(","),
        province: this.dataForm.citys[0], // 省
        city: this.dataForm.citys[1], // 市
        district: this.dataForm.citys[2], // 区
        description: this.dataForm.description.replace(/src="([^"]+)"/, "src='$1'"),
      };
      delete params.citys;

      this.$emit("submitFtn", params, (res) => {
        // 相应结束后的其他逻辑
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.form_title {
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 16px;
}
</style>
