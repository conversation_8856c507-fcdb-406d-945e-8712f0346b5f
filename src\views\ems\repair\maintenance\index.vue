<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <!--列表-->
  <div v-if="listFlag" class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     style="backgroundColor:#E1b980"
                     icon="el-icon-circle-plus-outline"
                     v-if="permissions.ems_emsregulations_add"
                     @click="toAdd()"
          >新增</el-button
          >
<!--          <el-button-->
<!--              type="success"-->
<!--              icon="el-icon-edit"-->
<!--              v-if="permissions.ems_emsregulations_edit"-->
<!--              :disabled="single"-->
<!--              @click="handleEdit"-->
<!--          >编辑</el-button-->
<!--          >-->
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="permissions.ems_emsregulations_del"
              @click.native="handleDel()"
              :disabled="multiple"
          >删除</el-button
          >
<!--          <el-button-->
<!--              type="check"-->
<!--              icon="el-icon-download"-->
<!--              @click="exportExcel"-->
<!--          >导出</el-button-->
<!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>

    </el-card>
    <basic-container>
      <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          @selection-change="selectionChange"
          @on-load="getList"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @size-change="sizeChange"
          @current-change="currentChange"
          @row-update="handleUpdate"
          @row-save="handleSave"
          @row-del="handleDel"
      >
        <template slot="header">
          <IconTitle class="selfTitle" title="维修工单" imgUrl="yunwei" />
        </template>

        <template slot="menuRight" slot-scope="{size}">
          <el-button @click="btnCli(null)">全部({{statusObj.all}})</el-button>
          <el-button @click="btnCli(0)" style="background-color: #E83672;color: #FFFFFF">待分配({{statusObj.dfp}})</el-button>
          <el-button @click="btnCli(1)" style="background-color: #DEA11E;color: #FFFFFF">已分配({{statusObj.yfp}})</el-button>
          <el-button @click="btnCli(2)" style="background-color: #358AEF;color: #FFFFFF">维修中({{statusObj.wxz}})</el-button>
          <el-button @click="btnCli(3)" style="background-color: #78BF34;color: #FFFFFF">已解决({{statusObj.yjj}})</el-button>
          <el-button @click="btnCli(4)" style="background-color: #57C4B0;color: #FFFFFF">已验收({{statusObj.yys}})</el-button>
        </template>
        <!--设备状态-->
        <template slot="deviceStatus" slot-scope="scope" >
          <el-tag v-if="scope.row.deviceStatus == 0" size="mini" :hit="false" color="#E83672" effect="dark">故障</el-tag>
          <el-tag v-else-if="scope.row.deviceStatus == 1" size="mini" :hit="false" color="#358AEF" effect="dark">正常</el-tag>
          <el-tag v-else size="mini" color="#F0AD4E" effect="dark">带病运行</el-tag>
        </template>
        <!--故障等级-->
        <template slot="faultGrade" slot-scope="scope" >
          <el-tag v-if="scope.row.faultGrade == 0" size="mini" :hit="false" color="#8DB3E2" effect="dark">一般</el-tag>
          <el-tag v-else-if="scope.row.faultGrade == 1" size="mini" :hit="false" color="#FFC000" effect="dark">严重</el-tag>
          <el-tag v-else-if="scope.row.faultGrade == 2" size="mini" :hit="false" color="#FF0000" effect="dark">紧急</el-tag>
          <el-tag v-else size="mini" color="#961717" effect="dark">其他</el-tag>
        </template>
        <!--状态-->
        <template slot="status" slot-scope="scope" >
          <el-tag v-if="scope.row.status == 0" size="mini" :hit="false" color="#E83672" effect="dark">待分配</el-tag>
          <el-tag v-else-if="scope.row.status == 1" size="mini" :hit="false" color="#DEA11E" effect="dark">已分配</el-tag>
          <el-tag v-else-if="scope.row.status == 2" size="mini" :hit="false" color="#358AEF" effect="dark">维修中</el-tag>
          <el-tag v-else-if="scope.row.status == 3" size="mini" :hit="false" color="#78BF34" effect="dark">已解决</el-tag>
          <el-tag v-else size="mini" color="#57C4B0" effect="dark">已验收</el-tag>
        </template>
        <!--维修方式-->
        <template slot="repairMethod" slot-scope="scope" >
          <span v-if="scope.row.repairMethod == 0">自修</span>
          <span v-else>外协</span>
        </template>
        <!--当前步骤-->
        <template slot="currentStep" slot-scope="scope" >
          <span v-if="scope.row.currentStep == 0">报修</span>
          <span v-else-if="scope.row.currentStep == 1">分配</span>
          <span v-else-if="scope.row.currentStep == 2">接收</span>
          <span v-else-if="scope.row.currentStep == 3">维修</span>
          <span v-else-if="scope.row.currentStep == 4">验收</span>
          <span v-else-if="scope.row.currentStep == 5">转外委</span>
        </template>
        <!--菜单-->
        <template slot-scope="{row,index}" slot="menu">
          <el-button type="text" v-if="row.currentStep == 0" @click="checkUser(row)">
            <i class="el-icon-edit"></i>分配</el-button
          >
          <el-button type="text" v-if="row.currentStep == 1" @click="jies(row)">
            <i class="el-icon-edit"></i>接收</el-button
          >
          <el-button type="text" @click="goLook(row)">
            <i class="el-icon-view"></i>查看</el-button
          >

        </template>

      </avue-crud>
    </basic-container>
    <!-- 查看详情 -->
    <el-drawer
        class="drawerStyle"
        title="工单详情"
        :visible.sync="drawer"
        direction="rtl"
        size="50%"
        append-to-body
    >
      <drawer-con :device="device" :order="order" :list="list" />
    </el-drawer>
    <!--用户弹框-->
    <el-dialog :title="user.title" :visible.sync="user.open" width="1000px" append-to-body>
      <el-row :gutter="20">
        <!--部门数据-->
        <el-col :span="4" :xs="24">
          <div class="head-container">
            <div class="tree">
              <el-tree
                  :data="treeDeptData"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  :filter-node-method="filterNode"
                  ref="tree"
                  default-expand-all
                  @node-click="handleNodeClick"
              />
            </div>
          </div>

        </el-col>
        <!--用户数据-->
        <el-col :span="20" :xs="24">
          <el-table v-loading="user.loading" :data="userList"
                    @row-click="userRowClick">
            <el-table-column label="用户编号" align="center" key="userId" prop="userId"/>
            <el-table-column label="用户名称" align="center" key="username" prop="username"/>
            <el-table-column label="部门" align="center" key="deptName" prop="deptName"/>
            <el-table-column label="角色" align="center" key="role" prop="roleList[0].roleName"/>
            <el-table-column label="手机号码" align="center" key="phone" prop="phone"/>
            <el-table-column label="状态" align="center" key="lockFlag">
              <template slot-scope="scope">
                                          <span>{{
                                              scope.row.lockFlag == 0
                                                  ? "有效"
                                                  : scope.row.lockFlag == 2
                                                  ? "锁定": ""
                                            }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime"
                             width="160">
            </el-table-column>
          </el-table>

          <pagination
              v-show="userQueryParams.total>0"
              :total="userQueryParams.total"
              :page.sync="userQueryParams.pageNum"
              :limit.sync="userQueryParams.pageSize"
              @pagination="getUserList"
          />
        </el-col>
      </el-row>
    </el-dialog>
  </div>

  <!--添加-->
  <div v-else-if="addFlag">
    <add-order />
  </div>
</template>
<script>
import {fetchList,addObj,putObj,delObj,getOrder,fenpei,jieshou,getCount} from "@/api/ems/repair/order";
import {tableOption} from '@/const/crud/ems/repair/order'
import { mapGetters } from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
import Pagination from "@/components/Pagination/index.vue"
import DrawerCon from "./drawerCon.vue";
import AddOrder from "./addOrder";
import {fetchTree} from "@/api/admin/dept";
import {fetchList as fetchListUser} from "@/api/admin/user";

export default {
  name: 'emsrepairorder',
  components: {
    AddOrder,
    IconTitle,
    DrawerCon,
    Pagination
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "name",
      },
      tableData: [],
      searchForm: {}, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList:[],
      drawer: false,//查看
      id: 0,
      device: {},
      order: {},
      list: [],
      listFlag: true,
      addFlag: false,
      //user弹窗配置
      user: {
        title: "",
        open: false,
        loading: false,
        type: null,
      },
      // 用户数组
      userList: [],
      //部门数据
      treeDeptData: [],
      //用户数据分页
      userQueryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        deptId: null,
      },
      //分配用户
      fenpei:{
        id:null,
        handlerId:null,
        handler:null
      },
      //接收任务
      jieshou:{
        id:null
      },
      //状态数量
      statusObj:{
        "all":"0",
        "dfp":"0",
        "yfp":"0",
        "wxz":"0",
        "yjj":"0",
        "yys":"0"
      }
    };
  },
  computed: {
    ...mapGetters(["permissions","theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsrepairorder_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsrepairorder_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsrepairorder_edit,false),
      };
    },
  },
  mounted() {
    this.initElement();
    this.changeThme();
    this.getSelect()
  },
  methods: {
    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList=list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },
    getSelect() {
      //部门
      fetchTree().then((response) => {
        this.treeDeptData = response.data.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.userQueryParams.deptId = data.id;
      this.getUserList();
    },
    //表格上方按钮事件
    btnCli(status){
      this.searchForm.status = status
      this.getList(this.page,{"status":status});
    },
    //获取用户数据
    getUserList() {
      fetchListUser(Object.assign(
          {
            current: this.userQueryParams.pageNum,
            size: this.userQueryParams.pageSize,
          },
          {deptId: this.userQueryParams.deptId}
          )
      ).then(response => {
        this.userList = response.data.data.records;
        this.userQueryParams.total = response.data.data.total;
        this.user.loading = false;
      });
    },
    //用户行点击事件
    userRowClick(row, event, column) {
      this.fenpei.handlerId = row.userId;
      this.fenpei.handler = row.username;
      fenpei(this.fenpei)
          .then((data) => {
            this.$message.success("分配成功");
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
      this.user.open = false;
    },
    checkUser(row){
      this.user.title = "任务分配";
      this.user.loading = true;
      this.user.open = true;
      this.fenpei.id = row.id
      this.getUserList()
    },
    getCountByStatus(){
      getCount()
          .then((data) => {
            this.statusObj = data.data.data
          })
          .catch(() => {
          });
    },
    //接收
    jies(row){
      this.jieshou.id = row.id
      jieshou(this.jieshou)
          .then((data) => {
            this.$message.success("接收成功");
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          },params,this.searchForm)).then((response) => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
      this.getCountByStatus()
    },
    //编辑
    handleEdit(){
      var refsDate = this.$refs
      refsDate.crud.rowEdit(this.selectionList[0],this.selectionList[0].$index);
    },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 更新
    handleUpdate: function (row,  index,done, loading) {
      putObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      addObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.downBlobFile(
          "/ems/emsrepairorder/export",
          this.searchForm,
          "emsrepairorder.xlsx"
      );
    },
    // 改变主题颜色
    changeThme(){
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor=this.theme;
    },
    goLook(row) {
      getOrder(row.id).then((res)=>{
        this.device = res.data.data.device
        this.order = res.data.data.order
        this.list = res.data.data.list
        console.log(res.data.data)
        this.drawer = true
      })
    },
    toAdd(){
      this.listFlag = false
      this.addFlag = true
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="less">

.el-tag {
  border-width: 0px;
  padding: 1px 15px !important;
}
.drawerStyle {
  ::v-deep .el-drawer__header {
    background-color: #f2f2f5;
    padding: 10px 0 10px 20px;
    color: #101010;
    margin-bottom: 20px;
  }
}
</style>
