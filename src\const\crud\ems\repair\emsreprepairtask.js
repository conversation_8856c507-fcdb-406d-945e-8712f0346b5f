const frontLabel = process.env.VUE_APP_BASE_API;
export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  editBtn:false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon":false,
  "searchShow": true,
  menu:false,
  "column": [
    {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
      hide: true
    },	  {
      "type": "input",
      "label": "任务编号",
      "prop": "taskNum",
      "span": 12,
      "search":true,
    },{
      "type": "input",
      "label": "计划编号",
      "prop": "planNum",
      "span": 12
    },	{
      "type": "input",
      "label": "计划名称",
      "prop": "planName",
      "span": 12
    },  {
      "type": "input",
      "label": "设备编号",
      "prop": "deviceNum",
      "span": 12
    }, {
      "type": "input",
      "label": "设备名称",
      "prop": "deviceName",
      "span": 12,
      "search":true,
    }, {
      "type": "input",
      "label": "开始时间",
      "prop": "planBeginTime",
      "span": 12
    },
    {
      "type": "input",
      "label": "结束时间",
      "prop": "planEndTime",
      "span": 12
    },
    // {
    //   "type": "input",
    //   "label": "检修人",
    //   "prop": "liableUserId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "验收人",
    //   "prop": "planLiableUserId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "任务状态",
      "prop": "status",
      "span": 12,
      slot:true,
      dicUrl:`${frontLabel}/system/dict/data/type/ems_ins_inspect_task_status`
    },
    {
      "type": "select",
      "label": "验收状态",
      "prop": "checkStatus",
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/task_check_status`
    },
  ]
}
