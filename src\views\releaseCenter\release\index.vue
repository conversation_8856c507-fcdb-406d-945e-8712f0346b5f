<template>
  <div
    class="app-container releasePage"
    v-resize="resize"
    v-loading="loading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
  >
    <!-- 头 -->
    <div class="title_header">
      <el-button @click="back" type="text">
        <svg-icon icon-class="back" style="margin-right: 5px" />
        返回
      </el-button>
      <h3 class="title">{{ title }}</h3>
    </div>
    <!-- 中 -->
    <div class="audit-detail" ref="content">
      <!-- 组 -->
      <component
        v-if="comTabKey"
        :is="comTabKey"
        :ref="type"
        :footerWidth="footerWidth"
        :labelIdList="labelIdList"
        :currentForm="currentForm"
        @submitFtn="submitFtn"
        @roll="roll"
      />
      <!-- 通 -->
      <!-- <template v-if="gridkey">
        <el-form
          :disabled="currentFormFlag"
          :model="currentForm"
          :rules="currentFormRules"
          ref="currentForm"
          :style="{ width: gridkey.width }"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="gridkey.colSpan" v-if="labelIdList.length > 0">
              <el-form-item label="产品标签" prop="labelIds">
                <el-select
                  multiple
                  v-model="currentForm.labelIds"
                  placeholder="请选择产品标签"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="t in labelIdList"
                    :key="t.id"
                    :label="t.labelName"
                    :value="t.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template> -->
    </div>
    <!-- 底 -->
    <template v-if="detailFlag !== 'detail' && type !== 'INDUSTRY_CLASS'">
      <div
        class="pageFooter"
        v-show="footerWidth && footerWidth != '0px'"
        :style="{ width: `${footerWidth}` }"
      >
        <div style="margin-right: 20px">
          <!-- 审 -->
          <template v-if="detailFlag === 'check'">
            <el-button
              type="primary"
              v-if="checkTypeMap.additionalCondition === 'FLOW_END'"
              @click="setExamineFtn()"
              >已读</el-button
            >
            <template v-else>
              <template
                v-if="
                  checkTypeMap.productType === 'APPEAL' ||
                  checkTypeMap.productType === 'CONSULT'
                "
              >
                <el-button type="primary" @click="restClick('回复')"
                  >回复</el-button
                >
                <el-button
                  type="primary"
                  v-if="checkTypeMap.additionalCondition === 'TRANSFER'"
                  @click="restClick('转派')"
                  >转派</el-button
                >
              </template>
              <template v-else-if="checkTypeMap.productType == 'DIAGNOSIS'">
                <template
                  v-if="
                    checkTypeMap.additionalCondition === 'FACILITATOR_SHARE'
                  "
                >
                  <el-button type="danger" @click="restClick('驳回原因')"
                    >驳回</el-button
                  >
                  <el-button type="primary" @click="restClick('服务商分配')"
                    >服务商分配</el-button
                  >
                </template>
                <el-button
                  v-if="
                    !checkTypeMap.additionalCondition ||
                    checkTypeMap.additionalCondition ===
                      'FACILITATOR_USER_SHARE' ||
                    checkTypeMap.additionalCondition === 'EXAMINE_ING'
                  "
                  type="danger"
                  @click="restClick('处理')"
                  >处理</el-button
                >
                <el-button
                  v-if="
                    checkTypeMap.additionalCondition ===
                    'FACILITATOR_USER_SHARE'
                  "
                  type="primary"
                  @click="restClick('用户分配')"
                  >用户分配</el-button
                >
                <el-button
                  type="primary"
                  v-if="checkTypeMap.additionalCondition === 'RUSH'"
                  @click="setExamineFtn()"
                  >抢办</el-button
                >
              </template>
              <template v-else>
                <el-button type="danger" @click="restClick('驳回原因')"
                  >驳回</el-button
                >
                <el-button
                  type="primary"
                  @click="setExamineFtn('PASS')"
                  :loading="dialoDing"
                  >通过</el-button
                >
              </template>
            </template>
          </template>
          <!-- add｜｜edit -->
          <template v-else>
            <el-button
              type="primary"
              @click="submitFtnSon"
              :loading="submitDing"
              >提交</el-button
            >
          </template>
        </div>
      </div>
    </template>
    <!-- 弹 -->
    <el-dialog
      :title="openTitle"
      :visible.sync="open"
      :width="openWidth"
      append-to-body
    >
      <el-form
        ref="restForm"
        :rules="restRules"
        :model="restForm"
        :label-width="openTitle === '驳回原因' ? '0px' : 'auto'"
        size="mini"
      >
        <el-form-item prop="reason" v-if="openTitle === '驳回原因'">
          <el-input
            style="width: 100%"
            type="textarea"
            :rows="3"
            placeholder="请输入驳回原因"
            v-model="restForm.reason"
          >
          </el-input>
        </el-form-item>
        <el-form-item
          label="服务商账号"
          v-if="openTitle === '服务商分配'"
          prop="facilitatorIds"
        >
          <!-- <el-select
            v-model="restForm.facilitatorIds"
            multiple
            placeholder="请选择服务服务商"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in facilitatorIdsList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            ></el-option>
          </el-select> -->
          <el-select
            v-model="restForm.facilitatorIds"
            multiple
            filterable
            placeholder="请选择服务服务商"
            style="width: 100%"
            clearable
          >
            <el-option-group
              v-for="group in facilitatorIdsList"
              :key="group.userId"
              :label="group.nickName"
            >
              <el-option
                v-for="item in group.options"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item
          label="用户"
          v-if="openTitle === '用户分配'"
          prop="userIds"
        >
          <el-select
            v-model="restForm.userIds"
            multiple
            placeholder="请选择用户"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in userIdsList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <template v-if="openTitle === '处理' && restForm.responses.length > 0">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col
              :span="24"
              v-if="dict.type.diagnostic_template.length"
              style="margin-bottom: 20px"
            >
              <el-form-item label="线上诊断">
                <el-button
                  type="text"
                  v-for="t in dict.type.diagnostic_template"
                  @click="templateClick(t.value)"
                  :key="t.value"
                  >查看报告</el-button
                >
              </el-form-item>
            </el-col>
            <!-- 线下诊断表格 -->
            <el-col :span="24">
              <offline
                :tableData="restForm.responses"
                @refresh="getConditionByFlowInstanceIdFtn"
              />
            </el-col>
            <!-- <el-col :span="24" v-for="(item, index) in restForm.responses" :key="item.id">
              <el-form-item class="uploadItem" :label="item.wordName" :prop="'responses.' + index + '.file'" :rules="{
                required: true,
                message: '必须上传项',
                trigger: 'blur',
              }">
                <el-upload ref="fileModel" :before-upload="(file) => beforeFileUpload(file, item.fileSuffix)
                  " :on-remove="(file) => removeFile(file, 'responses', index)"
                  :http-request="(event) => uploadFileApiFtn(event, 'responses', index)" :limit="1"
                  :accept="item.fileSuffix" action="#">
                  <el-button slot="trigger" type="primary" size="small"><i class="el-icon-upload el-icon--right"
                      style="margin-right: 5px"></i>选择文件</el-button>
                  <div slot="tip" class="el-upload__tip" style="color: silver; font-size: 12px">
                    支持扩展名：{{ item.fileSuffix }}
                  </div>
                </el-upload>
              </el-form-item>
            </el-col> -->
          </el-row>
        </template>
        <template v-if="openTitle === '回复'">
          <!-- 咨询不需要上传附件 -->
          <el-form-item
            v-if="checkTypeMap.productType !== 'CONSULT'"
            class="uploadItem"
            label="附件"
          >
            <el-upload
              :before-upload="(file) => beforeFileUpload(file, '')"
              :on-remove="(file) => removeFile(file, 'replyUrl')"
              :http-request="(event) => uploadFileApiFtn(event, 'replyUrl')"
              :limit="1"
              accept=".doc,.docx,.pdf,.xlsx"
              action="#"
            >
              <el-button slot="trigger" type="primary" size="small"
                ><i
                  class="el-icon-upload el-icon--right"
                  style="margin-right: 5px"
                ></i
                >选择文件</el-button
              >
              <div
                slot="tip"
                class="el-upload__tip"
                style="color: silver; font-size: 12px"
              >
                支持扩展名：.docx .doc .pdf .xlsx
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item prop="replyContent" label="内容">
            <el-input
              style="width: 100%"
              type="textarea"
              :rows="3"
              placeholder="请输入回复内容"
              v-model="restForm.replyContent"
            >
            </el-input>
          </el-form-item>
        </template>
        <el-form-item
          label="转派用户"
          v-if="openTitle === '转派'"
          prop="userIds"
        >
          <el-select
            v-model="restForm.userIds"
            style="width: 100%"
            multiple
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入关键词"
            :remote-method="remoteMethod"
            :loading="apploading"
          >
            <el-option
              v-for="item in userIdsList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" v-if="openTitle !== '处理'">
        <el-button @click="open = false">取消</el-button>
        <el-button type="primary" :loading="dialoDing" @click="submitRestForm()"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProductList,
  submitProduct,
  setExamine,
  getConditionByFlowInstanceId,
  getFacilitatorAdminList,
  getFacilitatorUserList,
  uploadApi,
  getSelectLikeUserByUserName,
  getListByProductType,
  getExInfo,
} from "@/api/release/index.js";
import policyGuide from "./components/policyGuide.vue"; //政策指引
import IndAppNorm from "./components/indAppNorm.vue"; //标准应用
import soluTion from "./components/soluTion.vue"; //解决方案
import resource from "./components/resource.vue"; //资源
import demand from "./components/demand.vue"; //需求
import FinanCial from "./components/financial.vue"; //金融服务
import TalEnts from "./components/talEnts.vue"; //人才
import SchoolOwned from "./components/schoolOwned.vue"; //校企
import benchmarkCase from "./components/benchmarkCase.vue"; //标杆案例
import ClassRoom from "./components/classRoom.vue"; //工业课堂
import IndApp from "./components/indApp.vue"; //工业应用
import declarationEnterprise from "./components/declarationEnterprise.vue"; //企业申报
import authIdentify from "@/views/authIdentify.vue";
import diagnosisOffline from "./components/diagnosisOffline.vue"; //线下诊断
import consultationProcessing from "./components/consultationProcessing.vue"; //线下诊断
import promptlyDeclare from "./components/promptlyDeclare.vue"; //立即申报审核认证

import offline from "./components/offline.vue"; //线下诊断的表格弹窗
import goodsDetails from "./components/goodsDetails.vue"; //商品详情

const comConfig = {
  POLICY: "policy-guide", //政策
  STANDARD_APPLICATION: "ind-app-norm", //标准应用
  SOLUTION: "solu-tion", //解决方案
  RESOURCE: "resource", //资源
  DEMAND: "demand", //需求
  FINANCE: "finan-cial", //金融服务
  PERSON: "tal-ents", //人才服务
  SCHOOL: "school-owned", //校企
  BENCHMARK_CASE: "benchmark-case", //标杆
  INDUSTRY_CLASS: "class-room", //工业课堂
  INDUSTRY_APPLICATION: "ind-app", //工业应用
  DECLARE: "declaration-enterprise", //企业申报
  ENTERPRISE_AUTH: "authIdentify", //审核认证
  FACILITATOR_AUTH: "authIdentify", //服务商认证
  DIAGNOSIS: "diagnosis-offline", //线下诊断
  CONSULT: "consultationProcessing", //咨询
  APPEAL: "consultationProcessing", //咨询
  PROMPTLY_DECLARE: "promptlyDeclare", //立即申报审核认证
  GOODS:'goodsDetails'
};
const gridConfig = [
  { key: "POLICY", width: "80%", colSpan: 20 },
  { key: "STANDARD_APPLICATION", width: "55%", colSpan: 24 },
  { key: "SOLUTION", width: "55%", colSpan: 24 },
  { key: "RESOURCE", width: "80%", colSpan: 20 },
  { key: "DEMAND", width: "80%", colSpan: 20 },
  { key: "FINANCE", width: "80%", colSpan: 20 },
  { key: "PERSON", width: "55%", colSpan: 24 },
  { key: "SCHOOL", width: "55%", colSpan: 24 },
  { key: "BENCHMARK_CASE", width: "80%", colSpan: 20 },
  { key: "INDUSTRY_APPLICATION", width: "65%", colSpan: 24 },
  { key: "DECLARE", width: "80%", colSpan: 20 },
  { key: "GOODS", width: "80%", colSpan: 20 },
];
export default {
  name: "releasePage",
  dicts: ["diagnostic_template"], // 诊断模板
  components: {
    IndApp,
    FinanCial,
    ClassRoom,
    policyGuide,
    IndAppNorm,
    resource,
    benchmarkCase,
    declarationEnterprise,
    TalEnts,
    SchoolOwned,
    demand,
    authIdentify,
    soluTion,
    diagnosisOffline,
    consultationProcessing,
    promptlyDeclare,
    offline,
    goodsDetails
  },
  data() {
    return {
      loading: false,
      footerWidth: "0px",
      productList: [],
      type: this.$route.query.type,
      title: "",
      detailFlag: this.$route.query.pageType,
      flowInstanceId: this.$route.query.flowInstanceId,
      submitDing: false,
      open: false,
      restForm: {},
      restRules: {
        reason: [
          { required: true, message: "驳回原因必须填写", trigger: "blur" },
        ],
        facilitatorIds: [{ required: false, message: "", trigger: "change" }],
        userIds: [{ required: true, message: "请选择用户", trigger: "change" }],
        replyContent: [
          { required: true, message: "驳回原因必须填写", trigger: "blur" },
        ],
      },
      dialoDing: false,
      checkTypeMap: {},
      facilitatorIdsList: [],
      userIdsList: [],
      openTitle: "",
      openWidth: "500px",
      apploading: false,
      gridkey: {},
      currentForm: {
        labelIds: [],
      },
      currentFormRules: {
        labelIds: [
          { required: false, message: "必填项不能为空", trigger: "change" },
        ],
      },
      labelIdList: [],
      currentFormFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },
  created() {
    this.getProductListFtn();
    this.getConditionByFlowInstanceIdFtn();
    //this.getListByProductTypeFtn();
    this.gridkey =
      gridConfig.find((v) => v.key == this.$route.query.type) || undefined;
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getcurrentFormDataFtn(flowInstanceId);
  },
  mounted() {
    // 滚动条
    this.roll();
  },
  computed: {
    comTabKey: function () {
      return comConfig[this.$route.query.type] || undefined;
    },
  },
  methods: {
    getcurrentFormDataFtn(flowInstanceId) {
      getExInfo({ flowInstanceId }).then((res) => {
        this.currentForm = { ...res.data };
      });
    },
    // getListByProductTypeFtn() {
    //   const params = { productType: this.$route.query.type };
    //   getListByProductType(params)
    //     .then((res) => {
    //       this.labelIdList = res.data;
    //     })
    //     .catch(() => {
    //       this.labelIdList = [];
    //     });
    // },
    getProductListFtn() {
      const { type, pageType = "" } = this.$route.query;
      getProductList(1).then((res) => {
        const labelType = res.data.filter((t) => t.dictValue == type)[0]
          .dictLabel;
        const mapTitle = {
          detail: labelType + "详情",
          check: "审核" + labelType,
          add: "新增" + labelType,
          edit: "编辑" + labelType,
        };
        this.title = mapTitle[pageType];
      });
    },
    getConditionByFlowInstanceIdFtn() {
      const { pageType, flowInstanceId } = this.$route.query;
      if (pageType === "check") {
        getConditionByFlowInstanceId(flowInstanceId).then((res) => {
          this.checkTypeMap = res.data;
          const { additionalCondition, productType, responses, businessId } =
            res.data;
          if (productType == "DIAGNOSIS") {
            // 服务商
            if (additionalCondition === "FACILITATOR_SHARE") {
              this.getFacilitatorAdminListFtn(businessId);
            }
            if (
              !additionalCondition ||
              additionalCondition === "FACILITATOR_USER_SHARE" ||
              additionalCondition === "EXAMINE_ING"
            ) {
              //用户分配
              if (additionalCondition === "FACILITATOR_USER_SHARE") {
                this.getFacilitatorUserListFtn();
              }
              //回复
              this.restForm = { ...this.restForm, responses };
            }
          } else if (productType === "APPEAL" || productType === "CONSULT") {
            if (additionalCondition === "TRANSFER") {
              // this.getSelectLikeUserByUserNameFtn()
            }
          }
        });
      }
    },
    getFacilitatorAdminListFtn(businessId) {
      getFacilitatorAdminList({ diagnosticOfflineId: businessId }).then(
        (res) => {
          const { hotServiceProviderList, noHotServiceProviderList } = res.data;
          if (hotServiceProviderList.length === 0) {
            this.facilitatorIdsList = [
              {
                nickName: "服务商列表",
                options: noHotServiceProviderList || [],
              },
            ];
          } else {
            this.facilitatorIdsList = [
              {
                nickName: "推荐服务商",
                options: hotServiceProviderList || [],
              },
              {
                nickName: "服务商列表",
                options: noHotServiceProviderList || [],
              },
            ];
          }
        }
      );
    },
    getFacilitatorUserListFtn() {
      getFacilitatorUserList().then(
        (res) => (this.userIdsList = res.data || [])
      );
    },
    getSelectLikeUserByUserNameFtn(val = "") {
      this.apploading = true;
      const params = { userName: val || "" };
      getSelectLikeUserByUserName(params)
        .then((res) => {
          this.userIdsList = res.data || [];
        })
        .finally(() => (this.apploading = false));
    },
    remoteMethod(val) {
      this.getSelectLikeUserByUserNameFtn(val);
    },
    templateClick(url) {
      window.open(this.ensureFullUrl(url));
    },
    beforeFileUpload(file, types) {
      const typeArr = types
        ? types.replace(/\./g, "").split(",")
        : ["doc", "docx", "pdf", "xlsx"];
      var FileExt = file.name.replace(/.+\./, "");
      if (typeArr.indexOf(FileExt.toLowerCase()) === -1) {
        this.$message({
          type: "warning",
          message: "请上传正确的文件！",
        });
        return false;
      }
    },
    uploadFileApiFtn(event, key, index) {
      let fileData = new FormData();
      fileData.append("file", event.file);
      uploadApi(fileData).then((res) => {
        const { data } = res;
        if (key == "responses") {
          this.restForm = {
            ...this.restForm,
            responses: this.setResponses(index, data),
          };
          this.$refs.restForm.clearValidate(`responses.${index}.file`);
        } else {
          this.restForm = { ...this.restForm, replyUrl: data.url };
        }
      });
    },
    removeFile(file, key, index) {
      if (key == "responses") {
        const { responses } = this.restForm;
        this.restForm = {
          ...this.restForm,
          responses: this.setResponses(index),
        };
      } else {
        this.restForm = { ...this.restForm, replyUrl: "" };
      }
    },
    setResponses(index, data = "") {
      const { responses } = this.restForm;
      return responses.map((t, i) => {
        return i == index
          ? {
              ...t,
              file: data ? data.url : null,
              fileName: data ? data.name : null,
            }
          : t;
      });
    },
    reset() {
      this.restForm = {
        ...this.restForm,
        userIds: [],
        facilitatorIds: [],
        // responses:[],
      };
      this.$refs["fileModel"] &&
        this.$refs["fileModel"].map((t) => t.clearFiles());
      this.resetForm("restForm");
    },
    restClick(t) {
      this.openTitle = t;
      this.$nextTick(() => {
        this.openWidth = t === "处理" ? "1200px" : "500px";
        this.open = true;
        this.reset();
      });
    },
    submitRestForm() {
      this.$refs["restForm"].validate((valid) => {
        if (valid) {
          const type = this.openTitle === "驳回原因" ? "REFUSE" : "";
          this.setExamineFtn(type);
        }
      });
    },
    setExamineFtn(type = "") {
      this.dialoDing = true;
      const { openTitle, restForm } = this;
      const params = {
        flowInstanceId: this.flowInstanceId,
        examineType: type || undefined,
      };
      if (openTitle == "驳回原因") {
        params.reason = restForm.reason;
      } else if (openTitle == "服务商分配") {
        params.facilitatorIds = restForm.facilitatorIds.join(",");
      } else if (openTitle == "用户分配" || openTitle == "转派") {
        params.userIds = restForm.userIds.join(",");
      } else if (openTitle === "处理") {
        params.responses = restForm.responses;
      } else if (openTitle === "回复") {
        params.request = {
          replyUrl: restForm.replyUrl || undefined,
          replyContent: restForm.replyContent,
        };
      }
      setExamine(params)
        .then((res) => {
          this.$modal.msgSuccess("提交成功");
          this.back();
        })
        .finally(() => (this.dialoDing = false));
    },
    resize(val) {
      this.footerWidth = val ? val.width : "0px";
    },
    back() {
      this.$router.back();
    },
    roll() {
      this.$nextTick(() => {
        this.$refs.content.scrollTop = 0;
      });
    },
    submitFtnSon() {
      this.$refs[this.type].submitForm();
    },
    async submitFtn(data, callback) {
      // this.$refs["currentForm"].validate(async (valid) => {
      //   if (valid) {
      //   }
      // });
      const { flowInstanceId = "" } = this;
      this.submitDing = true;
      const paramsAll = {
        productType: this.type,
        params: data,
        flowInstanceId: flowInstanceId || undefined,
        // labelIds: this.currentForm.labelIds,
      };
      const res = await submitProduct(paramsAll).catch((err) => {
        return err;
      });
      if (res && res.code == 200) {
        this.$modal.msgSuccess(!flowInstanceId ? "新增成功" : "修改成功");
        this.back();
      }
      callback(res);
      this.submitDing = false;
    },
  },
};
</script>

<style lang="scss">
.releasePage {
  height: calc(100vh - 132px);
  /* height: 100vh; */
  /* padding: 28px 0 20px; */
  padding: 28px 0 30px;
  background-color: #fff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;

  .el-button.el-button--danger {
    color: #fff !important;
  }

  .el-button--danger {
    background-color: #eb0101 !important;
    border-color: #eb0101 !important;

    &:hover {
      background-color: #d60606 !important;
      border-color: #d60606 !important;
    }
  }

  .title_header {
    padding: 0 24px 12px;
    border-bottom: 1px solid #dbdfe9;

    .el-button {
      padding: 0;
      margin-right: 16px;
    }

    .title {
      display: inline-block;
      font-size: 18px;
      margin: 0;
      vertical-align: middle;
      padding-left: 20px;
      color: #181f2d;
      font-weight: bold;
      border-left: 1px solid #dbdfe9;
    }
  }

  .audit-detail {
    /* margin-top: 40px; */
    margin-top: 30px;
    flex: 1;
    overflow: auto;
    padding: 0 20px;
    padding-bottom: 30px;
  }

  .pageFooter {
    position: fixed;
    bottom: 0px;
    background: #ffffff;
    box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
    opacity: 1;
    border-radius: 0px;
    height: 60px;
    display: flex;
    justify-content: end;
    align-items: center;
    right: 20px;
  }
}
</style>
