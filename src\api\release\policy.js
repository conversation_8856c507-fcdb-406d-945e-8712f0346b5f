import { requestPlatForm } from "@/utils/requestBase";
import request from "@/utils/request";

//行业
export function getSysIndustryType() {
  return request({
    url: '/system/sysIndustryType/tree',
    method: 'get',
  })
}

//产业行业
export function getSysIndustry() {
  return request({
    url: '/system/sysIndustryKind/getSubstitutionTree',
    method: 'get',
  })
}

//添加政策信息
export function saveOrUpdatePolicy(data) {
  return requestPlatForm({
    url: "/policyDynamics/saveOrUpdatePolicy",
    method: "post",
    data: data,
  });
}
