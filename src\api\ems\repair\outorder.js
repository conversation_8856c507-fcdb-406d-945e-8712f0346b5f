import request from '@/utils/request'

export function fetchList(query) {
    return request({
        url: '/ems/emsrepairorder/outPageVo',
        method: 'get',
        params: query
    })
}

export function getObj(id) {
    return request({
        url: '/ems/emsrepairorder/getOutOrder/' + id,
        method: 'get'
    })
}

/*审批通过*/
export function adoptOrder(obj) {
    return request({
        url: '/ems/emsrepairorder/adoptOrder',
        method: 'post',
        data: obj
    })
}
/*审批驳回*/
export function reject(obj) {
    return request({
        url: '/ems/emsrepairorder/reject',
        method: 'post',
        data: obj
    })
}

/*已解决*/
export function resolved(obj) {
    return request({
        url: '/ems/emsrepairorder/resolved',
        method: 'post',
        data: obj
    })
}