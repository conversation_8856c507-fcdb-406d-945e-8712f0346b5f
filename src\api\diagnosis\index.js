import request from "@/utils/request";

// 查询线下诊断列表
export function getOfflineList(data) {
  return request({
    url: "/platform/front/diagnosticOffline/queryByPageAdmin",
    method: "post",
    data,
  });
}

//查询线下诊断详情
export function getOfflineDetail(params) {
  return request({
    url: "/platform/product/getInfo",
    method: "get",
    params,
  });
}

//查询线上诊断模板列表
export function getOnlineModeList(params) {
  return request({
    url: "/platform/template/list",
    method: "get",
    params,
  });
}

//新增线上模板
export function addOnlieMode(data) {
  return request({
    url: "/platform/template",
    method: "post",
    data,
  });
}
//编辑线上模板
export function updateOnlieMode(data) {
  return request({
    url: "/platform/template/update",
    method: "post",
    data,
  });
}

//获取模板评分分页列表
export function getScoreTbData(data) {
  return request({
    url: "/platform/AssessmentSurveyTemplateScoreController/queryPage",
    method: "post",
    data,
  });
}

//新增模板评分
export function addScore(data) {
  return request({
    url: "/platform/AssessmentSurveyTemplateScoreController/add",
    method: "post",
    data,
  });
}

//编辑模板评分
export function editScore(data) {
  return request({
    url: "/platform/AssessmentSurveyTemplateScoreController/updateById",
    method: "post",
    data,
  });
}

//删除模板评分
export function deleteScore(idStr) {
  return request({
    url: "/platform/AssessmentSurveyTemplateScoreController/" + idStr,
    method: "delete",
  });
}

//获取模板list
export function getModeList(params) {
  return request({
    url: "/platform/template/listAll",
    method: "get",
    params,
  });
}

//获取能力域分页列表
export function getDomainTableData(params) {
  return request({
    url: "/platform/selfassessment/surveyType/list",
    method: "get",
    params,
  });
}

//新增编辑能力域
export function editDomainData(data) {
  return request({
    url: "/platform/selfassessment/surveyType",
    method: data.id ? "put" : "post",
    data,
  });
}
//删除能力域
export function deleteDomainData(idStr) {
  return request({
    url: "/platform/selfassessment/surveyType/" + idStr,
    method: "delete",
  });
}

//根据模板id获取能力域列表
export function getDomainByModeId(params) {
  return request({
    url: "/platform/selfassessment/surveyType/listByTemplateId",
    method: "get",
    params,
  });
}

//获取能力子域分页列表
export function getChildDomainTableData(params) {
  return request({
    url: "/platform/selfassessment/subType/list",
    method: "get",
    params,
  });
}

//新增编辑能力子域
export function editChildDomainData(data) {
  return request({
    url: "/platform/selfassessment/subType",
    method: data.id ? "put" : "post",
    data,
  });
}
//删除能力子域
export function deleteChildDomainData(idStr) {
  return request({
    url: "/platform/selfassessment/subType/" + idStr,
    method: "delete",
  });
}
//根据能力域id获取能力子域
export function getChildDomainByDomainId(params) {
  return request({
    url: "/platform/selfassessment/subType/listAll",
    method: "get",
    params,
  });
}

//获取题库分页列表
export function getQuestionTableData(params) {
  return request({
    url: "/platform/selfassessment/question/list",
    method: "get",
    params,
  });
}

//新增题库
export function addQuestionData(data) {
  return request({
    url: "/platform/selfassessment/question/addQuestionOption",
    method: "post",
    data,
  });
}
//编辑题库
export function updateQuestionData(data) {
  return request({
    url: "/platform/selfassessment/question/edit",
    method: "post",
    data,
  });
}

//根据id查询题库
export function getQuestionById(id) {
  return request({
    url: "/platform/selfassessment/question/" + id,
    method: "get",
  });
}

//删除题库
export function deleteQuestionData(idStr) {
  return request({
    url: "/platform/selfassessment/question/" + idStr,
    method: "delete",
  });
}

//获取报告分页列表
export function getReportTbData(data) {
  return request({
    url: "/platform/selfassessment/informant/queryByPageAssessment",
    method: "post",
    data,
  });
}
//获取报告分页列表(新)
export function getReportTbDataNew(params) {
  return request({
    url: "/platform/selfassessment/informant/queryByPageReport",
    method: "get",
    params,
  });
}
//获取报告详情
export function selectRecordsByFlowInstanceId(params) {
  return request({
    url: "/platform/selfassessment/informant/selectRecordsByFlowInstanceId",
    method: "get",
    params,
  });
}

export function getDiagnosisType(params) {
  return request({
    url: "/platform/front/diagnosticOffline/getDiagnosisType",
    method: "get",
    params,
  });
}

/**
 * @name 获取线下评价列表
 * @param {*} param0.pageNum 当前页
 * @param {*} param0.pageSize 页码数
 * @param {*} param0.districtName 区县
 * @param {*} param0.serviceProviderName 诊断机构
 * @param {*} param0.deptName 企业名称
 * @returns
 */
export function evaluateList({
  pageNum,
  pageSize,
  districtName,
  serviceProviderName,
  deptName,
}) {
  return request({
    url: "/platform/evaluate/score/all",
    method: "post",
    data: {
      pageNum,
      pageSize,
      districtName,
      serviceProviderName,
      deptName,
    },
  });
}
export function postEvaluateTable(data) {
  return request({
    url: "/platform/evaluate/all",
    method: "post",
    data,
  });
}
export function postEvaluateScore(data) {
  return request({
    url: "/platform/evaluate/score",
    method: "post",
    data,
  });
}
