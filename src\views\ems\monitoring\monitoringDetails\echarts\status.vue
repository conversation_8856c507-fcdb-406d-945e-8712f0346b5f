<template>
  <div id="status" :style="{width: '300px', height: '130px'}"></div>
</template>

<script>
export default {
  data() {
    return {};
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let status = this.$echarts.init(document.getElementById('status'))

      let dataList = [
        { value: 1048, name: '加工' },
        { value: 735, name: '待机' },
        { value: 580, name: '关机' }
      ]

      const colorList = ['#54b03a', '#f09035', '#ea3323'];

      // 绘制图表
      status.setOption({
        tooltip:{},
        color: colorList,
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            label: {
              show: false
            },
            data: dataList
          }
        ]
      });
    }
  }
}

</script>
