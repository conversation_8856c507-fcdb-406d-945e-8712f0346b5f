<template>
  <div id="repairNum" :style="{width: '500px', height: '250px'}"></div>
</template>

<script>

let tyOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '1%',
    right: '10%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    show: false
  },
  yAxis: {
    type: 'category',
    axisLabel: {
      show: true,
      textStyle: {
        fontSize: 14,
        color: '#aeaeb2'
      },
    },
    splitLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    data: []
  },
  series: [
    {
      type: 'bar',
      label: {
        show: true, //开启显示
        position: 'right', //在上方显示
        textStyle: { //数值样式
          color: '#76da91',
          fontSize: '12'
        }
      },
      barWidth: '15px',
      color: '#76da91',
      data: [],
    }
  ]
};

import {
  useStatusList,
} from "@/api/ems/statistical/device";
export default {
  data() {
    return {
      tyOption,
      useStatusData: []
    };
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let repairNum = this.$echarts.init(document.getElementById('repairNum'))
      useStatusList().then(res => {
        this.useStatusData= res.data.data;
        // console.log("111>>>>>>>>" , JSON.stringify(this.useStatusData))
        if (this.useStatusData.length > 0) {
          let xData = [];
          let yData = [];
          this.useStatusData.forEach(item => {
            if (item.useStatus == 0) {
              xData.push("在用");
              yData.push(item.useStatusNum);
            } else if (item.useStatus == 1) {
              xData.push("闲置");
              yData.push(item.useStatusNum);
            } else if (item.useStatus == 2) {
              xData.push("出租");
              yData.push(item.useStatusNum);
            } else if (item.useStatus == 3) {
              xData.push("禁用");
              yData.push(item.useStatusNum);
            }else if (item.useStatus == 4) {
              xData.push("报废");
              yData.push(item.useStatusNum);
            }

          });
          tyOption.yAxis.data = xData;
          tyOption.series[0].data = yData;
          repairNum.setOption(this.tyOption);
        } else {
          repairNum.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }
      });
    }
  }
}

</script>
