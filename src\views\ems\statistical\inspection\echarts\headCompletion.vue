<template>
  <div id="headCompletion" :style="{width: '600px', height: '300px'}"></div>
</template>

<script>
import {personCompletionList} from "@/api/ems/statistical/insperction";

let numberEchartsOptions = {

  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: []
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '应检设备数',
      type: 'bar',
      barWidth: 30,
      color: '#63b2ee',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#63b2ee',
              fontSize: 12,
            },
          },
        },
      },
    },
    {
      name: '已检设备数',
      type: 'bar',
      barWidth: 30,
      color: '#76da91',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#76da91',
              fontSize: 12,
            },
          },
        },
      },
    },
    {
      name: '漏检设备数',
      type: 'bar',
      barWidth: 30,
      color: '#f89588',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#f89588',
              fontSize: 12,
            },
          },
        },
      },
    },
    {
      name: '异常设备数',
      type: 'bar',
      barWidth: 30,
      color: '#f8cb7f',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#f8cb7f',
              fontSize: 12,
            },
          },
        },
      },
    }
  ]
}

export default {
  data() {
    return {
      allData: [],
      numberEchartsOptions,
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let headCompletion = this.$echarts.init(document.getElementById('headCompletion'))

      personCompletionList().then(res => {
        this.allData = res.data.data;
        console.log("1111>>>>>", JSON.stringify(this.allData));

        if (this.allData.length > 0) {
          //x轴数据
          let xData = [];
          let xAllData = [];
          xAllData = this.allData[0].userNameCharts;
          xAllData.forEach(function (item, index) {
            xData.push(item.userName);
          });
          //数据
          this.allData.forEach(function (item, index) {
            item.userNameCharts.forEach((item1) => {
              if (item1.value < 0) {
                item1.value = 0;
              }
            });
            numberEchartsOptions.series[index].name = item.showName;
            numberEchartsOptions.series[index].data = item.userNameCharts;
          });

          numberEchartsOptions.xAxis[0].data = xData;

          // 绘制图表xData
          headCompletion.setOption(this.numberEchartsOptions);
        } else {
          headCompletion.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }

      });
    }
  }
}

</script>
