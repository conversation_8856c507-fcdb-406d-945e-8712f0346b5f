import { requestPlatForm } from "@/utils/requestBase";



export function gatewayList(query) {
    return requestPlatForm({
      url: '/gateway/manage/list',
      method: 'get',
      params: query
    })
  }
//车间
export function getFlagTree() {
    return requestPlatForm({
      url: '/workshop/manage/tree/0',
      method: 'get',
    })
  }
//标签字典
export function getManageDict() {
    return requestPlatForm({
      url: '/label/manage/dict',
      method: 'get',
    })
  }
export function addManage(data) {
    return requestPlatForm({
      url: '/gateway/manage',
      method: 'post',
      data: data
    })
}
export function editManage(data) {
  return requestPlatForm({
    url: '/gateway/manage',
    method: 'put',
    data: data
  })
}
export function getManageData(id) {
  return requestPlatForm({
    url: '/gateway/manage/'+id,
    method: 'get',
  })
}
export function deleteManage(id) {
  return requestPlatForm({
    url: '/gateway/manage/',
    method: 'delete',
    data: [id]
  })
}
export function restart(data) {
  return requestPlatForm({
    url: '/gateway/manage/restart',
    method: 'post',
    data: data
  })
}










//驱动
export function gatDriveList(query) {
  return requestPlatForm({
    url: '/gateway/manage/agreement',
    method: 'get',
    params: query
  })
}

//设备类型
export function getDrivetypes() {
  return requestPlatForm({
    url: '/drive/manage/type',
    method: 'get',
  })
}


export function installManage() {
  return requestPlatForm({
    url: '/gateway/manage/agreement/install',
    method: 'post',
  })
}






