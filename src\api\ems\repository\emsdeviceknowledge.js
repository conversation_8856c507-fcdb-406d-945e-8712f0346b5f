import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/ems/emsdeviceknowledge/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/ems/emsdeviceknowledge',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/ems/emsdeviceknowledge/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/ems/emsdeviceknowledge/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/ems/emsdeviceknowledge',
    method: 'put',
    data: obj
  })
}
