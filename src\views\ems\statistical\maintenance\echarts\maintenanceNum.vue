<template>
  <div id="maintenanceNum" :style="{width: '100%', height: '300px'}"></div>
</template>

<script>
import {getMaintainNumberList} from "@/api/ems/inspection/emsinsinspecttask"

let numberEchartsOptions = {
  tooltip: {
    trigger: 'axis',
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      position: 'bottom',
    },
  ],
  yAxis: {
    type: 'value'
  },
  color: '#63b2ee',
  series: [
    {
      name: '保养次数',
      itemStyle : { normal: {label : {show: true}}},
      data: [],
      type: 'line',
      connectNulls: true,
    },
  ]
};
export default {
  data() {
    return {
      maintainNumberData: [],
      numberEchartsOptions,
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let maintenanceNum = this.$echarts.init(document.getElementById('maintenanceNum'))

      getMaintainNumberList().then(res => {
        this.maintainNumberData = res.data.data;
        this.maintainNumberData.forEach(function (item) {
          numberEchartsOptions.series[0].data.push(item.num);
        })
        // 绘制图表xData
        maintenanceNum.setOption(this.numberEchartsOptions)
      });


    }
  }
}

</script>
