<template>
    <div class="execution">
        <div v-if="listFlag">
            <el-card class="box-card btn-search page-search">
                <div slot="header" class="clearfix">
                    <div class="btn-box">
                        <el-button
                                type="info"
                                icon="el-icon-refresh-left"
                                @click="refreshChange()"
                        ></el-button>
                        <el-button
                                id="gwButton"
                                type="goon"
                                icon="el-icon-circle-plus-outline"
                                v-if="true"
                                @click="toAdd()"
                        >新增
                        </el-button
                        >
                        <el-button
                                type="success"
                                icon="el-icon-edit"
                                v-if="true"
                                :disabled="single"
                                @click="handleEdit"
                        >编辑
                        </el-button
                        >
                        <el-button
                                type="danger"
                                icon="el-icon-circle-close"
                                v-if="true"
                                @click.native="handleDel()"
                                :disabled="multiple"
                        >删除
                        </el-button
                        >
                        <el-button
                                type="primary"
                                icon="el-icon-upload"
                                @click="$refs.excelUpload.show()"
                        >导入
                        </el-button>

                        <el-button type="check" icon="el-icon-download" @click="exportExcel"
                        >导出
                        </el-button>
                    </div>
                    <div class="icon-box">
                        <i class="el-icon-search" @click="searchShow"></i>
                        <i class="el-icon-refresh" @click="refreshChange"></i>
                        <i class="el-icon-goods"></i>
                        <i class="el-icon-setting" @click="columnShow"></i>
                        <i class="icon-zuixiaohua"/>
                    </div>
                </div>
            </el-card>
            <!--excel 模板导入 -->
            <excel-upload
                    ref="excelUpload"
                    title="设备台账信息导入"
                    url="/platform/emsdeviceaccount/import"
                    temp-url="/admin/sys-file/local/file/deviceaccount.xlsx"
                    @refreshDataList="refreshChange"
            ></excel-upload>
            <basic-container>
                <avue-crud
                        ref="crud"
                        :page.sync="page"
                        :data="tableData || []"
                        :permission="permissionList"
                        :table-loading="tableLoading"
                        :option="tableOption || {}"
                        @selection-change="selectionChange"
                        @on-load="getList"
                        @search-change="searchChange"
                        @refresh-change="refreshChange"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        @row-update="handleUpdate"
                        @row-save="handleSave"
                        @row-del="handleDel"
                        :cell-style="cellStyle"
                        @cell-click="cellClick"
                >
                    <template slot="header">
                        <IconTitle class="selfTitle" title="设备台账" imgUrl="yunwei" />
                    </template>
                    <template slot-scope="scope" slot="menu">
                        <div style="float: left;height: 30px;width:52px;margin-top: 5px;"
                             v-if="scope.row.isInfoModel=='1'">

                                <div style="float: left;height: 30px">
                                    <img src="@/static/ems/deviceAccount/bug.png" style="width: 26px;height: 26px"/>
                                </div>
                                <div style="float: left;height: 30px;text-align: center">
                                    <el-tooltip placement="top" effect="light">
                                        <div slot="content">该设备从信息模型同步<br/>信息模型<span style="color:red;">已删除</span>该设备</div>
                                        <span style="color: #DEA11E;">异常</span>
                                    </el-tooltip>

                                </div>

                        </div>
                        <div style="float: left;height: 30px;margin-top: 5px;width:52px;" v-else>
                        </div>
                        <el-button type="text" @click="handleEdit(scope.row)">
                            <i class="el-icon-edit"></i>编辑
                        </el-button
                        >
                        <el-button type="text" @click="goLook(scope.row,scope.column)">
                            <i class="el-icon-view"></i>查看
                        </el-button
                        >
                    </template>
                </avue-crud>
            </basic-container>
        </div>
        <div v-else>
            <IndexAdd :id='addEditId' :listFlag='listFlag' :showStatus = "showStatus"/>
        </div>
        <!-- 查看详情 -->
        <el-drawer
                class="drawerStyle"
                title="设备详情"
                :visible.sync="drawer"
                direction="rtl"
                size="50%"
                append-to-body
        >
            <drawer-con :deviceData="deviceData" :imgArray="imgArray" :latestTallyData="latestTallyData"
                        :childDevice="childDevice"/>
        </el-drawer>
    </div>
</template>
<script>
    import {
        fetchList,
        getObj,
        addObj,
        putObj,
        delObj,
        getUpdateEcho,
        getLatestTally
    } from "@/api/ems/equipment/account";
    import {tableOption} from "@/const/crud/ems/equipment/account";

    import {mapGetters} from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/icon-title/index.vue";
    import IndexAdd from "./indexAdd.vue";
    import DrawerCon from "./drawerCon.vue";
    import ExcelUpload from "@/components/upload/excel";

    export default {
        name: "emsdeviceaccount",
        components: {
            IconTitle,
            IndexAdd,
            DrawerCon,
            ExcelUpload,
        },
        data() {
            return {
                tableData: [],
                searchForm: {}, // 查询参数
                single: true, // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableLoading: false,
                tableOption: tableOption,
                ids: [],
                addEditId: 0,
                selectionList: [],
                listFlag: true,
                showStatus: 0,
                drawer: false,
                deviceData: {},
                latestTallyData: {
                    provingTime: "",
                    surveyor: '',
                    abnormalNum: '',
                    insInspectTaskList: {
                        itemsName: "",
                        inspectValue: "",
                        isAbnormal: "",
                    },
                },
                imgArray: [],
                childDevice: [],
            };
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(
                        this.permissions.ems_emsdeviceaccount_addaaaa,
                        false
                    ),
                    delBtn: this.vaildData(
                        this.permissions.ems_emsdeviceaccount_delaaa,
                        false
                    ),
                    editBtn: this.vaildData(
                        this.permissions.ems_emsdeviceaccount_editaaaaa,
                        false
                    ),
                };
            },
        },
        mounted() {
            this.initElement();
        },

        methods: {
            cellStyle(data) {
                // 表格的第一列加上小手和padding为0
                // if (data.columnIndex === 1) {
                //     return "color:#02b980;cursor:pointer";
                // }
                return 'cursor:pointer';
            },
            toAdd() {
                this.addEditId = 0
                this.listFlag = false;
            },
            handleEdit(row) {
                this.addEditId = row.id || this.selectionList[0].id;
                this.listFlag = false;
                this.showStatus = 1;
            },
            cellClick(row, column) {
                // if (column.property === "deviceNum") {
                //     this.$router.push({
                //         path: "/ems/equipment/account/detail",
                //         query: {
                //             id: row.id
                //         }
                //     });
                // } else if (column.label == "操作") {
                // } else {
                    this.drawer = true;
                    getUpdateEcho(row.id).then(res => {
                        this.deviceData = res.data;
                        this.imgArray = res.data.imgArray;
                        this.childDevice = res.data.sonChildren;
                    });
                    getLatestTally(row.id).then(res => {
                        if (res.data != null) {
                            this.latestTallyData = res.data;
                        } else {
                            this.latestTallyData = {};
                        }
                    });
                // }
            },
            goLook(row, column) {
                // console.log("bbb", column)
                // this.drawer = true;
                // getUpdateEcho(row.id).then(res => {
                //   this.deviceData = res.data;
                //   this.imgArray = res.data.imgArray;
                //   this.childDevice = res.data.sonChildren;
                // })
                // getLatestTally(row.id).then(res => {
                //   if (res.data != null) {
                //     this.latestTallyData = res.data;
                //   } else {
                //     this.latestTallyData = {};
                //   }
                // })

                this.$router.push({
                    path: "./detail",
                    query: {
                        id: row.id
                    }
                })
            },
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList = list;
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },

            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow =
                    !this.$refs.crud.$refs.headerSearch.searchShow;
            },

            // 列表查询
            getList(page, params) {
                this.tableLoading = true;
                fetchList(
                    Object.assign(
                        {
                            current: page.currentPage,
                            size: page.pageSize,
                        },
                        params,
                        this.searchForm
                    )
                )
                    .then((response) => {
                        this.tableData = response.data.records;
                        this.page.total = response.data.total;
                        this.tableLoading = false;
                    })
                    .catch(() => {
                        this.tableLoading = false;
                    });
            },

            // 删除
            handleDel: function (row, index) {
                this.$confirm("是否确认删除所选数据项", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        let id = "";
                        if (row) {
                            id = row.id;
                        } else {
                            id = this.ids;
                        }
                        return delObj(id);
                    })
                    .then((data) => {
                        if (data.code === 200) {
                            this.$message.warning(data.msg);
                        } else {
                            this.$message.success("删除成功");
                        }
                        this.getList(this.page);
                    });
            },

            // 更新
            handleUpdate: function (row, done, loading) {
                putObj(row)
                    .then((data) => {
                        this.$message.success("修改成功");
                        done();
                        this.getList(this.page);
                    })
                    .catch(() => {
                        loading();
                    });
            },

            // 保存
            handleSave(row, index, done, loading) {
                addObj(row)
                    .then((data) => {
                        this.$message.success("添加成功");
                        done();
                        this.getList(this.page);
                    })
                    .catch(() => {
                        loading();
                    });
            },

            // 每页条数改变事件
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            // 当前页发生改变事件
            currentChange(current) {
                this.page.currentPage = current;
            },
            // 查询事件
            searchChange(form, done) {
                this.searchForm = form;
                this.page.currentPage = 1;
                this.getList(this.page, form);
                done();
            },
            // 刷新事件
            refreshChange() {
                this.getList(this.page);
            },
            // 导出excel
            exportExcel() {
                this.$download.getXlsx(
                    process.env.VUE_APP_BASE_API + "/platform/emsdeviceaccount/export",
                    this.searchForm,
                    "设备台账.xlsx"
                );
            }
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/ems/avue.scss";

    .el-button--goon.is-active,
    .el-button--goon:active {
        background: #02b980;
        border-color: #02b980;
        color: #fff;
    }

    .el-button--goon:focus,
    .el-button--goon:hover {
        background: #02b980;
        border-color: #02b980;
        color: #fff;
    }

    .el-button--goon {
        color: #FFF;
        background-color: #02b980;
        border-color: #02b980;
    }

    .drawerStyle {
        ::v-deep .el-drawer__header {
            background-color: #f2f2f5;
            padding: 20px 0 20px 20px;
            color: #101010;
            margin-bottom: 20px;
        }
    }
    .clearfix {
        height: 40px;
        position: relative;
        .btn-box {
            position: absolute;
            top: 0;
            left: 0;
        }
        .icon-box {
            position: absolute;
            right: 0;
            top: 0;
            height: 40px;
        }
    }
</style>
