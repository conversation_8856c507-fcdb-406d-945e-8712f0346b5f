<template>
  <div class="add-box" style="height: 100%">
    <div class="info-box">
      <el-card shadow="always" class="box-card">
        <div class="tableTitle"><span>基本信息</span></div>
        <div class="devTitle"><span>{{ form.standardName }}</span></div>
        <div>
          <div class="tableStyle">
            <div class="labelS">编号</div>
            <div class="contentS">{{ form.standardNum }}</div>
            <div class="labelS">名称</div>
            <div class="contentS">{{ form.standardName }}</div>
          </div>
          <div class="tableStyle">
            <div class="labelS">适用公司</div>
            <div class="contentS">所有</div>
            <div class="labelS">检修周期</div>
            <div class="contentS">{{ form.inspectCycle }}日</div>
          </div>
          <div class="tableStyle">
            <div class="labelS">适用设备类别</div>
            <div class="contentS">{{ form.categoryName }}</div>
          </div>
          <div class="tableStyle">
            <div class="labelS">作业内容</div>
            <div class="contentS">
              {{ form.jobContent }}
            </div>
          </div>
          <div class="tableStyle">
            <div class="labelS">技术要求</div>
            <div class="contentS">
              {{ form.technicalRequirement }}
            </div>
          </div>
          <div class="tableStyle">
            <div class="labelS">安全要点</div>
            <div class="contentS">
              {{ form.safety }}
            </div>
          </div>
          <div class="tableStyle">
            <div class="labelS">质量要求</div>
            <div class="contentS">{{ form.qualityRequirements }}</div>
          </div>
          <div class="tableStyle">
            <div class="labelS">备注</div>
            <div class="contentS">{{ form.remark }}</div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="info-box">
      <el-card shadow="always" class="box-card">
        <div class="headerStyle"><span class="tableTitle">指定设备</span></div>
        <el-table :data="deviceList">
          <el-table-column label="id" align="center" prop="id" v-if="false"/>
          <el-table-column label="设备编号" align="center" prop="deviceNum"/>
          <el-table-column label="设备名称" align="center" prop="deviceName"/>
          <el-table-column label="品牌" align="center" prop="brandNewName"/>
          <el-table-column label="规格型号" align="center" prop="specification"/>
        </el-table>
        <pagination
            v-show="queryParams.total>0"
            :total="queryParams.total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="deviceGetList"
        />
      </el-card>
    </div>
    <div class="info-btn-box">
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import Pagination from "@/components/Pagination/index.vue"
import {
  standardGetObj,
  deviceList
} from "@/api/ems/repair/emsreprepairstandard";

export default {
  name: "detailIndex",
  components: {
    IconTitle,
    Pagination
  },
  props: {
    id: {
      type: String,
    },
  },
  data() {
    return {
      // 设备数据
      deviceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      queryParamsCheckList: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },

      form: {
        id: null,
        standardNum: null,
        standardName: null,
        inspectCycle: null,
        categoryId: null,
        jobContent: null,
        technicalRequirement: null,
        safety: null,
        qualityRequirements: null,
        remark: null,
        deviceId: [],
      },
    };
  },
  mounted() {
    if (this.id > 0) {
      standardGetObj(this.id).then((res) => {
        this.form = res.data.data
        // console.log(JSON.stringify(res))
        deviceList(Object.assign(
                {
                  current: this.queryParams.pageNum,
                  size: this.queryParams.pageSize,
                },
                {id: this.id}
            )
        ).then(response => {
          this.deviceList = response.data.data.records;
          this.queryParams.total = response.data.data.total;
          for (var i = 0; i < this.deviceList.length; i++) {
            this.device.deviceForm.deviceId.push(this.deviceList[i].id);
          }
          this.device.open = false;
        });
      });
      this.checkGetList();

    }
    // fetchTree().then((response) => {
    //     this.treeData = response.data.data;
    // });
  },
  methods: {
    deviceGetList() {
      deviceList(Object.assign(
              {
                current: this.queryParamsDeviceList.pageNum,
                size: this.queryParamsDeviceList.pageSize,
              },
              {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceNewList = response.data.data.records;
        this.queryParamsDeviceList.total = response.data.data.total;
      });
    },
    checkGetList() {
      checkFetchList(Object.assign(
              {
                current: this.queryParamsCheckList.pageNum,
                size: this.queryParamsCheckList.pageSize,
              },
              {standardId: this.id}
          )
      ).then(response => {
        this.checkList = response.data.data.records;
        this.queryParamsCheckList.total = response.data.data.total;
      });
    },

    goBack() {
      this.$parent.listFlag = true;
      this.$parent.addllistFlag = false;
      this.$parent.detaillistFlag = false;
      //  this.$parent.refreshChange()
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.add-box {
  margin-bottom: 50px;

  .info-box {
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 10px 15px;
    overflow: hidden;

    .info-from {
      display: flex;
      flex-wrap: wrap;
      padding-top: 20px;
      position: relative;

      .el-form-item {
        width: 50%;
        padding-right: 10px;
      }
    }

    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {
      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }

  .info-btn-box {
    width: 100%;
    text-align: center;
  }

  .user {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}

.tableTitle {
  color: #333;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}

.devTitle {
  color: #262626;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;

  .el-card__body {
    padding-top: 10px;
  }

  .labelS {
    //display: inline-block;
    flex: 0 0 150px;
    //height: 40px;
    // margin-right: 10px;
    text-align: left;
    color: #606266;
    padding: 10px;
    border: 1px solid rgba(236, 240, 244, 100);
    margin-bottom: -1px;
  }

  .contentS {
    border: 1px solid rgba(236, 240, 244, 100);
    // height: 40px;
    color: #606266;
    width: 100%;
    margin-left: -1px;
    margin-bottom: -1px;
    padding: 10px;
    // margin: 10px 0;
    // width: calc(100% - 120px);
    // display: inline-block;
  }

  .tableStyle {
    display: flex;
  }

  .number {
    font-weight: bold;
    margin-top: 5px;
    font-size: 16px;
  }
}
</style>
