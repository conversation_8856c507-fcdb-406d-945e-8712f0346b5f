<template>
  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box" style="z-index: 9999999">
        <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <el-form-item label="标准编号" prop="standardNum">
            <el-input v-model="form.standardNum" maxlength="20" :disabled="true" placeholder="无需填写自动生成"/>
          </el-form-item>
          <el-form-item label="标准名称" prop="standardName">
            <el-input v-model="form.standardName" placeholder="请输入标准名称" maxlength="20"/>
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <treeselect
                v-model="form.deptId"
                :options="treeDeptData"
                :normalizer="normalizer"
                placeholder="请选择部门"
                :appendToBody="true"
            />
          </el-form-item>
          <el-form-item label="设备类别" prop="categoryId">
            <treeselect
                v-model="form.categoryId"
                :options="categoryList"
                :normalizer="normalizerEqu"
                placeholder="请选择设备类别"
                :appendToBody="true"
            />
          </el-form-item>
          <el-form-item label="要求" prop="requirement">
            <el-input type="textarea" v-model="form.requirement" placeholder="请输入要求" maxlength="100"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" maxlength="100"/>
          </el-form-item>
        </div>
      </div>
      <div class="info-box">
        <IconTitle title="设备配置" imgUrl="yunwei"></IconTitle>
        <!--                @selection-change="handleSelectionChange"-->
        <el-button style="float: right"
                   type="primary"
                   icon="el-icon-plus"
                   size="mini"
                   @click="addDeviceId"
        >新增
        </el-button>
        <el-table v-loading="loading" :data="deviceList">
          <el-table-column label="id" align="center" prop="id" v-if="false"/>
          <el-table-column label="设备编号" align="center" prop="deviceNum"/>
          <el-table-column label="设备名称" align="center" prop="deviceName"/>
          <el-table-column label="品牌" align="center" prop="brandNewName"/>
          <el-table-column label="规格型号" align="center" prop="specification"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="deviceListDele(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="queryParams.total>0"
            :total="queryParams.total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="deviceToList"
        />
        <!-- 用于添加设备的 -->
        <el-dialog :title="device.title" :visible.sync="device.open" width="800px" append-to-body>
          <el-table v-loading="device.loading" :data="deviceNewList"
                    :row-key="getRowKeys"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" align="center"  :reserve-selection="true" />
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备编号" align="center" prop="deviceNum"/>
            <el-table-column label="设备名称" align="center" prop="deviceName"/>
            <el-table-column label="品牌" align="center" prop="brandNewName"/>
            <el-table-column label="规格型号" align="center" prop="specification"/>
          </el-table>

          <pagination

              v-show="queryParamsDeviceList.total>0"
              :total="queryParamsDeviceList.total"
              :page.sync="queryParamsDeviceList.pageNum"
              :limit.sync="queryParamsDeviceList.pageSize"
              @pagination="deviceGetList"
          />
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" :disabled="device.dis" @click="submitDeiceList">确 定</el-button>
            <el-button @click="cancelDeviceList">取 消</el-button>
          </div>
        </el-dialog>
      </div>
      <div class="info-box" v-if="this.id>0 || this.criterionId>0">
        <IconTitle title="保养项目" imgUrl="yunwei"></IconTitle>
        <!--                @selection-change="handleSelectionChange"-->
        <el-button style="float: right"
                   type="primary"
                   icon="el-icon-plus"
                   size="mini"
                   @click="addCheck"
        >新增
        </el-button>
        <el-table v-loading="check.loading" :data="checkList">
          <el-table-column
              label="序号"
              width="70px">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="id" align="center" prop="id" v-if="false"/>
          <el-table-column label="编号" align="center" prop="itemsNum"/>
          <el-table-column label="项目" align="center" prop="itemsName"/>
          <el-table-column label="方法及基准" align="center" prop="methodBenchmark"/>
          <el-table-column label="类型" align="center" prop="type">
            <template slot-scope="scope">
                      <span>{{
                          scope.row.type == 1
                              ? "数字"
                              : scope.row.type == 2
                                  ? "选项"
                                  : ""
                        }}</span>
            </template>
          </el-table-column>
          <el-table-column label="下限" align="center" prop="lowerLimit"/>
          <el-table-column label="上线" align="center" prop="upperLimit"/>
          <el-table-column label="可选项" align="center" prop="optional"/>
          <el-table-column label="正常选项" align="center" prop="normalOption"/>
          <el-table-column label="参考图片" align="center" prop="referencePicture"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="checkEdit(scope.row)"
              >编辑
              </el-button>
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="checkDele(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="queryParamsCheckList.total>0"
            :total="queryParamsCheckList.total"
            :page.sync="queryParamsCheckList.pageNum"
            :limit.sync="queryParamsCheckList.pageSize"
            @pagination="checkGetList"
        />
        <!-- 用于添加设备的 -->
        <el-dialog :title="check.title" :visible.sync="check.open" width="600px" append-to-body>
          <el-form :model="checkForm" :rules="dataRule" ref="checkForm"
                   label-width="110px">
            <el-form-item label="保养项编号" prop="itemsNum">
              <el-input v-model="checkForm.itemsNum" maxlength="20" :disabled="true" placeholder="无需填写自动生成"></el-input>
            </el-form-item>
            <el-form-item label="项目" prop="itemsName">
              <el-input v-model="checkForm.itemsName" placeholder="项目" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="方法及基准" prop="methodBenchmark">
              <el-input v-model="checkForm.methodBenchmark" placeholder="方法及基准" maxlength="100"></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select
                  v-model="checkForm.type"
                  placeholder="请选择类型"
                  size="small"
                  clearable
                  value-key="type"
                  @change="changeDiaType()"
              >
                <el-option label="数字" :value="1"/>
                <el-option label="选项" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item label="最小值" prop="lowerLimit">
              <el-input
                  :disabled="checkForm.type != '1'"
                  v-model="checkForm.lowerLimit"
                  placeholder="请输入最小值"
                  maxlength="20"
              />
            </el-form-item>
            <el-form-item label="最大值" prop="upperLimit">
              <el-input
                  :disabled="checkForm.type != '1'"
                  v-model="checkForm.upperLimit"
                  placeholder="请输入最大值"
                  maxlength="20"
              />
            </el-form-item>

            <el-form-item label="可选项" prop="optional">
              <el-input
                  :disabled="true"
                  v-model="checkForm.optional"
                  placeholder="请输入可选项"
              />
            </el-form-item>
            <el-form-item label="正常选项" prop="normalOption">
              <el-input
                  :disabled="true"
                  v-model="checkForm.normalOption"
                  placeholder="请输入正常选项"
              />
            </el-form-item>
            <el-form-item label="参考图片" prop="referencePicture">
              <el-input v-model="checkForm.referencePicture" placeholder="参考图片"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
                      <el-button @click="checkBack">取消</el-button>
                      <el-button type="primary" :disabled="check.dis"
                                 @click="checkFormSubmit('checkForm')">确定</el-button>
                    </span>
        </el-dialog>
      </div>
      <div class="info-btn-box">
        <el-button type="primary" :disabled="dis" v-if="this.id>0 || this.criterionId>0"
                   @click="submitForm('ruleForm')">提交
        </el-button>
        <el-button type="primary" :disabled="dis" v-else-if="this.id<=0 ||this.criterionId<=0 "
                   @click="submitForm('ruleForm')">下一步
        </el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>

  </div>

</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";
import Pagination from "@/components/Pagination/index.vue"
import ImageUpload from "@/components/ems/ImageUpload/index.vue";
import {fetchListTree} from "@/api/ems/equipment/category";
import {getBrandList} from "@/api/ems/equipment/brand";
import {fetchTree} from "@/api/admin/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {mapGetters} from "vuex";
import {
  emsmaimaintenancestandardFetchList, emsmaimaintenancestandardGetObj,
  emsmaimaintenancestandardAddObj, emsmaimaintenancestandardPutObj,
  deviceList, deviceListAll
} from "@/api/ems/maintenance/emsmaimaintenancestandard";
import {
  checkFetchList,
  checkDelObj,
  checkGetObj,
  checkAddObj,
  checkPutObj
} from '@/api/ems/maintenance/emsmaimaintenanceitems';
import {remote} from '@/api/admin/dict';

export default {
  name: "AddIndex",
  components: {
    IconTitle,
    Treeselect,
    ImageUpload,
    Pagination
  },
  props: {
    id: {
      type: String,
    },
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "name",
      },
      criterionId: -1,
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
      },
      list: [],
      treeData: [],
      loading: false,
      dis: false,
      categoryList: [], //设备类别
      brandList: [], //设备品牌
      treeDeptData: [], //部门
      form: {
        id: null,
        standardNum: null,
        standardName: null,
        deptId: null,
        requirement: null,
        remark: null,
        deviceId: [],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      queryParamsDeviceList: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      queryParamsCheckList: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      device: {
        title: "",
        open: false,
        loading: false,
        dis: true,
        deviceForm: {
          id: null,
          standardNum: null,
          standardName: null,
          deptId: null,
          requirement: null,
          remark: null,
          deviceId: [],
        }
      },
      checkList: [],
      check: {
        title: "",
        open: false,
        loading: false,
        dis: true,

      },
      checkForm: {
        id: null,
        standardId: null,
        itemsNum: null,
        itemsName: null,
        methodBenchmark: null,
        type: null,
        lowerLimit: null,
        upperLimit: null,
        optional: null,
        normalOption: null,
        referencePicture: null,
      },
      dataRule: {
        inspectionType: [
          {required: true, message: '巡检类型不能为空', trigger: 'blur'}
        ],
        type: [
          {required: true, message: '类型不能为空', trigger: 'blur'}
        ],
        itemsName: [
          {required: true, message: '项目不能为空', trigger: 'blur'}
        ],
        upperLimit: [
          {required: false, trigger: "blur", message: "下限不能为空"},
        ],
        lowerLimit: [
          {required: false, trigger: "blur", message: "上限不能为空"},
        ],
        optional: [
          {required: false, trigger: "blur", message: "可选项不能为空"},
        ],
        normalOption: [
          {required: false, trigger: "blur", message: "正常选项不能为空"},
        ],

      },
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 类型字典
      typeList1: [{
        "id": 257,
        "dictId": 83,
        "value": "1",
        "label": "数字",
        "type": "ins_inspect_items_type",
        "description": "数字",
        "sort": 1,
        "createTime": "2022-01-13 17:21:15",
        "updateTime": "2022-01-13 17:21:15",
        "remarks": null,
        "delFlag": "0"
      }, {
        "id": 258,
        "dictId": 83,
        "value": "2",
        "label": "选项",
        "type": "ins_inspect_items_type",
        "description": "选项",
        "sort": 2,
        "createTime": "2022-01-13 17:21:33",
        "updateTime": "2022-01-13 17:21:33",
        "remarks": null,
        "delFlag": "0"
      }, {
        "id": 259,
        "dictId": 83,
        "value": "3",
        "label": "文本",
        "type": "ins_inspect_items_type",
        "description": "文本",
        "sort": 3,
        "createTime": "2022-01-13 17:21:45",
        "updateTime": "2022-01-13 17:21:45",
        "remarks": null,
        "delFlag": "0"
      }],
      // 设备数据
      deviceList: [],
      //所有的设备数据
      deviceNewList: [],
      coverImgTem: [],
      imgArrayTem: [],
      rules: {},
      dialogVisible: false,
    };
  },
  created() {
    this.getSelect();
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
      };
    },
  },
  mounted() {
    // remote("ins_inspect_items_type").then(response => {
    //   this.typeList = response.data;

    // });
    this.reset();
    this.deviceReset()
    if (this.id > 0) {
      emsmaimaintenancestandardGetObj(this.id).then((res) => {
        this.form = res.data
        // console.log(JSON.stringify(res))
        deviceList(Object.assign(
                {
                  current: this.queryParams.pageNum,
                  size: this.queryParams.pageSize,
                },
                {id: this.id}
            )
        ).then(response => {
          this.deviceList = response.data.records;
          this.queryParams.total = response.data.total;
          for (var i = 0; i < this.deviceList.length; i++) {
            this.device.deviceForm.deviceId.push(this.deviceList[i].id);
          }
          this.loading = false;
          this.device.open = false;
        });
      });
      this.checkGetList();

    }
    // fetchTree().then((response) => {
    //     this.treeData = response.data;
    // });
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    nodeClick(data) {
      this.page.page = 1;
      this.getListUser(this.page, {deptId: data.id});
    },
    deviceGetList() {

      this.device.loading = true;
      deviceList(Object.assign(
              {
                current: this.queryParamsDeviceList.pageNum,
                size: this.queryParamsDeviceList.pageSize,
              },
              {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceNewList = response.data.records;
        this.queryParamsDeviceList.total = response.data.total;
        this.device.loading = false;
      });
    },

    deviceToList() {
      this.loading = true;
      deviceListAll(Object.assign(
              {
                current: this.queryParams.pageNum,
                size: this.queryParams.pageSize,
              },
              {deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceList = response.data.records;
        this.queryParams.total = response.data.total;
        this.loading = false;
      });
    },
    checkGetList() {
      this.check.loading = true;
      checkFetchList(Object.assign(
              {
                current: this.queryParamsCheckList.pageNum,
                size: this.queryParamsCheckList.pageSize,
              },
              {standardId: this.id || this.criterionId}
          )
      ).then(response => {
        this.checkList = response.data.records;
        this.queryParamsCheckList.total = response.data.total;
        this.check.loading = false;
      });
    },
    deviceListDele(row) {
      for (var i = 0; i < this.deviceList.length; i++) {
        if (this.deviceList[i].id == row.id) {
          this.deviceList.splice(i, 1)
          this.queryParams.total = this.queryParams.total - 1;
        }
      }
      for (var i = 0; i < this.device.deviceForm.deviceId.length; i++) {
        if (this.device.deviceForm.deviceId[i] == row.id) {
          this.device.deviceForm.deviceId.splice(i, 1)
        }
      }
    },

    submitDeiceList() {
      this.device.dis = true;
      this.loading = true;
      this.ids = [...new Set(this.ids)];
      if (this.ids.length == 0) {
        this.device.open = false;
        this.loading = false;
        return;
      }
      for (var i = 0; i < this.ids.length; i++) {
        this.device.deviceForm.deviceId.push(this.ids[i]);
      }
      deviceListAll(Object.assign(
              {
                current: this.queryParams.pageNum,
                size: this.queryParams.pageSize,
              },
              {deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceList = response.data.records;
        this.queryParams.total = response.data.total;
        this.loading = false;
        this.device.open = false;
      });
    },
    changeDiaType() {
      if (this.checkForm.type == "2") {
        //选项
        this.checkForm.upperLimit = undefined;
        this.checkForm.lowerLimit = undefined;

        this.dataRule.optional[0].required = true;
        this.dataRule.normalOption[0].required = true;
        this.dataRule.lowerLimit[0].required = false;
        this.dataRule.upperLimit[0].required = false;
        this.checkForm.optional = "正常/异常"
        this.checkForm.normalOption = "正常"
      }
      if (this.checkForm.type == "1") {
        //数字
        this.checkForm.normalOption = undefined;
        this.checkForm.optional = undefined;

        this.dataRule.optional[0].required = false;
        this.dataRule.normalOption[0].required = false;
        this.dataRule.lowerLimit[0].required = true;
        this.dataRule.upperLimit[0].required = true;
      }
      // if (this.checkForm.type == "3") {
      //     //数字
      //     this.checkForm.upperLimit = undefined;
      //     this.checkForm.lowerLimit = undefined;
      //     this.checkForm.normalOption = undefined;
      //     this.checkForm.options = undefined;
      //
      //     this.dataRule.upperLimit[0].required = false;
      //     this.dataRule.lowerLimit[0].required = false;
      //     this.dataRule.options[0].required = false;
      //     this.dataRule.normalOption[0].required = false;
      // }
    },
    checkEdit(row) {
      this.check.loading = true;

      checkGetObj(row.id).then(response => {
        this.checkForm = response.data
        this.changeDiaType()
        if (this.checkForm.type == "1") {
          this.checkForm.type = 1
        } else if (this.checkForm.type == "2") {
          this.checkForm.type = 2
        }
        this.check.open = true;
        this.check.loading = false;
      })
    },
    checkDele(row) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return checkDelObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.checkGetList();
          });
    },
    checkBack() {
      this.check.open = false;
      this.check.loading = false;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    cancelDeviceList() {
      this.device.title = "";
      this.device.open = false;
    },
    addDeviceId() {
      this.device.title = "设备台账";
      this.device.open = true;
      this.device.dis = false;
      this.deviceGetList();
    },
    addCheck() {
      this.checkReset();
      this.check.title = "点检项目";
      this.check.open = true;
      this.check.dis = false;

    },

    selectUser(row) {
      this.form.liableUserName = row.username
      this.form.liableUserId = row.userId
      this.dialogVisible = false
    },
    toAdd() {
      this.device.title = "设备台账";
      this.device.open = true;
    },
    getListUser(page, params) {
      emsmaimaintenancestandardFetchList(
          Object.assign(
              {
                current: this.page.currentPage,
                size: this.page.pageSize,
              },
              params
          )
      ).then((response) => {
        this.list = response.data.records;
        this.page.total = response.data.total;
      });
    },

    checkFormSubmit(formName) {
      this.check.dis = true;
      this.checkForm.standardId = this.id || this.criterionId;
      let data = JSON.parse(JSON.stringify(this.checkForm));
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.canSubmit = false;
          if (this.checkForm.id) {
            checkPutObj(this.checkForm).then(data => {
              this.$notify.success('修改成功')
              this.visible = false
              this.check.open = false
              this.checkGetList()
            }).catch(() => {
              this.canSubmit = true;
            });
          } else {
            checkAddObj(this.checkForm).then(data => {
              this.$notify.success('添加成功')
              this.visible = false
              this.check.open = false
              this.checkGetList()
            }).catch(() => {
              this.canSubmit = true;
            });
          }
        }
      })
    },
    submitForm(formName) {
      this.dis = true;
      this.form.deviceId = this.device.deviceForm.deviceId;
      if (this.form.deviceId.length == 0) {
        this.$message.error("请添加设备");
        this.dis = false;
        return;
      }
      let data = JSON.parse(JSON.stringify(this.form));
      this.$refs[formName].validate((valid) => {
        if (data.id) {
          emsmaimaintenancestandardPutObj(data).then((res) => {
            this.reset();
            this.$parent.listFlag = true;
            this.$parent.addllistFlag = false;
            this.$parent.detaillistFlag = false;
            this.$message.success("修改成功")
            this.dis = false;
          });
        } else {
          emsmaimaintenancestandardAddObj(data).then((res) => {
            if (res.data.id != null) {
              this.criterionId = res.data.id;
              emsmaimaintenancestandardGetObj(this.criterionId).then((res) => {
                this.form = res.data
                // console.log(JSON.stringify(res))
                deviceList(Object.assign(
                        {
                          current: this.queryParams.pageNum,
                          size: this.queryParams.pageSize,
                        },
                        {id: this.criterionId}
                    )
                ).then(response => {
                  this.deviceList = response.data.records;
                  this.queryParams.total = response.data.total;
                  this.device.deviceForm.deviceId = [];
                  for (var i = 0; i < this.deviceList.length; i++) {
                    this.device.deviceForm.deviceId.push(this.deviceList[i].id);
                  }
                  this.loading = false;
                  this.device.open = false;
                });
              });
            }
            this.$parent.listFlag = false;
            this.$parent.addllistFlag = true;
            this.$parent.detaillistFlag = false;
            // this.$message.success("新增成功")
            this.dis = false;
          });
        }
      });
    },
    reset() {
      this.form = {
        id: null,
        standardNum: null,
        standardName: null,
        deptId: null,
        requirement: null,
        remark: null,
        deviceId: [],
        deviceNewList: [],
      };
    },
    deviceReset() {
      this.deviceForm = {
        id: null,
        standardNum: null,
        standardName: null,
        deptId: null,
        requirement: null,
        remark: null,
        deviceId: [],
        deviceNewList: [],
      };
    },
    checkReset() {
      this.checkForm = {
        id: null,
        standardId: null,
        itemsNum: null,
        itemsName: null,
        methodBenchmark: null,
        type: null,
        lowerLimit: null,
        upperLimit: null,
        optional: null,
        normalOption: null,
        referencePicture: null,
      };
    },

    getSelect() {
      // remote("ins_inspect_items_type").then(response => {
      //   this.typeList = response.data;
      // });
      fetchListTree("").then((res) => {
        this.categoryList = res.data ? res.data : [];
      });
      getBrandList().then((res) => {
        this.brandList = res.data;
      });
      //部门
      fetchTree().then((response) => {
        this.treeDeptData = response.data;
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    normalizerEqu(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    goBack() {
      this.$parent.listFlag = true;
      this.$parent.addllistFlag = false;
      this.$parent.detaillistFlag = false;
      //  this.$parent.refreshChange()
    },
  },
};
</script>
<style lang="scss">
.add-box {
  .el-dialog__body {
    height: 80vh;
  }

  .table-box {
    height: 100%;

    .table-big-box {
      overflow: auto;
      height: 80%;

    }

  }
}
</style>

<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.add-box {
  margin-bottom: 50px;

  .info-box {
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 10px 15px;
    overflow: hidden;

    .info-from {
      display: flex;
      flex-wrap: wrap;
      padding-top: 20px;
      position: relative;

      .el-form-item {
        width: 50%;
        padding-right: 10px;
      }
    }

    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {
      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }

  .info-btn-box {
    width: 100%;
    text-align: center;
  }

  .user {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
