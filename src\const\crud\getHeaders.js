import { getToken } from '@/utils/auth'
import store from '@/store'

export function getHeaders() {
    let avueHeaders = {}
    if (getToken()) {
        avueHeaders['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    const csrfToken = store.state.user['X-Csrf-Token'];
    if (csrfToken) {
        avueHeaders['X-Csrf-Token'] = csrfToken;
    }
    return avueHeaders;
}



