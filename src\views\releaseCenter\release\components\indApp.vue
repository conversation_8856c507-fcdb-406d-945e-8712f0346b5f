<template>
    <div class="indAppCommn">
        <el-form :disabled="detailFlag" ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="应用名称" prop="industrialName">
                <el-input v-model="form.industrialName" placeholder="应用名称" clearable style="width:100%" />
            </el-form-item>


            <div style="display: flex;">
                <el-form-item class="uploadItem" label='应用图片' prop="imgUrl">
                    <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                        :headers="headers" :action="uploadUrl" :show-file-list="false"
                        :on-success="(res, file) => handleAvatarSuccess(res, file, 'imgUrl')">
                        <img v-if="form.imgUrl" :src="ensureFullUrl(form.imgUrl)" class="appImage">
                        <i v-else class="el-icon-plus avatar-uploader-icon appIcon"></i>
                        <div slot="tip" style="font-size: 12px; color: #9EA5B6;">支持扩展名：.jpg .img .png</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="简介" prop="profiles" style="flex: 1;">
                    <el-input v-model="form.profiles" placeholder="请输入简介" clearable style="width:100%" type="textarea"
                        :rows="8" />
                </el-form-item>
            </div>

            <el-form-item label="模块分类" prop="modelTypes">
                <el-select v-model="form.modelTypes" placeholder="请选择模块" style="width:100%" clearable>
                    <el-option v-for="dict in dict.type.modeltype" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
<!--            <el-form-item label="行业" prop="industrialTypes">-->
<!--                <el-cascader v-model="form.industrialTypes" :options="industryTypes" :props="{-->
<!--                    children: 'childrenList',-->
<!--                    label: 'name',-->
<!--                    value: 'industryCode',-->
<!--                }" placeholder="请选择行业" style="width:100%" clearable>-->
<!--                </el-cascader>-->
<!--            </el-form-item>-->
            <el-form-item label="行业" prop="industrialSubstitutionIdJson">
                <el-cascader v-model="form.industrialSubstitutionIdJson" :options="industrys" :props="{
                    children: 'children',
                    label: 'vueName',
                    value: 'id',
                    multiple: true,
                }" placeholder="请选择行业" style="width:100%" clearable>
                </el-cascader>
            </el-form-item>

            <!-- 说的暂时关闭 -->
            <!-- <el-form-item label="标签" prop="applicationLabel">
                <el-select v-model="form.applicationLabel" placeholder="请选择标签" multiple style="width:100%" clearable>
                    <el-option v-for="item in []" :key="item" :value="item">{{ item }}</el-option>
                </el-select>
            </el-form-item> -->



            <el-form-item label="应用ID" prop="appId">
                <el-input style="width: 85%;margin-right: 2px;" disabled v-model="form.appId" placeholder="生成应用ID" />
                <el-button @click="generateIdKey()" size="small" type="info">点击生成</el-button>
            </el-form-item>
            <el-form-item label="应用密钥" prop="appKey">
                <el-input disabled style="width: 85%;margin-right: 2px;" v-model="form.appKey" placeholder="生成应用密钥" />
                <el-button @click="generateAppKey()" size="small" type="info">点击生成</el-button>
            </el-form-item>
            <el-form-item label="简介" prop="profiles">
                <el-input v-model="form.profiles" placeholder="请输入简介" style="width: 100%" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="认证类型" prop="authType">
                <el-select style="width: 100%" v-model="form.authType" placeholder="请选择认证类型">
                    <el-option v-for="dict in dict.type.user_app_apply_auth_type" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="授权类型" prop="grantType">
                <el-select style="width: 100%" v-model="form.grantType" placeholder="请选择授权类型">
                    <el-option v-for="(item, index) in dict.type.app_user_apply_grant_type" :key="index" :value="item.value"
                        :label="item.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="退出登录地址" prop="loginOutUrl">
                <el-input style="width: 100%" v-model="form.loginOutUrl" placeholder="请输入退出登录地址" />
            </el-form-item>
            <el-form-item label="回调地址" prop="redirectUrl">
                <el-input style="width: 100%" v-model="form.redirectUrl" placeholder="请输入回调地址" />
            </el-form-item>
            <el-form-item label="是否是单租户" prop="isSingleTenant">
                <el-select style="width: 100%" v-model="form.isSingleTenant" placeholder="请选择授权类型">
                    <el-option value="YES" label="是" />
                    <el-option value="NO" label="否" />
                </el-select>
            </el-form-item>
            <el-form-item label="用户接口" prop="userInterface">
                <el-input v-for="(item, index) in userInterfaceList" :key="index" style="width: 100%;margin-bottom: 10px;"
                    :placeholder="'请输入' + item.label + '接口'" v-model="item.requestUrl">
                    <template slot="prepend">{{ item.label }}</template>
                    <el-tooltip slot="append" effect="dark" :content="item.content" placement="top-start">
                        <i style="font-size: 16px;cursor: pointer;" class="el-icon-question"></i>
                    </el-tooltip>
                </el-input>
            </el-form-item>


            <el-form-item label="应用案例" prop="appCases">
                <el-select v-model="form.appCases" multiple filterable remote reserve-keyword placeholder="请选择应用案例"
                    :remote-method="(v) => { remoteMethod(v, 'app') }" :loading="apploading" style="width:100%" clearable>
                    <el-option v-for="item in appList" :key="item.caseId" :label="item.caseName" :value="item.caseId">
                    </el-option>
                </el-select>
            </el-form-item>

            <!-- <el-form-item label="服务商" prop="providerld">
                <el-select v-model="form.providerld" filterable remote reserve-keyword placeholder="请选择服务商"
                    :remote-method="(v) => { remoteMethod(v) }" :loading="provideloading" style="width:100%" clearable>
                    <el-option v-for="item in provideList" :key="item.providerId" :label="item.providerName"
                        :value="item.providerId">
                    </el-option>
                </el-select>
            </el-form-item> -->

            <div class="tagsTilte" style="margin-top: 30px;margin-bottom: 15px;">应用亮点</div>
            <div class="dynaCard" v-for="(item, index) in form.applicationHighlights" :key="item.key">
                <div class="dynaCardHead">
                    <div class="hintTitleSamil">亮点({{ index + 1 }})</div>
                    <el-popconfirm title="确认是否删除" @confirm="removeFormItem(item, 'applicationHighlights')"
                        v-if="!detailFlag&&(form.applicationHighlights && form.applicationHighlights.length > 1)"
                        >
                        <template #reference>
                            <i style="color: red;font-size: 18px;cursor:pointer" class="el-icon-delete"></i>
                        </template>
                    </el-popconfirm>
                </div>
                <el-row>
                    <el-col :span="18">
                        <el-form-item label='名字' :prop="'applicationHighlights.' + index + '.titleHighlights'" :rules="{
                            required: true, message: '必填项不能为空', actions: 'blur'
                        }">
                            <el-input placeholder="请输入名字" v-model="item.titleHighlights"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="18">
                        <el-form-item label='简介' :prop="'applicationHighlights.' + index + '.detailHighlights'" :rules="{
                            required: true, message: '必填项不能为空', actions: 'blur'
                        }">
                            <el-input type="textarea" :rows="3" placeholder="请输入简介"
                                v-model="item.detailHighlights"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="18">
                        <el-form-item class="uploadItem" label='图标'
                            :prop="'applicationHighlights.' + index + '.imgUrlHighlights'" :rules="{
                                required: true, message: '必填项不能为空', actions: 'change'
                            }">
                            <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                                :on-error="handleUploadError" :headers="headers" :action="uploadUrl" :show-file-list="false"
                                :on-success="(res, file) => handleAvatarSuccess(res, file, 'applicationHighlights', index)">
                                <img v-if="item.imgUrlHighlights" :src="ensureFullUrl(item.imgUrlHighlights)" class="miniImage">
                                <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
                            </el-upload>
                            <span style="font-size: 12px; color: #9EA5B6;">支持扩展名：.jpg .img .png</span>
                        </el-form-item>
                    </el-col>


                </el-row>
            </div>
            <el-button icon="el-icon-circle-plus-outline" type="text"
                @click="addFormItem('applicationHighlights')">新增应用亮点</el-button>

            <!-- 说的暂时关闭 -->
            <!-- <div class="tagsTilte" style="margin-top: 30px;margin-bottom: 15px;">应用详情</div>
            <div class="dynaCard">
                <el-row>
                    <el-col :span="10">
                        <el-form-item label="能力编码" prop="codeDetails">
                            <el-input v-model="form.codeDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="能力ID" prop="idDetails">
                            <el-input v-model="form.idDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="能力类型" prop="typeDetails">
                            <el-input v-model="form.typeDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="能力目录" prop="catalogueDetails">
                            <el-input v-model="form.catalogueDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="使用网络" prop="networkDetails">
                            <el-input v-model="form.networkDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="部署网关" prop="gatewayDetails">
                            <el-input v-model="form.gatewayDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="上架日期" prop="dateDetails">
                            <el-input v-model="form.dateDetails" placeholder="请输入能力编码" clearable style="width:100%" />
                        </el-form-item>
                    </el-col>

                </el-row>
            </div> -->

            <div class="tagsTilte" style="margin-top: 30px;margin-bottom: 15px;">应用场景</div>
            <div class="dynaCard">
                <el-row>
                    <el-col :span="18" style="margin-top: 24px;">
                        <el-form-item label='名字' prop="applicationScenario.titleScenario" :rules="{
                            required: true, message: '必填项不能为空', actions: 'blur'
                        }">
                            <el-input placeholder="请输入名字" v-model="form.applicationScenario.titleScenario"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="18">
                        <el-form-item label='简介' prop="applicationScenario.detailScenario" :rules="{
                            required: true, message: '必填项不能为空', actions: 'blur'
                        }">
                            <el-input type="textarea" :rows="3" placeholder="请输入简介"
                                v-model="form.applicationScenario.detailScenario"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="18">
                        <el-form-item class="uploadItem" label='图标' prop="applicationScenario.imgUrlScenario" :rules="{
                            required: true, message: '必填项不能为空', actions: 'change'
                        }">
                            <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                                :headers="headers" :action="uploadUrl" :show-file-list="false"
                                :on-success="(res, file) => handleAvatarSuccess(res, file, 'applicationScenario')">
                                <img v-if="form.applicationScenario.imgUrlScenario"
                                    :src="ensureFullUrl(form.applicationScenario.imgUrlScenario)" class="miniImage">
                                <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
                            </el-upload>
                            <span style="font-size: 12px; color: #9EA5B6;">支持扩展名：.jpg .img .png</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <el-form-item label="是否热门" prop="popularStatus">
                <el-switch v-model="form.popularStatus" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>

            <el-form-item label="是否免费" prop="isFree">
                <el-switch v-model="form.isFree" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>
        </el-form>


        <!-- <template v-if="detailFlag !== 'detail'">
            <div v-show="footerWidth && footerWidth != '0px'" class="pageFooter" :style="{ width: `${footerWidth}` }">
                <div style="margin-right: 20px;">
                    <template v-if="detailFlag === 'check'">
                        <el-button type="primary">驳回</el-button>
                        <el-button type="primary">通过</el-button>
                    </template>
                    <template v-else>
                        <el-button type="primary" @click="submitForm" :loading="submitDing">提交</el-button>
                    </template>
                </div>
            </div>
        </template> -->

    </div>
</template>

<script>
import { getToken } from "@/utils/auth";

import {
  getSysIndustryType,
  getAllCaseByName,
  getAllProviderByName, getSysIndustry
} from "@/api/release/indApp";
import {
    getInfo
} from "@/api/release/index.js";

export default {
    name: "indAppCommn",
    dicts: ["modeltype",'app_user_apply_grant_type', 'user_app_apply_auth_type'],
    props: {
        footerWidth: {
            type: String,
            default: '0px',
        },
    },
    data() {
        var validateInterface = (rule, value, callback) => {
            const isFlag = this.userInterfaceList.find(item => !item.requestUrl)
            if (isFlag) {
                callback(new Error('请填写完增删改接口'))
            } else {
                callback()
            }
        }
        var validateUrl = (rule, value, callback) => {
            const reg = /^https?:\/\//
            if (reg.test(value)) {
                callback()
            } else {
                callback(new Error())
            }
        }
        return {
            form: {
                applicationHighlights: [{
                    titleHighlights: '', detailHighlights: '', imgUrlHighlights: ''
                }],
                applicationScenario: {},
                // providerld: undefined,
                appCases: [],
                modelTypes: undefined,
                isSingleTenant: undefined,
                authType: undefined,
                grantType: undefined
                // applicationLabel: []
            },
            rules: {
                industrialName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                profiles: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                modelTypes: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                industrialSubstitutionIdJson: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                // applicationLabel: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                appCases: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                // providerld: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                profiles: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                imgUrl: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                isFree: [{ required: false, message: "必填项不能为空", trigger: "change" }],
                userInterface: [
                    { validator: validateInterface, trigger: "blur" }
                ],
                appId: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                appKey: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                authType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                grantType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                loginOutUrl: [
                    { required: true, message: "必填项不能为空", trigger: "blur" },
                    { validator: validateUrl, trigger: "blur", message: "地址应以http://或https://开头" }
                ],
                redirectUrl: [
                    { required: true, message: "必填项不能为空", trigger: "blur" },
                    { validator: validateUrl, trigger: "blur", message: "地址应以http://或https://开头" }
                ],
                isSingleTenant: [{ required: true, message: "必填项不能为空", trigger: "change" }],
            },
            uploadUrl: process.env.VUE_APP_BASE_API + "/system/file/upload",
            headers: {
                Authorization: "Bearer " + getToken()
            },
            industryTypes: [],
            industrys: [],
            apploading: false,
            appList: [],
            provideloading: false,
            provideList: [],
            detailFlag: (this.$route.query.pageType == 'detail' || this.$route.query.pageType == 'check') || false,
            userInterfaceList: [
                { interfaceType: 'add', label: '新增', requestUrl: '', content: '平台侧新建用户时，选择了相对应的应用后，保存时会向应用侧调用新增接口（接口请求方式为POST，参数为userName（用户名）和nickName（昵称））' },
                { interfaceType: 'delete', label: '删除', requestUrl: '', content: '平台侧删除用户时，会向用户对应的应用去调用删除接口（接口请求方式为GET，示例：http://127.0.0.1/delete/{userNames}，userNames为用户名称的集合，例如admin,test）' }
            ],
        };
    },
    created() {
        this.getSysIndustryFtn();  //行业
        this.getAllCaseByNameFtn(); //应用
        this.getAllProviderByNameFtn() //服务商
        const { flowInstanceId } = this.$route.query;
        flowInstanceId && this.getFormDataFtn(flowInstanceId)
    },


    destroyed() {
    },

    methods: {

        generateRandomString(length) {
            var result = '';
            var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            var charactersLength = characters.length;
            for (var i = 0; i < length; i++) {
                result += characters.charAt(Math.floor(Math.random() * charactersLength));
            }
            return result;
        },
        generateAppKey() {
            this.$set(this.form, 'appKey', this.generateRandomString(12))
        },
        generateIdKey() {
            this.$set(this.form, 'appId', this.generateRandomString(12))
        },

        getFormDataFtn(flowInstanceId) {
            getInfo({ flowInstanceId }).then(res => {
                const { params } = res.data;
                this.form = {
                    ...params,
                    industrialSubstitutionIdJson: JSON.parse(params.industrialSubstitutionIdJson),
                    appCases: params.appCases && params.appCases.split(',').map(v=>v-0),
                    // industrialTypes: params.industrialTypes && params.industrialTypes.split(',').map(v=>v-0),
                    isSingleTenant: params.isSingleTenant || undefined,
                    authType: params.authType || undefined,
                    grantType: params.grantType || undefined,
                }
                const userInterface = params.userInterface ? JSON.parse(params.userInterface) : []
                this.userInterfaceList.forEach(item => {
                    let requestUrl = "";
                    userInterface.forEach(ii => {
                        if (ii.interfaceType === item.interfaceType) {
                            requestUrl = ii.requestUrl;
                        }
                    })
                    item.requestUrl = requestUrl;
                })
            })
        },

        // getSysIndustryTypeFtn() {
        //     getSysIndustryType().then(res => {
        //         // this.industryTypes = this.getTreeData(res.data);
        //         this.industryTypes = res.data;
        //     });
        // },

        getSysIndustryFtn() {
          getSysIndustry().then(res => {
                this.industrys = res.data;
            });
        },

        getTreeData(data) {
            for (var i = 0; i < data.length; i++) {
                if (data[i].childrenList.length < 1) {
                    data[i].childrenList = undefined;
                } else {
                    this.getTreeData(data[i].childrenList);
                }
            }
            return data;
        },

        getAllProviderByNameFtn(val = '') {
            this.provideloading = true;
            const params = { providerName: val || undefined };
            getAllProviderByName(params).then(res => {
                this.provideList = res.data;
            }).finally(() => this.provideloading = false)
        },

        getAllCaseByNameFtn(val = '') {
            this.apploading = true;
            const params = { caseName: val || undefined };
            getAllCaseByName(params).then(res => {
                this.appList = res.data;
            }).finally(() => this.apploading = false)
        },

        remoteMethod(val, key) {
            key === 'app' ? this.getAllCaseByNameFtn(val) : this.getAllProviderByNameFtn(val);
        },

        removeFormItem(item, key) {
            var index = this.form[key].indexOf(item)
            if (index !== -1) {
                this.form[key].splice(index, 1)
            }
        },

        addFormItem(key) {
            let dataOp = {
                titleHighlights: '', detailHighlights: '', imgUrlHighlights: '',
                key: Date.now()
            }
            this.form[key].push(dataOp);
        },

        handleUploadError(err) {
            console.error('上传失败', err);
        },

        handleAvatarSuccess(res, file, key, index) {
            if (res.code == 500) {
                this.$message.error('上传失败');
                return
            }
            const { data } = res;
            let formkey;
            if (key == 'applicationHighlights') {
                const appData = this.form.applicationHighlights;
                appData[index].imgUrlHighlights = data.url;
                this.form = {
                    ...this.form,
                    applicationHighlights: [...appData]
                };
                formkey = key + '.' + index + '.imgUrlHighlights';
            }
            else if (key == 'applicationScenario') {
                this.form = {
                    ...this.form,
                    applicationScenario: { ...this.form.applicationScenario, imgUrlScenario: data.url }
                };
                formkey = 'applicationScenario.imgUrlScenario';
            }
            else {
                this.form = { ...this.form, imgUrl: data.url };
                formkey = 'imgUrl';
            }
            if (res.code === 200) {
                this.$refs.form.clearValidate(formkey)
            }
        },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (['jpg', 'img', 'png'].indexOf(fileType) == -1) {
                this.$message.error('请上传后缀为.jpg .img .png格式的图片文件');
                return false;
            }
        },

        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    if (this.form.industrialSubstitutionIdJson) {
                      const uniqueElements = [...new Set(this.form.industrialSubstitutionIdJson.map(subArray => subArray[0]))];
                      this.form.industrialSubstitutionIds = uniqueElements.join(',');
                    }
                    const userInterface = this.userInterfaceList.map(item => {
                        return {
                            interfaceType: item.interfaceType,
                            requestUrl: item.requestUrl
                        }
                    })
                    const params = {
                        ...this.form,
                        // industrialTypes: this.form.industrialTypes.join(','),
                        appCases: this.form.appCases.join(','),
                        industrialType: 1,
                        userInterface: JSON.stringify(userInterface)
                    }
                    this.$emit('submitFtn', params, (res) => {
                        // 相应结束后的其他逻辑
                    });
                }
            });
        },
    },

};
</script>



<style lang="scss" >
.indAppCommn {
    width: 65%;

    .dynaCard {
        /* width: 100%; */
        background: #F6F8FC;
        border-radius: 5px;
        padding: 12px 24px;
        margin-bottom: 20px;

        .dynaCardHead {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            margin-top: 12px;
            margin-left: 22px;

            .hintTitleSamil {
                font-weight: bold;
                font-size: 15px;
                color: #0D162A;

                &:before {
                    content: '';
                    display: inline-block;
                    width: 4px;
                    height: 10px;
                    background: #6FC342;
                    border-radius: 0px;
                    margin-right: 6px;
                }
            }
        }
    }

    .uploadItem {
        .el-form-item__content {
            line-height: normal;
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        text-align: center;
    }

    .avatar-uploader {
        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
    }

    .appIcon {
        width: 160px;
        height: 160px;
        line-height: 160px;
    }

    .appImage {
        width: 160px;
        height: 160px;
    }

    .miniIcon {
        width: 80px;
        height: 80px;
        line-height: 80px;
    }

    .miniImage {
        width: 80px;
        height: 80px;
    }
}
</style>
