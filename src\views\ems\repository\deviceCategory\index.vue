<template>
  <div class="meun-box">
    <el-row :gutter="10">
      <el-col :span="10">
        <div class="grid-content grid-content1">
          <div class="grid-content-1">
            <IconTitle title="资料模块/资料类目" imgUrl="yunwei"> </IconTitle>
            <el-card class="box-card btn-search page-search">
              <div slot="header" class="clearfix">
                <div class="btn-box">
                  <el-button
                      type="info"
                      icon="el-icon-refresh-left"
                      @click="getTreeList"
                  ></el-button>
                  <el-button
                      id="gwButton"
                      type="goon"
                      icon="el-icon-circle-plus-outline"
                      v-if="permissions.sys_menu_add"
                      @click="addTreeMenu"
                  >新增类目
                    <el-tooltip style="color: #000000" content="新增时先增加设备知识类型、设备资料类型、故障库类型和规章制度类型四大类型，对应下面菜单树形展示，新增后不可修改" placement="top" @click.stop.prevent>
                      <i class="el-icon-question"/>
                    </el-tooltip></el-button
                  >

                  <!-- <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="small"
                    @click="editTreeMenu"
                     v-if="permissions.sys_menu_edit"
                    >编辑</el-button
                  > -->
                  <el-button
                      type="danger"
                      size="small"
                      style="margin-left: 10px"
                      icon="el-icon-circle-close"
                      v-if="permissions.sys_menu_del"
                      @click="delTreeMenu"
                  >删除</el-button
                  >
                </div>
              </div>
            </el-card>
            <el-tabs v-model="tabIndex" tab-position="left"   @tab-click="openTag">
              <el-tab-pane
                  :label="item.name"
                  v-for="(item, index) in menuOptionsTree"
                  :key="item.id"
              >
                <el-tree
                    :data="item.children"
                    :props="defaultProps"
                    accordion
                    @node-click="handleNodeClick"
                ></el-tree>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
      <el-col :span="14"
      ><div class="grid-content">
        <IconTitle title="类目编辑" imgUrl="yunwei"> </IconTitle>
        <NewForm ref="addMenuUpdate"  @refreshDataTree="refreshAll"></NewForm>
      </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  fetchMenuTreeMenu,
  fetchMenuTree,
  getEmsKnoCategoryIsNotNull,
  fetchMenuBtn,
  delObj
} from "@/api/ems/repository/deviceCategory";
import NewForm from "./new-menu-form.vue";
import { mapGetters } from "vuex";
import IconTitle from "@/components/ems/icon-title/index.vue";

export default {
  name: 'emsknocategory',
  components: {
     IconTitle, NewForm
  },
  data() {
    return {
      addOrUpdateVisible: false,
      // 遮罩层
      loading: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      menuItem: {},
      menuOptionsTree: [],
      single: true, //可以编辑了
      multiple: true, // 选择了一个按钮
      tabIndex:'0',
      defaultProps: {
        children: "children",
        label: "name",
      },
      btnSelect: [],
    };
  },
  created() {
    this.getEmsKnoCategoryIsNotNull();
    this.getList();
    this.getTreeList(); //树形结构

  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {

    getEmsKnoCategoryIsNotNull() {
      getEmsKnoCategoryIsNotNull().then(res => {
        let a = res.data.data;
        // console.log("111>>" , JSON.stringify(a))
        this.getList();
        this.getTreeList();
      });
    },

    deviceSelectionChange(selection) {
      this.btnSelect = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    refreshAll() {
      this.getTreeList();
      this.clearData();
    },
    //获取按钮和菜单默认值
    handleNodeClick(data) {
      this.menuItem = data;
      this.clearData();
      this.editTreeMenu()
      //默认菜单数据
    },
    clearData() {
      this.$refs.addMenuUpdate.init(false);
    },
    //新增
    addTreeMenu() {
      if (this.menuItem) {
        this.$nextTick(() => {
          this.$refs.addMenuUpdate.init(false, this.menuItem.id);
        });
      } else {
        this.$nextTick(() => {
          this.$refs.addMenuUpdate.init(false,this.menuItem.id);
        });
      }
    },
    //编辑
    editTreeMenu() {
      this.$nextTick(() => {
        this.$refs.addMenuUpdate.init(true, this.menuItem.id);
      });

    },
    //删除
    delTreeMenu() {
      let row = this.menuItem;
      // console.log("?????>>>>>>" , JSON.stringify(row))
      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(function () {
            // alert(row.id)
            return delObj(row.id);
          })
          .then(() => {
            this.refreshAll();
            this.$message.success("删除成功");
          });
    },
    openTag(data) {
      let index = data.index;
      // console.log("444>>>" + index);

      this.menuItem = this.menuOptionsTree[index];
      // console.log(JSON.stringify(this.menuItem));
      this.clearData();

      this.editTreeMenu()
    },

    delBtn(){
      this.$confirm('是否确认删除所选的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then( () =>{
            return delObj(this.btnSelect);
          })
          .then(() => {
            this.$message.success("删除成功");
          });
    },
    getTreeList() {
      this.menuOptionsTree=[]
      fetchMenuTreeMenu().then((response) => {
        this.menuOptionsTree = response.data.data;
        this.tabIndex='0'
        if (this.menuOptionsTree != null) {
          this.menuItem = this.menuOptionsTree[0];
        }
      });
    },
    addOrUpdateHandle(isEdit, id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(isEdit, id);
      });
    },
    getList() {
      this.loading = true;
      fetchMenuTree(false).then((response) => {
        this.menuList = response.data.data;
        this.loading = false;
      });
    },

    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(function () {
            return delObj(row.id);
          })
          .then(() => {
            this.getList();
            this.$message.success("删除成功");
          });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.el-button--goon.is-active,
.el-button--goon:active {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon:focus,
.el-button--goon:hover {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon {
  color: #FFF;
  background-color: #02b980;
  border-color: #02b980;
}

.meun-box {
  .grid-content {
    background: #fff;
    padding: 10px;
    box-sizing: border-box;
    height: calc(100vh - 120px);
    box-shadow: 0px 0px 7px 0px #eff2f5;
    ::v-deep .el-card__header {
      padding: 10px 0;
      border-bottom: none;
    }
    ::v-deep .el-tabs__item {
      padding-right: 10px;
      font-size: 12px;
    }
  }
  .grid-content1 {
    overflow: auto;
  }
}
</style>
