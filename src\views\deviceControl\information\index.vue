<template>
    <div class="app-container informationPage">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="工站" prop="workstationId">
          <el-select v-model="queryParams.workstationId" placeholder="请选择工站" clearable>
            <el-option v-for="dict in worStation" :key="dict.id" :label="dict.workstation"
              :value="dict.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备状态" prop="deviceStatus">
          <el-select v-model="queryParams.deviceStatus" placeholder="请选择设备状态" clearable>
            <el-option v-for="dict in dict.type.device_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="hintTitle titleTags">设备信息</div>

      <el-row  v-loading="loading" :gutter="20" class="mb cardRow" >
        <div  v-if="postList.length>0">
          <el-col :xs="24" :sm="24" :md="12" :lg="8" class="cardCol" v-for="t in postList">
          <div class="cardItem">
            <div class="cardHead">
              <span class="cardHeadText">{{ t.deviceName }}</span>
              <span :class="['cardHeadTag','tagGreen']" v-if="t.deviceStatus==0">运行中</span>
              <span :class="['cardHeadTag','tagRed']" v-else-if="t.deviceStatus==1">故障</span>
              <span :class="['cardHeadTag','tagWarn']" v-else>带病运行</span>
            </div>
            <div class="cardConent">
                <img class="conentImg" :src="t.deviceImg" alt="">
                <div class="conentText">
                  <div>
                    <p>上线时间</p>
                    <p>{{ t.installDate }}</p>
                    <p>设备控制参数/策略</p>
                    <p>{{t.paramCount}}/{{t.strategyCount}}</p>
                  </div>
                  <div class="conentBtn">
                    <el-button-group>
                      <el-button @click='detailsRoute(t,"parame")'  style="margin-bottom: 2px;" size="small" type="primary" >查看参数</el-button>
                      <el-button   @click='detailsRoute(t,"strategy")' size="small" type="primary">查看策略</el-button>
                    </el-button-group>
                  </div>
                </div>
            </div>
          </div>
        </el-col>
        </div>

      <el-empty v-else :image-size="200"></el-empty>
      </el-row>


  
      <pagination v-show="total > 0" 
        :total="total" 
        :page.sync="queryParams.page" 
        :limit.sync="queryParams.limit"
        :pageSizes=[9,18,27]
        @pagination="getList" />

    </div>
  </template>
    
  <script>

  import { getWorStation,controlList} from "@/api/deviceControl/information";


  
  export default {
    name: "Post",
    dicts: ['sys_normal_disable','device_status'],
    data() {
      return {
        screenHeight: null,
        sizeList: {
          '--dynamicHeight': 410,
        },
        loading: false,
        showSearch: true,
        total: 0,
        postList: [],
        queryParams: {
          page: 1, //pageNum
          limit: 9,  //pageSize
          workstationId:undefined,
          deviceStatus:undefined
        },
        worStation:[],
      };
    },
    created() {
      this.getList();
      getWorStation().then(res=>this.worStation=res.data);
    },
    watch: {

    },
    methods: {

      getList() {
        this.loading = true;
        controlList(this.queryParams).then(response => {
          const {list,total} = response.data;
          this.postList = list;
          this.total = total;
        }).finally(()=>this.loading = false)
      },

      detailsRoute(row,key){
         this.$router.push({ path: `information/details`,query: {id:row.id,key } })
      },

      handleQuery() {
        this.queryParams.page = 1;
        this.getList();
      },

      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

    }
  };
  </script>
  
  <style lang="scss" >
  .informationPage {
    .titleTags{
      margin: 10px 0px 24px 0px !important;
      &:before{
                content: '';
                display:inline-block;
                width: 4px;
                height: 12px;
                background: #6FC342;
                border-radius: 0px;
                margin-right: 6px;
      }
    }
    .cardRow{
      .cardCol{
        padding-bottom: 20px;
        .cardItem{
          min-height: 242px; 
          border-radius: 3px;
          border: 1px solid #DCDFE8;
          box-sizing: border-box;
        }
        .cardHead{
          height: 42px;
          background: #F3F5F8;
          padding: 0px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
            .cardHeadText{
              font-family: PingFang SC;
              font-weight: bold;
              font-size: 18px;
              color: #111827;
            }          
            .cardHeadTag{
              font-family: PingFang SC;
              font-weight: 400;
              font-size: 13px;
              line-height: 0px;
            }
            .tagRed{
              color: #FF4D4F;
              &:before{
                content: '';
                display:inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background:#FF4D4F;
                margin: 0px 5px 1px 0px;
              }
            }
            .tagGreen{
              color: #34B576;
              &:before{
                content: '';
                display:inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background:#34B576;
                margin: 0px 5px 1px 0px;
              }
            }
            .tagWarn{
              color: #E6A23C;
              &:before{
                content: '';
                display:inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background:#E6A23C;
                margin: 0px 5px 1px 0px;
              }
            }
          }
          .cardConent{
            padding: 16px 20px 20px 20px;
            display: flex;
            .conentImg{
              width: 170px;
              min-height: 170px;
            }
            .conentText{
              margin-left: 18px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              p{
                margin: 0;
              }
              p:nth-child(1){
                font-family: PingFang SC;
                font-weight: bold;
                font-size: 16px;
                color: #515A6E;
                padding-bottom: 4px;
              }
              p:nth-child(2){
                font-family: PingFang HK;
                font-weight: 400;
                font-size: 14px;
                color: #515A6E;
                padding-bottom: 10px;
              }
              p:nth-child(3){
                font-family: PingFang SC;
                font-weight: bold;
                font-size: 16px;
                color: #515A6E;
                padding-bottom: 6px;
              }
              p:nth-child(4){
                font-family: HelveticaNeue-Bold;
                font-size: 18px;
                color: #111827;
                padding-bottom: 6px;
              } 
              .conentBtn{
              }
            }
          }
      }
    }
  }

  </style>
    
    