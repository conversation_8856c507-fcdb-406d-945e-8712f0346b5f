<template>
  <div id="myChart" :style="{width: '100px', height: '100px'}"></div>
</template>

<script>
import echarts from 'echarts'
import 'echarts-liquidfill'

export default {
  data() {
    return {
      msg: 'hello'
    }
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let myChart = this.$echarts.init(document.getElementById('myChart'))

      let value = 0.5;
      let value1 = 75.2
      let value2 = 75.2
      let data = [value, value, value,];

      // 绘制图表
      myChart.setOption({
        backgroundColor: '#fff',
        title: [
          {
            text: '开机率',
            left: '48%',
            top: "58%",
            textAlign: 'center',
            textStyle: {
              fontSize: '12',
              fontWeight: '400',
              color: '#fff',
              textAlign: 'center',
            },
          },
          {
            text: (value * 100).toFixed(0) + '%',
            left: '48%',
            top: '25%',
            textAlign: 'center',
            textStyle: {
              fontSize: 25,
              color: '#fff',
            },
          },
        ],
        series: [{
          type: 'liquidFill',
          radius: '90%',
          z: 6,
          center: ['50%', '50%'],
          amplitude: 5,
          backgroundStyle: {
            borderWidth: 1,
          },
          color: [
            // '#def2fe',
            // '#b6e3fc',
            new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: "#72D1FB",
            }, {
              offset: 0.8,
              color: "#43ABF7",
            }
            ])],
          data: [value + 0.02,
            {
              value: value - 0.01,
              direction: 'left',
            },
            value - 0.01,
          ],
          label: {
            normal: {
              formatter: '',
            }
          },
          outline: {
            show: true,
            itemStyle: {
              borderWidth: 0,
            },
            borderDistance: 0,
          }
        }
        ]
      });
    }
  }
}

</script>
