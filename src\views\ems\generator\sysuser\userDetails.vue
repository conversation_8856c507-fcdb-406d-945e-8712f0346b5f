<template>
  <div class="details_view">
    <el-card style="border-radius: 10px">
      <div class="details_data">
        <IconTitle class="selfTitle" title="基本信息" imgUrl="yunwei"/>
        <h3>{{ information.name }}</h3>
        <table class="table_details" border="1">
          <tr>
            <th>名称</th>
            <td colspan="3">{{ information.name }}</td>
          </tr>
          <tr>
            <th>出生日期</th>
            <td>{{ information.birthday }}</td>
            <th>性别</th>
            <td v-if="information.sex == 0">未知</td>
            <td v-if="information.sex == 1">男</td>
            <td v-if="information.sex == 2">女</td>
          </tr>
          <tr>
            <th>资质证书</th>
            <td colspan="3">{{ information.certificate }}</td>
          </tr>
          <tr>
            <th>所属部门</th>
            <td>{{ information.deptName }}</td>
            <th>联系电话</th>
            <td>{{ information.phone }}</td>
          </tr>
          <tr>
            <th>用户名</th>
            <td colspan="3">{{ information.username }}</td>
          </tr>
          <tr>
            <th>说明</th>
            <td colspan="3">{{ information.remark }}</td>
          </tr>
        </table>
      </div>
    </el-card>

    <el-card style="border-radius: 10px;margin-top: 15px">
      <div class="details_attachment">
        <IconTitle class="selfTitle" title="相关附件" imgUrl="yunwei"/>
        <el-table
            :data="fileArray"
            border
            :header-row-style="{color: '#343141'}"
            style="width: 100%; margin-top: 10px">
          <el-table-column
              prop="original"
              label="文件名称"
              width="240">
          </el-table-column>
          <el-table-column
              prop="type"
              label="文件类型"
              width="180">
          </el-table-column>
          <el-table-column
              prop="fileSize"
              label="文件大小">
          </el-table-column>
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="查看次数">-->
          <!--          </el-table-column>-->
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="下载次数">-->
          <!--          </el-table-column>-->
          <el-table-column
              prop="createTime"
              label="上传时间">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-search"
                  @click="selectFile(scope.row)"
              >查看
              </el-button>
              <el-button
                  type="text"
                  size="small"
                  icon="el-icon-download"
                  @click="download(scope.row, scope.index)"
              >下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="预览" :visible.sync="viewVisible" width="50%" height="30%">
          <pdf
              :src="url"
              :page="pdfPage"
              @num-pages="pdfPageCount = $event"
              @page-loaded="pdfPage = $event"
          ></pdf>
          <!-- 上下翻页 -->
          <button @click="previousPage" style="float: left;">上一页</button>
          <button @click="nextPage" style="float: right">下一页</button>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="viewVisibleImg" width="50%" height="30%">
          <img :src="url" style="width: 100%; height: 100%">
        </el-dialog>
      </div>
    </el-card>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getObj} from "@/api/ems/generator/sysuser"
import {changeByte} from "@/util/changeByte"
import pdf from 'vue-pdf'

export default {
  name: "userDetails",
  data() {
    return {
      // 下载文件
      searchForm: {
        fileName: ''
      },
      // 资料信息数据
      information: [],
      // 相关附件
      fileArray: [],
      viewVisible: false,
      viewVisibleImg: false,
      pdfPage: 1,
      pdfPageCount: 1,
      url: ''
    }
  },
  components: {
    IconTitle,
    pdf
  },
  mounted() {
    this.getListData();
  },
  methods: {

    // 获取基本数据
    getListData() {
      let id = this.$route.query.userId;
      getObj(id).then(res => {
        this.information = res.data.data;
        this.fileArray = res.data.data.fileArray;
        for (let i = 0; i <= this.fileArray.length; i++) {
          this.fileArray[i].fileSize = changeByte(this.fileArray[i].fileSize);
        }

      })
    },

    // 查看功能
    selectFile(row) {
      if (row.type == 'pdf') {
        this.viewVisible = true;
        this.url = row.url;
      }
      if (row.type == 'jpg' || row.type == 'jpeg' || row.type == 'png') {
        this.viewVisibleImg = true;
        this.url = row.url;
      }
    },

    // 上一页
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pdfPageCount;
      this.pdfPage = p;
    },
    // 下一页
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pdfPageCount ? p + 1 : 1;
      this.pdfPage = p;
    },

    // 下载功能
    download: function (row, index) {
      this.downBlobFile(
          "/admin/sys-file/" + row.bucketName + "/" + row.fileName,
          this.searchForm,
          row.fileName
      );
    },
  }
}
</script>

<style scoped lang="less">
.details_view {
  .details_data {
    .table_details {
      border: 1px solid #e8eef4;
      font-size: 12px;
      width: 1241px;
      height: 205px;

      td {
        padding-left: 10px;
      }
    }
  }

  .details_attachment {
    .selfTitle {
      margin-bottom: 10px;
    }
  }
}
</style>
