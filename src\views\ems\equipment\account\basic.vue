<template>
  <div class="info-box">
    <div class="info-image">
      <div class="info-box">
        <div class="info-basic">
          <IconTitle title="基本信息" imgUrl="yunwei">
          </IconTitle>
          <b>{{ basic.deviceName }}</b>
          <!--          <div class="tag-box">-->
          <!--            <el-tag size="mini">特种设备</el-tag>-->
          <!--            <el-tag size="mini">特种设备</el-tag>-->
          <!--            <el-tag type="warning" size="mini">生产二部</el-tag>-->
          <!--            <el-tag type="danger" size="mini">故障风险预测 2点</el-tag>-->
          <!--            <el-tag type="danger" size="mini">设备异常 2条</el-tag>-->
          <!--          </div>-->
          <div class="equi-box">

            <table class="table_basic" border="1px solid #e8eef5" cellpadding="-5">
              <tr height="15px">
                <th width="20%" >
                  设备状态：
                </th>
                <td width="30%">
                  <!--<div class="equi-item">-->
                  <!--  <div class="spec-item">-->
                  <!--    <div v-if="basic.status == 0" class="sbdj">-->
                  <!--      <span class="circle-red" style="margin: 10px -3px 0 10px"></span>-->
                  <!--      <p>故障</p>-->
                  <!--    </div>-->
                  <!--    <div v-if="basic.status == 1" class="sbdj">-->
                  <!--      <span class="circle-green" style="margin: 10px -3px 0 10px"></span>-->
                  <!--      <p>正常</p>-->
                  <!--    </div>-->
                  <!--    <div v-if="basic.status == 2" class="sbdj">-->
                  <!--      <span class="circle-yellow" style="margin: 10px -3px 0 10px"></span>-->
                  <!--      <p>带病运行</p>-->
                  <!--    </div>-->
                  <!--  </div>-->
                  <!--</div>-->
                  <div class="equi-item">
                    <div class="spec-item">
                      <div v-if="basic.status == 0" class="sbdj">
                        <span class="circle-green" style="margin: 10px 5px 0 60px"></span>
                        <p>正常</p>
                      </div>
                      <div v-if="basic.status == 1" class="sbdj">
                        <span class="circle-red" style="margin: 10px 5px 0 60px"></span>
                        <p>故障</p>
                      </div>
                      <div v-if="basic.status == 2" class="sbdj">
                        <span class="circle-yellow" style="margin: 10px 5px 0 60px"></span>
                        <p>带病运行</p>
                      </div>
                    </div>
                  </div>

                </td>

                <th width="20%">
                  设备使用状态：
                </th>
                <td width="30%">
                  <div class="equi-item">
                    <div class="spec-item">
                      <div v-if="basic.useStatus == '0'" class="sbdj">
                        <span class="circle-blue" style="margin: 10px 5px 0 60px"></span>
                        <p>在用</p>
                      </div>
                      <div v-if="basic.useStatus == '1'" class="sbdj">
                        <span class="circle-green" style="margin: 10px 5px 0 60px"></span>
                        <p>闲置</p>
                      </div>
                      <div v-if="basic.useStatus == '2'" class="sbdj">
                        <span class="circle-purple" style="margin: 10px 5px 0 60px"></span>
                        <p>出租</p>
                      </div>
                      <div v-if="basic.useStatus == '3'" class="sbdj">
                        <span class="circle-red" style="margin: 10px 5px 0 60px"></span>
                        <p>禁用</p>
                      </div>
                      <div v-if="basic.useStatus == '4'" class="sbdj">
                        <span class="circle-yellow" style="margin: 10px 5px 0 60px"></span>
                        <p>报废</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr height="30px">
                <th width="20%">设备等级：</th>
                <td colspan="3">
                  <div class="equi-item">
                    <div class="spec-item">
                      <div v-if="basic.deviceLevel == 'A'" class="sbdj">
                        <span class="circle-blue" style="margin: 10px 5px 0 60px"></span>
                        <p>A(关键)</p>
                      </div>
                      <div v-if="basic.deviceLevel == 'B'" class="sbdj">
                        <span class="circle-green" style="margin: 10px 5px 0 60px"></span>
                        <p>B(重要)</p>
                      </div>
                      <div v-if="basic.deviceLevel == 'C'" class="sbdj">
                        <span class="circle-purple" style="margin: 10px 5px 0 60px"></span>
                        <p>C(一般)</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr height="30px">
                <th>设备编号：</th>
                <td style="text-align: center;">
                  {{ basic.deviceNum }}
                </td>
                <th>
                  资产编号：
                </th>
                <td style="text-align: center;">
                  {{ basic.assetNum }}
                </td>
              </tr>
              <tr height="30px">
                <th>
                  设备名称：
                </th>
                <td style="text-align: center;">
                  {{basic.deviceName }}
                </td>
                <th>
                  单位：
                </th>
                <td style="text-align: center;">
                  <div class="item" v-if="basic.unit == 0">个</div>
                  <div class="item" v-if="basic.unit == 1">台</div>
                  <div class="item" v-if="basic.unit == 2">件</div>
                  <div class="item" v-if="basic.unit == 3">双</div>
                  <div class="item" v-if="basic.unit == 4">捆</div>
                  <div class="item" v-if="basic.unit == 5">堆</div>
                  <div class="item" v-if="basic.unit == 6">份</div>
                </td>
              </tr>
              <tr height="30px">
                <th>
                  设备类别：
                </th>
                <td style="text-align: center;">
                  {{basic.category }}
                </td>
                <th>
                  规格型号：
                </th>
                <td style="text-align: center;">
                  {{ basic.specification }}
                </td>
              </tr>
              <tr height="30px">
                <th>品牌：</th>
                <td style="text-align: center;">
                  {{ basic.brandName }}
                </td>
                <th>
                  电子标签：
                </th>
                <td style="text-align: center;">
                  {{ basic.electronicLabel }}
                </td>
              </tr>
              <tr height="30px">
                <th>供应商：</th>
                <td colspan="3" style="text-align: center;">
                  {{ basic.supplierName }}
                </td>
              </tr>
              <tr height="30px">
                <th>设备来源:</th>
                <td style="text-align: center;">
                  {{ basic.source }}
                </td>
                <th>采购金额:</th>
                <td style="text-align: center;">
                  {{ basic.purchaseAmount }}
                </td>
              </tr>
              <tr height="30px">
                <th>购置日期：</th>
                <td style="text-align: center;">
                  {{ basic.purchaseDate }}
                </td>
                <th>保修期至：</th>
                <td style="text-align: center;">
                  {{ basic.warrantyDate }}
                </td>
              </tr>
              <tr height="30px">
                <th>净值：</th>
                <td style="text-align: center;">
                  {{ basic.currentNetWorth }}
                </td>
                <th>是否开启折旧：</th>
                <td style="text-align: center;">
                  <div class="item" v-if="basic.depreciationOpen == 0">否</div>
                  <div class="item" v-if="basic.depreciationOpen == 1">是</div>
                </td>
              </tr>
              <tr height="30px">
                <th>启用日期：</th>
                <td style="text-align: center;">
                  {{ basic.introductionDate }}
                </td>
                <th>预计报废日期：</th>
                <td style="text-align: center;">
                  {{basic.expectedScrapDate}}
                </td>
              </tr>
              <tr height="30px">
                <th>负责人：</th>
                <td style="text-align: center;">
                  {{ basic.liableUser }}
                </td>
                <th>所属部门：</th>
                <td style="text-align: center;">
                  {{basic.deptName}}
                </td>
              </tr>
              <!--<tr height="30px">-->
              <!--  <th>设备地址：</th>-->
              <!--  <td colspan="3" style="text-align: center;">-->
              <!--    {{ basic.location }}-->
              <!--  </td>-->
              <!--</tr>-->
              <!--<tr height="30px">-->
              <!--  <th>是否计量设备：</th>-->
              <!--  <td colspan="3" style="text-align: center;">-->
              <!--    <div class="item" v-if="basic.meteringDevice == 0">否</div>-->
              <!--    <div class="item" v-if="basic.meteringDevice == 1">是</div>-->
              <!--  </td>-->
              <!--</tr>-->
              <tr height="30px">
                <th>备注：</th>
                <td colspan="3" style="text-align: center;">
                  {{ basic.remark }}
                </td>
              </tr>
            </table>
          </div>
        </div>
        <!--        <div class="info-basic no-first-info-basic">-->
        <!--          <IconTitle title="计量信息" imgUrl="yunwei">-->
        <!--            <span class="slot">品牌管理</span>-->
        <!--          </IconTitle>-->
        <!--          <div class="equi-box">-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">计量编号：</span>-->
        <!--              <div class="item">KYUAOA555550923876813</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">计量范围：</span>-->
        <!--              <div class="item">KYUAOA00923876813</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">计量点位：</span>-->
        <!--              <div class="item">KYUAOA00923876813</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">仪器精度：</span>-->
        <!--              <div class="item">KYUAOA00923876813</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">允许误差：</span>-->
        <!--              <div class="item">90</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">管理等级：</span>-->
        <!--              <div class="item">99999</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">计量方式：</span>-->
        <!--              <div class="item">台</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">下次计量日起：</span>-->
        <!--              <div class="item">2021-11-12</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">计量周期：</span>-->
        <!--              <div class="item">1周</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">提前提醒时间：</span>-->
        <!--              <div class="item">1天</div>-->
        <!--            </div>-->

        <!--            <div class="equi-item">-->
        <!--              <span class="title">负责人：</span>-->
        <!--              <div class="item">西门子</div>-->
        <!--            </div>-->
        <!--            <div class="equi-item">-->
        <!--              <span class="title">说明：</span>-->
        <!--              <div class="item">西门子</div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="info-basic no-first-info-basic">
          <IconTitle title="折旧信息" imgUrl="yunwei">
          </IconTitle>
          <div class="equi-box">
            <div class="equi-item">
              <span class="title">使用寿命(月)：</span>
              <div class="item">{{ basic.serviceLife }}</div>
            </div>
            <div class="equi-item">
              <span class="title">原值：</span>
              <div class="item">{{ basic.purchaseAmount }}</div>
            </div>
            <div class="equi-item">
              <span class="title">净残率(%)：</span>
              <div class="item">{{ basic.residualRate != null ? basic.residualRate * 100 + "%" : "" }}</div>
            </div>
            <div class="equi-item">
              <span class="title">折旧方法：</span>
              <div class="item" v-if="basic.depreciationMethod">平均年限法</div>
            </div>
            <div class="equi-item">
              <span class="title">月折旧额：</span>
              <div class="item">{{ basic.monthDepreciation }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="image-box">
        <IconTitle title="设备图片" imgUrl="yunwei">
        </IconTitle>
        <div class="card-image">
          <span v-for="(item, index) of imgArray" :key="index">
            <img :src="item ? item : ''" class="image">
          </span>
          <el-dialog title="预览" :visible.sync="dialogVisible" width="50%" height="30%"
                     :modal="false" @close="dialogVisibleClose" :close-on-click-modal="false">
            <img :src="url" style="width: 100%; height: 100%">
          </el-dialog>
        </div>
      </div>
    </div>
    <div class="info-table">
      <div class="table-box">
        <IconTitle title="设备相关文档" imgUrl="yunwei">
        </IconTitle>
        <!-- 设备文档 -->
        <el-table
            :data="deviceData"
            border
            style="width: 100%"
            @selection-change="deviceSelectionChange"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column prop="dataName" label="资料名称" align="center">
          </el-table-column>
          <!--          0-图片  1-文档   -->
          <el-table-column prop="categoryName" label="资料类别" align="center">
            <!--            <template slot-scope="scope">-->
            <!--              <span v-if="scope.row.dataType == 0">图片</span>-->
            <!--              <span v-if="scope.row.dataType == 1">文档</span>-->
            <!--            </template>-->
          </el-table-column>
          <el-table-column prop="documentEncryptionLevel" label="文档密级" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.documentEncryptionLevel == 0">无</span>
              <span v-if="scope.row.documentEncryptionLevel == 1">低</span>
              <span v-if="scope.row.documentEncryptionLevel == 2">中</span>
              <span v-if="scope.row.documentEncryptionLevel == 3">高</span>
            </template>
          </el-table-column>
          <el-table-column prop="fileNum" label="文件数量" align="center">
          </el-table-column>
          <el-table-column prop="remark" label="说明" align="center">
          </el-table-column>
        </el-table>
      </div>
      <div class="table-box">
        <IconTitle title="子设备" imgUrl="yunwei">
        </IconTitle>
        <!-- 设备文档 -->
        <el-table
            :data="childDevice"
            border
            style="width: 100%"
            @selection-change="deviceSelectionChange"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column prop="deviceNum" label="资料编号" align="center">
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称" align="center">
          </el-table-column>
          <el-table-column prop="brandName" label="品牌" align="center">
          </el-table-column>
          <el-table-column prop="specification" label="规格型号" align="center">
          </el-table-column>
        </el-table>
      </div>
      <div class="table-box">
        <IconTitle title="父设备" imgUrl="yunwei">
        </IconTitle>
        <!-- 设备文档 -->
        <el-table
            :data="parentDevice"
            border
            style="width: 100%"
            @selection-change="deviceSelectionChange"
        >
          <el-table-column type="selection" align="center" width="55" />
          <el-table-column prop="deviceNum" label="资料编号" align="center">
          </el-table-column>
          <el-table-column prop="deviceName" label="设备名称" align="center">
          </el-table-column>
          <el-table-column prop="brandName" label="品牌" align="center">
          </el-table-column>
          <el-table-column prop="specification" label="规格型号" align="center">
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";
import { getUpdateEcho} from "@/api/ems/equipment/account";
export default {
  name: "basic",
  components: {
    IconTitle,
  },
  data() {
    return {
      // 基本信息
      basic: [],
      // 子设备表格数据
      childDevice: [],
      // 父设备表格数据
      parentDevice: [],
      // 设备相关文档表格数据
      deviceData: [],
      // 设备图片
      imgArray: [],
      dialogVisible: false,
      url: ''
    };
  },
  mounted() {
    this.getListJbxx()
  },
  methods: {
    deviceSelectionChange() {},
    getListJbxx(){
      getUpdateEcho(this.$route.query.id).then(res => {
        this.parentDevice = [];
        if (res.code === 200){
          this.basic = res.data;
          this.parentDevice.push( res.data.parentDevice)
          this.childDevice = res.data.sonChildren;
          this.deviceData = res.data.emsDeviceDataList;
          this.imgArray = res.data.imgArray;
        }
      })
    },

    image_tar(item) {
      this.url = item.url ? item.url : '';
      this.dialogVisible = true;
    },
    dialogVisibleClose(){
      this.dialogVisible = false;
      this.url = '';
    }

  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.info-box {
  .info-image {
    display: flex;
    width: 100%;
    position: relative;
    .info-box {
      width: 75%;
      margin-right: 10px;
      .info-basic {
        padding: 10px 15px;
        border-radius: 4px;
        background: #fff;
        box-sizing: border-box;
        b {
          display: block;
          margin: 10px 0;
        }
        .tag-box {
          .el-tag {
            margin-right: 15px;
          }
        }
        .equi-box {
          width: 100%;
          margin-top: 10px;
          display: flex;
          flex-wrap: wrap;
          font-size: 14px;
          border-bottom: 1px dotted #ccc;
          height: 100%;

          .table_basic{
            width: 100%;
            border: 1px solid #e8eef5;
            text-align: center;
            height: 100%;
            tr{
              th{
                font-size: 12px;
                color: #6b6d71;
                //text-align: left;
                //margin-left: -15px;
              }
              td{
                font-size: 12px;
                //width:98%;
                padding-right: -2px;
                text-align: left;
              }
            }
          }

          .equi-item {
            width: 33.333%;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            .title {
              display: inline-block;
              //width: 38%;
              //width: 70px;
              color: #666;
              text-align: right;
              font-size: 12px;
              margin-top: 10px;
            }
            //有圈圈的item
            .spec-item {
              width: 70%;
              color: #000;
              display: flex;
              align-items: center;
              font-size: 12px;
              .sbdj {
                display: flex;
                flex-direction: row;
                p{
                  width: 50px;
                  margin-top: 7px;
                }
              }
              .circle-blue {
                @include gradeCircle-v1(10px, #498ae8);
              }
              .circle-orange {
                @include gradeCircle-v1(10px, #fdaf5b);
              }
              .circle-green {
                @include gradeCircle-v1(10px, #5ec28e);
              }
              .circle-purple {
                @include gradeCircle-v1(10px, #7748d4);
              }
              .circle-red {
                @include gradeCircle-v1(10px, #ff0000);
              }
              .circle-yellow {
                @include gradeCircle-v1(10px, #ffff00);
              }
            }

            //一般的文字
            .item {
              width: 70%;
              color: #000;
              font-size: 12px;
              word-wrap: break-word;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              margin-top: 10px;
            }
            //长度很长的文字
            .spec-item-long {
              width: 70%;
              color: #000;
              white-space: nowrap;
            }
          }
        }
        .equi-box:last-child {
          border-bottom: 0;
        }
      }
      .no-first-info-basic {
        margin-top: 10px;
      }
    }

    .image-box {
      position: absolute;
      height: 100%;
      top: 0;
      width: calc(100% - 75% - 10px);
      right: 0;
      padding: 10px 15px;
      border-radius: 4px;
      background: #fff;
      box-sizing: border-box;
      height: 100%;

      .card-image{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin-left: 10px;

        .image {
          left: 1435px;
          top: 776px;
          width: 124px;
          height: 95px;
          margin-top: 7px;
          margin-left: 5px;
          border-radius: 5px;
        }
      }
    }
  }

}
</style>