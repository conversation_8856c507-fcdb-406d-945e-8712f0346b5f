<template>
  <div id="maintenanceDate" :style="{width: '100%', height: '300px'}"></div>
</template>

<script>
import {getServiceTime} from "@/api/ems/inspection/maintenanceStatistical"

let numberEchartsOptions = {
  tooltip: {
    trigger: 'axis',
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      position: 'bottom',
    },
  ],
  yAxis: {
    type: 'value'
  },
  color: '#63b2ee',
  series: [
    {
      name: '保养时间(分钟)',
      itemStyle : { normal: {label : {show: true}}},
      data: [],
      type: 'line',
      connectNulls: true,
    },
  ]
};
export default {
  data() {
    return {
      serviceTimeData: [],
      numberEchartsOptions,
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let maintenanceDate = this.$echarts.init(document.getElementById('maintenanceDate'))

      getServiceTime().then(res => {
        this.serviceTimeData = res.data.data;
        this.serviceTimeData.forEach(function (item) {
          numberEchartsOptions.series[0].data.push(item.num);
        })
        // 绘制图表
        maintenanceDate.setOption(this.numberEchartsOptions);
      });

    }
  }
}

</script>
