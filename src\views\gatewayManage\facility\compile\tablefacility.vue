<template>
    <div class="addTable">
        <div class="addTableTilte">寄存器点表】数据采集</div>
        <div class="addTableBtn">
            <el-button type="primary" @click="addformBtn" icon="el-icon-plus">新增</el-button>
            <el-button type="danger" :disabled="multiple" icon="el-icon-delete" @click="handleDelete" plain>删除</el-button>
        </div>

        <el-table v-loading="loading" border :data="dataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="属性名称" align="center" prop="propertyName" />
            <el-table-column label="key" align="center" prop="keyValue" />
            <el-table-column label="数据地址" align="center" prop="dataAddress" />
            <el-table-column label="数据类型" align="center" prop="dataType" />
            <el-table-column label="参数描述" align="center" prop="paramDesc" />
            <el-table-column label="读写属性" align="center" prop="rwValue" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="addformBtn(scope.row)">修改</el-button>
                    <el-button style="color: red;" size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getDataList" />


        <el-dialog :visible.sync="dialogVisible" :title="(!dataForm.id ? '新增' : '修改') + '采集数据'" width="600px">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="100px" @keyup.enter="submitHandle()">
                <el-form-item label="属性名称" prop="propertyName">
                    <el-input v-model="dataForm.propertyName" placeholder="请输入" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="key值" prop="keyValue">
                    <el-input v-model="dataForm.keyValue" placeholder="请输入" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="数据地址" prop="dataAddress">
                    <el-input v-model="dataForm.dataAddress" placeholder="请输入" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="数据类型" prop="dataType">
                    <el-select v-model="dataForm.dataType" placeholder="请选择" size="large" style="width: 90%">
                        <el-option v-for="item in typeData" :key="item.id" :label="item.dataType"
                            :value="item.dataType"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="参数描述" prop="paramDesc">
                    <el-input v-model="dataForm.paramDesc" placeholder="请输入" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="读写属性" prop="rwValue">
                    <el-select v-model="dataForm.rwValue" placeholder="请选择" size="large" style="width: 90%">
                        <el-option :value="0" label="只读"></el-option>
                        <el-option :value="1" label="只写"></el-option>
                        <el-option :value="2" label="读写"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitHandle" :loading="submitFlag">保存</el-button>
            </span>
        </el-dialog>


        <div v-show="footerWidth && footerWidth != '0px'" class="pageFooter" :style="{ width: `${footerWidth}` }">
            <div style="margin-right: 20px;">
                <el-button @click="lastNextStep">上一步</el-button>
                <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
        </div>

    </div>
</template>
 
  
<script>

import { getCollectList, getTypeDict, submitCollect, delCollect } from "@/api/gatewayManage/facility/index.js";
import { SessionStorage } from '@/utils/storage'


export default {
    props: {
        footerWidth: {
            type: String,
            default: '0px'
        }
    },
    data() {
        return {
            // 遮罩层
            loading: false,
            total: 0,
            dataList: [],
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                deviceName: '',
                gatewayId: '',
            },
            queryConfig: {},
            dialogVisible: false,
            dataForm: {
                propertyName: '',
                keyValue: '',
                dataAddress: '',
                dataType: undefined,
                paramDesc: '',
                rwValue: undefined,
            },
            dataRules: {
                propertyName: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
                keyValue: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
                dataAddress: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
                dataType: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
                rwValue: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
            },
            typeData: [],
            submitFlag: false,
            ids: [],
            single: true,
            multiple: true,
        };
    },
    created() {
        this.queryConfig = this.$route.query;
        this.getDataList();
        getTypeDict().then(res => this.typeData = res.data);
    },

    methods: {

        nextStep() {
            this.$emit('loadFuc', true);
            this.$emit('next');
        },

        lastNextStep() {
            this.$emit('loadFuc', true);
            this.$emit('lastnext');
        },

        reset() {
            this.dataForm = {
                propertyName: '',
                keyValue: '',
                dataAddress: '',
                dataType: undefined,
                paramDesc: '',
                rwValue: undefined,
            };
            this.resetForm("dataForm");
        },

        addformBtn(val) {
            this.reset();
            this.dialogVisible = true;
            this.dataForm.id = ''
            if (val.id) {
                const obj = { ...val };
                // nextTick(() => {
                //     
                // })
                this.dataForm = {
                    ...this.dataForm,
                    ...obj
                }
            }
        },

        getDataList() {
            // this.loading = true;
            const params = { ...this.queryParams, id: this.$route.query.id };
            getCollectList(params).then(response => {
                const { list, total } = response.data
                this.dataList = list || [];
                this.total = total;
            }).finally(() => this.loading = false)
        },

        submitHandle() {
            this.$refs["dataForm"].validate(valid => {
                if (valid) {
                    this.submitFlag = true;
                    const parmes = {
                        ...this.dataForm,
                        deviceId: SessionStorage.getItem('facilityPams').id
                    }
                    submitCollect(parmes).then(() => {
                        this.$message.success(!this.dataForm.id ? "添加成功" : '修改成功');
                        this.dialogVisible = false;
                        this.getDataList();
                    }).finally(() => this.submitFlag = false)
                }
            });
        },

        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length != 1
            this.multiple = !selection.length
        },

        handleDelete(row) {
            const data = row.id ? [row.id] : this.ids
            this.$modal.confirm('是否确认删除？').then(function () {
                return delCollect(data);
            }).then(() => {
                this.getDataList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },

    }
};
</script>
 
<style lang="scss" scoped>
.addTable {
    margin-top: 30px;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 50px;

    .addTableTilte {
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 18px;
        color: #0D162A;

        &:before {
            content: '';
            width: 4px;
            height: 12px;
            background: #0147EB;
            display: inline-block;
            margin-right: 10px;
        }
    }

    .addTableBtn {
        margin-top: 16px;
        margin-bottom: 16px;
    }


}
</style>