<template>
  <div>
    <div class="image-list">
      <div v-for="(item, index) in fileList" :key="item.url || item" class="image-item">
        <el-image lazy :src="ensureFullUrl(item.url || item)" />
        <div class="image-hover">
          <i @click="handlePreview(item)" class="el-icon-zoom-in"></i>
          <i v-if="!disabled" @click="handleRemove(item)" class="el-icon-delete"></i>
        </div>
      </div>
      <el-upload v-if="fileList.length < limit && !disabled" v-loading="uploadLoading" class="image-upload" action="#"
        :disabled="disabled" accept="image/*" :http-request="uploadApiFtn" :before-upload="beforeUploadImage"
        :on-success="handleSuccess" :on-error="handleError" :on-remove="handleRemove" :on-preview="handlePreview"
        :show-file-list="false" :multiple="multiple" :limit="limit">
        <i class="el-icon-plus"></i>
      </el-upload>
      <el-empty image-size="80" description="暂无图片" v-else-if="fileList.length === 0"></el-empty>
    </div>
    <p class="el-upload__tip">
      支持扩展名:<b style="color: #f56c6c">{{ imageTypes.join("/") }}</b>，且不超过<b style="color: #f56c6c">{{ imgSize }}M</b>
    </p>
    <el-dialog append-to-body title="预览" :visible.sync="dialogVisible">
      <el-image style="width: 100%" :src="ensureFullUrl(dialogImageUrl)" />
    </el-dialog>
  </div>
</template>

<script>
import { uploadApi } from "@/api/release/index.js";
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: [Number, String],
      default: 1,
    },
    //数据格式[{name: 'xxx', url: 'xxx'}]或者['xxx','xxx'],避免图片地址存在特殊字符导致图片无法显示
    fileList: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    imgSize: {
      type: Number,
      default: 2,
    },
    imageTypes: {
      type: Array,
      default: () => ["jpg", "jpeg", "png"],
    },
  },
  data() {
    return {
      dialogImageUrl: "",//对话框中显示的图片URL
      dialogVisible: false,//对话框是否可见
      uploadLoading: false,//上传文件时是否处于加载状态
    };
  },
  methods: {
    async uploadApiFtn(event) {
      let fileData = new FormData();
      fileData.append("file", event.file);
      this.uploadLoading = true;
      try {
        const res = await uploadApi(fileData);
        if (res.code === 200) {
          this.$emit("addUpload", res.data);
        }
      } catch (err) {
        this.handleError(err);
      } finally {
        this.uploadLoading = false;
      }
    },
    handleSuccess(response, file, fileList) {
      console.log("上传成功:", response);
      // 这里可以处理上传成功后的逻辑，例如更新图片地址
    },
    handleError(error, file, fileList) {
      this.uploadLoading = false; // 公共逻辑，上传后无论成功或失败都设置
      if (error) {
        // 在这里可以添加额外的错误处理逻辑
        console.error("文件上传失败:", error);
      }
      // 这里可以处理上传失败后的逻辑，例如提示错误信息
    },
    handleRemove(file, fileList) {
      this.showIndex = undefined; // 公共逻辑，删除图片后取消显示索引
      // 这里可以处理删除图片后的逻辑，例如更新图片列表
      this.$emit("removeUpload", file);
    },
    handlePreview(file) {
      this.dialogVisible = true;
      this.dialogImageUrl = file.url || file;
      // 这里可以实现图片预览功能，例如使用`<img>`标签显示图片
    },

    beforeUploadImage(file) {
      // 1. 添加对 file.name 不含 '.' 的异常处理
      let fileType = file.name.split(".").pop().toLowerCase();
      // 2. 将文件类型检查抽离为一个单独的函数，便于扩展
      const isValidFileType = (fileType) => {
        const allowedTypes = this.imageTypes; // 可以通过 props 或其他方式传递
        return allowedTypes.includes(fileType.toLowerCase());
      };
      // 3. 整合所有的条件判断，并统一处理不满足条件的情况
      const isJPG = isValidFileType(fileType);
      const isLt2M = file.size / 1024 / 1024 < this.imgSize;
      const isLength = this.limit >= this.fileList.length + 1;
      if (!isJPG) {
        this.$message.warning("上传图片只能是 JPG/PNG 格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.warning(`上传图片大小不能超过 ${this.imgSize}MB!`);
        return false;
      }
      if (!isLength) {
        this.$message.warning(`上传图片数量不能超过 ${this.limit} 个!`);
        return false;
      }
      return true; // 所有条件都满足，返回 true 允许上传
    },
  },
};
</script>
<style lang="scss" scoped>
.el-empty{
  padding: 0;
}
.el-icon-plus {
  width: 160px;
  height: 160px;
  line-height: 160px;
  border: 1px dashed #d9d9d9;

  &:hover {
    border-color: #409eff;
  }
}

.image-upload {
  width: 160px;
  height: 160px;
  border: 1px dashed #d9d9d9;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 10px;
  margin-bottom: 0;
  letter-spacing: 1px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;

  .image-item {
    margin-right: 10px;
    margin-bottom: 10px;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 160px;
    height: 160px;
    display: inline-block;
    position: relative;

    .el-image {
      width: 100%;
      height: 100%;
    }

    .image-hover {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      transition: 0.3 ease-out;

      i {
        cursor: pointer;
      }
    }

    &:hover .image-hover {
      opacity: 1;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
