import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/admin/user/implementer/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/user/implementer/',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/user/implementer/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/user/' + id,
    method: 'delete'
  })
}

export function getSex(){
  return request({
    url: '/admin/dict/type/gender',
    method: 'get',
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/user/implementer/',
    method: 'put',
    data: obj
  })
}
