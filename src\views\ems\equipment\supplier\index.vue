<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
    <div class="execution">
        <el-card class="box-card btn-search page-search">
            <div slot="header" class="clearfix">
                <div class="btn-box">
                    <el-button
                            type="info"
                            icon="el-icon-refresh-left"
                            @click="refreshChange()"
                    ></el-button
                    >
                    <el-button id="gwButton"
                               type="primary"
                               icon="el-icon-circle-plus-outline"
                               v-if="true"
                               @click="$refs.crud.rowAdd()"
                    >新增</el-button
                    >
                    <el-button
                            type="success"
                            icon="el-icon-edit"
                            v-if="true"
                            :disabled="single"
                            @click="handleEdit"
                    >编辑</el-button
                    >
                    <el-button
                            type="danger"
                            icon="el-icon-circle-close"
                            v-if="true"
                            @click.native="handleDel()"
                            :disabled="multiple"
                    >删除</el-button
                    >
                    <el-button
                            type="check"
                            icon="el-icon-download"
                            @click="exportExcel"
                    >导出</el-button
                    >
                </div>
                <div class="icon-box">
                    <i class="el-icon-search" @click="searchShow"></i>
                    <i class="el-icon-refresh" @click="refreshChange"></i>
                    <i class="el-icon-goods"></i>
                    <i class="el-icon-setting" @click="columnShow"></i>
                    <i class="icon-zuixiaohua" />
                </div>
            </div>

        </el-card>
        <basic-container>
            <avue-crud
                    ref="crud"
                    :page.sync="page"
                    :data="tableData"
                    :table-loading="tableLoading"
                    :option="tableOption"
                    @selection-change="selectionChange"
                    @on-load="getList"
                    @search-change="searchChange"
                    @refresh-change="refreshChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    @row-update="handleUpdate"
                    @row-save="handleSave"
                    @row-del="handleDel"
            >
                <template slot="header">
                    <IconTitle class="selfTitle" title="设备供应商" imgUrl="yunwei" />
                </template>

            </avue-crud>
        </basic-container>
    </div>
</template>
<script>
    import {fetchList,getObj,addObj,putObj,delObj} from  "@/api/ems/equipment/supplier";
    import {tableOption} from '@/const/crud/ems/equipment/supplier'
    import { mapGetters } from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/icon-title/index.vue";

    export default {
        name: 'emsdevicesupplier',
        components: {
            IconTitle,
        },
        data() {
            return {
                tableData: [],
                searchForm: {}, // 查询参数
                single: true,  // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableLoading: false,
                tableOption: tableOption,
                ids: [],
                selectionList:[]
            };
        },
        computed: {
            ...mapGetters(["permissions","theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsdevicesupplier_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsdevicesupplier_del, false),
                    editBtn: this.vaildData(this.permissions.ems_emsdevicesupplier_edit,false),
                };
            },
        },
        mounted() {
            this.initElement();
        },
        methods: {
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList=list
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },

            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
            },

            // 列表查询
            getList(page, params) {
                this.tableLoading = true;
                fetchList(
                        Object.assign({
                            current: page.currentPage,
                            size: page.pageSize,
                        },params,this.searchForm)).then((response) => {
                    this.tableData = response.data.records;
                    this.page.total = response.data.total;
                    this.tableLoading = false;
                })
                        .catch(() => {
                            this.tableLoading = false;
                        });
            },
            //编辑
            handleEdit(){
                var refsDate = this.$refs
                refsDate.crud.rowEdit(this.selectionList[0],this.selectionList[0].$index);
            },
            // 删除
            handleDel: function (row, index) {
                this.$confirm("是否确认删除所选数据项", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                        .then(() => {
                            let id = "";
                            if (row) {
                                id = row.id;
                            } else {
                                id = this.ids;
                            }
                            return delObj(id);
                        })
                        .then((data) => {
                            this.$message.success("删除成功");
                            this.getList(this.page);
                        });
            },
            // 更新
            handleUpdate: function (row,  index,done, loading) {
                putObj(row)
                        .then((data) => {
                            this.$message.success("修改成功");
                            done();
                            this.getList(this.page);
                        })
                        .catch(() => {
                            loading();
                        });
            },
            // 保存
            handleSave: function (row, done, loading) {
                addObj(row)
                        .then((data) => {
                            this.$message.success("添加成功");
                            done();
                            this.getList(this.page);
                        })
                        .catch(() => {
                            loading();
                        });
            },
            // 每页条数改变事件
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            // 当前页发生改变事件
            currentChange(current) {
                this.page.currentPage = current;
            },
            // 查询事件
            searchChange(form, done) {
                this.searchForm = form
                this.page.currentPage = 1
                this.getList(this.page, form)
                done()
            },
            // 刷新事件
            refreshChange() {
                this.getList(this.page);
            },
            // 导出excel
            exportExcel() {
                this.$download.getXlsx(
                        process.env.VUE_APP_BASE_API + "/platform/emsdevicesupplier/export",
                        this.searchForm,
                        "设备供应商.xlsx"
                );
            }
        },
    };
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";

.clearfix {
    height: 40px;
    position: relative;
    .btn-box {
        position: absolute;
        top: 0;
        left: 0;
    }
    .icon-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 40px;
    }
}
</style>
