<template>
  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box">
        <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <el-form-item label="异常编号" prop="repairNum">
            <el-input :disabled="true" placeholder="无需填写自动生成"/>
          </el-form-item>
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="form.deviceName" readonly placeholder="请选择设备">
              <el-button @click="handleDev()" style="padding-right:10px" slot="suffix" type="text">
                选择
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item v-if="form.deviceId > 0" label="故障类型" prop="falutCategoriesId">
            <el-select v-model="form.falutCategoriesId" placeholder="请选择" clearable
                       style="width: 100%">
              <el-option label="请选择" value="0" :disabled="true"></el-option>
              <el-option
                  v-for="item in falutCategoriesList"
                  :key="item.id"
                  :label="item.categories"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.deviceId > 0" label="故障描述" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入故障描述"/>
          </el-form-item>
        </div>
      </div>
      <div class="info-btn-box">
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>
    <el-dialog title="选择设备" width="70%" class="abow_dialog" :visible.sync="dialogTableVisible">
      <select-one ref="selectDev" :key="timer"></select-one>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="devOk()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getCatList} from "@/api/ems/repository/emsregulations";
import {addObj} from "@/api/ems/repair/exceptions"
import SelectOne from "@/components/ems/selectone/selectone"
import {Message} from "element-ui";

export default {
  name: "addPage",
  components: {
    IconTitle,
    SelectOne
  },
  data() {
    return {
      form: {
        "falutCategoriesId": null,
        "deviceName": null,
        "deviceId": null,
        "remark": null,
      },
      dialogTableVisible: false,
      timer: '',
      rules: {
        falutCategoriesId: [
          {required: true, message: '请选择故障类型', trigger: 'blur'},
        ],
        deviceId: [
          {required: true, message: '请选择设备', trigger: 'blur'},
        ],
        remark: [
          {required: true, message: '请输入故障描述', trigger: 'blur'},
        ],
      },
      //故障类型列表
      falutCategoriesList: [],
    }
  },
  created() {

  },
  methods: {
    goBack() {
      this.$parent.listFlag = true;
      this.$parent.addFlag = false;
    },
    submitForm(formName) {
      if (!this.form.deviceId) {
        this.$message.warning("请先选择设备");
        return false;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form));
          addObj(data).then((res) => {
            this.$parent.listFlag = true;
            this.$parent.addFlag = false;
            this.$message.success("新增成功");
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleDev() {
      this.timer = new Date().getTime()
      this.dialogTableVisible = true
    },
    devOk() {
      const obj = this.$refs["selectDev"].rows;
      this.form.deviceId = obj.id;
      this.form.deviceName = obj.deviceName
      this.dialogTableVisible = false
      this.getFalutList()
    },
    getFalutList() {
      this.form.falutCategoriesId = null
      getCatList(this.form.deviceId).then(res => {
        const list = res.data.data
        this.falutCategoriesList = list
        if (list.length == 0) {
          Message({
            showClose: true,
            message: '该设备暂未完善故障库！',
            type: 'warning'
          })
        }
        if (list.length == 1) {
          this.form.falutCategoriesId = list[0].id
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>

.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 60%;
    overflow: hidden;

    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

.add-box {
  /*.el-dialog__body {
    height: 80vh;
  }*/

  .table-box {
    height: 100%;

    .table-big-box {
      overflow: auto;
      height: 80%;
    }
  }
}
</style>

<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.add-box {
  margin-bottom: 50px;

  .info-box {
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 10px 15px;
    overflow: hidden;

    .info-from {
      display: flex;
      flex-wrap: wrap;
      padding-top: 20px;
      position: relative;

      .el-form-item {
        width: 50%;
        padding-right: 10px;
      }
    }

    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {
      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }

  .info-btn-box {
    width: 100%;
    text-align: center;
  }

  .user {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
