export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
  "column": [
	  {
      "type": "input",
      "label": "供应商",
      "prop": "supplierName",
      "span": 12,
      search: true,
          rules: [{
              required: true,
              message: '请输入供应商',
              trigger: 'blur'
          },
              {
                  min: 0,
                  max: 20,
                  message: '长度在 0 到 20 个字符',
                  trigger: 'blur'
              }]
    },	  {
      "type": "input",
      "label": "供应商编号",
      "prop": "supplierNum",
      "span": 12,
          rules: [{
              required: true,
              message: '请输入供应商编号',
              trigger: 'blur'
          },
              {
                  min: 0,
                  max: 20,
                  message: '长度在 0 到 20 个字符',
                  trigger: 'blur'
              }]
      // search: true

    },	  {
      "type": "input",
      "label": "地址",
      "prop": "address",
      "span": 12,
          rules: [
              {
                  min: 0,
                  max: 60,
                  message: '长度在 0 到 60 个字符',
                  trigger: 'blur'
              }]
    },	  {
      "type": "input",
      "label": "联系人",
      "prop": "linkman",
      "span": 12,
          rules: [
              {
                  min: 0,
                  max: 5,
                  message: '长度在 0 到 5 个字符',
                  trigger: 'blur'
              }]
    },	  {
      "type": "input",
      "label": "电话",
      "prop": "phone",
      "span": 12,
      rules: [
          {
              min: 0,
              max: 20,
              message: '长度在0-20字符',
              trigger: 'blur'
          }]
    },
    {
      row: true,
      minRows: 2,
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12,
        rules: [
            {
                min: 0,
                max: 255,
                message: '长度在0-255字符',
                trigger: 'blur'
            }]
    }	  ]
}
