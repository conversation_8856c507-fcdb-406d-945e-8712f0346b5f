//hide: true列表没有 新增和编辑有
//addDisplay: false, 新增没
//editDisplay: false,编辑没
//editDisabled: true,编辑禁止

import { getHeaders } from "@/const/crud/getHeaders"

const headers = getHeaders();

const frontLabel = process.env.VUE_APP_BASE_API;

export const tableOption = {
  "border": true,
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "editBtn":false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": true,
  "gridBtn": false,
  "column": [
    {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
      hide: true
    },
    {
      "type": "input",
      "label": "设备编号",
      "prop": "deviceNum",
      "minWidth": 160,
      "span": 12,
      "search":true,
    }, {
      "type": "input",
      "label": "设备名称",
      "prop": "deviceName",
      "minWidth": 120,
      "span": 12,
      "search":true,
    }, {
      "type": "input",
      "label": "资产编号",
      "prop": "assetNum",
      "minWidth": 160,
      "span": 12
    },
    // {
    //   "type": "input",
    //   "label": "序列号",
    //   "prop": "serialNum",
    //   "minWidth": 120,
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "类别",
      "prop": "category",
      "minWidth": 120,
      "span": 12
    },
    {
      "type": "input",
      "label": "设备状态",
      "prop": "status",
      "minWidth": 120,
      "span": 12,
      dicUrl: `${frontLabel}/system/dict/data/type/device_status`,
      dicHeaders: headers,
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      }
    }, {
      "type": "input",
      "label": "使用状态",
      "prop": "useStatus",
      "minWidth": 120,
      "span": 12,
      dicUrl: `${frontLabel}/system/dict/data/type/device_use_status`,
      dicHeaders: headers,
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      }
    },
    // {
    //   "type": "input",
    //   "label": "设备等级",
    //   "prop": "deviceLevel",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "负责人",
      "prop": "liableUser",
      "minWidth": 120,
      "span": 12
    },
    {
      "type": "input",
      "label": "规格型号",
      "prop": "specification",
      "minWidth": 160,
      "span": 12
    },
    {
      "type": "input",
      "label": "所属部门",
      "prop": "deptName",
      "minWidth": 120,
      "span": 12
    },

    {
      "type": "input",
      "label": "位置",
      "prop": "location",
      "minWidth": 120,
      "span": 12
    }, {
      "type": "input",
      "label": "单位",
      "prop": "unit",
      "minWidth": 120,
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/unit`,
      dicHeaders: headers
    },
    // {
    //   "type": "input",
    //   "label": "品牌id",
    //   "prop": "brandId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "供应商",
      "prop": "supplierName",
      "minWidth": 230,
      "span": 12
    },
    // {
    //   "type": "input",
    //   "label": "设备来源",
    //   "prop": "source",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "购置日期",
      "prop": "purchaseDate",
      "minWidth": 120,
      "span": 12
    }, {
      "type": "input",
      "label": "购置金额",
      "prop": "purchaseAmount",
      "minWidth": 120,
      "span": 12
    }, {
      "type": "input",
      "label": "保修期至",
      "prop": "warrantyDate",
      "minWidth": 120,
      "span": 12
    }, {
      "type": "input",
      "label": "启用日期",
      "prop": "introductionDate",
      "minWidth": 120,
      "span": 12
    }, {
      "type": "input",
      "label": "预计报废日期",
      "prop": "expectedScrapDate",
      "minWidth": 120,
      "span": 12
    },
    // {
    //   "type": "input",
    //   "label": "是否计量设备",
    //   "prop": "meteringDevice",
    //   "span": 12,
    //   dicUrl:'/system/dict/data/type/meteringDevice_status'
    // }, {
    //   "type": "input",
    //   "label": "是否开启折旧",
    //   "prop": "depreciationOpen",
    //   "span": 12,
    //   dicUrl:'/system/dict/data/type/meteringDevice_status'
    // },
    // {
    //   "type": "input",
    //   "label": "技术参数",
    //   "prop": "technicalParam",
    //   "span": 12
    // },
    //咨询
    // {
    //   "type": "input",
    //   "label": "文件id",
    //   "prop": "fileId",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "父id",
    //   "prop": "parentId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "创建者",
    //   "prop": "createBy",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "创建时间",
    //   "prop": "createTime",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "更新者",
    //   "prop": "updateBy",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "更新时间",
    //   "prop": "updateTime",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "备注",
    //   "prop": "remark",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "删除标志(0正常 1删除)",
    //   "prop": "delFlag",
    //   "span": 12
    // }, {
    //   "type": "input",
    //   "label": "租户Id",
    //   "prop": "tenantId",
    //   "span": 12
    // }
  ]
}
