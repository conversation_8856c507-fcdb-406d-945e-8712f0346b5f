<template>
    <div class="add-box">
        <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="150px"
                size="small"
                class="demo-ruleForm"
        >
            <div class="info-box" style="z-index: 9999999">
                <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
                <div class="info-from">
                    <el-form-item label="标准编号" prop="standardNum">
                        <el-input v-model="form.standardNum" :disabled="true" placeholder="无需填写自动生成"/>
                    </el-form-item>
                    <el-form-item label="标准名称" prop="standardName">
                        <el-input v-model="form.standardName" placeholder="请输入标准名称"/>
                    </el-form-item>
                    <el-form-item label="设备类别" prop="categoryId">
                        <treeselect
                                v-model="form.categoryId"
                                :options="categoryList"
                                :normalizer="normalizer"
                                placeholder="请选择设备类别"
                                :appendToBody="true"
                        />
                    </el-form-item>
                    <el-form-item label="检修周期(单位：日)" prop="inspectCycle">
                        <el-input v-model="form.inspectCycle"
                                  placeholder="请输入检修周期"
                                  oninput="value=value.replace(/[^\d]/g,'')"
                                  />
                    </el-form-item>
                    <el-form-item label="作业内容" prop="jobContent">
                        <el-input type="textarea" v-model="form.jobContent" placeholder="请输入作业内容"/>
                    </el-form-item>
                    <el-form-item label="技术要求" prop="technicalRequirement">
                        <el-input type="textarea" v-model="form.technicalRequirement" placeholder="请输入技术要求"/>
                    </el-form-item>
                    <el-form-item label="安全要求" prop="safety">
                        <el-input type="textarea" v-model="form.safety" placeholder="请输入安全要求"/>
                    </el-form-item>
                    <el-form-item label="质量要求" prop="qualityRequirements">
                        <el-input type="textarea" v-model="form.qualityRequirements" placeholder="请输入质量要求"/>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input type="textarea" v-model="form.remark" placeholder="请输入备注"/>
                    </el-form-item>
                </div>
            </div>
            <div class="info-box">
                <IconTitle title="设备配置" imgUrl="yunwei"></IconTitle>
                <!--                @selection-change="handleSelectionChange"-->
                <el-button style="float: right"
                           type="primary"
                           icon="el-icon-plus"
                           size="mini"
                           @click="addDeviceId"
                >新增
                </el-button>
                <el-table v-loading="loading" :data="deviceList">
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                    <el-table-column label="设备名称" align="center" prop="deviceName"/>
                    <el-table-column label="品牌" align="center" prop="brandNewName"/>
                    <el-table-column label="规格型号" align="center" prop="specification"/>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="deviceListDele(scope.row)"
                            >删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-show="queryParams.total>0"
                        :total="queryParams.total"
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="deviceGetList"
                />
                <!-- 用于添加设备的 -->
                <el-dialog :title="device.title" :visible.sync="device.open" width="800px" append-to-body>
                    <el-table v-loading="device.loading" :data="deviceNewList"
                              @selection-change="handleSelectionChange"
                    >
                        <el-table-column type="selection" align="center"/>
                        <el-table-column label="id" align="center" prop="id" v-if="false"/>
                        <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                        <el-table-column label="设备名称" align="center" prop="deviceName"/>
                        <el-table-column label="品牌" align="center" prop="brandNewName"/>
                        <el-table-column label="规格型号" align="center" prop="specification"/>
                    </el-table>
                    <pagination
                            v-show="queryParamsDeviceList.total>0"
                            :total="queryParamsDeviceList.total"
                            :page.sync="queryParamsDeviceList.pageNum"
                            :limit.sync="queryParamsDeviceList.pageSize"
                            @pagination="deviceGetList"
                    />
                    <div slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="submitDeiceList">确 定</el-button>
                        <el-button @click="cancelDeviceList">取 消</el-button>
                    </div>
                </el-dialog>
            </div>
            <div class="info-btn-box">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
        </el-form>

    </div>

</template>
<script>
    import IconTitle from "@/components/ems/icon-title/index.vue";
    import ImageUpload from "@/components/ImageUpload/index.vue";
    import {fetchListTree} from "@/api/ems/equipment/category";
    import {getBrandList} from "@/api/ems/equipment/brand";
    import {fetchTree} from "@/api/admin/dept";
    import Treeselect from "@riophae/vue-treeselect";
    import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    import {mapGetters} from "vuex";
    import {standardFetchList, standardAddObj,standardGetObj,
        standardPutObj, delObj,deviceList, deviceListAll} from "@/api/ems/repair/emsreprepairstandard";
    import { remote } from '@/api/admin/dict';

    export default {
        name: "AddIndex",
        components: {
            IconTitle,
            Treeselect,
            ImageUpload,
        },
        props: {
            id: {
                type: String,
            },
        },
        data() {
            return {
                defaultProps: {
                    children: "children",
                    label: "name",
                },
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条,
                },
                list: [],
                treeData: [],
                loading: false,
                categoryList: [], //设备类别
                brandList: [], //设备品牌
                treeDeptData: [], //部门
                form: {
                    id: null,
                    standardNum: null,
                    standardName: null,
                    inspectCycle:null,
                    categoryId:null,
                    jobContent:null,
                    technicalRequirement:null,
                    safety:null,
                    qualityRequirements:null,
                    remark: null,
                    deviceId: [],
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                queryParamsDeviceList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                device: {
                    title: "",
                    open: false,
                    loading: false,
                    deviceForm: {
                        id: null,
                        standardNum: null,
                        standardName: null,
                        deptId: null,
                        requirement: null,
                        remark: null,
                        deviceId: [],
                    }
                },
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 类型字典
                typeList1: [{"id":257,"dictId":83,"value":"1","label":"数字","type":"ins_inspect_items_type","description":"数字","sort":1,"createTime":"2022-01-13 17:21:15","updateTime":"2022-01-13 17:21:15","remarks":null,"delFlag":"0"},{"id":258,"dictId":83,"value":"2","label":"选项","type":"ins_inspect_items_type","description":"选项","sort":2,"createTime":"2022-01-13 17:21:33","updateTime":"2022-01-13 17:21:33","remarks":null,"delFlag":"0"},{"id":259,"dictId":83,"value":"3","label":"文本","type":"ins_inspect_items_type","description":"文本","sort":3,"createTime":"2022-01-13 17:21:45","updateTime":"2022-01-13 17:21:45","remarks":null,"delFlag":"0"}],
                // 设备数据
                deviceList: [],
                //所有的设备数据
                deviceNewList: [],
                coverImgTem: [],
                imgArrayTem: [],
                rules: {
                    standardName: [
                        {required: true, message: '请输入标准名称', trigger: 'blur'}
                    ],
                    categoryId: [
                        {required: true, message: '请选择类别', trigger: 'blur'}
                    ],
                    inspectCycle:[
                        {required: true, message: '请输入检修周期', trigger: 'blur'}
                    ],
                },
                dialogVisible: false,
            };
        },
        created() {
            this.getSelect();
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
                };
            },
        },
        mounted() {
            remote("ins_inspect_items_type").then(response => {
                this.typeList = response.data;

            });
            this.reset();
            this.deviceReset()
            if (this.id > 0) {
                standardGetObj(this.id).then((res) => {
                    this.form = res.data.data
                    // console.log(JSON.stringify(res))
                    deviceList(Object.assign(
                        {
                            current: this.queryParams.pageNum,
                            size: this.queryParams.pageSize,
                        },
                        {id: this.id}
                        )
                    ).then(response => {
                        this.deviceList = response.data.data.records;
                        this.queryParams.total = response.data.data.total;
                        for (var i = 0; i < this.deviceList.length; i++) {
                            this.device.deviceForm.deviceId.push(this.deviceList[i].id);
                        }
                        this.loading = false;
                        this.device.open = false;
                    });
                });

            }
            // fetchTree().then((response) => {
            //     this.treeData = response.data.data;
            // });
        },
        methods: {
            nodeClick(data) {
                this.page.page = 1;
                this.getListUser(this.page, {deptId: data.id});
            },
            deviceGetList() {
                this.device.loading = true;
                deviceList(Object.assign(
                    {
                        current: this.queryParamsDeviceList.pageNum,
                        size: this.queryParamsDeviceList.pageSize,
                    },
                    {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceNewList = response.data.data.records;
                    this.queryParamsDeviceList.total = response.data.data.total;
                    this.device.loading = false;
                });
            },

            deviceListDele(row) {
                for (var i = 0; i < this.deviceList.length; i++) {
                    if (this.deviceList[i].id == row.id) {
                        this.deviceList.splice(i, 1)
                        this.queryParams.total = this.queryParams.total - 1;
                    }
                }
                for (var i = 0; i < this.device.deviceForm.deviceId.length; i++) {
                    if (this.device.deviceForm.deviceId[i] == row.id) {
                        this.device.deviceForm.deviceId.splice(i, 1)
                    }
                }
            },

            submitDeiceList() {
                this.loading = true;
                for (var i = 0; i < this.ids.length; i++) {
                    this.device.deviceForm.deviceId.push(this.ids[i]);
                }

                deviceListAll(Object.assign(
                    {
                        current: this.queryParams.pageNum,
                        size: this.queryParams.pageSize,
                    },
                    {deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceList = response.data.data.records;
                    this.queryParams.total = response.data.data.total;
                    this.loading = false;
                    this.device.open = false;
                });
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            cancelDeviceList() {
                this.device.title = "";
                this.device.open = false;
            },
            addDeviceId() {
                this.device.title = "设备台账";
                this.device.open = true;
                this.deviceGetList();
            },
            selectUser(row) {
                this.form.liableUserName = row.username
                this.form.liableUserId = row.userId
                this.dialogVisible = false
            },
            toAdd() {
                this.device.title = "设备台账";
                this.device.open = true;
            },
            getListUser(page, params) {
                standardFetchList(
                    Object.assign(
                        {
                            current: this.page.currentPage,
                            size: this.page.pageSize,
                        },
                        params
                    )
                ).then((response) => {
                    this.list = response.data.data.records;
                    this.page.total = response.data.data.total;
                });
            },
            submitForm(formName) {
                this.form.deviceId = this.device.deviceForm.deviceId;
                if (this.form.deviceId.length==0){
                    this.$message.error("请添加设备");
                    return;
                }
                if (this.form.inspectCycle<=0){
                    this.$message.error("检修周期不能小于，请重新输入");
                    this.form.inspectCycle=null;
                    return;
                }
                let data = JSON.parse(JSON.stringify(this.form));
                this.$refs[formName].validate((valid) => {
                    if(valid){
                        if (data.id) {
                            standardPutObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("修改成功");
                            });
                        } else {
                            standardAddObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("新增成功");
                            });
                        }
                    }
                });
            },
            reset() {
                this.form = {
                    id: null,
                    standardNum: null,
                    standardName: null,
                    inspectCycle:null,
                    categoryId:null,
                    jobContent:null,
                    technicalRequirement:null,
                    safety:null,
                    qualityRequirements:null,
                    remark: null,
                    deviceId: [],
                    deviceNewList: [],
                };
                //this.resetForm("form");
            },
            deviceReset() {
                this.deviceForm = {
                    id: null,
                    standardNum: null,
                    standardName: null,
                    deptId: null,
                    requirement: null,
                    remark: null,
                    deviceId: [],
                    deviceNewList: [],
                };
                // this.resetForm("deviceForm");
            },
            getSelect() {
                remote("ins_inspect_items_type").then(response => {
                    this.typeList = response.data.data;
                });
                fetchListTree("").then((res) => {
                    this.categoryList = res.data.data ? res.data.data : [];
                });
                getBrandList().then((res) => {
                    this.brandList = res.data.data;
                });
            },
            normalizer(node) {
                if (node.children && !node.children.length) {
                    delete node.children;
                }
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children,
                };
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss">
    .add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;

            }

        }
    }
</style>

<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
</style>
