export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    // 'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "editBtn": false,
    "delBtn": false,
    // "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": false,
    "gridBtn": false,
    menu:false,
  "column": [
      {
          "type": "input",
          "label": "id",
          "prop": "id",
          "span": 16,
          hide:true
      },
	  {
      "type": "input",
      "label": "设备编号",
      "prop": "deviceNum",
      "span": 16
    },	  {
      "type": "input",
      "label": "设备名称",
      "prop": "deviceName",
      "span": 12
    },
    //   {
    //   "type": "input",
    //   "label": "所属部门",
    //   "prop": "deptId",
    //   "span": 12
    // },
      {
      "type": "input",
      "label": "类别",
      "prop": "categoryName",
      "span": 12
    },	  {
      "type": "input",
      "label": "规则型号",
      "prop": "specification",
      "span": 12
    },	  {
      "type": "input",
      "label": "位置",
      "prop": "locationName",
      "span": 12
    },	  {
      "type": "input",
      "label": "所属策略",
      "prop": "strategyName",
      "span": 14
    },	  {
      "type": "input",
      "label": "标准",
      "prop": "standardName",
      "span": 12
    },
      // {
      //     "type": "input",
      //     "label": "负责人",
      //     "prop": "liableUserId",
      //     "span": 12
      // }
      ]
}
