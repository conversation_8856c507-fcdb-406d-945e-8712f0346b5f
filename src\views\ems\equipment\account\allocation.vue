<template>
  <div class="allocation">
    <div class="table-box">
      <IconTitle title="调拨记录" imgUrl="yunwei">
        <span class="slot">调拨转移记录表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="deviceData"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="date" label="调拨单号" align="center">
        </el-table-column>
          <el-table-column prop="date" label="申请时间" align="center">
        </el-table-column>
        <el-table-column prop="name" label="申请人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调出部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调出地点" align="center">
        </el-table-column>
         <el-table-column prop="name" label="原负责人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调入部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调入地点" align="center">
        </el-table-column>
        <el-table-column prop="name" label="新负责人" align="center">
        </el-table-column>
        <el-table-column prop="address" label="备注" align="center">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";

export default {
  name: "allocation",
  components: {
    IconTitle,
  },
  data() {
    return {
         deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区",
        },
      ],
    };
  },
  methods: {
       deviceSelectionChange(){}
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
.allocation {
 
}
</style>
