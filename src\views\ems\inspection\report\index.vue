<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
    <div  v-if="listFlag" class="execution">
        <el-card class="box-card btn-search page-search">
            <div slot="header" class="clearfix">
                <div class="btn-box">
                    <el-button
                            type="info"
                            icon="el-icon-refresh-left"
                            @click="refreshChange()"
                    ></el-button
                    >
                    <el-button
                            type="check"
                            icon="el-icon-download"
                            @click="exportExcel"
                    >导出</el-button
                    >
                </div>
                <div class="icon-box">
                    <i class="el-icon-search" @click="searchShow"></i>
                    <i class="el-icon-refresh" @click="refreshChange"></i>
                    <i class="el-icon-goods"></i>
                    <i class="el-icon-setting" @click="columnShow"></i>
                    <i class="icon-zuixiaohua" />
                </div>
            </div>

        </el-card>
        <basic-container>
            <avue-crud
                    ref="crud"
                    :page.sync="page"
                    :data="tableData"
                    :permission="permissionList"
                    :table-loading="tableLoading"
                    :option="tableOption"
                    @selection-change="selectionChange"
                    @on-load="getList"
                    @search-change="searchChange"
                    @refresh-change="refreshChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    @row-update="handleUpdate"
                    @row-save="handleSave"
                    @row-del="handleDel"
                    :cell-style="cellStyle"
                    @row-click="rowClick"
                    @cell-click="cellClick"
            >
                <template slot="header">
                    <IconTitle class="selfTitle" title="点检记录" imgUrl="yunwei" />
                </template>

            </avue-crud>
        </basic-container>
        <el-drawer
                class="drawerStyle"
                title="点检记录"
                :show-close="false"
                :visible.sync="detail"
                direction="rtl"
                size="45%"
                append-to-body
        >
            <div>
                <img style="float: right;margin-right: 180px" :src="require('@/assets/imagesAssets/xgfj.png')">
                <div style="width: 66%">
                    <span class="labelS">记录编号：</span>
                    <span class="contentS">{{ rowCheck.recordNum }}</span>
                </div>
                <div style="width: 66%">
                    <span class="labelS">设备名称：</span>
                    <span class="contentS">{{ rowCheck.deviceNum}}</span>
                </div>
                <div style="width: 66%">
                    <span class="labelS">设备编号：</span>
                    <span class="contentS">{{rowCheck.deviceName }}</span>
                </div>

            </div>
            <div class="line">
            </div>
            <div>
                <el-card shadow="always" class="box-card">
                    <div><span class="tableTitle">点检结果</span></div>
                    <el-table v-loading="rowCheck.loading" :data="rowCheck.deviceList">
                        <el-table-column label="id" align="center" prop="id" v-if="false"/>
                        <el-table-column label="项目" align="center" prop="itemsName"/>
                        <el-table-column label="点检值" align="center" prop="resultsInspectValue"/>
                        <el-table-column label="点检时间" align="center" prop="resultsCreateTime"/>
                        <el-table-column label="是否异常" align="center" prop="specification">
                            <template slot-scope="scope">
                                          <span>{{
                                            scope.row.resultsIsAbnormal == 0
                                              ? "正常"
                                              : scope.row.resultsIsAbnormal == 1
                                              ? "异常": ""
                                          }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="点检照片" align="center" prop="resultsImageList"/>
                        <el-table-column label="清除异常项" align="center" prop="specification"/>
                    </el-table>
                    <pagination
                            v-show="rowCheck.queryParams.total>0"
                            :total="rowCheck.queryParams.total"
                            :page.sync="rowCheck.queryParams.pageNum"
                            :limit.sync="rowCheck.queryParams.pageSize"
                            @pagination="resultsGetList"
                    />
                </el-card>
            </div>
        </el-drawer>
    </div>
    <div v-else-if="detaillistFlag">
        <IndexDetail :id='detailId'/>
    </div>
</template>

<script>
    import {inspectrecordFetchList,inspectrecordGetObj,getEmsInsCheckResultsRecordIdPage,inspectrecordAddObj,inspectrecordPutObj,inspectrecordDelObj} from "@/api/ems/inspection/emsinsinspectrecord";
    import {tableOption} from '@/const/crud/ems/inspection/emsinsinspectrecord'
    import { mapGetters } from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/icon-title/index.vue";
    import IndexDetail from "./detail.vue";

    export default {
        name: 'emsinsinspectrecord',
        components: {
            IconTitle,
            IndexDetail,
        },
        data() {
            return {
                tableData: [],
                searchForm: {}, // 查询参数
                single: true,  // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableLoading: false,
                tableOption: tableOption,
                ids: [],
                selectionList:[],

                listFlag: true,
                detailId: 0,
                detaillistFlag: false,
                detail:false,
                //以下为抽屉参数
                rowCheck: {
                    recordNum:null,
                    deviceNum:null,
                    deviceName:null,

                    loading:false,
                    rowId:null,
                    deviceList:[],
                    queryParams: {
                        pageNum: 1,
                        pageSize: 10,
                        total: 0,
                    },
                }

            };
        },
        computed: {
            ...mapGetters(["permissions","theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinsinspectrecord_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinsinspectrecord_del, false),
                    editBtn: this.vaildData(this.permissions.ems_emsinsinspectrecord_edit,false),
                };
            },
        },
        mounted() {
            this.initElement();
        },
        methods: {
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList=list
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },
            cellStyle(data) {
                if (data.columnIndex == 2) {
                    return "color:#02b980;cursor:pointer";
                }
            },
            cellClick(row, column) {
                if (column.property === "recordNum") {
                    this.toDetail(row);
                } else {
                    return;
                }
            },
            rowClick(row, column) {
                if (column.property === "recordNum") {
                    return;
                } else {
                    this.detail = true;
                    this.rowCheck.recordNum=row.recordNum;
                    this.rowCheck.deviceNum=row.deviceNum;
                    this.rowCheck.deviceName=row.deviceName;

                    this.rowCheck.rowId=row.id
                    this.resultsGetList();
                }
            },
            resultsGetList(){
                this.rowCheck.loading = true;
                getEmsInsCheckResultsRecordIdPage(Object.assign(
                    {
                        current: this.rowCheck.queryParams.pageNum,
                        size: this.rowCheck.queryParams.pageSize,
                    },
                    {id: this.rowCheck.rowId}
                    )
                ).then(response => {
                    this.rowCheck.deviceList = response.data.records;
                    this.rowCheck.queryParams.total = response.data.total;
                    this.rowCheck.loading = false;
                });
            },
            toDetail(row) {
                this.detailId = row.id
                this.listFlag = false;
                this.detaillistFlag = true;
            },
            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
            },

            // 列表查询
            getList(page, params) {
                this.tableLoading = true;
                inspectrecordFetchList(
                        Object.assign({
                            current: page.currentPage,
                            size: page.pageSize,
                        },params,this.searchForm)).then((response) => {
                    this.tableData = response.data.records;
                    this.page.total = response.data.total;
                    this.tableLoading = false;
                })
                        .catch(() => {
                            this.tableLoading = false;
                        });
            },
            //编辑
            handleEdit(){
                var refsDate = this.$refs
                refsDate.crud.rowEdit(this.selectionList[0],this.selectionList[0].$index);
            },
            // 删除
            handleDel: function (row, index) {
                this.$confirm("是否确认删除所选数据项", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                        .then(() => {
                            let id = "";
                            if (row) {
                                id = row.id;
                            } else {
                                id = this.ids;
                            }
                            return inspectrecordDelObj(id);
                        })
                        .then((data) => {
                            this.$message.success("删除成功");
                            this.getList(this.page);
                        });
            },
            // 更新
            handleUpdate: function (row,  index,done, loading) {
                inspectrecordPutObj(row)
                        .then((data) => {
                            this.$message.success("修改成功");
                            done();
                            this.getList(this.page);
                        })
                        .catch(() => {
                            loading();
                        });
            },
            // 保存
            handleSave: function (row, done, loading) {
                inspectrecordAddObj(row)
                        .then((data) => {
                            this.$message.success("添加成功");
                            done();
                            this.getList(this.page);
                        })
                        .catch(() => {
                            loading();
                        });
            },
            // 每页条数改变事件
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            // 当前页发生改变事件
            currentChange(current) {
                this.page.currentPage = current;
            },
            // 查询事件
            searchChange(form, done) {
                this.searchForm = form
                this.page.currentPage = 1
                this.getList(this.page, form)
                done()
            },
            // 刷新事件
            refreshChange() {
                this.getList(this.page);
            },
            // 导出excel
            exportExcel() {
                this.$download.getXlsx(
                        process.env.VUE_APP_BASE_API + "/platform/emsinsinspectrecord/export",
                        this.searchForm,
                        "点检记录.xlsx"
                );
            },
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/avue.scss";
    @import "@/styles/ems/public-styles.scss";

    .statusList {
        margin-top: 20px;

        .labelS {
            color: #C1C8D2;
        }

        .dottedStyle {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background-color: #F7CA60;
        }

        .detailInfo {
            color: #5D5D5D;
            font-size: 14px;
            margin-right: 10px;
        }

        .statusContent {
            font-size: 14px;
        }
    }

    .labelS {
        display: inline-block;
        width: 100px;
        font-size: 14px;
        margin-right: 10px;
        text-align: right;
        color: #888888;

    }

    .contentS {
        font-weight: bold;
        font-size: 14px;
        color: #101010;
        margin: 10px 0;
        display: inline-block;
    }

    .line {
        border: 2px solid rgba(236, 240, 244, 100);
        margin: 30px 0;
    }

    ::v-deep.drawerStyle {
        .el-drawer__header {
            background-color: #F2F2F5;
            padding: 20px 0 20px 20px;
            color: #101010;
            margin-bottom: 10px;
        }

        .box-card {
            box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, .12);
            margin: 0 20px 10px;
        }

        .tableTitle {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: inline-block;
        }
    }

    .icon-style {
        display: flex;

    }

    .con-left {
        width: 80%;
        height: 400px;
        overflow-y: scroll;

        .contentS {
            font-weight: normal;
        }

        .small-title {
            font-size: 12px;
            font-weight: 600;
            margin: 10px 15px;
            display: block;
        }
    }

    .con-right {
        width: 20%;
        border-right: 1px solid #ECF0F4;
        padding-left: 23px;
        text-align: center;
        box-sizing: border-box;

        .my-steps-box {
            font-size: 12px;
            margin-top: 50px;

            .step-item {
                margin-bottom: 15px;
                padding: 6px 0;
                position: relative;

                .text {
                    cursor: pointer;
                }
            }

            .step-item.step-active {
                border-right: 2px solid #26AE61;

                .text {
                    color: $theme;
                }
            }

            .step-item:nth-last-child(1) .line {
                display: none;
            }
        }
    }
</style>
