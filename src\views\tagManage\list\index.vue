<template>
    <div class="app-container tagManageList">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
            <el-form-item label="标签名称" prop="labelName" >
                <el-input v-model="queryParams.labelName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:post:add']">新增</el-button>
            </el-col>
        </el-row>

        <el-table v-loading="loading" :data="postList" style="width: 100%">
            <el-table-column label="标签名称" prop="labelName" width="150" />
            <el-table-column label="标签颜色" prop="labelColour" width="120">
                <template slot-scope="scope">
                    <el-tag :style="{ backgroundColor: scope.row.labelColour }" effect="dark"></el-tag>
                </template>
            </el-table-column>
            <el-table-column label="标签名称颜色" prop="labelNameColour" width="120">
                <template slot-scope="scope">
                    <el-tag :style="{ backgroundColor: scope.row.labelNameColour }" effect="dark"></el-tag>
                </template>
            </el-table-column>
            <el-table-column label="标签效果" prop="labelNameColour" width="150">
                <template slot-scope="scope">
                    <el-tag :style="{ backgroundColor: scope.row.labelColour, color: scope.row.labelNameColour }"
                        effect="dark">{{ scope.row.labelName }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="使用次数" prop="useNumber" width="120" />
            <el-table-column label="查询次数" prop="queryNumber" width="120" />
            <el-table-column label="标签描述" prop="description" width="380" />
            <el-table-column label="创建时间" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="创建人" prop="creator" width="120" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getList" />

        <!-- 添加或修改岗位对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="标签名称" prop="labelName">
                    <el-input v-model="form.labelName" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="标签颜色">
                    <el-color-picker v-model="form.labelColour" />
                </el-form-item>
                <el-form-item label="标签名称颜色">
                    <el-color-picker v-model="form.labelNameColour" />
                </el-form-item>
                <el-form-item label="标签预览">
                    <el-tag v-if="form.labelName"
                        :style="{ color: form.labelNameColour, backgroundColor: form.labelColour }" 
                        effect="dark">{{form.labelName }}</el-tag>
                </el-form-item>
                <el-form-item label="标签描述" prop="description">
                    <el-input :rows="3" type="textarea" v-model="form.description" placeholder="请输入"
                        style="width: 90%;"></el-input>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>
import { tagManageList ,addTag,editTage,delTag} from "@/api/tagManage/index.js";

export default {
    name: "tagManageList",
    dicts: ['sys_normal_disable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            showSearch: true,
            total: 0,
            postList: [],
            title: "",
            open: false,
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                labelName: undefined,
            },
            form: {
                labelColour:'#409EFF',
                labelNameColour:'#FFFFFF'
            },
            rules: {
                labelName: [
                    { required: true, message: "标签名称不能为空", trigger: "blur" }
                ],
                description: [
                    { required: true, message: "标签描述不能为空", trigger: "blur" }
                ]
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {

        getList() {
            this.loading = true;
            tagManageList(this.queryParams).then(response => {
                const { list, total } = response.data
                this.postList = list;
                this.total = total;
            }).finally(() => this.loading = false)
        },

        cancel() {
            this.open = false;
            this.reset();
        },

        reset() {
            this.form = {
                labelColour:'#409EFF',
                labelNameColour:'#FFFFFF'
            };
            this.resetForm("form");
        },

        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },

        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },

        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加岗位";
        },

        handleUpdate(row) {
                this.reset();
                this.form ={
                    id:row.id,
                    labelName:row.labelName,
                    description:row.description,
                    labelColour:row.labelColour,
                    labelNameColour:row.labelNameColour,
                };
                this.open = true;
                this.title = "修改岗位";
        },

        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != undefined) {
                        editTage(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addTag(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },

        handleDelete(row) {
            const id = [row.id]
            this.$modal.confirm('是否确认删除？').then(function () {
                return delTag(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },

    }
};
</script>



<style lang="scss" scoped>
.tagManageList {}
</style>