//颜色配置
@import '@/styles/color.scss';

//卡片 上面按钮下面搜索

.btn-search{
  .el-button--info{
    background-color:$colorRefresh;
    border-color: $colorRefresh;
  }
  .el-button--info.is-disabled, .el-button--info.is-disabled:active, .el-button--info.is-disabled:focus, .el-button--infos.is-disabled:hover {
    color: #FFF;
    background-color:$colorRefresh;
    border-color: $colorRefresh;
  }
  .el-button--primary {
    background-color:$colorAdd;
    border-color: $colorAdd;
  }
  .el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
    color: #FFF;
    background-color: $colorAdd;
    border-color: $colorAdd;
  }


  .el-button--success{
    background-color:$colorEdit;
    border-color:$colorEdit;
  }
  .el-button--success.is-disabled, .el-button--success.is-disabled:active, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:hover {
    color: #FFF;
    background-color:$colorEdit;
    border-color:$colorEdit;
  }
  .el-button--danger{
    background-color:$colorDe;
    border-color:$colorDe;
  }
  .el-button--danger.is-disabled, .el-button--danger.is-disabled:active, .el-button--danger.is-disabled:focus, .el-button--danger.is-disabled:hover {
    color: #FFF;
    background-color:$colorDe;
    border-color:$colorDe;
  }

  .el-card__body{
    padding-bottom: 0px;
    // display: none;
  }
  .clearfix{
    display: flex;
    justify-content: space-between;

    .icon-box{
      display: flex;
      align-items: center;
      border: 1px solid #EBEEF5;
      border-radius: 10px;
      margin-right: 20px;
      padding-right: 10px;
      i{
        margin-left: 10px;
        cursor: pointer;
      }

    }
  }


}
.page-search{
  .el-card__body{
    display: none;
  }
}
.execution{
  .avue-crud__search::after{
    height: 10px;
    content: '';
    margin: 0 -30px;
    display: block;
    background: #eff2f5;
  }
  .el-tag--small{
    height: 32px;
  }
  .icon-title{
    margin-top: 10px;
  }
  .avue-crud__right .is-circle{
    display: none;
  }
}
::v-deep .avue-crud{
  .avue-crud__menu{
   /* display: none;*/
  }
  .avue-crud__tip{
    display: none;
  }
  .icon-title{
    margin-bottom: 10px;
  }
}

