<template>
  <div class="execution">
    <el-card style="margin-left: 1%" class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button>

          <el-button
              type="check"
              icon="el-icon-download"
              @click="centerDialogVisible = true"
          >导入logo
          </el-button>
        </div>
      </div>
      <div>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="设备编号" prop="name">
            <el-input
                v-model="queryParams.deviceNum"
                placeholder="请输入设备编号"
                clearable
                style="width: 240px;"
            />
          </el-form-item>
          <el-form-item label="设备名称" prop="name">
            <el-input
                v-model="queryParams.deviceName"
                placeholder="请输入设备名称"
                clearable
                style="width: 240px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="onSubmit()">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <div class="table-left">
      <div style="background: #fff;">
        <FormTitle title="设备列表"/>
      </div>
      <div class="avue-crud">
        <el-table
            :data="tableData"
            border
            height="500px"
            class="tableListValue"
            @selection-change="handleSelectionChange"
            style="width: 100%">
          <el-table-column
              type="selection"
              align="center"
              >
          </el-table-column>
          <el-table-column
              align="center"
              header-align="center"
              prop="deviceNum"
              label="设备编号"
          >
          </el-table-column>
          <el-table-column
              align="center"
              header-align="center"
              prop="deviceName"
              label="设备名称">
          </el-table-column>
          <el-table-column
              align="center"
              header-align="center"
              prop="serialNum"
              label="序列号"
          >
          </el-table-column>
          <el-table-column
              align="center"
              header-align="center"
              prop="location"
              label="位置"
          >
          </el-table-column>
          <el-table-column
              align="center"
              header-align="center"
              prop="specification"
              label="规格型号"
          >
          </el-table-column>
          <el-table-column
              prop="zip"
              align="center"
              header-align="center"
              label="最后打印时间"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="table-right">
      <div style="background: #fff;">
        <FormTitle title="设备图片"/>
      </div>
      <div>
        <el-button size="medium"  @click="templateDialogVisible = true" type="success">选择模板</el-button>
        <el-button size="medium" type="primary" icon="el-icon-view">预览</el-button>
        <el-button size="medium" icon="el-icon-printer">打印</el-button>
      </div>
    </div>

    <!--  导入logo  -->
    <el-dialog
        title="导入企业LOGO"
        :visible.sync="centerDialogVisible"
        width="25%"
        center>
      <div class="info-from">
        <ImageUpload
            ref="fileCoverImg"
            :fileListTem="coverImgTem"
            :limit="1"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!--  选择模板  -->
    <el-dialog
        title="选择模板"
        center
        :visible.sync="templateDialogVisible"
        width="80%"
        >
      <div class="d-image">
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-03.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-01.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-02.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-02.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-02.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-02.png" class="image">
          </span>
        </div>
        <div class="c-image">
          <span>
            <img src="@/static/ems/template-02.png" class="image">
          </span>
        </div>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="templateDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import {fetchList} from "@/api/ems/equipment/account";
import jQuery from "jquery";
import {tableOption} from '@/const/crud/ems/equipment/brand'
import ImageUpload from "@/components/ems/ImageUpload/index.vue";
import IconTitle from "@/components/ems/icon-title/index.vue";
import FormTitle from "@/components/edge/form-title";

export default {
  name: 'lable',
  components: {
    IconTitle,
    ImageUpload,
    FormTitle
  },
  data() {
    return {
      imageUrl: '',
      imageArray: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      centerDialogVisible: false,
      templateDialogVisible: false,
      tableData: [],
      coverImgTem: [],
      queryParams: {
        deviceNum: '',
        deviceName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: []
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    onSubmit() {
      this.getList();
    },

    resetQuery() {
      this.queryParams.deviceName = '';
      this.queryParams.deviceNum = '';
    },
    // 列表查询
    getList() {
      fetchList(
         this.queryParams).then((response) => {
        this.tableData = response.data.data.records;
      })
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    // exportExcel() {
    //     this.downBlobFile(
    //             "/ems/emsdevicebrand/export",
    //             this.searchForm,
    //             "emsdevicebrand.xlsx"
    //     );
    // },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    }

  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";

.table-left {
  padding: 10px 10px 10px 10px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 10px;
  margin-left: 1%;
  width: 59%;
  height: 550px;
  float: left;
}
.table-right {
  padding: 10px 10px 10px 10px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 10px;
  margin-left: 1%;
  width: 39%;
  height: 550px;
  float: right;
}

.tableListValue{
  //滚动条的宽高
  ::-webkit-scrollbar {
    width: 1vh;
    height: 2.1vh;
  }
  //滚动条的滑块背景色
  ::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 1vh;
  }
  //滚动条的背景色
  ::-webkit-scrollbar-track {
    background: rgba(#ccc,0.3);
  }

}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.d-image{
  background-color: #f6f6f6;
  width: 100%;
  padding: 30px 5px 30px 5px;
  display: flex;
  flex-wrap: wrap;
  .c-image {
    margin: 0 0 15px 10px ;
    width: 24%;
    background-color: #ffffff;
    height: 170px;
    text-align: center;
    vertical-align: middle;
    border-radius: 10px;
    &:hover {
      opacity: .8;
      cursor: pointer;
      box-shadow: darkgrey 0px 0px 20px 5px;
      transition: 200ms;
    }
    .image {
      max-width: 100%;
      max-height: 100%;
      vertical-align: middle;
    }
  }
}

img:hover {
  background-color:yellow;
}
</style>
