<template>
  <div class="financialCommn">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="板块分类" prop="plateType">
            <el-select
              v-model="form.plateType"
              placeholder="请选择板块类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.policy_plate_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="政策等级" prop="policyLevel">
            <el-select
              v-model="form.policyLevel"
              placeholder="请选择政策等级"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.policy_level"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="行业" prop="industrialSubstitutionIdJson">
            <el-cascader
              v-model="form.industrialSubstitutionIdJson"
              :options="industrys"
              :props="{
                children: 'children',
                label: 'vueName',
                value: 'id',
                    multiple: true,
              }"
              placeholder="请选择行业"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>
<!--        <el-col :span="10">
          <el-form-item label="行业" prop="industryCode">
            <el-cascader
              v-model="form.industryCode"
              :options="industryTypes"
              :props="{
                children: 'childrenList',
                label: 'name',
                value: 'industryCode',
              }"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>-->
        <el-col :span="10">
          <el-form-item label="申报类型" prop="policyApplyType">
            <el-select
              v-model="form.policyApplyType"
              placeholder="请选择申报类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.policy_apply_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="发文部门" prop="lssuingDepartment">
            <el-input
              v-model="form.lssuingDepartment"
              placeholder="请输入发文部门"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="申报对象" prop="declarationObject">
            <el-input
              v-model="form.declarationObject"
              placeholder="请输入申报对象"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="标题" prop="policyTitle">
            <el-input
              v-model="form.policyTitle"
              placeholder="请输入标题"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="概述" prop="applyOverview">
            <el-input
              v-model="form.applyOverview"
              placeholder="请输入概述"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="申报时间" prop="dateRange">
            <el-date-picker
              v-model="form.dateRange"
              style="width: 100%"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="最高奖励" prop="maximumReward">
            <el-input
              class="inputNum"
              :min="1"
              type="number"
              v-model="form.maximumReward"
              placeholder="请输入最高奖励"
              style="width: 100%"
            >
              <template slot="append">万元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="最低奖励" prop="minimumReward">
            <el-input
              class="inputNum"
              :min="1"
              type="number"
              v-model="form.minimumReward"
              placeholder="请输入最低奖励"
              style="width: 100%"
            >
              <template slot="append">万元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="申报内容" prop="richText">
            <editor
              :readOnly="detailFlag"
              v-model="form.richText"
              :min-height="192"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item class="uploadItem" label="附件" prop="policyUrl">
            <el-upload
              ref="upload"
              :limit="9"
              accept="*"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :before-remove="beforeRemove"
              :file-list="form.policyUrl"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getCityData } from "@/api/release/financial";
import { getSysIndustryType } from "@/api/release/policy";
import { uploadApi } from "@/api/release/index.js";
import { getToken } from "@/utils/auth";
import { getInfo } from "@/api/release/index.js";
import {getSysIndustry} from "@/api/release/indApp";
export default {
  name: "declarationEnterprise",
  dicts: ["policy_plate_type", "policy_level", "policy_apply_type"],
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  data() {
    return {
      form: {
        plateType: undefined, //板块分类
        industryCode: undefined, //行业分类
        industrialSubstitutionIdJson: undefined, //行业分类
        policyApplyType: undefined, //申报类型
        policyLevel: undefined, //政策等级
        policyTitle: undefined, //政策标题
        lssuingDepartment: undefined, //发文部门
        declarationObject: undefined, //申报对象
        applyOverview: undefined, //申报概述
        applyStartTime: undefined,
        applyEndTime: undefined,
        dateRange: [],
        maximumReward: 0, //最高奖励
        minimumReward: 0, //最低奖励
        richText: undefined,
        imgUrl: undefined,
        policyUrl: [], //附件
        hotStatus: 0, //热门状态
        groundingStatus: 0, //上架状态
      },
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/file/upload",
      },
      rules: {
        plateType: [
          { required: true, message: "请选择板块分类", trigger: "blur" },
        ],
        industryCode: [
          { required: true, message: "请选择行业分类", trigger: "blur" },
        ],
        policyApplyType: [
          { required: true, message: "请选择申报类型", trigger: "blur" },
        ],
        policyLevel: [
          { required: true, message: "请选择政策等级", trigger: "blur" },
        ],
        policyTitle: [
          { required: true, message: "请输入政策标题", trigger: "blur" },
        ],
        lssuingDepartment: [
          { required: true, message: "请输入发文部门", trigger: "blur" },
        ],
        declarationObject: [
          { required: true, message: "请输入申报对象", trigger: "blur" },
        ],
        applyOverview: [
          { required: true, message: "请输入申报概述", trigger: "blur" },
        ],
        dateRange: [
          { required: true, message: "请选择申报时间", trigger: "blur" },
        ],
        maximumReward: [
          { required: true, message: "请输入最高奖励", trigger: "blur" },
        ],
        minimumReward: [
          { required: true, message: "请输入最低奖励", trigger: "blur" },
        ],
        policyUrl: [{ required: true, message: "请上传附件", trigger: "blur" }],
        richText: [
          { required: true, message: "请输入政策内容", trigger: "blur" },
        ],
        industrialSubstitutionIdJson: [{ required: true, message: "请选择行业", trigger: "change" }],
      },
      industrys: [],
      industryTypes: [],
      cityData: [],
      submitDing: false,
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },

  created() {
    this.getCityDataFtn();
    this.getSysIndustryFtn(); //行业
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },

  destroyed() {},

  methods: {
    getSysIndustryFtn() {
      getSysIndustry().then(res => {
        this.industrys = res.data;
      });
    },
    //详情接口回显
    getFormDataFtn(flowInstanceId) {
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        params.dateRange = [params.applyStartTime, params.applyEndTime];
        this.form = {
          ...params,
          industrialSubstitutionIdJson: JSON.parse(params.industrialSubstitutionIdJson),
          // industryCode: JSON.parse(params.industryCode),
          policyUrl: JSON.parse(params.policyUrl),
        };
      });
    },
    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },
    getSysIndustryTypeFtn() {
      getSysIndustryType().then((res) => {
        this.industryTypes = res.data;
      });
    },
    // 文件上传进度处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      const data = {
        ...response.data,
        uid:this.generateUniqueId()
      }
      this.form.policyUrl.push(data);
    },
    // 生成唯一ID的函数
    generateUniqueId() {
      // 这里可以使用例如UUID库或其他方式生成唯一ID
      return Math.random().toString(36).substring(7);
    },
    // 文件移除事件
    beforeRemove(file, fileList) {
      // 手动在fileList中删除文件
      const index = this.form.policyUrl.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        this.form.policyUrl.splice(index, 1);
      }
      return true;
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.industrialSubstitutionIdJson) {
            const uniqueElements = [...new Set(this.form.industrialSubstitutionIdJson.map(subArray => subArray[0]))];
            this.form.industrialSubstitutionIds = uniqueElements.join(',');
          }
          this.form.applyStartTime = this.form.dateRange[0];
          this.form.applyEndTime = this.form.dateRange[1];
          const params = {
            ...this.form,
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.financialCommn {
  width: 80%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }
}
</style>
