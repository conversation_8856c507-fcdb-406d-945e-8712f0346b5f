<template>
    <div>
        <el-form :disabled="detailFlag" :model="activityForm" :rules="activityFormRules" ref="activityForm" style="width: 75%;" label-width="130px">
            <el-form-item label="标题" prop="title">
                <el-input v-model="activityForm.title" placeholder="请输入标题" style="width: 100%;" />
            </el-form-item>

            <el-form-item label="上传图片" prop="imagePath">
                <NewImageUpload
                :disabled="detailFlag"
                v-model="activityForm.imagePath"
                :limit="1"/>
            </el-form-item>

            <el-form-item label="活动时间" prop="activityDate">
                <el-date-picker
                    v-model="activityForm.activityDate"
                    style="width: 100%"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>

            <el-form-item label="活动报名时间" prop="activityRegisterDate">
                <el-date-picker
                    v-model="activityForm.activityRegisterDate"
                    style="width: 100%"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>

            <el-form-item label="活动规模" prop="scale">
                <el-input-number v-model="activityForm.scale" placeholder="请输入活动规模人数" controls-position="right" step-strictly :min="0" :step="1"></el-input-number>
            </el-form-item>

            <el-form-item label="主办单位" prop="mainDivision">
                <el-input v-model="activityForm.mainDivision" placeholder="请输入主办单位" style="width: 100%;" />
            </el-form-item>

            <el-form-item label="指导单位" prop="guideDivision">
                <el-input v-model="activityForm.guideDivision" placeholder="请输入指导单位" style="width: 100%;" />
            </el-form-item>

            <el-form-item label="承办单位" prop="undertakeDivision">
                <el-input v-model="activityForm.undertakeDivision" placeholder="请输入承办单位" style="width: 100%;" />
            </el-form-item>

            <el-form-item label="协办单位" prop="assistDivision">
                <el-input v-model="activityForm.assistDivision" placeholder="请输入协办单位" style="width: 100%;" />
            </el-form-item>
            <el-form-item label="简介" prop="briefIntroduction">
                <el-input  type="textarea" v-model="activityForm.briefIntroduction" placeholder="请输入简介" :autosize="{ minRows: 3,}" style="width: 100%;" />
            </el-form-item>
        </el-form>

        <div class="tagsTilte" style="margin-bottom: 15px;">活动议程</div>

        <el-form
            :disabled="detailFlag"
            ref="timeForm"
            :model="timeForm"
            label-width="130px"
            label-position="top"
            size="mini"
            >
             <div v-for="(item, index) in timeForm.timeData" :key="index" >
                <div class="card-box">
                    <el-row style="display: flex;justify-content: space-between;">
                        <el-col>
                            <el-form-item
                            label="活动日期"
                            :prop="`timeData[${index}].day`"
                            :rules="{
                            required: true,
                            message: '活动日期不能为空',
                            trigger: 'change',
                            }"
                        >
                            <el-date-picker
                                v-model="item.day"
                                type="date"
                                value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd"
                                placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        </el-col>
                        <el-col style="width: 100px;">
                            <div style="margin-right: 20px;"
                                @click.stop="(event) => { stopBubbling(event) }">
                                <el-popconfirm title="确定是否删除此日期？"
                                    @onConfirm="deleteTable(index)"
                                    @confirm="deleteTable(index)">
                                    <el-button class="el-icon-delete-solid tableDelete"
                                        slot="reference" type="text">删除</el-button>
                                </el-popconfirm>
                            </div>
                        </el-col>
                    </el-row>
                    <div v-for="(item1, index1) in item.planList" :key="index1" style="padding-left: 20px;">
                        <el-row :gutter="16">
                        <el-col :span="6">
                            <el-form-item
                            label="时间节点"
                            :prop="`timeData[${index}].planList[${index1}].time`"
                            :rules="{
                                required: true,
                                message: '时间节点不能为空',
                                trigger: 'blur',
                            }"
                            >
                                <el-time-picker
                                    is-range
                                    v-model="item1.time"
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    placeholder="选择时间范围"
                                    value-format="HH:mm"
                                    format="HH:mm"
                                    style="width: 100%">
                                </el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                label="计划"
                                :prop="`timeData[${index}].planList[${index1}].plan`"
                                :rules="{
                                required: true,
                                message: '计划不能为空',
                                trigger: 'blur',}"
                                >
                                    <el-input
                                        v-model="item1.plan"
                                        style="width: 100%"
                                    ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <div style="height: 38px"></div>
                            <el-button
                                type="danger"
                                size="mini"
                                icon="el-icon-delete"
                                circle
                                style="color: #fff;"
                                @click="deleteRule(index, index1)"
                            ></el-button>
                        </el-col>
                        </el-row>
                        <el-button
                            size="mini"
                            type="primary"
                            @click="addRule(index, index1)"
                            v-if="(index1 === item.planList.length-1) ||(item.planList.length===0)"
                            class="el-icon-plus"
                            >增加节点
                        </el-button>
                    </div>
                    <el-button
                            v-if="item.planList.length===0"
                            type="primary"
                            size="mini"
                            @click="addRule(index, 0)"
                            style="margin-left: 16px;"
                            class="el-icon-plus"
                            >增加节点
                        </el-button>
                </div>
                <el-row v-if="index == timeForm.timeData.length - 1" style="text-align: center;">
                    <el-button class="addChapter" type="text" @click="createTable(index)">
                        <i class="el-icon-circle-plus-outline"></i>
                        新增日期
                    </el-button>
                </el-row>
               <!-- <el-divider content-position="left"></el-divider> -->
             </div>
             <el-row v-if="timeForm.timeData.length === 0" style="text-align: center;">
                <el-button class="addChapter" type="text" @click="createTable(0)">
                    <i class="el-icon-circle-plus-outline"></i>
                    新增日期
                </el-button>
            </el-row>
        </el-form>
    </div>
</template>
<script>
    import NewImageUpload from '@/components/NewImageUpload'
    export default{
        components:{
            NewImageUpload
        },
        data() {
            return {
                activityForm:{
                    title:null,//标题
                    imagePath:null,//图片
                    activityDate:[],//活动时间
                    activityRegisterDate:[],//活动报名时间
                    scale :null,//活动规模
                    mainDivision:null,//主办单位
                    guideDivision:null,//指导单位
                    undertakeDivision:null,//承办单位
                    assistDivision:null,//协办单位
                    briefIntroduction:null,//简介
                },
                activityFormRules:{
                    title:[{ required: true, message: "请输入标题", trigger: "blur" }],
                    imagePath:[{ required: true, message: "请上传图片", trigger: "blur" }],
                    activityDate: [
                        { required: true, message: "请选择活动时间", trigger: "blur" },
                    ],
                    activityRegisterDate: [
                        { required: true, message: "请选择活动报名时间", trigger: "blur" },
                    ],
                    scale :[
                        { required: true, message: "请输入活动规模人数", trigger: "blur" },
                    ],
                    mainDivision:[{ required: true, message: "请输入主办单位", trigger: "blur" }],
                    guideDivision:[{ required: true, message: "请输入指导单位", trigger: "blur" }],
                    undertakeDivision:[{ required: true, message: "请输入承办单位", trigger: "blur" }],
                    assistDivision:[{ required: true, message: "请输入协办单位", trigger: "blur" }],
                    briefIntroduction:[{ required: true, message: "请输入简介", trigger: "blur" }],
                },
                // 活动议程
                timeForm: {
                    timeData: [
                        {
                            day: "",//日期
                            planList: [
                                {
                                    time: null,//时间节点
                                    plan: null,//计划安排
                                }
                            ]
                        }
                    ]
                },
                detailFlag: (this.$route.query.pageType == 'detail' || this.$route.query.pageType == 'check') || false
            }
        },
        methods: {
            stopBubbling(event) {
                event.stopPropagation();
            },
            init(data){
                const {
                    title,
                    imagePath,
                    activityStartTime,
                    activityEndTime,
                    activityRegisterStartTime,
                    activityRegisterEndTime,
                    scale,
                    mainDivision,
                    guideDivision,
                    undertakeDivision,
                    assistDivision,
                    briefIntroduction,
                    activityFlow
                } = data
                this.activityForm.title = title
                this.activityForm.imagePath = imagePath
                this.activityForm.activityDate.push(activityStartTime)
                this.activityForm.activityDate.push(activityEndTime)
                this.activityForm.activityRegisterDate.push(activityRegisterStartTime)
                this.activityForm.activityRegisterDate.push(activityRegisterEndTime)
                this.activityForm.scale = scale
                this.activityForm.mainDivision = mainDivision
                this.activityForm.guideDivision = guideDivision
                this.activityForm.undertakeDivision = undertakeDivision
                this.activityForm.assistDivision = assistDivision
                this.activityForm.briefIntroduction = briefIntroduction

                this.timeForm.timeData = JSON.parse(activityFlow)
            },
            //删除计划
            deleteRule(index, index1) {
                this.timeForm.timeData[index].planList.splice(index1, 1)
            },
            //添加计划
            addRule(index, index1) {
                console.log(index, index1);
                let timeDataItem = {
                    time: null,
                    plan: null,
                }
                this.timeForm.timeData[index]["planList"].push(timeDataItem)
            },

            //新增日期
            createTable(index) {
                let newTable = {
                    day: "",
                    planList: [
                        {
                            time: null,
                            plan: null,
                        },
                    ],
                };
                this.timeForm.timeData.push(newTable)
            },
            //删除日期
            deleteTable(index) {
                this.timeForm.timeData.splice(index, 1)
            },
            //重置表单
            resetForm(formName) {
                this.$refs[formName].resetFields()
            },
            //提交表单验证
            async submitForm() {
                try {
                    const activityForm = this.$refs['activityForm']
                    const timeForm = this.$refs['timeForm']
                    const res1 = await activityForm.validate()
                    const res2 = await timeForm.validate()

                    if(res1 && res2){
                        const params = {
                            title:this.activityForm.title,//标题
                            imagePath:this.activityForm.imagePath,//图片
                            activityStartTime:this.activityForm.activityDate[0],//活动开始时间
                            activityEndTime:this.activityForm.activityDate[1],//活动结束时间
                            activityRegisterStartTime:this.activityForm.activityRegisterDate[0],//活动报名开始时间
                            activityRegisterEndTime:this.activityForm.activityRegisterDate[1],//活动结束报名时间
                            scale :this.activityForm.scale ,//活动规模
                            mainDivision:this.activityForm.mainDivision,//主办单位
                            guideDivision:this.activityForm.guideDivision,//指导单位
                            undertakeDivision:this.activityForm.undertakeDivision,//承办单位
                            assistDivision:this.activityForm.assistDivision,//协办单位
                            briefIntroduction:this.activityForm.briefIntroduction,//简介
                            activityFlow:JSON.parse(JSON.stringify(this.timeForm.timeData))//活动议程
                        }
                        this.$emit('submit',params,(res)=>{
                            console.log(123);
                        })
                    }
                } catch (error) {
                    console.log(error);
                }

            },
        },
    }
</script>

<style lang="scss" scoped>
.card-box{
    padding: 16px;
    box-shadow: 0 0 3px 0 rgba(0,0,0,.2);
}
</style>
