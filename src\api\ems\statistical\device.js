import request from '@/utils/request'

export function deviceStatusList() {
  return request({
    url: '/ems/deviceLedgerReport/deviceLevelList',
    method: 'get',
  })
}

// 查询设备状态
export function statusList() {
  return request({
    url: '/ems/deviceLedgerReport/statusList',
    method: 'get',
  })
}

// 查询设备使用状态
export function useStatusList() {
  return request({
    url: '/ems/deviceLedgerReport/useStatusList',
    method: 'get',
  })
}

// 查询采购金额
export function amountList() {
  return request({
    url: '/ems/deviceLedgerReport/amountList',
    method: 'get',
  })
}

export function addObj(obj) {
  return request({
    url: '/ems/emsrunrecord',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/ems/emsrunrecord/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/ems/emsrunrecord/' + id,
    method: 'delete'
  })
}
//分类统计-价值波动
export function getFluctuationsValue(id) {
  return request({
    url: '/ems/deviceLedgerReport/fluctuationsValue/' + id,
  })
}

export function getEquipmentAge() {
  return request({
    url: '/ems/deviceLedgerReport/equipmentAgeList',
    method: 'get'
  })

}

export function getNetAssetValue() {
  return request({
    url: '/ems/deviceLedgerReport/netAssetValue',
    method: 'get'
  })
}

export function getEquipmentAccount() {
  return request({
    url: '/ems/deviceLedgerReport/equipmentAccount',
    method: 'get'
  })
}

export function putObj(obj) {
  return request({
    url: '/ems/emsrunrecord',
    method: 'put',
    data: obj
  })
}
