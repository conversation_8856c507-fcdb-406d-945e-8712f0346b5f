const frontLabel = process.env.VUE_APP_BASE_API;
export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "editBtn": false,
    "delBtn": true,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
    "column": [
         {
            "type": "input",
            "label": "设备编号",
            "prop": "deviceNum",
            "span": 18,
            search: true
        }, {
            "type": "input",
            "label": "设备名称",
            "prop": "deviceName",
            "span": 12
        },{
            "type": "input",
            "label": "折旧年月",
            "prop": "depreciationDate",
            "width": 110,
            "span": 18
        },{
            "type": "input",
            "label": "折旧方法",
            "prop": "depreciationMethod",
            dicUrl:`${frontLabel}/system/dict/data/type/depreciation_method`,
            "span": 12
        },{
            "type": "input",
            "label": "采购金额",
            "prop": "purchaseAmount",
            "span": 12
        },{
            "type": "input",
            "label": "使用寿命(月)",
            "prop": "serviceLife",
            "width": 110,
            "span": 16
        },{
            "type": "input",
            "label": "净残率(%)",
            "prop": "residualRate",
            "span": 12
        },{
            "type": "input",
            "label": "折旧月数",
            "prop": "monthCount",
            "span": 12
        }, {
            "type": "input",
            "label": "原值",
            "prop": "originalValue",
            "span": 12
        }, {
          "type": "input",
          "label": "当前折旧",
          "prop": "monthDepreciation",
          "span": 12
        }, {
          "type": "input",
          "label": "累计折旧",
          "prop": "accumulatedDepreciation",
          "span": 12
        },{
            "type": "input",
            "label": "净值",
            "prop": "netWorth",
            "span": 12
        }, {
            "type": "input",
            "label": "备注",
            "prop": "remark",
            "span": 12
        },
    ]
}
