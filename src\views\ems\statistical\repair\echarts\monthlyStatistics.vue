<template>
  <div id="monthlyStatistics" :style="{width: '1400px', height: '400px'}"></div>
</template>

<script>
import {
  getMonthlyStatisticsList
} from "@/api/ems/statistical/maintain";

let monthOption = {

  grid: {
    left: '5%',
    right: '10%',
    top: '20%',
    bottom: '15%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: [
    {
      type: 'category',
      axisLabel: {
        color: '#9a9a9a',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#397cbc',
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: '#f4f4f4',
        },
      },
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    },
  ],
  yAxis: [
    {
      type: 'value'
    },
  ],
  series: [
    {
      name: '故障次数',
      type: 'line',
      symbolSize: 8,
      itemStyle: {
        normal: {
          color: '#63b2ee',
          label : {show: true},
          lineStyle: {
            color: '#63b2ee',
            width: 1,
          },
          areaStyle: {
            color: '#ccd7f6'
          }
        },
      },
      markPoint: {
        itemStyle: {
          normal: {
            color: 'red',
          },
        },
      },
      data: [900, 632, 701, 734, 590, 630, 510, 582, 591, 534, 590, 530],
    }
  ]
};

export default {
  data() {
    return {
      monthOption,
      monthlyStatistics: []
  }
    ;
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let monthlyStatistics = this.$echarts.init(document.getElementById('monthlyStatistics'))

      getMonthlyStatisticsList().then(res => {
        this.monthlyStatistics = res.data.data;
        if (this.monthlyStatistics.length > 0) {
          let yData = [];
          this.monthlyStatistics.forEach(item => {
            yData.push(item.num)
          });
          monthOption.series[0].data = yData;
          monthlyStatistics.setOption(this.monthOption);
        } else {
          monthlyStatistics.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }
        // // 绘制图表
        // monthlyStatistics.setOption(this.monthOption);
      });


    }
  }
}

</script>
