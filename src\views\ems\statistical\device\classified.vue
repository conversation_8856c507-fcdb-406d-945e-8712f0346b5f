<template>
  <div class="classified">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box" style="height: 100px">
        <div class="basic">
          <div class="data">统计时间 【{{ classifiedStatisticDate.newDate }}】</div>
          <div>
            <img :src="require('@/assets/imagesAssets/fltjtp.png')" alt="" class="fltjtp">
          </div>
          <div class="device">
            <div class="deviceNum">
              <span class="dName">设备总数</span>
              <span class="dNum">{{ classifiedStatisticDate.deviceAllNum }}</span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div class="deviceNum">
              <span class="dName">设备类别</span>
              <span class="dNum">{{ classifiedStatisticDate.deviceCategoryNum }}</span>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div class="deviceNum">
              <span class="dName">设备位置</span>
              <span class="dNum">{{ classifiedStatisticDate.deviceLocationNum }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-box" style="height: 411px">
      <IconTitle title="类别统计" imgUrl="yunwei">
        <span class="slot">设备类别</span>
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 350px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>设备数量</span>
                <p>num</p>
              </div>
              <div>
                <device-num style="margin-left: 30px"
                            :classifyDeviceNumDate="classifyDeviceNumDate"
                />
              </div>
            </div>
          </el-col
          >
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 350px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>购置金额</span>
                <p>price</p>
              </div>
              <div>
                <money style="margin-left: 30px" :classifyPurchaseAmountDate = "classifyPurchaseAmountDate"/>
              </div>
            </div>
          </el-col
          >
        </el-row>
      </div>
    </div>

    <div class="table-box" style="height: 411px">
      <IconTitle title="位置统计" imgUrl="yunwei">
        <span class="slot">设备位置</span>
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 350px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>设备数量</span>
                <p>num</p>
              </div>
              <div>
                <location-num style="margin-left: 30px" :locationDeviceNumDate="locationDeviceNumDate"/>
              </div>
            </div>
          </el-col
          >
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 350px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>购置金额</span>
                <p>price</p>
              </div>
              <div>
                <location-money style="margin-left: 30px" :locationPurchaseAmountDate="locationPurchaseAmountDate"/>
              </div>
            </div>
          </el-col
          >
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import deviceNum from "./echarts/deviceNum";
import money from "./echarts/money";
import locationNum from "./echarts/locationNum";
import locationMoney from "./echarts/locationMoney";
import {getClassifiedStatisticList} from "@/api/ems/equipment/account";
export default {
  name: "classified",
  components: {
    IconTitle,
    deviceNum,
    money,
    locationNum,
    locationMoney
  },
  data() {
    return {
      classifiedStatisticDate: [],
      classifyDeviceNumDate: [],
      classifyPurchaseAmountDate: [],
      locationPurchaseAmountDate: [],
      locationDeviceNumDate: [],
    };
  },
  created() {
    this.getClassifiedStatisticList();
  },
  methods:{

    getClassifiedStatisticList() {
      getClassifiedStatisticList().then(res => {
        this.classifiedStatisticDate = res.data.data;
        this.classifyDeviceNumDate = res.data.data.classifyDeviceNumChartsList;
        this.classifyPurchaseAmountDate = res.data.data.classifyPurchaseAmountList;
        this.locationDeviceNumDate = res.data.data.locationDeviceNumList;
        this.locationPurchaseAmountDate = res.data.data.locationPurchaseAmountList;
      })
    },


  }

}
</script>

<style scoped lang="less">
.classified {
  font-size: 12px;

  .table-box {
    .echarts-box {
      .basic {
        display: flex;

        .data {
          color: #837d77;
          font-weight: 600;
          margin-top: 40px;
        }

        .fltjtp {
          margin: -55px 100px 0 300px;
        }

        .device {
          display: flex;

          .deviceNum {
            display: flex;
            flex-direction: column;
            margin: 20px 50px 0 50px;

            .dName {
              color: #88888a;
              margin-bottom: 5px;
            }

            .dNum {
              color: #000c17;
              font-weight: 600;
              font-size: 30px;
              text-align: center;
            }
          }

          .el-divider--vertical {
            display: inline-block;
            width: 1px;
            height: 4em;
            margin: 20px 8px;
            vertical-align: middle;
            position: relative;
          }
        }
      }
    }
  }
}
</style>
