<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="16">
<el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>设备信息</span></div>
      <div class="devTitle"><span>冲压机巡检计划</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">设备名称</div>
          <div class="contentS">冲压机巡检计划</div>
          <div class="labelS">设备编号</div>
          <div class="contentS">220</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">规格型号</div>
          <div class="contentS">LK32*8732-010012</div>
          <div class="labelS">使用状态</div>
          <div class="contentS"><span class="nocomplate complate">在用</span></div>
        </div>
        <div class="tableStyle">
          <div class="labelS">所属部门</div>
          <div class="contentS">2021-12-02 21:23:45</div>
          <div class="labelS">位置</div>
          <div class="contentS">2021-12-02 21:23:45</div>
        </div>
      </div>
    </el-card>
    <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>故障信息</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">报修编号</div>
          <div class="contentS">BX29834092812311</div>
          <div class="labelS">报障时间</div>
          <div class="contentS">2021-12-02 21:23:45</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">故障类型</div>
          <div class="contentS">电器故障</div>
            <div class="labelS">报障人</div>
          <div class="contentS">姜龙</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">设备状态</div>
          <div class="contentS"><span class="nocomplate">故障</span></div>
          <div class="labelS">工单状态</div>
          <div class="contentS"><span class="nocomplate complate">已验收
</span></div>
        </div>
         <div class="tableStyle">
          <div class="labelS">故障等级</div>
          <div class="contentS">一般</div>
        </div> 
        <div class="tableStyle">
          <div class="labelS">故障描述</div>
          <div class="contentS">漏气</div>
        </div> <div class="tableStyle">
          <div class="labelS">故障图片</div>
          <div class="contentS"></div>
        </div>
      </div>
    </el-card>
   <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>处理信息</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">故障类型</div>
          <div class="contentS">电器故障</div>
          <div class="labelS">维修方式</div>
          <div class="contentS">自修</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">故障原因</div>
          <div class="contentS">电器故障</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">处理时间</div>
          <div class="contentS">2021-12-02 21:23:45</div>
          <div class="labelS">当前步骤</div>
          <div class="contentS"><span class="nocomplate complate">已验收
</span></div>
        </div>
         <div class="tableStyle">
          <div class="labelS">停机时间</div>
          <div class="contentS">1时21分</div>
        </div> 
        <div class="tableStyle">
          <div class="labelS">处理内容</div>
          <div class="contentS">漏气</div>
        </div> <div class="tableStyle">
          <div class="labelS">预防措施</div>
          <div class="contentS"></div>
        </div>
        <div class="tableStyle">
          <div class="labelS">处理人</div>
          <div class="contentS">小虎</div>
        </div> <div class="tableStyle">
          <div class="labelS">协调处理人</div>
          <div class="contentS"></div>
        </div>
      </div>
    </el-card>
    <el-card shadow="always" class="box-card">
      <div class="headerStyle"><span class="tableTitle">步骤信息</span>
      <span class="smallTitle">设备管理使用中的状态</span>
      </div>
      <el-table :data="tableData2" border style="width: 100%">
           <el-table-column
         label="序号"
      type="index"
      width="50">
    </el-table-column>
        <el-table-column prop="number" label="设备编号" >
        </el-table-column>
        <el-table-column prop="project" label="设备名称"> </el-table-column>
        <el-table-column prop="time" label="处理时间"> </el-table-column>
        <el-table-column prop="person" label="处理人"  width="60"> </el-table-column>
        <el-table-column prop="recordpic" label="故障原因"  > </el-table-column>
        <el-table-column prop="intr" label="协调处理人"  width="100"> </el-table-column>
        <el-table-column prop="kinds" label="处理内容"> </el-table-column>
        <el-table-column prop="number" label="预防措施"> </el-table-column>
        <el-table-column prop="isexc" label="动作"  width="50"> </el-table-column>
      </el-table>
    </el-card>
      </el-col>
      <el-col :span="8">
        <div class="icon-style">
          <div class="con-left" id="scrollBox">
               <el-card shadow="always" class="box-card">
                   <div  class="tableTitle">设备信息</div>
                   <div class="vertical">
            <el-card shadow="always" class="box-card " v-for="item in 4" :key="item">
              <div>
                <div class="headerStyle">
                    <span class="dottedS"></span>
                  <i class="el-icon-date" style="color: #0ccb82"></i>
                  <span style="color: #0ccb82">任务节点：报销</span>
                  <i class="el-icon-arrow-up status"></i>
                </div>
                <div class="contentStyle">
                  <div class="boxContent">
                    <span class="desc">故障描述：漏气</span>
                    <span class="status">
                      <i class="el-icon-success"></i>
                      正常
                    </span>
                  </div>
                  <div>
                    <span class="label">处理人：</span>
                      <span class="content">江龙</span>
                      </div>
                  <div>
                      <span class="label">执行时间：</span>
                         <span class="content"> 2021/11/21 12:00:00</span>
                          </div>
                  <div class="line"></div>
                  <div>
                    <span></span>
                    <span class="label timeTotal">用时：</span>
                    <span>6时32分</span>
                  </div>
                </div>
              </div>
              
            </el-card>
            </div>
               </el-card>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          number: "GW923700-12312",
          planName: "喷涂机",
          devName: "海天",
          devNo: "喷涂",
          kind: "KJ*2990-1293",
        },
      ],
      tableData2: [
        {
        
          number: "1",
          project: "滑件",
          ways: "",
          type: "文本",
          low: "",
          high: "",
          choose: "",
          normal: "",
          pic: "",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
.headerStyle{
   margin-bottom: 20px;

}
.tableTitle {
  color: #333;
  font-size: 16px;
  font-weight: bold;
 margin-bottom: 20px;
 
}
.smallTitle{
  color: rgba(153, 153, 153, 100);
font-size: 12px;
margin-left: 10px;
}
.devTitle {
  color: #262626;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
  .el-card__body {
    padding-top: 10px;
  }
  .labelS {
    //display: inline-block;
    flex:0 0 150px;
    //height: 40px;
    // margin-right: 10px;
    text-align: left;
    color: #606266;
    padding: 10px;
    border: 1px solid rgba(236, 240, 244, 100);
    margin-bottom: -1px;
  }
  .contentS {
    border: 1px solid rgba(236, 240, 244, 100);
    // height: 40px;
    color: #606266;
    width: 100%;
    margin-left: -1px;
    margin-bottom: -1px;
    padding: 10px;
    // margin: 10px 0;
    // width: calc(100% - 120px);
    // display: inline-block;
  }
  .tableStyle {
    display: flex;
  }
  .number{
      font-weight: bold;
      margin-top: 5px;
      font-size: 16px;
  }
  .nocomplate{
      color: white;
      background-color: #E83672;
      padding: 3px 5px;
      border-radius: 3px;
      font-size: 12px;
  }
  .complate{
     background-color: #78BF34;
  }
}
.con-left {
  
  font-size: 12px;
  position: relative;
  .headerStyle {
      .dottedS{
          width: 10px;
          height: 10px;
          display: inline-block;
          border-radius: 50%;
          background-color: #666;
          position: absolute;
          left: -6px;
      }
    color: #0ccb82;
    i{
        margin-right: 25px;
    }
  }
 ::v-deep.vertical{
      border-left: 2px dashed #EDEEF2;
      position: relative;
      padding-left: 10px;
      .el-card__body{
        padding: 10px 0 10px 10px;
      }
      .box-card {
    box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, 0.12);
   
  }
  }
  .contentStyle{
      padding: 10px;
      .line{
          border: 1px solid rgba(236, 240, 244, 100);
          margin: 15px 0;
      }
  }
   .status{
          float: right;
          margin-right: 10px;
          i{
               color: #0ccb82;
                margin-right: 5px;
          }
      }
  .boxContent{
      background-color: #F6F6FC;
      font-size: 14px;
      height: 50px;
      line-height: 50px;
     margin: 10px 0 15px;
     position: relative;
      &::before{
          content: "";
          position: absolute;
          height: 50px;
          width: 5px;
          background-color: #DEDEE4;
          
      }
      .desc{
          margin-left: 10px;
      }
    
  }
   .label{
         color: #999;
         margin-right: 10px;
         display: inline-block;
         margin-bottom: 10px;
         width: 70px;
     }
     .content{
         color: #101010;
     }
     .timeTotal{
         margin-right: 20px;
     }
}

</style>
