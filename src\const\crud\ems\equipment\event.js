import { getHeaders } from "@/const/crud/getHeaders"
import { getDicts } from "@/api/system/dict/data"

const headers = getHeaders();

const frontLabel = process.env.VUE_APP_BASE_API;

const DIC = {
  vaild: [{
    label: '否',
    value: '0'
  }, {
    label: '是',
    value: '1'
  }]
}



export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": true,
  "gridBtn": false,
  display: true,
  "column": [
    {
      "type": "select",
      "label": "设备名称",
      "prop": "deviceId",
      "span": 12,
      dicUrl: `${frontLabel}/platform/emsdeviceaccount/list`,
      dicHeaders: headers,
      "editDisabled": false,
      props: {
        label: "deviceName",
        value: "id"
      },
      rules: [{
        required: true,
        message: '请选择设备',
        trigger: 'blur'
      }]
    },
    {
      label: '事件类型',
      prop: 'eventType',
      type: 'select',
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      dicUrl: `${frontLabel}/system/dict/data/type/event_type`,
      dicHeaders: headers,
      search: true,
      "editDisabled": false,
    },
    {
      "type": "input",
      "label": "开销",
      "prop": "expenses",
      "span": 12,
      "editDisabled": false
    },

    {
      label: '价值变动',
      prop: 'changeStatus',
      width: 80,
      type: 'select',
      dicData: DIC.vaild,
      rules: [{
        required: true,
        message: '请选择价值变动',
        trigger: 'blur'
      }]
    },

    {
      "type": "input",
      "label": "变动值",
      "prop": "changeValue",
      "span": 12
    }, {
      "type": "input",
      "label": "资产净值",
      "prop": "originalValue",
      "span": 12,
      addDisplay: false,
      editDisplay: false
    }, {
      row: true,
      minRows: 2,
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12,
      hide: true
    },]
}
