<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-descriptions title="申报内容" direction="vertical" :column="2" border>
      <el-descriptions-item label="申报名称">{{ consultationData.declareName }}</el-descriptions-item>
      <el-descriptions-item label="申报人">{{ consultationData.serviceProviderName }}</el-descriptions-item>
      <el-descriptions-item label="申报内容" :span="2">{{ consultationData.declareDesc }}</el-descriptions-item>
      <el-descriptions-item v-if="consultationData.declareFile" label="申报附件" :span="2">
        <span style="color: #409EFF;cursor: pointer;"
          @click="downloadLocalTemplate(consultationData.declareFile, '申报附件')">下载附件信息</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getInfo } from "@/api/release/index.js";

export default {
  name: 'IndustrialBrainNewMgmtPromptlyDeclare',
  data() {
    return {
      submitDing: false,
      consultationData: {},
      labelTypeList: []
    };
  },
  created() {
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },
  methods: {
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.consultationData = params
        this.submitDing = false;
      });
    },
    downloadLocalTemplate(path, name) {
      let a = document.createElement('a');
      a.href = this.ensureFullUrl(path);  // 如果后端返回文件地址，path值就是后端返回的地址
      a.download = name; // 设置下载文件文件名,要完整的文件名+后缀，比如：test.xlsx
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    }
  },
};
</script>
<style lang="scss">
.financialCommn {
  width: 80%;

  ::v-deep.el-descriptions {
    .el-descriptions-item__label.is-bordered-label {
      color: #0D162A;
      background-color: #F6F8FC;
    }
  }
}
</style>