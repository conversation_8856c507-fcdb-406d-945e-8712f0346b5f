<template>
  <div class="execution">
    <basic-container>
      <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          @on-load="getList"
          :before-open="beforeOpen"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @size-change="sizeChange"
          @current-change="currentChange"
          @row-update="handleUpdate"
          @row-save="handleSave"
          @row-del="rowDel">
        <template slot-scope="scope" slot="menu">
          <el-button type="text" v-if="scope.row.id!=1&&permissions.admin_synchronous_menu" @click="goSynchronousMenu(scope.row)">

            <el-tooltip class="item" effect="dark"
                        :content="'同步菜单:会把默认的租户的菜单同步到租户名为:【'+scope.row.name+'】的租户上,在同步之前保存一份租户名为：【'+scope.row.name+'】的租户的菜单'" placement="top">
              <span><i class="el-icon-smoking"></i>同步菜单</span>
            </el-tooltip>
          </el-button>
<!--          <el-button type="text" v-if="scope.row.id!=1" @click="goLook(scope.row,scope.column)">-->
<!--            <i class="el-icon-view"></i>同步字典-->
<!--          </el-button>-->
          <el-button type="text" v-if="scope.row.id!=1&&permissions.admin_restore_menu&&scope.row.isMenuStored" @click="goRestoreMenu(scope.row)">
            <i class="el-icon-refresh"></i>还原菜单
          </el-button>
          <el-button type="text" v-if="scope.row.id!=1&&permissions.admin_del_restore_menu&&scope.row.isMenuStored" @click="goDelRestoreMenu(scope.row)">
            <i class="el-icon-delete"></i>删除储存的菜单
          </el-button>
          <el-button type="text" v-if="scope.row.id!=1&&permissions.admin_view_restore_menu&&scope.row.isMenuStored" @click="goViewRestoreMenu(scope.row)">
            <i class="el-icon-view"></i>查看储存的菜单
          </el-button>
        </template>
      </avue-crud>
      <el-dialog title="储存的菜单" :visible.sync="viewMeuu" width="80%" height="60%">
        <el-table
            border
            v-loading="loading"
            :data="menuList"
            row-key="id"
            :tree-props="{children: 'children', hasChildren: 'hasChildrens'}">
          <el-table-column prop="name" label="菜单名称" :show-overflow-tooltip="true" width="180"></el-table-column>
          <el-table-column prop="icon" label="图标" align="center" width="100">
            <template slot-scope="scope">
              <i :class="scope.row.icon"/>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" width="60"></el-table-column>
          <el-table-column prop="path" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column prop="menuType" label="类型" width="80" align="center">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.menuType === '0'">左菜单</el-tag>
              <el-tag type="success" v-if="scope.row.menuType === '2'">顶菜单</el-tag>
              <el-tag type="info" v-if="scope.row.menuType === '1'">按钮</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="keepAlive" label="缓冲" width="80" align="center">
            <template slot-scope="scope">
              <el-tag type="info" v-if="scope.row.keepAlive === '0'">关闭</el-tag>
              <el-tag type="success" v-if="scope.row.keepAlive === '1'">开启</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="permission" label="权限标识" :show-overflow-tooltip="true">

          </el-table-column>
        </el-table>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import {
  addObj,
  delObj,
  fetchPage,
  putObj,
  menuGoRestoreMenu,
  menuGoSynchronousMenu,
  menuViewRestoreMenu,
  menuDelRestoreMenu
} from '@/api/admin/tenant'
  import {tableOption} from '@/const/crud/admin/tenant'
  import {mapGetters} from 'vuex'

  export default {
    name: 'Tenant',
    data() {
      return {
        tableData: [],
        searchForm: {},
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20 // 每页显示多少条
        },
        tableLoading: false,
        tableOption: tableOption,
        viewMeuu: false,
        // 遮罩层
        loading: true,
        // 菜单表格树数据
        menuList: [],

      }
    },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permissions.admin_systenant_add, false),
          delBtn: this.vaildData(this.permissions.admin_systenant_del, false),
          editBtn: this.vaildData(this.permissions.admin_systenant_edit, false)
        }
      }
    },
    methods: {
      //同步
      goSynchronousMenu(row) {
        var _this = this
        this.$confirm('是否确认同步默认租户的菜单', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return menuGoSynchronousMenu({id:row.id})
        }).then(data => {
          if (data.code === 200) {
            window.sessionStorage.setItem('synchronous_menu', new Date().getTime());
            _this.$message.success('同步成功');
          }
          this.getList(this.page)
        })
      },
      //还原
      goRestoreMenu(row) {
       let time = window.sessionStorage.getItem('synchronous_menu');
        if (time != undefined && ((Number(time) + 1800000) > new Date().getTime())) {
          this.$message.error("为了防止菜单数据覆盖,30分钟内不可重复同步")
          return;
        }
        var _this = this;
        this.$confirm('是否还原该租户最近一次同步的菜单', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return menuGoRestoreMenu({id:row.id})
        }).then(data => {
          _this.$message.success('还原成功')
          this.getList(this.page)
        })
      },

      goDelRestoreMenu(row) {
        var _this = this
        this.$confirm('是否删除该租户最近一次同步的菜单', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return menuDelRestoreMenu({id:row.id})
        }).then(data => {
          _this.$message.success('删除成功')
          this.getList(this.page)
        })
      },
      goViewRestoreMenu(row) {
        this.viewMeuu = true;
        menuViewRestoreMenu({id:row.id}).then(response => {
          this.menuList = response.data
          this.loading = false;
        });
      },







      getList(page, params) {
        this.tableLoading = true
        fetchPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize
        }, params, this.searchForm)).then(response => {
          this.tableData = response.data.records
          this.page.total = response.data.total
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },
      rowDel: function (row, index) {
        var _this = this
        this.$confirm('是否确认删除ID为' + row.id, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(row.id)
        }).then(data => {
          _this.$message.success('删除成功')
          this.getList(this.page)
        })
      },
      handleUpdate: function (row, index, done, loading) {
        putObj(row).then(data => {
          this.$message.success('修改成功')
          done()
          this.getList(this.page)
        }).catch(() => {
          loading()
        })
      },
      handleSave: function (row, done, loading) {
        addObj(row).then(data => {
          this.$message.success('添加成功')
          done()
          this.getList(this.page)
        }).catch(() => {
          loading()
        })
      },
      searchChange(form, done) {
        this.searchForm = form
        this.page.currentPage = 1
        this.getList(this.page, form)
        done()
      },
      beforeOpen(show,type) {
        window.boxType = type;
        show()
      },
      refreshChange() {
        this.getList(this.page)
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize
      },
      currentChange(current) {
        this.page.currentPage = current
      },
    }
  }
</script>
