<template>
  <div class="baseInfo fixed-container">
    <h3 class="container-title">企业申报</h3>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="申报名称" prop="declareName">
          <el-input style="width: 200px" v-model="queryParams.declareName" placeholder="请输入申报名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="审核状态" prop="examineStatus">
          <el-select v-model="queryParams.examineStatus" placeholder="请选择咨询类型" clearable style="width: 200px">
            <el-option v-for="t in labelTypeList" :key="t.dictValue" :label="t.dictLabel" :value="t.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" height="calc(100vh - 380px)" :data="documentList">
        <el-table-column label="申报名称" prop="declareName" />
        <el-table-column label="服务提供商名称" prop="serviceProviderName" />
        <el-table-column label="申报内容" prop="declareDesc" />
        <el-table-column label="审核状态" prop="examineStatus">
          <template slot-scope="scope">
            {{
              (productTypeItem => productTypeItem ? productTypeItem.dictLabel : '无')(
                labelTypeList.find(item => scope.row.examineStatus == item.dictValue)
              )
            }}
          </template>
        </el-table-column>
        <el-table-column label="拒绝原因" prop="reason">
          <template slot-scope="scope">
            {{
              scope.row.examineStatus !== 'PASS' ? scope.row.reason : null
            }}
          </template>
        </el-table-column>
        <el-table-column label="申报时间" prop="createTime" />
        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.examineStatus === 'NO_EXAMINE'" size="mini" v-hasPermi="['qy:declare:edit']"
              type="text" @click="handle(scope.row)">立即处理</el-button>
            <el-button size="mini" v-hasPermi="['qy:declare:schedule']" type="text"
              @click="handleLook(scope.row, '进度详情')">进度详情</el-button>
            <el-button size="mini" v-hasPermi="['qy:declare:details']" type="text"
              @click="handleLook(scope.row, '查看详情')">查看详情</el-button>

          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>
    <!-- 添加或修改流程对话框 -->
    <el-dialog :title="title" :visible.sync="open">
      <div v-loading="loadingOpen">
        <div v-if="title === '进度详情'" class="flex" style="height: 400px;">
          <el-steps style="width: 40%;" direction="vertical" finish-status="success" :active="active">
            <el-step :title="t.pointName" v-for="t in stepsData" :key="t.pointName"
              :description="t.pointTime"></el-step>
          </el-steps>
          <div style="width: 60%;">
            <div v-for="t in stepsData" :key="t.pointName">
              <div v-if="t.handleTimeValue" class="steps-title">{{ t.pointName }}</div>
              <div class="steps-content">
                <div v-if="t.enterpriseNameValue">{{ t.enterpriseNameKey }}:{{ t.enterpriseNameValue }}</div>
                <div v-if="t.handleValue">{{ t.handleKey }}:{{ t.handleValue }}</div>
                <div v-if="t.handleTimeValue">{{ t.handleTimeKey }}:{{ t.handleTimeValue }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div v-for="t in stepsData" :key="t.pointName">
            <div v-if="t.handleTimeValue" class="steps-title">{{ t.pointName }}</div>
            <div class="steps-content">
              <div v-if="t.enterpriseNameValue">{{ t.enterpriseNameKey }}:{{ t.enterpriseNameValue }}</div>
              <div v-if="t.handleValue">{{ t.handleKey }}:{{ t.handleValue }}</div>
              <div v-if="t.handleTimeValue">{{ t.handleTimeKey }}:{{ t.handleTimeValue }}</div>
            </div>
          </div>
          <div>
            <div class="steps-title">申报内容：</div>
            <div class="steps-content">
              {{ rowData.declareDesc }}
            </div>
          </div>
          <div v-if="rowData.declareFile">
            <div class="steps-title">附件资料：</div>
            <div class="steps-content">
              <el-button @click="downloadFile(rowData.declareFile)" icon="el-icon-download"
                type="primary">附件下载</el-button>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPromptlyDeclare, getFlow } from "@/api/base/declare";

export default {
  name: 'informationConsultationDeclare',
  // dicts: [""],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文档类型管理表格数据
      documentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        declareName: undefined,
        examineStatus: undefined
      },
      productTypeList: [],
      labelTypeList: [{
        dictLabel: '通过',
        dictValue: 'PASS',
      }, {
        dictLabel: '拒绝',
        dictValue: 'REFUSE',
      }, {
        dictLabel: '待审核',
        dictValue: 'NO_EXAMINE',
      },],
      open: false,
      stepsData: {},
      active: 0,
      loadingOpen: false,
      title: '详情',
      rowData: {}
    };
  },
  created() {
    if (this.$route.params && this.$route.params.id) {
      this.queryParams.id = this.$route.params.id;
    }
    this.getList();
  },
  methods: {
    /** 查询文档类型管理列表 */
    getList() {
      this.loading = true;
      getPromptlyDeclare(this.queryParams).then((response) => {
        this.documentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handle(row) {
      const { flowInstanceId } = row;
      this.$router.push({
        path: "/checkCenter/release",
        query: { type: 'PROMPTLY_DECLARE', flowInstanceId, pageType: "check" },
      });
    },
    handleLook(row, title) {
      const { id } = row;
      this.open = true;
      this.title = title;
      this.loadingOpen = true;
      getFlow(id).then((response) => {
        this.stepsData = response.data || [];
        const index = this.stepsData.findIndex((value) => value.handleTimeValue == null);
        this.active = index === -1 ? this.stepsData.length : index;
        this.rowData = { ...row };
      }).finally(() => {
        this.loadingOpen = false;
      });
    },
    downloadFile(file) {
      const link = document.createElement("a");
      link.href = '企业申报附件资料';
      link.setAttribute("download", this.ensureFullUrl(file));
      // 模拟点击链接进行下载
      link.click();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding-top: 20px;
}

.flex {
  display: flex;
}

.steps-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
  color: #181f2d;
  line-height: 27px;

  &:before {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    content: "";
    width: 4px;
    height: 12px;
    background-color: #0055e2;
  }
}

.steps-content {
  font-size: 14px;
  color: #181f2d;
  margin-bottom: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
  line-height: 30px;
}
</style>
