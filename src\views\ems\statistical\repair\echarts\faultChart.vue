<template>
  <div id="faultChart" :style="{width: '300px', height: '130px'}"></div>
</template>

<script>
export default {
  data() {
    return {};
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let faultChart = this.$echarts.init(document.getElementById('faultChart'))

      let dataList = [
        { value: 23, name: '油路故障' },
        { value: 2, name: '管道故障' },
        { value: 3, name: '电器故障' },
      ];
      const colorList = ['#4D88FE', '#50CCCB', '#FFBF3C'];


      // 绘制图表
      faultChart.setOption({
        series: [
          {
            type: 'pie',
            radius: ['30%', '60%'],
            center: ['50%', '50%'],
            label: {
              fontWeight:'bold',
              rich: {
                rich_blue: {
                  color: '#4D88FE',
                },
                rich_orange: {
                  color: '#FFBF3C',
                },
                rich_green: {
                  color: '#50CCCB',
                },
              },
              formatter: function (params) {
                if (params.name === '油路故障') {
                  return `{rich_blue|${params.name}: }` + `{rich_blue|${params.value}} `
                }else if(params.name === '管道故障'){
                  return `{rich_green|${params.name}: }` + `{rich_green|${params.value}} `
                }else if(params.name === '电器故障'){
                  return `{rich_orange|${params.name}: }` + `{rich_orange|${params.value}} `
                }
              },
            },
            itemStyle: {
              normal: {
                borderColor: '#fff',
                borderWidth: 2,
                color: function (params) {
                  return colorList[params.dataIndex];
                },
              },
            },
            data: dataList,
          },
        ]
      });
    }
  }
}

</script>
