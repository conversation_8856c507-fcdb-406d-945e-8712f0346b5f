const DIC = {
    checkStatus: [{
        label: '进行中',
        value: '0'
    }, {
        label: '已通过',
        value: '1'
    }, {
        label: '未通过',
        value: '2'
    }]
}


export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': false,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "column": [
        {
            "type": "input",
            "label": "主键ID",
            "prop": "id",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "报修编号",
            "prop": "repairNum",
            "span": 12,
        "search":true,
        },	  {
            "type": "input",
            "label": "设备编号",
            "prop": "deviceNum",
            "span": 12
        },    {
            "type": "input",
            "label": "设备名称",
            "prop": "deviceName",
            "span": 12,
        "search":true,
        },	  {
            "type": "input",
            "label": "外委单位",
            "prop": "outName",
            "span": 12,
        },	  {
            "type": "input",
            "label": "申请时间",
            "prop": "applyTime",
            "span": 12,
        },	  {
            "type": "input",
            "label": "申请人",
            "prop": "applyBy",
            "span": 12,
        },	 /* {
            "type": "input",
            "label": "当前节点",
            "prop": "currentNode",
            "span": 12,
        },*/	{
            "type": "select",
            "label": "审批状态",
            "prop": "checkStatus",
            "slot":true,
            "span": 12,
            "search":true,
            dicData: DIC.checkStatus,
            rules: [{
                required: true,
                message: '请选择类型',
                trigger: 'blur'
            }]
        },	  {
            "type": "input",
            "label": "审批结束时间",
            "prop": "checkEndTime",
            "span": 12
        }]
}
