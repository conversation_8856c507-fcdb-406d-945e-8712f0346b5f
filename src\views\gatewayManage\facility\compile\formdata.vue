<template>
    <div class="formDataDemo">
        <el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="100px" label-position="top"
            :validate-on-rule-change="false">
            <div class="dataComent">
                <div class="addTableTilte">设备基本信息</div>
                <el-card class="dataCard">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="设备类型" prop="name">
                                <span>{{ tyepName }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="安装日期" prop="installDate">
                                <el-date-picker v-model="dataForm.installDate" type="date" placeholder="请选择日期"
                                    value-format="yyyy-MM-dd" style="width: 80%" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备名称" prop="deviceNum">
                                <el-select v-model="dataForm.deviceNum"  filterable remote reserve-keyword
                                    placeholder="请输入关键词" :remote-method="remoteMethod" style="width: 80%"
                                    :loading="selectLoading">
                                    <el-option v-for="(item,i) in accountList" :key="item.deviceNum" :label="item.deviceName"
                                        :value="item.deviceNum">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备编号" >
                                <el-input disabled v-model="dataForm.deviceNum" placeholder="设备编号" style="width: 80%"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备描述信息" prop="description">
                                <el-input v-model="dataForm.description" placeholder="请输入" style="width: 80%" :rows="3"
                                    type="textarea">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
            </div>

            <div class="dataComent">
                <div class="addTableTilte">网络信息</div>
                <el-card class="dataCard">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="IP地址" prop="ipInfo">
                                <el-input v-model="dataForm.ipInfo" placeholder="请输入" style="width: 80%"
                                    clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="端口号信息" prop="portInfo">
                                <el-input v-model="dataForm.portInfo" placeholder="请输入" style="width: 80%"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="连接超时时间" prop="connectTimeout">
                                <el-input-number v-model="dataForm.connectTimeout" :min="1" placeholder="请输入"
                                    style="width: 80%" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="通讯超时时间" prop="comTimeout">
                                <el-input-number v-model="dataForm.comTimeout" :min="1" placeholder="请输入"
                                    style="width: 80%" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="串口" prop="serialPort">
                                <el-input v-model="dataForm.serialPort" placeholder="请输入" style="width: 80%"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
            </div>

            <div class="dataComent" v-if="pclJson && pclJson.length > 0">
                <div class="addTableTilte">PLC信息</div>
                <el-card class="dataCard">
                    <el-row>
                        <el-col :span="12" v-for="item in pclJson" :key="item.id">
                            <el-form-item :label="item.paramName" :prop="item.paramNameEng">
                                <el-input v-if="item.paramType === 'input'" v-model="dataForm[item.paramNameEng]"
                                    placeholder="请输入" style="width: 80%" clearable></el-input>
                                <el-select v-else-if="item.paramType === 'select'" v-model="dataForm[item.paramNameEng]"
                                    placeholder="请选择" style="width: 80%" clearable>
                                    <template v-if="item.valueArr">
                                        <el-option v-for="ops in item.valueArr.split(',')" :key="ops" :label="ops"
                                            :value="ops" />
                                    </template>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-card>
            </div>
        </el-form>

        <div v-show="footerWidth && footerWidth != '0px'" class="pageFooter" :style="{ width: `${footerWidth}` }">
            <div style="margin-right: 20px;">
                <el-button type="primary" @click="textCommun">测试通信</el-button>
                <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
        </div>

    </div>
</template>
    
<script>
import { getFormJson, checkNum, getFormData, setTextComm, getAccount } from "@/api/gatewayManage/facility/index.js";
import { SessionStorage } from '@/utils/storage'

export default {
    props: {
        footerWidth: {
            type: String,
            default: '0px'
        }
    },
    data() {
        const deviceNumBlur = ((rule, value, callback) => {
            const storAgesRef = SessionStorage.getItem('facilityPams')
            const deviceId = storAgesRef && storAgesRef.id ? storAgesRef.id : undefined
            if (value) {
                checkNum(value, deviceId).then(res => {
                    // if (res.msg === 'success') {
                    //     callback();
                    // }
                    // else {
                    //     callback('此设备已存在,设备编号重复,请重新选择'); 
                    // }
                    callback();
                }).catch(()=>{
                    callback('此设备已存在,设备编号重复,请重新选择');
                })
            }
            else {
                callback('必填项不能为空');
            }
        })
        return {
            dataForm: {},
            dataRules: {
                installDate: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
                // deviceName: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
                // deviceNum: [{ required: true,  trigger: 'blur',validator: deviceNumBlur2 }],
                deviceNum: [{ required: true, trigger: 'change', validator: deviceNumBlur }],
                description: [{ required: false, message: '必填项不能为空', trigger: 'blur' }],
                ipInfo: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
                portInfo: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
                connectTimeout: [{ required: true, message: '必填项不能为空', trigger: ['change', 'blur'] }],
                comTimeout: [{ required: true, message: '必填项不能为空', trigger: ['change', 'blur'] }],
                serialPort: [{ required: false, message: '必填项不能为空', trigger: 'blur' }],
            },
            tyepName: '',
            pclJson: [],
            accountList: [],
            selectLoading: false
        };
    },
    created() {
        const storages = SessionStorage.getItem('facilityPams');
        if (!storages.id) { //新增
            this.getFormJsonFtn(storages.agreementId);
        }
        else { //编辑
            this.getFormDataFun(storages);
        }
        this.getAccountFtn()
    },
    methods: {

        getAccountFtn(data={}) {
            getAccount(data).then(res => { this.accountList = res.data });
        },

        remoteMethod(val) {
            this.getAccountFtn({deviceName:val})
        },

        nextStep() {
            this.$refs["dataFormRef"].validate(valid => {
                if (valid) {
                    SessionStorage.setItem('facilityPams', {
                        ...SessionStorage.getItem('facilityPams'),
                        ...this.newParmes(this.dataForm),
                        deviceName:this.accountList.filter(v=>v.deviceNum==this.dataForm.deviceNum)[0].deviceName,
                    });
                    this.$emit("loadFuc", true);
                    this.$emit("next");
                }
            });
        },

        textCommun() {
            const storages = SessionStorage.getItem('facilityPams');
            this.$refs["dataFormRef"].validate(valid => {
                if (valid) {
                    const parmes = {
                        ...this.newParmes(this.dataForm),
                        deviceName:this.accountList.filter(v=>v.deviceNum==this.dataForm.deviceNum)[0].deviceName,
                        gatewayId: storages.gatewayId,
                        agreementId: storages.agreementId
                    }
                    setTextComm(parmes).then(res => {
                        this.$message.success("测试通信成功！");
                    })
                }
            });
        },

        newParmes(data) {
            const { pclJson } = this;
            let parmes = JSON.parse(JSON.stringify(data))
            if (pclJson.length > 0) {
                let paraJson = {};
                pclJson.map((t) => {
                    paraJson[t.paramNameEng] = parmes[t.paramNameEng];
                    delete parmes[t.paramNameEng]
                });
                parmes = { ...parmes, paramJson: JSON.stringify(paraJson) }
            }
            return parmes
        },

        getFormDataFun(vals) {
            getFormData(vals.id).then(res => {
                this.getFormJsonFtn(res.data.agreementId, res.data);
            })
        },

        getFormJsonFtn(agrId, formData) {
            getFormJson(agrId).then(res => {
                // 加载动态表单
                if (res.data && res.data.length > 0) {
                    this.pclJson = res.data
                    const staticRules = this.dataRules;
                    let liveFromKey = {};
                    res.data.filter((t) => t.isRequired === 1).map((t) => {
                        staticRules[t.paramNameEng] = [{
                            required: true, message: '必填项不能为空',
                            trigger: t.paramType === 'input' ? 'blur' : 'change'
                        }];
                        liveFromKey[t.paramNameEng] = ''
                    })
                    this.dataRules = { ...staticRules }
                    this.dataForm = { ...this.dataForm, ...liveFromKey }
                }
                //数据回显
                // return
                const storages = SessionStorage.getItem('facilityPams');
                if (storages.installDate) { //有缓存用缓存(判断表单是否已经点过下一步)
                    this.setFormData(storages);
                }
                else {  //无缓存
                    if (formData) {   //编辑回显
                        this.setFormData(formData)
                    }
                    else {
                        this.tyepName = storages.deviceType;
                    }
                }
            })
        },

        setFormData(data, key) {
            this.tyepName = data.deviceType;
            let paramJson = {};
            if (data.paramJson) {
                paramJson = JSON.parse(data.paramJson)
            }
            this.dataForm = { ...data, ...paramJson }
        }

    },


};
</script>
    
    
    
<style lang="scss" scoped>
.formDataDemo {
    margin-top: 30px;
    /* padding: 0 20px; */
    padding-bottom: 50px;

    ::v-deep .el-form-item__label {
        padding-bottom: 0px !important;
    }

    .dataComent {
        margin-bottom: 30px;

        .addTableTilte {
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 18px;
            color: #0D162A;

            &:before {
                content: '';
                width: 4px;
                height: 12px;
                background: #0147EB;
                display: inline-block;
                margin-right: 10px;
            }
        }

        .dataCard {
            background: #F6F8FC;
            border-radius: 5px;
            border: none;
            margin-top: 16px;
        }
    }

    /* .pageFooter {
           position: fixed;
            bottom: 0px;
            background: #FFFFFF;
            box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
            opacity: 1;
            border-radius: 0px;
            height: 60px;
            display: flex;
            justify-content: end;
            align-items: center;
            right: 24px
    } */
}
</style>