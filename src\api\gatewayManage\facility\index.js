import { requestPlatForm } from "@/utils/requestBase";


//左list
export function getFlagTree() {
    return requestPlatForm({
        url: '/workshop/manage/tree/1',
        method: 'get',
    })
}
//右list
export function getDeviceList(query) {
    return requestPlatForm({
        url: '/produce/device/list',
        method: 'get',
        params: query
    })
}
//停
export function setStep(data) {
    return requestPlatForm({
        url: '/produce/device/stop/collect',
        method: 'post',
        data: data
    })
}
//启
export function setCollect(data) {
    return requestPlatForm({
        url: '/produce/device/start/collect',
        method: 'post',
        data: data
    })
}
//删
export function deleteProduce(data) {
    return requestPlatForm({
        url: '/produce/device',
        method: 'delete',
        data
    })
}







//compile
//add
export function submitAll(data) {
    return requestPlatForm({
        url: '/produce/device/save/device/config',
        method: 'post',
        data: data
    })
}
//edit
export function submitPutAll(data) {
    return requestPlatForm({
        url: '/produce/device',
        method: 'put',
        data: data
    })
}
export function issueFile(data) {
    return requestPlatForm({
        url: '/produce/device/push/config/file',
        method: 'post',
        data
    })
}
export function setRestart(dataForm) {
    return requestPlatForm({
        url: '/gateway/manage/restart',
        method: 'post',
        data: dataForm
    })
}

//add step1
export function getStep1List(query) {
    return requestPlatForm({
        url: '/drive/manage/type/page',
        method: 'get',
        params: query
    })
}
export function getAgreement(id) {
    return requestPlatForm({
        url: '/drive/manage/agreement/' + id,
        method: 'get',
    })
}


//step-form
export function getFormJson(id) {
    return requestPlatForm({
        url: '/produce/device/dynamic/params/' + id,
        method: 'get',
    })
}
export function checkNum(val, deviceId) {
    if (deviceId) {
        return requestPlatForm({ url: '/produce/device/check/num?num=' + val + '&deviceId=' + deviceId, method: 'get' })
    }
    else {
        return requestPlatForm({ url: '/produce/device/check/num?num=' + val, method: 'get' })
    }
}
export const getFormData = (id) => {
    return requestPlatForm({
        url: '/produce/device/' + id,
        method: 'get',
    })
}
export function setTextComm(data) {
    return requestPlatForm({
        url: '/produce/device/test/comm',
        method: 'post',
        data: data
    })
}
export function getAccount  (query)  {
    return requestPlatForm({
        url: '/produce/device/account',
        method: 'get',
        params: query
    })
}

//step-table
export function getCollectList(query) {
    return requestPlatForm({
        url: '/produce/device/data/collect/list',
        method: 'get',
        params: query
    })
}
export function getTypeDict (){
    return requestPlatForm({ url: '/produce/device/data/type/dict' , method: 'get' })
}
export function submitCollect(dataForm) {
    if (dataForm.id) {
        return requestPlatForm({ 
            url: '/produce/device/data/collect', 
            method: 'put' ,
            data:dataForm
        })
    }
    else {
        return requestPlatForm({ 
            url: '/produce/device/data/collect', 
            method: 'post' ,
            data:dataForm
        })
    }
}
export function delCollect(data) {
    console.log(data,'dsajkjlk');
    return requestPlatForm({
      url: '/produce/device/data/collect',
      method: 'delete',
      data
    })
  }

