<template>
    <div class="app-container reportRulePage">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"  @submit.native.prevent>
        <!-- <el-form-item label="设备" prop="postCode">
          <el-select v-model="queryParams.id" placeholder="请选择" clearable>
            <el-option v-for="dict in deviceDicts" :key="dict.id" :label="dict.deviceName" :value="dict.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="规则名称" prop="ruleName">
                <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable   @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="设备编号" prop="deviceNum">
                <el-input v-model="queryParams.deviceNum" placeholder="请输入设备编号" clearable   @keyup.enter.native="handleQuery"/>
            </el-form-item>
            <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable   @keyup.enter.native="handleQuery"/>
            </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-table v-loading="loading" :data="postList">
        <el-table-column label="设备名称" prop="deviceName" />
        <el-table-column label="设备编号" prop="deviceNum" />
        <el-table-column label="规则名称" prop="ruleName" />
        <el-table-column label="规则表达式" prop="ruleExpression" width="450"/>
        <el-table-column label="采集结果" prop="collectResult" />
        <el-table-column label="告警时间" prop="warnTime" />
        <!-- <el-table-column label="操作" class-name="small-padding fixed-width" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" >控制策略</el-button>
          </template>
        </el-table-column> -->
      </el-table>
  
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
        @pagination="getList" />
  
    </div>
  </template>
    
  <script>
  import { reportRuleList } from "@/api/deviceControl/reportRule";
  import { getDeviceDict } from "@/api/deviceControl/control";

  
  export default {
    name: "reportRulePage",
    dicts: ['sys_normal_disable'],
    data() {
      return {
        loading: true,
        showSearch: true,
        total: 0,
        postList: [],
        queryParams: {
          page: 1, //page
          limit: 10,  //limit
          ruleName: '',
          deviceNum:'',
          deviceName:''
        },
        deviceDicts: []
      };
    },
    created() {
      this.getList();
    //   getDeviceDict().then(res => this.deviceDicts = res.data);
    },
    methods: {
      getList() {
        this.loading = true;
        reportRuleList(this.queryParams).then(response => {
          const { list, total } = response.data;
          this.postList = list;
          this.total = total;
          this.loading = false;
        });
      },
      handleQuery() {
        this.queryParams.page = 1;
        this.getList();
      },
      resetQuery() {  
        this.queryParams= {
          page: 1,
          limit: 10,
          id: undefined
        }
        this.resetForm("queryForm");
        this.handleQuery();
      },
  
    }
  };
  </script>
  
  <style lang="scss" >
  .reportRulePage {}
  </style>
    
    