<template>
  <div class="app-container controlPage">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="设备" prop="postCode">
        <el-select v-model="queryParams.id" placeholder="请选择" clearable>
          <el-option v-for="dict in deviceDicts" :key="dict.id" :label="dict.deviceName" :value="dict.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="postList">
      <el-table-column prop="id" label="序号" width="100">
        <template slot-scope="scope">
          {{ (queryParams.page - 1) * queryParams.limit + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="设备编号" prop="deviceName" />
      <el-table-column label="设备名称" prop="deviceNum" />
      <el-table-column label="操作" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleTactic(scope.row)"
            v-hasPermi="['system:post:edit']">控制策略</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
      @pagination="getList" />

  </div>
</template>
  
<script>
import { controlList, getDeviceDict } from "@/api/deviceControl/control";

export default {
  name: "ControlPage",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      postList: [],
      queryParams: {
        page: 1, //page
        limit: 10,  //limit
        id: undefined
      },
      deviceDicts: []
    };
  },
  created() {
    this.getList();
    getDeviceDict().then(res => this.deviceDicts = res.data);

  },
  methods: {
    getList() {
      this.loading = true;
      controlList(this.queryParams).then(response => {
        const { list, total } = response.data;
        this.postList = list;
        this.total = total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    resetQuery() {  
      this.queryParams= {
        page: 1,
        limit: 10,
        id: undefined
      }
      this.resetForm("queryForm");
      this.handleQuery();
    },

    handleTactic(row) {
      this.$router.push({ path: `control/redact` ,query:{ id: row.id,name:row.deviceName}})
    },

  }
};
</script>

<style lang="scss" >
.controlPage {}
</style>
  
  