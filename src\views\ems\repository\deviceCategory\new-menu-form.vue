<template>
  <!-- 添加或修改结构类型对话框 -->
  <el-form ref="dataForm" :model="form" :rules="rules" size="small" label-width="80px">
    <el-row>
      <el-col :span="24">
        <el-form-item label="结构类型" prop="type">
          <el-radio-group v-model="form.type" size="small">
            <el-radio-button  label="0">类目</el-radio-button>
            <el-radio-button  label="3">类型应用</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="上级类目" prop="parentId" >
          <treeselect
            :disabled="form.type== 3"
            v-model="form.parentId"
            :options="menuOptions"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择上级类目"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="名称" prop="categories">
      <el-input v-model="form.categories" maxlength="20" show-word-limit :disabled="form.parentId == -1" placeholder="请输入类目名称"  />
    </el-form-item>
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            controls-position="right"
            :min="0"
            :disabled="form.parentId == -1"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="启用状态" prop="delFlag" >
          <el-radio-group :disabled="form.parentId == -1" v-model="form.delFlag">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="备注" prop="name">
      <el-input v-model="form.remark" maxlength="255" show-word-limit :disabled="form.parentId == -1" placeholder="请输入备注" />
    </el-form-item>
    <div class="footer">
      <el-button type="primary" v-if="form.id != null" @click="dataFormSubmit">修 改</el-button>
      <el-button type="primary" v-else @click="dataFormSubmit">新 增</el-button>
    </div>
  </el-form>
</template>

<script>
import { addObj, fetchMenuTree, getObj, putObj } from "@/api/ems/repository/deviceCategory";
import Treeselect from "@riophae/vue-treeselect";
import iconList from "@/const/iconList";
import TableForm from "./";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Menu",
  components: { Treeselect, TableForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      menuOptions: [],
      // 是否显示弹出层
      visible: false,
      // 图标
      iconList: iconList,
      form: {
        id: undefined,
        categories: undefined,
        type: "0",
        keepAlive: "0",
        sort: 0,
        delFlag: "0",
        status: "0",
        joinCenter: "0",
        parentId:undefined,
        oldType:null,
      },
      // 表单校验
      rules: {
        categories: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        sort: [
          { required: true, message: "顺序不能为空", trigger: "blur" },
        ],
        parentId: [
          { required: true, message: "上级类目不能为空", trigger: "blur" },
        ],
        // keepAlive: [
        //   { required: true, message: "路由缓冲不能为空", trigger: "blur" },
        // ],
        // permission: [
        //   { required: true, message: "权限标识不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  created() {
    this.getTreeselect();
  },
  methods: {
    init(isEdit, id) {
        if (id != null) {
          this.form.parentId = id;
        }else{
          this.form.parentId = undefined
        }
        this.getTreeselect();
        this.$nextTick(() => {
          this.$refs["dataForm"].resetFields();
          if (isEdit) {
            getObj(id).then((response) => {
              this.form = response.data.data;
              this.form.oldType=isEdit;
            });
          } else {
                this.form.id=null;
                this.form.parentId=id;
                this.form.categories=null;
                this.form.remark=null;
                this.form.delFlag=null;
                this.form.sort=null;
                this.form.type="0"
                this.form.oldType=isEdit;
                this.getTreeselect();
          }
        });

    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }
          if (this.form.oldType){
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.$emit("refreshDataTree");
            });
          }else {
            if (this.form.parentId == -1) {
              this.form.type = "3";
            }
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.$emit("refreshDataTree");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      fetchMenuTree().then((response) => {
        this.menuOptions = [];
        const menu = { id: -1, name: "资料类目", children: [] };
        menu.children = response.data.data;
        this.menuOptions.push(menu);
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";
::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 8px;
}
.footer{
    text-align: center;
}
</style>
