<template>
  <div id="myCharts" :style="{width: '100px', height: '100px'}"></div>
</template>

<script>
import {
  getMaintenance
} from "@/api/home/<USER>";

import echarts from 'echarts'
import 'echarts-liquidfill'

export default {
  data() {
    return {
      status: [],
      array: [],
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.getStatusList();
  },
  methods: {

    // 状态统计
    getStatusList() {
      this.drawLine();
    },

    drawLine() {
      getMaintenance().then(res => {
        this.status = res.data.data;

        let value;
        if (this.status.maintenanceTaskCount == 0) {
          value = 0;
        } else {
          value = this.status.completedCount / this.status.maintenanceTaskCount;
        }
        // 基于准备好的dom，初始化echarts实例
        let myCharts = this.$echarts.init(document.getElementById('myCharts'))

        // 绘制图表
        myCharts.setOption({
          backgroundColor: '#fff',
          title: [
            {
              text: '完成率',
              left: '48%',
              top: "58%",
              textAlign: 'center',
              textStyle: {
                fontSize: '12',
                fontWeight: '400',
                color: '#fff',
                textAlign: 'center',
              },
            },
            {
              text: (value * 100).toFixed(2) + '%',
              left: '48%',
              top: '25%',
              textAlign: 'center',
              textStyle: {
                fontSize: 20,
                color: '#fff',
              },
            },
          ],
          series: [{
            type: 'liquidFill',
            radius: '90%',
            z: 6,
            center: ['50%', '50%'],
            amplitude: 5,
            backgroundStyle: {
              borderWidth: 1,
            },
            color: [
              new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0.6,
                color: "#72D1FB",
              }, {
                offset: 0.8,
                color: "#43ABF7",
              }
              ])],
            data: [value + 0.02,
              {
                value: value - 0.01,
                direction: 'left',
              },
              value - 0.01,
            ],
            label: {
              normal: {
                formatter: '',
              }
            },
            outline: {
              show: true,
              itemStyle: {
                borderWidth: 0,
              },
              borderDistance: 0,
            }
          }
          ]
        });
      });

    }
  }
}

</script>
