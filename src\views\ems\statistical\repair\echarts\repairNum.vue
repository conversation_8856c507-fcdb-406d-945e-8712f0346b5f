<template>
  <div id="repairNum" :style="{width: '100%', height: '240px'}"></div>
</template>

<script>

let tyOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '1%',
    right: '10%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
  },
  yAxis: {
    type: 'category',
    data: []
  },
  series: [
    {
      type: 'bar',
      label: {
        show: true, //开启显示
        position: 'right', //在上方显示
        textStyle: { //数值样式
          color: '#76da91',
          fontSize: '12'
        }
      },
      barWidth: '15px',
      color: '#76da91',
      data: [],
    }
  ]
};

import {
  getFailureNumberList,
} from "@/api/ems/statistical/maintain"
export default {
  data() {
    return {
      tyOption,
      failureNumber: [],
    };
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let repairNum1 = this.$echarts.init(document.getElementById('repairNum'))
      getFailureNumberList().then(res => {
        this.failureNumber= res.data.data;
        if (this.failureNumber.length > 0) {
          let xData = [];
          let yData = [];
          this.failureNumber.forEach(item => {
            xData.push(item.name);
            yData.push(item.value)
          });
          tyOption.yAxis.data = xData;
          tyOption.series[0].data = yData;
          repairNum1.setOption(this.tyOption);
        } else {
          repairNum1.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }
      });
    }
  }
}

</script>
