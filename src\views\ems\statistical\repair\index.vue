<template>
  <div class="account-detail">
    <div class="tab-box">
      <div
          :class="['item', index === tabIndex ? 'active' : '']"
          v-for="(item, index) in tabArr"
          @click="changeTab(index)"
      >
        <i :class="item.imgUrl"></i>
        <span>{{ item.text }}</span>
      </div>
    </div>
    <div class="con-box">
      <repair v-if="tabIndex === 0"/>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import repair from "./repair";

export default {
  name: "account-detail",
  components: {
    IconTitle,
    repair,
  },

  data() {
    return {
      tabArr: [
        { imgUrl: "icon-gongyezujian-yibiaopanlan", text: "维修统计" },
      ],
      tabIndex: 0,
    };
  },
  methods: {
    changeTab(index) {
      this.tabIndex = index;
    },
  },
};
</script>
<style lang="scss" >
@import "@/styles/color.scss";
.account-detail {
  width: 100%;
  margin-bottom: 50px;
  .tab-box {
    width: 100%;
    background: #fff;
    padding: 10px 15px;
    height: 55px;
    box-sizing: border-box;
    display: flex;
    border-radius: 4px;
    position: fixed;
    top: 115px;
    z-index: 10;
    .item {
      padding: 3px 5px;
      cursor: pointer;
      margin-right: 30px;
      border-radius: 4px;
      color: #666;
      display: flex;
      align-items: center;
      font-size: 15px;
      i {
        margin-right: 5px;
      }
    }
    .item.active {
      background: $theme;
      color: #fff;
    }
  }
  .con-box {
    margin-top: 65px;
    width: 100%;
    //各个组件公共样式
    .table-box {
      margin-top: 10px;
      background: #fff;
      padding: 10px 15px;
      border-radius: 4px;
      box-sizing: border-box;
      .echarts-box {
        width: 100%;
        margin-top: 10px;
        .echarts-item {
          padding: 10px 15px;
          background: #fff;
          height: 180px;
          box-shadow: 0px 0px 7px 0px #eff2f5;
          .item-title {
            display: flex;
            align-items: center;
            i {
              color: #c9c184;
              margin-right: 5px;
            }
            span {
              font-size: 14px;
            }
            p {
              margin-left: 10px;
              padding: 0 5px;
              border: 1px solid #ccc;
              border-radius: 15px;
              font-weight: bold;
            }
          }
        }
      }
      .el-table {
        margin-top: 10px;
      }
    }
    .box-card {
      margin-top: 20px;
      .btn-box {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
