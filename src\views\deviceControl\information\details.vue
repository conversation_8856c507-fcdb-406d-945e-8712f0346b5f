<template>
  <div class="app-container informationDetaPage">
    <div v-loading="infoloading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading" class="header">
      <div class="title_header">
        <el-button @click="$router.back()" type="text">
          返回
        </el-button>
        <h3 class="title">详情</h3>
      </div>
      <div class="introduce">
        <img :src="infoData.deviceImg" alt="">
        <el-descriptions :column="4" class="desCommn">
          <el-descriptions-item labelClassName="desTilte">
            <span class="desTilteText">{{ infoData.deviceName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ infoData.deviceType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="工站">
            <el-tag size="small"> {{ infoData.workstation || '-' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="品牌与型号">{{ infoData.brandAndModel || '-' }}</el-descriptions-item>
          <el-descriptions-item label="设备编号">
            <el-tag size="small" type="danger"> {{ infoData.deviceNum || '-' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设所属位置">{{ infoData.location || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="conent">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="控制参数" name="parame">
          <el-table border  v-loading="loading" :data="parameList" height="calc(-478px + 100vh)"  ref="parame" v-if="activeName === 'parame'" :key="activeName">
            <el-table-column prop="propertyName" label="参数名称">
            </el-table-column>
            <el-table-column prop="dataType" label="数据类型">
            </el-table-column>
            <el-table-column prop="collectTime" label="采集时间">
            </el-table-column>
            <el-table-column prop="paramValue" label="当前值">
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="控制策略" name="strategy" >
          <el-table border  v-loading="loading" :data="strategyList" height="calc(-478px + 100vh)" ref="strategy"  v-if="activeName === 'strategy'" :key="activeName">
            <el-table-column prop="warnName" label="策略名称">
            </el-table-column>
            <el-table-column prop="validTime" label="有效时间">
            </el-table-column>
            <el-table-column prop="description" label="规则描述">
            </el-table-column>
            <el-table-column label="状态" prop="status" width="200">
          <template slot-scope="scope">
            <el-tag effect="dark" v-if="scope.row.status===1" type="success">开启</el-tag>
            <el-tag effect="dark" v-else type="danger">停止</el-tag>
          </template>
        </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
        @pagination="getList" />
    </div>




  </div>
</template>
    
<script>
import { getInfo, getParamList, getStrategyList } from "@/api/deviceControl/information";


export default {
  dicts: ['sys_normal_disable'],
  data() {
    return {
      loading: true,
      infoloading: true,
      showSearch: true,
      total: 10,
      postList: [],
      queryParams: {
        page: 1,
        limit: 10,
      },
      infoData: {
      },
      activeName:this.$route.query.key ,
      tableData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      },
      ],
      parameList: [],
      strategyList: []
    };
  },
  created() {
    const { query } = this.$route;
    this.getInfoFtn(query.id);
    this.getList();
  },
  methods: {

    switchClick(row){

    },

    handleClick(tab, event) {
      this.queryParams={
        page: 1,
        limit: 10,
      }
      this.getList();
    },

    getInfoFtn(id) {
      getInfo(id).then(res => this.infoData = res.data).finally(() => this.infoloading = false);
    },

    getList() {
      const {  id } = this.$route.query;
      const {activeName} =this;
      const data = { id, ...this.queryParams };
      this.loading = true;
      let api = activeName === 'parame' ? getParamList(data) : getStrategyList(data);
      api.then(response => {
        const { list, total } = response.data;
        activeName === "parame"
          ? (this.parameList = list)
          : (this.strategyList = list);
        this.total = total;
        this.$nextTick(() => {
          this.$refs[activeName].doLayout();
          this.loading = false;
        });
      });
    },


  }
};
</script>
  
<style lang="scss" >
.informationDetaPage {
  background-color: #F4F7FC !important;
  min-height: calc(100vh - 152px);
  padding: 0;
  display: flex;
  flex-direction: column;
  .el-tabs__item{
    font-weight: bold;
    font-size: 18px;
  }
  .header {
    display: flex;
    flex-direction: column;
    height: 180px;
    background: white;
    margin-bottom: 24px;
  }

  .title_header {
    padding: 28px 24px 12px;
    border-bottom: 1px solid #dbdfe9;

    .el-button {
      padding: 0;
      margin-right: 16px;
    }

    .title {
      display: inline-block;
      font-size: 18px;
      margin: 0;
      vertical-align: middle;
      padding-left: 20px;
      color: #181f2d;
      font-weight: bold;
      border-left: 1px solid #dbdfe9;
    }
  }

  .introduce {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 0 24px;
    margin-top: 6px;
    img {
      width: 60px;
      height: 60px;
      margin-right: 20px;
    }
    .el-descriptions-item__content {
      font-family: PingFangSC-Regular;
      color: #515A6E;
    }
    .desCommn {
      width: 90%;
      margin-top: 6px;

      .el-descriptions-item {
        margin-bottom: 8px !important;
      }
    }
    .desTilte {
      display: none;
    }
    .desTilteText {
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #181F2D;
    }
  }

  .conent {
    background: white;
    padding: 10px 24px 0px 24px;
    .el-table__body {

    }
    .pagination-container {
      padding: 0px 20px !important;
      margin-bottom: 16px !important;
    }
  }
}
</style>
    
    