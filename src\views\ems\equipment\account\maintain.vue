<template>
  <div v-if="listFlag" class="maintain-box">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>保养情况</span>
              </div>
              <div class="main-item">
                <span>最近保养</span>
                <div class="flex">
                  <span>{{
                      recentTimeAndUserData != null ? recentTimeAndUserData.executeTime : "暂无数据"
                    }} ~ {{ recentTimeAndUserData != null ? recentTimeAndUserData.executeEndTime : "暂无数据" }}</span>
                  <span>日</span>
                  <span>{{ recentTimeAndUserData != null ? recentTimeAndUserData.userName : "暂无数据" }}</span>
                </div>
              </div>
              <div class="main-item">
                <span>下次保养</span>
                <div class="flex green">
                  <span>{{
                      nextTimeAndUserData != null ? nextTimeAndUserData.planBeginTime : "暂无数据"
                    }} ~ {{ nextTimeAndUserData != null ? nextTimeAndUserData.planEndTime : "暂无数据" }}</span>
                  <span>日</span>
                  <span>{{ nextTimeAndUserData != null ? nextTimeAndUserData.userName : "暂无数据" }}</span>
                </div>
              </div>
            </div>
          </el-col
          >
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>今年执行情况</span>
              </div>
              <div id="faultEcharts" style="height: 140px"></div>

            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>近五年保养次数对比</span>
              </div>
              <div id="numsEcharts" style="height: 140px"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="table-box">
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="16"
          >
            <div class="echarts-item">
              <IconTitle title="关联计划" imgUrl="yunwei">
                <span class="slot">关联保养计划</span>
              </IconTitle>
              <el-table
                  :data="planData"
                  border
                  style="width: 100%"
                  height="140"
                  @selection-change="deviceSelectionChange"
                  :cell-style="changeCellStyle"
                  @cell-click="cellClick"
              >
                <el-table-column prop="planNum" label="计划编号" align="center" @click="toDetail(scope.row)">
                </el-table-column>
                <el-table-column prop="planName" label="计划名称" align="center">
                </el-table-column>
                <el-table-column prop="liableUserName" label="负责人" align="center">
                </el-table-column>
                <el-table-column prop="inspectCycle" label="循环类型" align="center">
                  <template slot-scope="scope">
                    <span>{{
                        scope.row.inspectCycle == 2
                            ? "日"
                            : ""
                      }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item">
              <IconTitle title="关联标准" imgUrl="yunwei">
                <span class="slot">关联保养标准</span>
              </IconTitle>
              <el-table
                  :data="standardData"
                  border
                  style="width: 100%"
                  height="140"
                  @selection-change="deviceSelectionChange"
                  :cell-style="changeCellStyle"
                  @cell-click="cellClick"
              >
                <el-table-column prop="standardNum" label="标准编号" align="center" @click="goDetail(scope.row)">
                </el-table-column>
                <el-table-column prop="standardName" label="标准名称" align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <el-card class="box-card btn-search page-search">
      <div slot="header">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left"></el-button>
          <el-button type="check" icon="el-icon-download" @click="exportExcel">导出</el-button>
        </div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="任务编号">
            <el-input
                placeholder="请输入任务编号"
                v-model="searchForm.taskNum"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="计划名称">
            <el-input
                placeholder="请输入计划名称"
                v-model="searchForm.planName"
                clearable
            ></el-input>
          </el-form-item>

          <el-form-item label="负责人">
            <el-select
                placeholder="请选择负责人"
                v-model="searchForm.liableUserId"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.userName"
                  :value="item.userId"
                  :key="item.userId"
                  v-for="item in userAll"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="验收人">
            <el-select
                placeholder="请选择验收人"
                v-model="searchForm.auditUserId"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.userName"
                  :value="item.userId"
                  :key="item.userId"
                  v-for="item in userAll"
              ></el-option>
            </el-select>
          </el-form-item>

          <!--<el-form-item label="负责人">-->
          <!--  <el-input-->
          <!--      placeholder="请输入负责人"-->
          <!--      v-model="searchForm.liableUserId"-->
          <!--      clearable-->
          <!--  ></el-input>-->
          <!--</el-form-item>-->

          <!--<el-form-item label="验收人">-->
          <!--  <el-input-->
          <!--      placeholder="请输入验收人"-->
          <!--      v-model="searchForm.auditUserId"-->
          <!--      clearable-->
          <!--  ></el-input>-->
          <!--</el-form-item>-->

          <el-form-item label="任务状态">
            <el-select
                placeholder="请选择任务状态"
                v-model="searchForm.status"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="index"
                  v-for="(item, index) of dict.type.task_status"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChange">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
        <div></div>
      </div>
    </el-card>
    <div class="table-box">
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="24"
          >
            <IconTitle title="保养记录" imgUrl="yunwei">
              <span class="slot">保养记录表</span>
            </IconTitle>
            <!-- 设备文档 -->
            <el-table
                :data="maiMaintenanceTaskData"
                border
                style="width: 100%"
                @selection-change="deviceSelectionChange"
            >
              <el-table-column prop="taskNum" label="任务编号" align="center">
              </el-table-column>
              <el-table-column prop="inspectCycle" label="保养类型" align="center">
                <template slot-scope="scope">
                    <span>{{
                        scope.row.inspectCycle == 2
                            ? "日"
                            : ""
                      }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="任务状态" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 0"><el-tag type="info" effect="dark">未开始</el-tag></span>
                  <span v-if="scope.row.status == 1"><el-tag effect="dark">执行中</el-tag></span>
                  <span v-if="scope.row.status == 2"><el-tag type="warning" effect="dark">待核验</el-tag></span>
                  <span v-if="scope.row.status == 3"><el-tag type="success" effect="dark">已完成</el-tag></span>
                  <span v-if="scope.row.status == 4"><el-tag type="danger" effect="dark">已过期</el-tag></span>
                </template>
              </el-table-column>
              <el-table-column prop="planBeginTime" label="开始时间" align="center">
              </el-table-column>
              <el-table-column prop="planEndTime" label="结束时间" align="center">
              </el-table-column>
              <el-table-column prop="executeTime" label="执行时间" align="center">
              </el-table-column>
              <el-table-column prop="liableUserName" label="负责人" align="center">
              </el-table-column>
              <!--        <el-table-column prop="address" label="协同处理人" align="center">-->
              <!--        </el-table-column>-->
              <el-table-column prop="auditUserName" label="验收人" align="center">
              </el-table-column>
              <!--        <el-table-column prop="address" label="保养说明" align="center">-->
              <!--        </el-table-column>-->
              <!--        <el-table-column prop="address" label="验收时间" align="center">-->
              <!--        </el-table-column>-->
              <el-table-column prop="planName" label="所属计划" align="center">
              </el-table-column>
            </el-table>
            <pagination
                style="float: right"
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="listMaiMaintenanceTaskPage"
            />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
  <div v-else-if="detaillistFlagJh">
    <IndexDetailJh :id='detailId'/>
  </div>
  <div v-else-if="detaillistFlagBz">
    <IndexDetailBz :id='detailId'/>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {
  getMaintenancePlanById,
  getRecentFiveYears,
  getImplementationYear
} from "@/api/ems/equipment/account";
import { listUser } from '@/api/system/user';
import {getSelectTaskStatus} from "@/api/ems/inspection/task"
import {listMaiMaintenanceTaskPage} from "@/api/ems/maintenance/emsmaimaintenancetask";
import IndexDetailJh from "../../maintenance/emsmaimaintenanceplan/detail.vue";
import IndexDetailBz from "../../maintenance/emsmaimaintenancestandard/detail.vue";

import IconTitle from "@/components/icon-title/index.vue";

let numsEchartsOptions = {
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: ["2016年", "2017年", "2018年", "2019年", "2020年"],
  },
  yAxis: {
    type: "value",
    show: false,
    splitLine: {
      show: false,
    },
  },
  grid: {
    top: "20%",
    left: "0%",
    right: "5%",
    bottom: "0%",
    containLabel: true,
  },
  series: [
    {
      data: [4, 10, 6, 9, 6],
      type: "line",
      itemStyle: {
        normal: {
          color: "#79a8f9",
          lineStyle: {
            color: "#79a8f9",
          },
          label: {show: true}
        },
      },
      areaStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: "#79a8f9"},
            {offset: 0.5, color: "#a1c2fa"},
            {offset: 1, color: "#e4eefd"},
          ]),
        },
      },
      lineStyle: {
        color: "#79a8f9",
      },
      smooth: true, //true 为平滑曲线，false为直线
    }
  ]
};
let faultOption = {
  grid: {
    top: "20%",
    left: "0%",
    right: "10%",
    bottom: "0%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月",'6月','7月','8月'],
    axisLabel: {
      rotate: 40,
    },
  },
  yAxis: {
    type: "value",
    show: false,
  },

  series: [
    {
      data: [20, 30, 10, 40, 30, 28, 36],
      type: "bar",
      label: {
        show: true,
        position: "top",
      },
      color: "#63b6f8",
      markLine: {
        data: [
          {type: 'average', name: '平均次数'}
        ]
      }
   }
  ]
};
export default {
  name: "account-detail",
  components: {
    IconTitle,
    IndexDetailJh,
    IndexDetailBz
  },
  dicts: ['task_status'],
  data() {
    return {
      maintenancePlanData: [],
      recentTimeAndUserData: [],//最近保养
      nextTimeAndUserData: [],//下次保养
      standardData: [],
      planData: [],
      maiMaintenanceTaskData: [],
      searchForm: {
        taskNum: '',
        status: '',
        liableUserId: '',
        deviceId: '',
        planName: ''
      },
      total: 0,
      taskStatusSelect: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      recentFiveYearsData: [],
      implementationYearData: [],
      deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
      ],
      numsEchartsOptions,
      faultOption,
      userAll: [],
      listFlag: true,
      detaillistFlagJh: false,
      detaillistFlagBz: false,
      detailId: 0
    };
  },
  created() {
    this.getSelect();
  },
  mounted() {
    this.getMaintenancePlanList();
    this.listMaiMaintenanceTaskPage();
    this.getRecentFiveYears();
    this.getImplementationYear();
    this.getListUser();
  },
  methods: {

    // 修改table列的颜色
    changeCellStyle (row, column, rowIndex, columnIndex) {
      //列的label的名称
      // console.log("aaa",row);

      if (row.column.label === "计划编号" || row.column.label === "标准编号") {
        return 'color:#02b9a0' //修改的样式
      } else {
        return ''
      }

    },


    // 关联计划
    toDetail(row) {
      // console.log("111",row.id);
      this.detailId = row
      this.listFlag = false;
      this.detaillistFlagJh = true;
    },

    // 关联标准
    goDetail(row){
      this.detailId = row
      this.listFlag = false;
      this.detaillistFlagBz = true;
    },

    // 跳转页面
    cellClick(row, column) {

      // 关联计划跳转
      if (column.property === "planNum") {
        this.toDetail(7);
      } else if (column.property === "standardNum"){   // 关联标准跳转
        this.goDetail(17);
      } else {
        return;
      }
    },


    // 获取负责人
    getListUser() {
      listUser().then(res => {
        this.userAll = res.rows
      })
    },

    deviceSelectionChange() {
    },
    resetBtn() {
      this.searchForm.taskNum = '';
      this.searchForm.status = '';
      this.searchForm.liableUserId = '';
      this.searchForm.auditUserId = '';
      this.searchForm.planName = '';
    },
    getSelect() {
      // getSelectTaskStatus().then((res) => {
      //   this.taskStatusSelect = res.data;
      // });
    },

    getImplementationYear() {
      getImplementationYear(this.$route.query.id).then(res => {
        this.implementationYearData = res.data;
        let faultDom = document.getElementById("faultEcharts");
        let faultEcharts = echarts.init(faultDom);

        let xData = [], resultData = [];
        this.implementationYearData.forEach(function (item) {
          xData.push(item.mon + "月");
          resultData.push(item.num);
        })
        faultOption.xAxis.data = xData;
        faultOption.series[0].data = resultData;
        faultEcharts.setOption(this.faultOption);
      });
    },

    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsmaimaintenancetask/maiMaintenanceTask/export",
          this.searchForm,
          "保养记录.xlsx"
      );
    },

    getRecentFiveYears() {
      getRecentFiveYears(this.$route.query.id).then(res => {
        this.recentFiveYearsData = res.data;

        let numsEchartsDom = document.getElementById("numsEcharts");
        let numsEcharts = echarts.init(numsEchartsDom);

        if (this.recentFiveYearsData.length > 0) {
          let xData = [];
          let resultData = [];
          this.recentFiveYearsData.forEach(function (item) {
            xData.push(item.mon + "年")
            resultData.push(item.num)
          });
          numsEchartsOptions.xAxis.data = xData;
          numsEchartsOptions.series[0].data = resultData;
          numsEcharts.setOption(this.numsEchartsOptions);
        } else {
          numsEcharts.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }
      });
    },

    searchChange() {
      this.listMaiMaintenanceTaskPage(this.searchForm);
    },

    listMaiMaintenanceTaskPage(params) {
      let id = this.$route.query.id;
      this.searchForm.deviceId = id;
      listMaiMaintenanceTaskPage(this.searchForm).then(res => {
        if (res.code === 200) {
          this.maiMaintenanceTaskData = res.data.records;
          this.total = res.data.total;
        }
      });
    },

    getMaintenancePlanList() {
      function extracted(res) {
        this.recentTimeAndUserData = res.data.maintenanceVo;
        this.nextTimeAndUserData = res.data.nextMaintenance;
      }

      getMaintenancePlanById(this.$route.query.id).then(res => {
        this.maintenancePlanData = res.data;
        if (this.maintenancePlanData != null) {

          extracted.call(this, res);
          this.planData = res.data.emsMaiMaintenancePlanList;
          this.standardData = res.data.maiMaintenanceStandardList;
        }
      });
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.maintain-box {
  .main-item {
    margin-top: 15px;

    .flex {
      padding: 5px;
      background: #f3f5fb;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .flex.green {
      background: #effcf4;
    }
  }
}
</style>
