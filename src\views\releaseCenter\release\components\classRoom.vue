<template>
  <div class="app-container pageMainTop">
    <el-form
      v-if="step == 1"
      :disabled="detailFlag"
      :model="defaultForm"
      :rules="formRules"
      ref="defaultForm"
      style="width: 75%"
      label-width="120px"
    >
      <el-form-item label="课堂类型" prop="type">
        <el-radio-group v-model="defaultForm.type">
          <el-radio :label="0">线上视频</el-radio>
          <el-radio :label="1">线下活动</el-radio>
          <el-radio :label="2">文档</el-radio>
        </el-radio-group>
      </el-form-item>
      <FileClassRoom
        v-show="defaultForm.type === 2"
        ref="fileClassRoom"
        @submit="emitSubmit"
      />
      <el-form-item label="场景类别" prop="sceneCategory">
        <el-select
          v-model="defaultForm.sceneCategory"
          placeholder="请选择场景类别"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.course_scene_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="模块分类" prop="moduleCategory">
        <el-select
          v-model="defaultForm.moduleCategory"
          placeholder="请选择模块分类"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.course_module_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="解决方案标签" prop="solutionCategory">
        <el-select
          v-model="defaultForm.solutionCategory"
          placeholder="请选择解决方案标签"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.course_solution_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="行业" prop="industrialSubstitutionIdJson">
        <el-cascader
          v-model="defaultForm.industrialSubstitutionIdJson"
          :options="industrys"
          :props="{
            children: 'children',
            label: 'vueName',
            value: 'id',
            multiple: true,
          }"
          placeholder="请选择行业"
          style="width: 100%"
          clearable
        >
        </el-cascader>
      </el-form-item>

      <el-form-item
        label="产品标签"
        prop="labelIds"
        v-if="labelIdListState.length > 0"
      >
        <el-select
          multiple
          v-model="defaultForm.labelIds"
          placeholder="请选择产品标签"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="t in labelIdListState"
            :key="t.id"
            :label="t.labelName"
            :value="t.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <OldClassRoom
      v-show="defaultForm.type === 0"
      ref="oldClassRoom"
      @submit="emitSubmit"
      @back="back"
      @submitFormVideoFlag="submitFormVideoFlag"
    />
    <NewClassRoom
      v-show="defaultForm.type === 1"
      ref="newClassRoom"
      @submit="emitSubmit"
    />

    <template v-if="buttonFlag !== 'detail' || defaultForm.type == 0">
      <div
        v-show="footerWidth && footerWidth != '0px'"
        class="pageFooter"
        :style="{ width: `${footerWidth}` }"
      >
        <div style="margin-right: 20px">
          <template v-if="defaultForm.type == 0">
            <el-button
              v-if="stepSon(1)"
              @click="submitFormNext"
              type="primary"
              :loading="submitDing"
              >下一步</el-button
            >
            <el-button
              v-else-if="stepSon(2)"
              @click="getFormDataFtn"
              type="primary"
              >上一步</el-button
            >
          </template>
          <template v-if="buttonFlag === 'check'">
            <el-button type="danger" @click="restClick">驳回</el-button>
            <el-button
              type="primary"
              @click="setExamineFtn('PASS')"
              :loading="dialoDing"
              >通过</el-button
            >
          </template>
          <template v-else>
            <template v-if="defaultForm.type == 1 || defaultForm.type == 2">
              <el-button
                type="primary"
                @click="submitFtnSon"
                :loading="submitDing"
                >提交</el-button
              >
            </template>
            <template v-else>
              <el-button
                v-if="stepSon(2) && buttonFlag !== 'detail'"
                :loading="oldloading"
                @click="submitFormVideo"
                type="primary"
                >提交</el-button
              >
            </template>
          </template>
        </div>
      </div>
    </template>

    <el-dialog
      title="驳回原因"
      :visible.sync="open"
      width="500px"
      append-to-body
    >
      <el-form
        ref="restForm"
        :rules="restRules"
        :model="restForm"
        label-width="0px"
        size="mini"
      >
        <el-form-item prop="reason">
          <el-input
            style="width: 100%"
            type="textarea"
            :rows="3"
            placeholder="请输入驳回原因"
            v-model="restForm.reason"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          :loading="dialoDing"
          @click="submitRestForm('REFUSE')"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import OldClassRoom from "./oldClassRoom.vue";
import NewClassRoom from "./newClassRoom.vue";
import FileClassRoom from "./fileClassRoom.vue";
import { getInfo, setExamine, submitProduct } from "@/api/release/index.js";
import { getSysIndustry, getSysIndustryType } from "@/api/release/indApp";
export default {
  dicts: [
    "course_scene_category",
    "course_module_category",
    "course_solution_category",
  ],
  components: {
    OldClassRoom,
    NewClassRoom,
    FileClassRoom,
  },
  props: {
    footerWidth: {
      options: {
        type: String,
        default: "0px",
      },
    },
    labelIdList: {
      options: {
        type: Array,
        default: () => [],
      },
    },
    currentForm: {
      options: {
        type: Object,
        default: () => {},
      },
    },
  },
  computed: {
    formRules() {
      return this.defaultForm.type === 2 ? {} : this.defaultFormRules;
    },
  },
  data() {
    return {
      // footerWidth: '',
      open: false,
      submitDing: false,
      restForm: {
        reason: "",
      },
      restRules: {
        reason: [
          { required: true, message: "驳回原因必须填写", trigger: "blur" },
        ],
      },
      dialoDing: false,
      flowInstanceId: this.$route.query.flowInstanceId,
      type: this.$route.query.type,
      defaultForm: {
        type: 0,
        labelIds: [],
        // sceneCategory: '1',
        // moduleCategory: '1',
        // solutionCategory: '1'
      },
      industrys: [],
      defaultFormRules: {
        sceneCategory: [
          { required: true, message: "请选择场景类别", trigger: "change" },
        ],
        moduleCategory: [
          { required: true, message: "请选择模块分类", trigger: "change" },
        ],
        solutionCategory: [
          { required: true, message: "请选择解决方案标签", trigger: "change" },
        ],
        industrialSubstitutionIdJson: [
          { required: true, message: "请选择行业", trigger: "change" },
        ],
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
      buttonFlag: this.$route.query.pageType,
      step: 1,
      oldloading: false,
      labelIdListState: [],
    };
  },
  watch: {
    labelIdList(val, oldVal) {
      if (val !== oldVal) {
        this.labelIdListState = val;
      }
    },
    currentForm(val, oldVal) {
      if (val !== oldVal) {
        this.defaultForm = { ...this.defaultForm, labelIds: val.labelIds };
      }
    },
    "defaultForm.type"(val, oldVal) {
      this.$nextTick(() => {
        if (this.$refs.defaultForm) {
          this.$refs.defaultForm.clearValidate();
        }
      });
    },
  },
  created() {
    this.getSysIndustryFtn();
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn();
  },
  methods: {
    getSysIndustryFtn() {
      getSysIndustry().then((res) => {
        this.industrys = res.data;
      });
    },
    submitFormVideoFlag(flag) {
      this.oldloading = flag;
    },

    submitFormVideo() {
      this.oldloading = true;
      this.$refs["oldClassRoom"].submitForm("formTwo");
    },

    submitFormNext() {
      const { buttonFlag } = this;
      if (buttonFlag === "edit" || buttonFlag === "add") {
        this.submitFtnSon();
      } else {
        this.$refs["oldClassRoom"].getFormTwoData();
      }
    },

    stepSon(key) {
      const stepFlag = this.$refs["oldClassRoom"]
        ? this.$refs["oldClassRoom"].step
        : 1;
      this.step = stepFlag;
      return key == stepFlag;
    },

    getFormDataFtn() {
      const { flowInstanceId } = this.$route.query;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        // this.defaultForm.type = params.type
        // this.defaultForm.sceneCategory = params.sceneCategory
        // this.defaultForm.moduleCategory = params.moduleCategory
        // this.defaultForm.solutionCategory = params.solutionCategory
        this.defaultForm = {
          type: params.type,
          industrialSubstitutionIdJson: JSON.parse(
            params.industrialSubstitutionIdJson
          ),
          sceneCategory: params.sceneCategory + "",
          moduleCategory: params.moduleCategory + "",
          solutionCategory: params.solutionCategory + "",
          labelIds: this.defaultForm.labelIds,
        };
        if (params.type === 1) {
          this.$nextTick(() => {
            this.$refs["newClassRoom"].init(params);
          });
        } else if (params.type === 2) {
          this.$nextTick(() => {
            this.$refs["fileClassRoom"].init(params);
          });
        } else {
          const dataMap = {
            title: params.title,
            category: params.category,
            link: params.link,
            teacherNickName: params.teacherNickName,
            recommendFlag: params.recommendFlag,
            file: {
              imagePath: params.imagePath,
            },
            imagePath: params.imagePath,
            id: params.id,
          };
          this.$refs["oldClassRoom"].getFormData(dataMap);
        }
      });
    },
    // 验证表单规则，并呼叫子组件表单验证
    async submitFtnSon() {
      try {
        const res = await this.$refs["defaultForm"].validate();
        if (this.defaultForm.type === 1) {
          this.$refs["newClassRoom"].submitForm();
        } else if (this.defaultForm.type === 2) {
          this.$refs["fileClassRoom"].submitForm("formOne");
        } else {
          this.$refs["oldClassRoom"].submitForm("formOne");
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 将子组件数据拿到并合并
    async emitSubmit(data, callback) {
      const params = {
        ...data,
        ...this.defaultForm,
      };
      this.submitFtn(params, callback);
    },

    // 提交数据
    async submitFtn(data, callback) {
      const { flowInstanceId = "" } = this;
      this.submitDing = true;
      if (data.industrialSubstitutionIdJson) {
        const uniqueElements = [
          ...new Set(
            data.industrialSubstitutionIdJson.map((subArray) => subArray[0])
          ),
        ];
        data.industrialSubstitutionIds = uniqueElements.join(",");
      }
      const paramsAll = {
        productType: this.type,
        params: data,
        labelIds: data.labelIds,
        flowInstanceId: flowInstanceId || undefined,
      };
      const res = await submitProduct(paramsAll).catch((err) => {
        return err;
      });
      callback(res);
      if (data.type == 0 && this.step == 1) {
      } else {
        if (res && res.code == 200) {
          this.$modal.msgSuccess(!flowInstanceId ? "新增成功" : "修改成功");
          this.back();
        }
      }
      this.submitDing = false;
    },

    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.restForm = { reason: "" };
      this.resetForm("restForm");
    },
    restClick() {
      this.open = true;
    },
    resize(val) {
      let appMain = document.querySelector(".app-main");
      this.footerWidth = appMain.offsetWidth ? appMain.offsetWidth : "0px";
    },
    // 返回原路径
    back() {
      this.$router.back();
    },
    // 验证驳回的表单
    submitRestForm(type) {
      this.$refs["restForm"].validate((valid) => {
        if (valid) {
          this.setExamineFtn(type);
        }
      });
    },
    // 提交驳回的原因
    setExamineFtn(type) {
      this.dialoDing = true;
      const { reason } = this.restForm;
      const params = {
        examineType: type,
        flowInstanceId: this.flowInstanceId,
        reason: reason || undefined,
      };
      setExamine(params)
        .then((res) => {
          this.$modal.msgSuccess(reason ? "审核驳回成功" : "审核通过成功");
          this.back();
        })
        .finally(() => (this.dialoDing = false));
    },
  },
};
</script>

<style scoped>
.pageMainTop {
  width: 100%;

  .pageFooter {
    width: 100%;
    position: fixed;
    bottom: 0px;
    background: #ffffff;
    box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
    opacity: 1;
    border-radius: 0px;
    height: 60px;
    display: flex;
    justify-content: end;
    align-items: center;
    right: 20px;
  }
}
</style>
