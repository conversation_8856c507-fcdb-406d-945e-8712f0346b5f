<template>
  <div class="imgUp">
    <el-upload
      :action="uploadImgUrl"
      :headers="selfHeaders"
      :class="{ hide: coverAddhide }"
      list-type="picture-card"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-remove="handleDelete"
      :on-preview="handlePictureCardPreview"
    >
      <i class="el-icon-plus"></i>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="isShowTip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>
<script>
import store from "@/store";
import { delObj } from "@/api/admin/sys-file";
import { getToken } from '@/utils/auth'
export default {
  props: {
    // 值
    value: [String, Object, Array],
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 2,
    },
    fileListTem: {
      type: Array,
      default: [],
    },
    limit: {
      type: Number
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/file/upload",
      selfHeaders: {},
      fileList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      coverAddhide: false, // 封面加号隐藏
    };
  },
  watch: {
    fileListTem(val) {
      this.coverAddhide = this.fileList.length === this.limit;
      this.fileList = val;
    },
  },
  mounted() {
    this.selfHeaders = this.defineHeaders();
    this.fileList = this.fileListTem;
  },
  methods: {

   // 自定义请求头
    defineHeaders() {
      let headers = {}
      if (getToken()) {
        headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      const csrfToken = store.state.user['X-Csrf-Token'];
      if (csrfToken) {
        headers['X-Csrf-Token'] = csrfToken;
      }
      return headers;
    },

    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`请最多上传 ${this.limit} 个文件。`);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 删除文件
    handleDelete(file, fileList) {
      if (file && file.status === "success") {
        //在执行删除
        let { url } = file;
        const i = this.fileList.findIndex((item) => item.url === url);
        this.fileList.splice(i, 1);
      }
    },
    handleUploadSuccess(res, file) {
      let tmepObj = {};
      tmepObj.name = file.name;
      // tmepObj.id = res.data.fileId;
      tmepObj.url = URL.createObjectURL(file.raw);
      tmepObj.temUrl = res.data.url;
      this.fileList.push(tmepObj);
      this.coverAddhide = this.fileList.length === this.limit;
      this.$message.success("上传成功");
    },
  },
};
</script>
<style >
.imgUp .hide .el-upload--picture-card {
  display: none;
}
</style>

