<template>
    <div :id="chartId" :style="{ height: height, width: width }" />
</template>
<script>
import * as echarts from "echarts";
require("echarts/theme/macarons");
import resize from "./resize";

export default {
    mixins: [resize],
    props: {
        chartId: {
            type: String,
            default: new Date().getTime(),
        },
        chartData: {
            type: Object,
            default: {},
        },
        width: {
            type: String,
            default: "100%",
        },
        height: {
            type: String,
            default: "100%",
        },
    },
    data() {
        return {};
    },
    watch: {
        chartData: {
            handler(val, oldVal) {
                this.initChart();
            },
            deep: true,
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
        });
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        initChart() {
            const { chartData } = this;
            const {xData,oeeYData} =chartData;
            this.chart && this.chart.dispose();
            this.chart = echarts.init(document.getElementById(this.chartId));
            this.chart.setOption({
                grid: {
                    right: '10%'
                },
                legend: {
                    data: ['OEE'],
                    right: '10%',
                    top:'10%'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: (params) => {
                        let str = params ? params[0].name + '<br/>' : ''
                        for (let item of params) {
                            if (item.seriesType == 'bar') {
                                str += item.marker + item.seriesName + '：' + item.value + '个' + '<br/>'
                            } else {
                                // str += item.marker + item.seriesName + '：' + item.value + '%' + '<br/>'
                                str += item.marker + item.seriesName + '：' + item.value +  '<br/>'
                            }
                        }
                        return str
                    }
                },
                xAxis: {
                    type: 'category',
                    axisLabel: {
                        interval: 0
                    },
                    axisTick: {
                        alignWithLabel: false
                    },
                    data: xData
                },
                yAxis: {
                        type: 'value',
                        name: 'OEE',
                        position: 'left',
                        axisLabel: {
                            // formatter: '{value}%'
                            formatter: '{value}'
                        }
                    },
                series: [
                    {
                        name: 'OEE',
                        type: 'line',
                        yAxisIndex: 0,
                        // symbol: 'none',
                        // connectNulls: true,
                        itemStyle: {
                            color: '#1890FF'
                        },
                        data: oeeYData
                    }
                ]
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.pieChart {
    flex: 1;
}
</style>
