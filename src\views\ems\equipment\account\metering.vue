<template>
  <div class="metering">

   <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
        <span class="slot">品牌管理</span>
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="8"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>近五年计量次数曲线</span>
              </div>
              <div id="numsEcharts" style="height: 140px"></div>
          
            </div></el-col
          >
          <el-col :span="8"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>计量情况</span>
              </div>
              <div class="main-item">
                <span>最近计量</span>
                <div class="flex">
                  <span>2021-10-23 ~ 2021-10-239</span>
                  <span>月</span>
                  <span>张如玉</span>
                </div>
              </div>
              <div class="main-item">
                <span>下次计量</span>
                <div class="flex green">
                  <span>2021-10-23 ~ 2021-10-239</span>
                  <span>月</span>
                  <span>张如玉</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>计量结果</span>
              </div>
              <div id="typeEcharts" style="height: 140px"></div>
            </div>
          </el-col>
        </el-row>
      </div>

    </div>    
    <div class="table-box">
      <IconTitle title="计量设备信息" imgUrl="yunwei">
        <span class="slot">计量列表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="deviceData"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="date" label="设备编号" align="center">
        </el-table-column>
          <el-table-column prop="date" label="发生日期" align="center">
        </el-table-column>
        <el-table-column prop="name" label="创建人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="开销" align="center">
        </el-table-column>
        <el-table-column prop="address" label="价值变动" align="center">
        </el-table-column>
         <el-table-column prop="name" label="变动值" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调入部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调入地点" align="center">
        </el-table-column>
        <el-table-column prop="name" label="新负责人" align="center">
        </el-table-column>
        <el-table-column prop="address" label="备注" align="center">
        </el-table-column>
      </el-table>
    </div>
      <div class="table-box">
      <IconTitle title="计量记录" imgUrl="yunwei">
        <span class="slot">计量记录表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="deviceData"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="date" label="设备编号" align="center">
        </el-table-column>
          <el-table-column prop="date" label="发生日期" align="center">
        </el-table-column>
        <el-table-column prop="name" label="创建人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="开销" align="center">
        </el-table-column>
        <el-table-column prop="address" label="价值变动" align="center">
        </el-table-column>
         <el-table-column prop="name" label="变动值" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调入部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调入地点" align="center">
        </el-table-column>
        <el-table-column prop="name" label="新负责人" align="center">
        </el-table-column>
        <el-table-column prop="address" label="备注" align="center">
        </el-table-column>
      </el-table>
    </div>
  </div>

</template>
<script>
import echarts from "echarts";

import IconTitle from "@/components/icon-title/index.vue";
let typeOption = {
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  color: ["#D75746", "#02b980"],
  series: [
    {
      type: "pie",
      radius: ["35%", "50%"],
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      data: [
        { value: 3, name: "合格" },
        { value: 2, name: "不合格" },
      ],
    },
  ],
};
let numsEchartsOptions = {
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: ["2016年", "2017年", "2018年", "2019年", "2020年"],
  },
  yAxis: {
    type: "value",
    show: false,
    splitLine: {
      show: false,
    },
  },
  grid: {
    top: "20%",
    left: "0%",
    right: "5%",
    bottom: "0%",
    containLabel: true,
  },
  series: [
    {
      data: [4, 10, 6, 9, 6],
      type: "line",
      itemStyle: {
        normal: {
          color: "#79a8f9",
          lineStyle: {
            color: "#79a8f9",
          },
          label: { show: true }
        },
      },
      areaStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#79a8f9" },
            { offset: 0.5, color: "#a1c2fa" },
            { offset: 1, color: "#e4eefd" },
          ]),
        },
      },
      lineStyle: {
        color: "#79a8f9",
      },

      smooth: true, //true 为平滑曲线，false为直线
    },
  ],
};
export default {
  name: "metering",
  components: {
    IconTitle,
  },
  data() {
    return {
      numsEchartsOptions,
      typeOption,
         deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区",
        },
      ],
    };
  },
    mounted() {
    var chartDom = document.getElementById("typeEcharts");
    var myChart = echarts.init(chartDom);
    myChart.setOption(this.typeOption);
     var numsEchartsDom = document.getElementById("numsEcharts");
    var numsEcharts = echarts.init(numsEchartsDom);
    numsEcharts.setOption(this.numsEchartsOptions);
  },
  methods: {
       deviceSelectionChange(){}
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
.metering {
     .total-tj-gz {
          padding: 25px 0 20px 0;
          display: flex;
          .tj {
            width: 50%;
            .top {
              display: block;
              margin-bottom: 10px;
            }
            b {
              font-size: 20px;
            }
            b.wancheng {
              color: $theme;
            }
            b.zhixing {
              color: $colorEdit;
            }
            b.guoqi {
              color: $colorDe;
            }
          }
        }
    .main-item {
          margin-top: 15px;
          .flex {
            padding: 5px;
            background: #f3f5fb;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .flex.green {
            background: #effcf4;
          }
        }
}
</style>
