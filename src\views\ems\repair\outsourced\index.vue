<template>
    <!--列表-->
    <div class="execution">
        <el-card class="box-card btn-search page-search">
            <div slot="header" class="clearfix">
                <div class="btn-box">
                    <el-button
                            type="info"
                            icon="el-icon-refresh-left"
                            @click="refreshChange()"
                    ></el-button
                    >
                    <!--<el-button id="gwButton"
                               type="primary"
                               style="backgroundColor:#E1b980"
                               icon="el-icon-circle-plus-outline"
                               v-if="permissions.ems_emsregulations_add"
                               @click="toAdd()"
                    >新增</el-button
                    >
                    <el-button
                            type="success"
                            icon="el-icon-edit"
                            v-if="permissions.ems_emsregulations_edit"
                            :disabled="single"
                            @click="handleEdit"
                    >编辑</el-button
                    >-->
                    <!--<el-button
                            type="danger"
                            icon="el-icon-circle-close"
                            v-if="permissions.ems_emsregulations_del"
                            @click.native="handleDel()"
                            :disabled="multiple"
                    >删除</el-button
                    >-->
                    <el-button
                            type="check"
                            icon="el-icon-download"
                            @click="exportExcel"
                    >导出</el-button
                    >
                </div>
                <div class="icon-box">
                    <i class="el-icon-search" @click="searchShow"></i>
                    <i class="el-icon-refresh" @click="refreshChange"></i>
                    <i class="el-icon-goods"></i>
                    <i class="el-icon-setting" @click="columnShow"></i>
                    <i class="icon-zuixiaohua" />
                </div>
            </div>

        </el-card>
        <basic-container>
            <avue-crud
                    ref="crud"
                    :page.sync="page"
                    :data="tableData"
                    :permission="permissionList"
                    :table-loading="tableLoading"
                    :option="tableOption"
                    @selection-change="selectionChange"
                    @on-load="getList"
                    @search-change="searchChange"
                    @refresh-change="refreshChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    @row-update="handleUpdate"
                    @row-save="handleSave"
                    @row-del="handleDel"
            >
                <template slot="header">
                    <IconTitle class="selfTitle" title="维修工单" imgUrl="yunwei" />
                </template>
                <!--状态-->
                <template slot="checkStatus" slot-scope="scope" >
                    <el-tag v-if="scope.row.checkStatus == 0" size="mini" :hit="false" color="#E83672" effect="dark">进行中</el-tag>
                    <el-tag v-else-if="scope.row.checkStatus == 1" size="mini" :hit="false" color="#DEA11E" effect="dark">已通过</el-tag>
                    <el-tag v-else-if="scope.row.checkStatus == 2" size="mini" :hit="false" color="#358AEF" effect="dark">未通过</el-tag>
                </template>
                <!--菜单-->
                <template slot-scope="{row,index}" slot="menu">
                    <el-button v-if="row.checkStatus == 0" type="text" @click="adopt(row)">
                        <i class="el-icon-view"></i>通过</el-button
                    >
                    <el-button v-if="row.checkStatus == 0" type="text" @click="pass(row)">
                        <i class="el-icon-view"></i>驳回</el-button
                    >
                    <el-button v-if="row.checkStatus == 1 && row.status == 2" type="text" @click="reso(row)">
                        <i class="el-icon-view"></i>已解决</el-button
                    >
                    <el-button type="text" @click="goLook(row)">
                        <i class="el-icon-view"></i>查看</el-button
                    >
                    <!--<el-button type="text" @click="$refs.crud.rowDel(row,index)">
                        <i class="el-icon-delete"></i>删除</el-button
                    >-->

                </template>

            </avue-crud>
            <!-- 审核通过弹窗 -->
            <el-dialog :title="tg.title" :visible.sync="tg.open" width="500px" append-to-body>
                <el-form ref="form" :model="tg.form" :rules="tg.rules" label-width="80px">
                    <el-form-item label="委外单位" prop="outName">
                        <el-input v-model="tg.form.outName" placeholder="请输入" />
                    </el-form-item>
                    <el-form-item label="工期要求" prop="duration">
                        <el-input v-model="tg.form.duration" placeholder="请输入" />
                    </el-form-item>
                    <el-form-item label="预计金额" prop="expectMoney">
                        <el-input v-model="tg.form.expectMoney" placeholder="请输入" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </el-dialog>
        </basic-container>
    </div>
</template>
<script>
    import {fetchList,adoptOrder,resolved,reject} from "@/api/ems/repair/outorder";
    import {tableOption} from '@/const/crud/ems/repair/outorder'
    import { mapGetters } from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/ems/icon-title/index.vue";

    export default {
        name: 'emsrepairorder',
        components: {
            IconTitle,
        },
        data() {
            return {
                tableData: [],
                searchForm: {}, // 查询参数
                single: true,  // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableLoading: false,
                tableOption: tableOption,
                ids: [],
                selectionList:[],
                tg:{
                    "title":"审批通过",
                    "open":false,
                    "form":{
                        "id":null,
                        "outName":null,
                        "duration":null,
                        "expectMoney":null
                    },
                    // 表单校验
                    rules: {
                        outName: [
                            { required: true, message: "外委单位不能为空", trigger: "blur" }
                        ],
                        duration: [
                            { required: true, message: "工期不能为空", trigger: "blur" }
                        ],
                        expectMoney: [
                            { required: true, message: "预计金额不能为空", trigger: "blur" }
                        ]
                    }
                }
            };
        },
        computed: {
            ...mapGetters(["permissions","theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsrepairorder_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsrepairorder_del, false),
                    editBtn: this.vaildData(this.permissions.ems_emsrepairorder_edit,false),
                };
            },
        },
        mounted() {
            this.initElement();
            /*this.changeThme();*/
        },
        methods: {
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList=list
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },

            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
            },

            // 列表查询
            getList(page, params) {
                this.tableLoading = true;
                fetchList(
                    Object.assign({
                        current: page.currentPage,
                        size: page.pageSize,
                    },params,this.searchForm)).then((response) => {
                    this.tableData = response.data.data.records;
                    this.page.total = response.data.data.total;
                    this.tableLoading = false;
                })
                    .catch(() => {
                        this.tableLoading = false;
                    });
            },
            //通过
            adopt(row){
                this.tg.open = true
                this.tg.form.id = row.id
            },
            //驳回
            pass(row){
                this.$confirm('驳回会重置异常项状态，是否确认驳回?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    return reject({"id":row.id})
                }).then(() => {
                    this.$message.success("操作成功");
                    this.getList(this.page);
                }).catch(() => {});
            },
            //已解决
            reso(row){
                this.$confirm('是否确认提交?', "提醒", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    return resolved({"id":row.id})
                }).then(() => {
                    this.$message.success("提交成功");
                    this.getList(this.page);
                }).catch(() => {});
            },
            // 取消按钮
            cancel() {
                this.tg.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.tg.form = {
                    "id":null,
                    "outName":null,
                    "duration":null,
                    "expectMoney":null
                };
                //this.resetForm("form");
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs["form"].validate(valid => {
                    if (valid) {
                        adoptOrder(this.tg.form)
                            .then((data) => {
                                this.$message.success("审批完成");
                                this.getList(this.page);
                                this.tg.open = false
                            })
                            .catch(() => {
                                this.tg.open = false
                            });

                    }
                });
            },
            //编辑
            handleEdit(){
                var refsDate = this.$refs
                refsDate.crud.rowEdit(this.selectionList[0],this.selectionList[0].$index);
            },
            // 删除
            handleDel: function (row, index) {
                this.$confirm("是否确认删除所选数据项", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        let id = "";
                        if (row) {
                            id = row.id;
                        } else {
                            id = this.ids;
                        }
                        return delObj(id);
                    })
                    .then((data) => {
                        this.$message.success("删除成功");
                        this.getList(this.page);
                    });
            },
            // 更新
            handleUpdate: function (row,  index,done, loading) {
                putObj(row)
                    .then((data) => {
                        this.$message.success("修改成功");
                        done();
                        this.getList(this.page);
                    })
                    .catch(() => {
                        loading();
                    });
            },
            // 保存
            handleSave: function (row, done, loading) {
                addObj(row)
                    .then((data) => {
                        this.$message.success("添加成功");
                        done();
                        this.getList(this.page);
                    })
                    .catch(() => {
                        loading();
                    });
            },
            // 每页条数改变事件
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            // 当前页发生改变事件
            currentChange(current) {
                this.page.currentPage = current;
            },
            // 查询事件
            searchChange(form, done) {
                this.searchForm = form
                this.page.currentPage = 1
                this.getList(this.page, form)
                done()
            },
            // 刷新事件
            refreshChange() {
                this.getList(this.page);
            },
            // 导出excel
            exportExcel() {
                this.downBlobFile(
                    "/ems/emsrepairorder/export",
                    this.searchForm,
                    "emsrepairorder.xlsx"
                );
            },
            // 改变主题颜色
            changeThme(){
                //"#02b980"
                document.getElementById("gwButton").style.backgroundColor=this.theme;
            },
            goLook(row) {
                this.$router.push({
                    path: "/ems/repair/outsourced/detail",
                    query: {
                        id: row.id
                    }
                });
            },
            toAdd(){
                this.listFlag = false
                this.addFlag = true
            }
        },
    };
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="less">
    .el-tag {
        border-width: 0px;
        padding: 1px 15px !important;
    }
    .drawerStyle {
        ::v-deep .el-drawer__header {
            background-color: #f2f2f5;
            padding: 10px 0 10px 20px;
            color: #101010;
            margin-bottom: 20px;
        }
    }
</style>
