import { requestPlatForm } from "@/utils/requestBase";
import request from '@/utils/request'




export function getTableList(query) {
    return requestPlatForm({
      url: '/product/examineList',
      method: 'get',
      params: query
    })
  }


//类型
export function getProductList(whetherExaminePage) {
    return requestPlatForm({
      url: '/product/getProductList/' + whetherExaminePage,
      method: 'get'
    })
  }

//上下架
export function groundUpIng(data) {
    return requestPlatForm({
      url: '/product/groundUpIng',
      method: 'post',
      data: data
    })
  }
  //热门
  export function groundHot(data) {
    return requestPlatForm({
      url: '/product/hot',
      method: 'post',
      data: data
    })
  }



  //提交
  export function submitProduct(data) {
    const {flowInstanceId=''}= data;
    if(!flowInstanceId){
      return requestPlatForm({
        url: '/product',
        method: 'post',
        data: data
      })
    }
    else{
      return requestPlatForm({
        url: '/product',
        method: 'put',
        data: data
      })
    }
  }


  //获取详情数据
  export function getInfo(query) {
    return requestPlatForm({
      url: '/product/getInfo',
      method: 'get',
      params: query
    })
  }
  


// 删除
export function delProduct(noticeIds) {
  console.log(noticeIds,'noticeIdsnoticeIds');
  return requestPlatForm({
    url: '/product/' + noticeIds,
    method: 'delete'
  })
}


export function uploadApi(params) {
  return request({
    url: '/system/file/upload',
    method: 'POST',
    data:params
  })
}



// 任务分配
export function getDiagnosticList(query) {
  return requestPlatForm({
    url: "/product/diagnosticList",
    method: "get",
    params: query,
  });
}

/**
 * @name 中止诊断任务
 * @param {*} param0.diagnosticOfflineId 线下诊断id
 * @returns 
 */
export function suspend({
  diagnosticOfflineId
}){
  return requestPlatForm({
    url:'/product/suspend',
    method:'get',
    params:{
      diagnosticOfflineId
    }
  })
}

/**
 * @name 线下诊断诊断类型
 * @returns 
 */
export function getDiagnosisType(){
  return requestPlatForm({
    url:'/front/diagnosticOffline/getDiagnosisType',
    method:'get'
  })
}




