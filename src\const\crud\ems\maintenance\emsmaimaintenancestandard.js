export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": true,
  "gridBtn": false,
  "column": [
    {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
      "hide": true,
      "addDisplay": false,
      "eddDisplay": false
    }, {
      "type": "input",
      "label": "标准编号",
      "prop": "standardNum",
      "span": 12,
      search: true

    }, {
      "type": "input",
      "label": "标准名称",
      "prop": "standardName",
      "span": 12,
      search: true
    },
    // {
    //   "type": "input",
    //   "label": "部门id",
    //   "prop": "deptId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "类别id",
    //   "prop": "categoryId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "要求",
      "prop": "requirement",
      "span": 20
    }, {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12,
      "addDisplay": false,
      "eddDisplay": false
    }, {
      "type": "input",
      "label": "创建者",
      "prop": "createBy",
      "span": 12,
      "addDisplay": false,
      "eddDisplay": false
    }, {
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12
    }]
}
