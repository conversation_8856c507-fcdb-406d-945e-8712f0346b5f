import { requestPlatForm } from "@/utils/requestBase";


export function controlList(query) {
  return requestPlatForm({
    url: '/device/control/page',
    method: 'get',
    params: query
  })
}
//工站字典
export function getWorStation() {
  return requestPlatForm({
    url: '/produce/device/workstation',
    method: 'get',
  })
}
//设备详情基本信息
export function getInfo(id) {
  return requestPlatForm({
    url: '/device/control/info/' + id,
    method: 'get',
  })
}
//详情 参数
export function getParamList(query) {
  return requestPlatForm({
    url: '/device/control/param',
    method: 'get',
    params: query
  })
}
//详情 策略
export function getStrategyList(query) {
  return requestPlatForm({
    url: '/device/control/strategy',
    method: 'get',
    params: query
  })
}


