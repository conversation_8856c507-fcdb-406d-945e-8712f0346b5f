<template>
  <div class="schoolOwnedPage">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="校企名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入校企名称"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item class="uploadItem" label="图标" prop="iconUrl">
        <UploadImage
          :disabled="detailFlag"
          :fileList="form.iconUrl"
          @addUpload="addUpload($event, 'iconUrl')"
          @removeUpload="removeUpload($event, 'iconUrl')"
        />
        <!-- <el-upload
          class="avatar-uploader"
          accept="image/*"
          :before-upload="beforeUploadImage"
          :http-request="(event) => uploadApiFtn(event, 'iconUrl')"
          action="#"
          :show-file-list="false"
        >
          <img
            v-if="form.iconUrl"
            :src="ensureFullUrl(form.iconUrl)"
            class="miniImage"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
          <div slot="tip" style="font-size: 12px; color: #9ea5b6">
            支持扩展名：.jpg .img .png
          </div>
        </el-upload> -->
      </el-form-item>
      <el-form-item label="主页地址" prop="homeAddress">
        <el-input
          v-model="form.homeAddress"
          placeholder="请输入主页地址"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="所在地区" prop="citys">
        <el-cascader
          v-model="form.citys"
          :options="cityData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'code',
          }"
          placeholder="请选择所在地区"
          style="width: 100%"
          clearable
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          placeholder="请选择标签"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.school_tags"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="介绍" prop="biography">
        <el-input
          v-model="form.biography"
          placeholder="请输入介绍"
          style="width: 100%"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item class="uploadItem" label="校园图片" prop="imageUrl">
        <UploadImage
          :disabled="detailFlag"
          :fileList="form.imageUrl"
          @addUpload="addUpload($event, 'imageUrl')"
          @removeUpload="removeUpload($event, 'imageUrl')"
        />
      </el-form-item>
      <el-form-item class="uploadItem" label="相关附件" prop="files">
        <el-upload
          :before-upload="
            (file) => beforeFileUpload(file, ['doc', 'docx', 'pdf', 'xlsx'])
          "
          :on-preview="previewLoad"
          :on-remove="(file) => removeFile(file)"
          :http-request="(event) => uploadFileApiFtn(event, 'files')"
          accept=".doc,.docx,.pdf,.xlsx"
          action="#"
          :file-list="form.files"
        >
          <el-button slot="trigger" type="primary" size="small"
            ><i class="el-icon-upload el-icon--right"></i>选择文件</el-button
          >
          <div
            slot="tip"
            class="el-upload__tip"
            style="color: silver; font-size: 12px"
          >
            支持扩展名：.docx .doc .pdf .xlsx
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getInfo, uploadApi } from "@/api/release/index.js";
import { getCityData } from "@/api/release/financial";
import UploadImage from "@/components/UploadImage";

export default {
  name: "schoolOwnedPage",
  dicts: ["areas_of_expertise", "school_tags"],
  props: {},
  components: {
    UploadImage,
  },
  data() {
    var validateURL = (rule, value, callback) => {
      const urlRegex = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
      if (!urlRegex.test(value)) {
        callback(new Error("请输入有效的URL"));
      } else {
        callback();
      }
    };
    return {
      form: {
        tags: [],
        imageUrl:[],
        iconUrl:[]
      },
      rules: {
        name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
        iconUrl: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        homeAddress: [
          { required: true, message: "必填项不能为空", trigger: "blur" },
          { validator: validateURL, trigger: "blur" },
        ],
        imageUrl: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        citys: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        tags: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        biography: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
      files: [],
      cityData: [],
    };
  },

  created() {
    this.getCityDataFtn(); //地区
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },

  methods: {
    getFormDataFtn(flowInstanceId) {
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        const { province, city, district, tags } = params;
        this.form = {
          ...params,
          iconUrl: params.iconUrl ? params.iconUrl.split(",") : [],
          imageUrl: params.imageUrl ? params.imageUrl.split(",") : [],
          citys: [
            province || undefined,
            city || undefined,
            district || undefined,
          ].filter((v) => v),
          tags: tags.map((v) => v + ""),
        };
      });
    },

    removeFile(file) {
      const findex = this.files.map((f) => f.uid).indexOf(file.uid);
      if (findex > -1) {
        this.files.splice(findex, 1);
      }
    },

    previewLoad(file) {
      const { status, url } = file;
      if (status === "success") {
        window.open(url, "_blank");
      }
    },

    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },

    beforeFileUpload(file, typeArr) {
      var FileExt = file.name.replace(/.+\./, "");
      if (typeArr.indexOf(FileExt.toLowerCase()) === -1) {
        this.$message({
          type: "warning",
          message: "请上传正确的文件！",
        });
        return false;
      }
    },

    uploadFileApiFtn(event, key) {
      let fileData = new FormData();
      fileData.append("file", event.file);
      uploadApi(fileData).then((res) => {
        const { data } = res;
        this[key].push({
          name: data.name,
          url: data.url,
          uid: event.file.uid,
        });
      });
    },
    addUpload(res, key) {
      this.form[key] = [...this.form[key], res.url];
    },
    removeUpload(file, key) {
      const index = this.form[key].indexOf(file);
      if (index > -1) {
        this.form[key].splice(index, 1);
      }
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { citys } = this.form;
          const params = {
            ...this.form,
            province: citys[0] || undefined,
            city: citys[1] || undefined,
            district: citys[2] || undefined,
            files: this.files,
            iconUrl: this.form.iconUrl.join(),
            imageUrl: this.form.imageUrl.join(),
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.schoolOwnedPage {
  width: 55%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }

  .miniIcon {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }

  .miniImage {
    width: 80px;
    height: 80px;
  }
}
</style>
