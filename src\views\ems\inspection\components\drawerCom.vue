<template>
  <div>
    <el-drawer
      class="drawerStyle"
      title="巡检标准"
      :show-close="false"
      :visible.sync="detail"
      direction="rtl"
      size="60%"
      append-to-body
    >
      <div>
        <div>
          <span class="labelS">标准编号：</span>
          <span class="contentS">10001SHL2020102100003</span>
        </div>
        <div>
          <span class="labelS">标准名称：</span>
          <span class="contentS">喷涂机巡检标准</span>
        </div>
        <div>
          <span class="labelS">所属部门：</span>
          <span class="contentS">设备部</span>
        </div>
      </div>
      <div class="line"></div>
      <div>
        <el-card shadow="always" class="box-card">
          <div class="tableTitle"><span>关联计划</span></div>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column type="selection" width="55"> </el-table-column>

            <el-table-column
              v-for="(item, index) in columnList"
              :prop="item.prop"
              :label="item.label"
              :key="index"
            >
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small"> 启用 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>
<script>
export default {
  name: "drawer",
  props: ["tableData", "detail", "columnList"],
};
</script>

<style lang="scss" scoped>
.labelS {
  display: inline-block;
  width: 100px;
  margin-right: 10px;
  text-align: right;
  color: #888888;
}
.contentS {
  font-weight: bold;
  color: #101010;
  margin: 10px 0;
  display: inline-block;
}
.line {
  border: 2px solid rgba(236, 240, 244, 100);
  margin: 40px 0 30px;
}
::v-deep.drawerStyle {
  .el-drawer__header {
    background-color: #f2f2f5;
    padding: 20px 0 20px 20px;
    color: #101010;
  }
  .box-card {
    box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, 12);
    margin: 0 20px;
  }
}
.tableTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  display: inline-block;
}
</style>