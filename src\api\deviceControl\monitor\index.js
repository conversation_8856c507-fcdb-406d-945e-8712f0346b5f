import { requestPlatForm } from "@/utils/requestBase";



//总数
export function getCountData(query) {
    return requestPlatForm({
      url: '/device/control/monitor/count',
      method: 'get',
      params: query
    })
  }

//oee
export function getOeeData(query) {
    return requestPlatForm({
      url: '/device/control/monitor/oee',
      method: 'get',
      params: query
    })
  }

  export function monitorList(query) {
    return requestPlatForm({
      url: '/device/control/monitor/list',
      method: 'get',
      params: query
    })
  }



