<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
            <el-form-item label="流程名" prop="flowName">
                <el-input v-model="queryParams.flowName" placeholder="请输入流程名" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>

            <el-form-item label="流程状态" prop="flowStatus">
                <el-select v-model="queryParams.flowStatus" placeholder="请选择流程状态" clearable>
                    <el-option label="启用" value="ENABLE" />
                    <el-option label="禁用" value="DISABLE" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </el-col>
            <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
        </el-row>

        <el-table v-loading="loading" :data="flowList" @selection-change="handleSelectionChange">
            <template slot="empty">
                <EmptyModel />
            </template>
            <el-table-column label="流程名" prop="flowName" />
            <el-table-column label="流程名" prop="flowType">
                <template slot-scope="scope">
                    <span>{{ typeMap[scope.row.flowType] }}</span>
                </template>
            </el-table-column>

            <el-table-column label="流程状态" prop="flowStatus">
                <template slot-scope="scope">
                    <div :class="scope.row.flowStatus === 'DISABLE' ? 'el-icon-close' : 'el-icon-check'"
                        :style="scope.row.flowStatus === 'DISABLE' ? 'color: red' : 'color: green'"></div>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" />
            <el-table-column label="更新时间" prop="updateTime" />
            <el-table-column label="操作" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="handleStart(scope.row)">{{
                        scope.row.flowStatus === 'DISABLE' ? '启用' : '禁用' }}</el-button>
                    <el-button size="mini" type="text" @click="showFlowDesign(scope.row, 'look')"
                       v-if="scope.row.flowStatus === 'ENABLE'">查看</el-button>
                    <el-button size="mini" type="text" @click="showFlowDesign(scope.row)" 
                        v-if="scope.row.flowStatus === 'DISABLE'">流程设计</el-button>
                    <el-button size="mini" type="text" @click="handleUpdate(scope.row)" 
                        v-if="scope.row.flowStatus === 'DISABLE'">修改</el-button>
                    <el-button class="button--danger" size="mini" type="text" @click="handleDelete(scope.row)"
                        v-if="scope.row.flowStatus === 'DISABLE'">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <!-- 添加或修改流程对话框 -->
        <el-dialog :title="title" :visible.sync="open" class="smallDialog" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px" @submit.native.prevent>
                <el-form-item label="流程名" prop="flowName">
                    <el-input v-model="form.flowName" placeholder="请输入流程名" style="width: 80%;" />
                </el-form-item>
                <el-form-item label="流程类型" prop="flowType">
                    <el-select v-model="form.flowType" placeholder="请选择流程类型" clearable style="width: 80%;">
                        <el-option v-for="(t) in typeList" :key="t.key" :label='t.name' :value="t.key" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>

        <el-dialog :close-on-press-escape="false" :v-loading="flowDesignLoading" :close-on-click-modal="false"
            class="middleDialog" @close="cancelFlow" :visible.sync="flowModalVisible" title="流程" :destroy-on-close="true"
            append-to-body>
            <div>
                <el-button :disabled="lookFlag" @click="addFlowDesign">添加</el-button>
            </div>

            <flow-map :lookFlag='lookFlag' :relationList="relationList" :nodesList="nodesList" ref="flowMaps"
                @saveData="saveData" @deleteNode="deleteNode" :isDraggable="isDraggable">
            </flow-map>

            <div>
                <el-table v-loading="loading" :data="dataSourceNode">
                    <template slot="empty">
                        <EmptyModel />
                    </template>
                    <el-table-column label="序号" prop="number" width="55" />

                    <el-table-column label="名称" prop="nodeName">
                        <template slot-scope="record">
                            <el-input :disabled="lookFlag" v-model="record.row.nodeName" placeholder="请填写名称"
                                @blur="changeNodeName(record.row)" :maxLength="11" style="width: 280px" />
                        </template>
                    </el-table-column>

                    <!-- <el-table-column label="审批角色"  prop="approveRoleArr">
                        <template slot-scope="record">
                            <el-select :disabled="lookFlag" multiple v-model="record.row.approveRoleArr" placeholder="请添加" @change="changeAuditRole"
                                style="width: 280px">
                              <el-option v-for="(t) in roleList" :key="t.roleKey" :label='t.roleName' :value="t.roleKey" />
                            </el-select>
                        </template>
                    </el-table-column> -->

                    <el-table-column label="审批角色" prop="approveRoleArr">
                        <template slot-scope="record">
                            <!-- <el-select :disabled="lookFlag" multiple v-model="record.row.approveRoleArr" placeholder="请添加" @change="changeAuditRole"
                                style="width: 280px">
                              <el-option v-for="(t) in roleList" :key="t.roleKey" :label='t.roleName' :value="t.roleKey" />
                            </el-select> -->
                            <el-cascader 
                              v-model="record.row.approveRoleArr"
                              :disabled="lookFlag"
                              :options="roleList" 
                              :props="{ 
                                checkStrictly: true,
                                multiple: true, 
                                children: 'sonRoles',
                                label: 'roleName',
                                value: 'roleKey',
                            }" 
                              placeholder="请添加"
                              style="width: 280px"
                               clearable>
                            </el-cascader>
                        </template>
                    </el-table-column>

                    <el-table-column label="添加文档类型" prop="docTypeIdArr">
                        <template slot-scope="record">
                            <el-select :disabled="lookFlag" multiple v-model="record.row.docTypeIdArr" placeholder="请添加"
                                @change="changeAuditRole" style="width: 280px" clearable>
                                <el-option v-for="(t) in documentList" :key="t.id" :label='t.docName' :value="t.id" />
                            </el-select>
                        </template>
                    </el-table-column>


                </el-table>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancelFlow">取 消</el-button>
                <el-button v-if="!lookFlag" type="primary" @click="saveFlowDesign">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listFlow, getFlow, delFlow, addFlow, updateFlow, getSelectList, saveFlowData, queryFlowNodesByFlowId, topping, getFlowTypeList } from "@/api/base/flow";

export default {
    components: {
        flowMap: () => import('@/views/flowmap/components/flowMap')
    },
    name: "Flow",
    data() {
        return {
            //流程
            sortNumber: 0,
            mapNode: null,
            flowDesignLoading: false,
            flowModalVisible: false,
            dataSourceNode: [
                // {number:1,nodeName:'2',approveRoleArr:3}
            ],
            businessSurveyList: [],
            relationList: [],
            nodesList: [],
            // 节点可拖动
            isDraggable: true,
            flagAudit: false,
            flagNode: false,
            flagName: false,
            //角色
            roleList: [],
            documentList: [],

            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 流程表格数据
            flowList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                flowName: null,
                // flowType: null,
                flowStatus: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                flowName: [
                    { required: true, message: "请输入流程名称", trigger: "blur" },
                ],
                flowType: [
                    { required: true, message: "请选择类型", trigger: "change" },
                ]
            },
            lookFlag: false,
            typeList: []
        };
    },

    watch: {
        'queryParams.flowType': function (val) { //单个数据验证
        }
    },

    created() {
        this.getList();
        this.getSelectLists();
        getFlowTypeList().then(res => {
            const arr = Object.keys(res.data).map(v => {
                return {
                    key: v,
                    name: res.data[v]
                }
            })
            this.typeList = [...arr]
            this.typeMap = res.data;
        });
    },

    methods: {
        //流程
        handleStart(data) {
            const type = data.flowStatus === 'DISABLE' ? 'ENABLE' : 'DISABLE'
            const params = {
                id: data.id,
                flowStatus: type
            }
            const label = type === 'ENABLE' ? "启用" : "禁用"
            this.$modal.confirm('是否确认"' + label + data.flowName + '"数据').then(function () {
                return updateFlow(params);
            }).then(() => {
                this.$modal.msgSuccess(type === 'ENABLE' ? "启用成功" : "禁用成功");
                this.getList();
            }).catch(() => {
            });
        },

        getSelectLists() {
            getSelectList().then(response => {
                const { documents, roleList } = response.data
                this.documentList = documents
                this.roleList = roleList
            });
        },

        changeNodeName(record) {
            this.$refs.flowMaps.changeNodeName(record.flowNodeId, record.nodeName)
        },

        changeAuditRole() {

        },

        addFlowDesign() {
            const randomNum = Math.ceil(Math.random() * 1000 + 9000) + this.sortNumber
            const node = {
                id: randomNum,
                left: 0,
                top: 0,
                text: '',
                classList: ['node', 'jtk-endpoint-anchor', 'jtk-managed', 'jtk-draggable', 'jtk-connected', 'ui-draggable-handle', 'ui-draggable']
            }
            const nodeData = {
                flowNodeId: '_' + randomNum,
                approveRoleArr: null,
                docTypeIdArr: null,
                nodeName: null,
                // businessSurveyIdNum:null,
                // listSetups: [],
                flowDefinitionNodeId: randomNum,
                number: this.dataSourceNode.length + 1
            }
            this.$refs.flowMaps.addNode(node)
            this.dataSourceNode.push(nodeData)
            this.mapNode.set(nodeData.flowNodeId, nodeData)
            this.sortNumber++
        },

        deleteNode(nodes) {
            var index = 0
            var flag = false
            for (var ii = 0; ii < this.dataSourceNode.length; ii++) {
                const item = this.dataSourceNode[ii]
                if (item.flowNodeId === nodes.id) {
                    index = ii
                    flag = true
                } else if (flag) {
                    item.number -= 1
                }
            }
            this.dataSourceNode.splice(index, 1)
            this.mapNode.delete(nodes.id)
        },

        cancelFlow() {
            this.flowModalVisible = false
            this.$refs.flowMaps.clearNode()
        },

        saveData(params) {
            const nodeFlow = params.nodes
            const nodeRelation = params.ledges
            if (!nodeFlow || nodeFlow.length < 1) {
                this.$message.warning('数据错误,请刷新后重试')
                return
            }
            if (this.dataSourceNode.length === 0) {
                this.$message.warning('数据错误,请添加节点')
                return
            }
            const map = new Map()
            this.flagAudit = false
            this.flagNode = false
            this.flagName = false
            nodeFlow.forEach(node => {
                const id = node.id
                const nodeData = this.mapNode.get(id)
                if (nodeData === null || nodeData === undefined) {
                    this.flagNode = true

                    return
                }
                if (!nodeData.nodeName || !nodeData.nodeName.trim()) {
                    this.flagName = true
                    return
                }
                if (nodeData.approveRoleArr === null || nodeData.approveRoleArr === undefined || nodeData.approveRoleArr.length <= 0) {
                    this.flagAudit = true
                    return
                }
                // if (nodeData.docTypeIdArr === null || nodeData.docTypeIdArr === undefined||nodeData.docTypeIdArr.length<=0) {
                //     this.flagAudit = true
                //     return
                // }
                nodeData.x = node.left
                nodeData.y = node.top
                nodeData.flowNodeId = id
                nodeData.relation = []
                map.set(id, nodeData)
            })
            if (this.flagNode) {
                this.flagNode = false
                this.$message.warning('数据错误,请刷新页面后重试')
                return
            }
            else if (this.flagName) {
                this.flagName = false
                this.$message.warning('审批节点名称不能为空')
                return
            }
            else if (this.flagAudit) {
                this.flagAudit = false
                this.$message.warning('审批角色不能为空，请添加')
                // this.$message.warning('审批角色和文档类型不能为空，请添加')
                return
            }
            nodeRelation.forEach(nodeRela => {
                const source = nodeRela.id
                const target = nodeRela.nextId
                const targetNode = map.get(target)
                const sourceNode = map.get(source)
                if (!targetNode || !sourceNode) {
                    this.flagNode = true
                    return
                }
                sourceNode.relation.push(target)
            })
            if (this.flagNode) {
                this.flagNode = false
                this.$message.warning('数据错误,请刷新页面后重试')
                return
            }
            const listNode = []
            map.forEach(function (value, key) {
                listNode.push(value)
            })
            if (listNode.length < 1) {
                this.$message.warning('请添加节点数据')
                return
            }
            if (this.flowDesignLoading) {
                return
            }
            this.flowDesignLoading = true
            const listData = JSON.stringify(listNode)

            saveFlowData({ flowNodeList: listNode, flowId: this.flowDefinedId }).then(res => {
                if (res.code === 200) {
                    this.$message.success('操作成功')
                    this.$refs.flowMaps.clearNode()
                    this.flowModalVisible = false
                    this.getList()
                } else {
                    this.$message.error(res.message)
                }
                this.flowDesignLoading = false
            })
        },

        saveFlowDesign() {
            this.$refs.flowMaps.saveData()
        },

        showFlowDesign(data, type = '') {
            const { id } = data
            this.flowDefinedId = id
            this.relationList = []
            this.nodesList = []
            this.flowModalVisible = true
            this.$nextTick(function () {
                queryFlowNodesByFlowId(id).then(res => {
                    if (res.code === 200) {
                        // if (true) {
                        // const dataList = []
                        // const dataList = res.result.listNode ? res.result.listNode : []
                        const a = [
                            {
                                "nodeName": "2",
                                "number": 1,
                                "nextNodeId": 1140,
                                "flowNodeId": 1139,
                                "x": 100,
                                "y": 60,
                                // approveRoleArr: v.approveRoleArr,
                                // docTypeIdArr: v.docTypeIdArr,
                            },
                            {
                                "nodeName": "1",
                                "number": 2,
                                "flowNodeId": 1140,
                                "nextNodeId": null,
                                "x": 370,
                                "y": 30
                            }
                        ]

                        const newdata = res.data.map(v => {
                            return {
                                ...v,
                                x: v.x - 0,
                                y: v.y - 0
                            }
                        })

                        const dataList = newdata
                        this.dataSourceNode = []
                        this.mapNode = new Map()
                        if (dataList.length > 0) {
                            dataList.forEach(element => {
                                const flowNodeId = element.flowNodeId
                                const flowDefinitionNodeId = element.flowDefinitionNodeId
                                if (element.nextNodeId) {
                                    var relation = {
                                        source: 'right_' + flowNodeId,
                                        target: 'left_' + element.nextNodeId
                                    }
                                    this.relationList.push(relation)
                                }
                                var nodeId = this.mapNode.get('_' + flowNodeId)
                                if (nodeId == null) {
                                    var node = {
                                        top: element.y,
                                        left: element.x,
                                        id: element.flowNodeId,
                                        text: element.nodeName
                                    }
                                    this.nodesList.push(node)
                                    element.flowNodeId = '_' + element.flowNodeId
                                    this.dataSourceNode.push(element)
                                    this.mapNode.set('_' + flowNodeId, element)
                                }
                            })
                        }
                        type === 'look' ? this.lookFlag = true : this.lookFlag = false;
                    }
                })
            })
        },

        /** 查询流程列表 */
        getList() {
            this.loading = true;
            listFlow(this.queryParams).then(response => {
                this.flowList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                flowName: null,
                flowType: null,
                flowStatus: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加流程";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getFlow(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改流程";
            });
        },

        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateFlow(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addFlow(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除流程名为"' + row.flowName + '"的数据项？').then(function () {
                return delFlow(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('platform/base/flow/export', {
                ...this.queryParams
            }, `flow_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>


<style lang="scss" scoped>
.middleDialog {
    ::v-deep .el-dialog {
        width: 1200px !important;
    }
}
</style>