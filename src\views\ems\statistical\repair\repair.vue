<template>
  <div class="repair">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="7"
          >
            <div class="echarts-item" style="height: 130px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">故障统计</span>
              </div>
              <div class="statistical">
                <div class="fault">
                  <div style="display: flex; margin-top: 15px">
                    <div style="display: flex; flex-direction: column; margin: 10px 0 0 20px">
                      <span style="font-weight: 600; margin-bottom: 10px">总费用</span>
                      <div>
                        <span style="font-weight: 600; font-size: 18px; color: #cac27b">{{ basicInformation.upkeepCosts }}</span>
                      </div>
                    </div>

                    <div style="display: flex; flex-direction: column; margin: 10px 0 0 130px">
                      <span style="font-weight: 600; margin-bottom: 10px">故障次数</span>
                      <div>
                        <span style="font-weight: 600; font-size: 18px; color: #676767">{{ basicInformation.failureNumber }}</span>
                        <span style="color: #b4aeb1; margin-left: 5px">次</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="echarts-item" style="height: 50px">
              <div class="fault" style="margin-top: 2px ; margin-left: 110px">
                <el-button type="text" style="font-weight: 600; color: #259eff" @click="goRanking()"> 设备故障排名 </el-button>
              </div>
            </div>
          </el-col
          >
          <el-col :span="9"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">故障等级分布</span>
              </div>
              <div class="progress-box" style="margin-top: 20px" v-if="failureLevelData.length != 0" v-for="item in failureLevelData">
                <span class="width:20% ; float: left;" v-if="item.faultGrade == '0'">一般
                  <span class="pro_nums">{{ ((item.gradeNum / allNum) * 100).toFixed(2)}}%</span>
                </span>

                <span class="width:20% ; float: left;" v-if="item.faultGrade == '1'">严重
                  <span class="pro_nums">{{ ((item.gradeNum / allNum) * 100).toFixed(2)}}%</span>
                </span>
                <span class="width:20% ; float: left;" v-if="item.faultGrade == '2'">紧急
                  <span class="pro_nums">{{ ((item.gradeNum / allNum) * 100).toFixed(2)}}%</span>
                </span>
                <span class="width:20% ; float: left;" v-if="item.faultGrade == '3'">其他
                  <span class="pro_nums">{{ ((item.gradeNum / allNum) * 100).toFixed(2)}}%</span>
                </span>
                <div style="width:60% ; float: right; margin-top: 2px">
                  <el-progress v-if="item.faultGrade == '0'"  :percentage="item.gradeNum" :format="format"
                               color="#02b980"></el-progress>
                  <el-progress v-if="item.faultGrade == '1'" :percentage="item.gradeNum" :format="format"
                               color="#f29c38"></el-progress>
                  <el-progress v-if="item.faultGrade == '2'" :percentage="item.gradeNum" :format="format"
                               color="#D75746"></el-progress>
                  <el-progress v-if="item.faultGrade == '3'" :percentage="item.gradeNum" :format="format"
                               color="#D75746"></el-progress>
                </div>
              </div>
              <div class="progress-box" style="margin-left: 165px ; color: #9d9d9d; margin-top: 55px" v-if="failureLevelData.length == 0">
                <p style="font-size: 14px">暂无数据</p>
              </div>
            </div>
          </el-col>
<!--          <el-col :span="6"-->
<!--          >-->
<!--            <div class="echarts-item">-->
<!--              <div class="item-title">-->
<!--                <i class="icon-ziliao"></i>-->
<!--                <span style="font-weight: 600; color: #847f8c">故障类型分布</span>-->
<!--                <p>fault</p>-->
<!--              </div>-->
<!--              <div>-->
<!--                <fault-chart/>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-col>-->
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">故障类型分布</span>
              </div>
              <div>
                <fault-radar/>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="table-box">
      <IconTitle title="统计信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box" style="height: 270px">
        <el-row :gutter="10">
          <el-col :span="8"
          >
            <div class="echarts-item" style="height: 265px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">故障次数</span>
              </div>
              <div style="width: 100%">
                <repair-num />
              </div>
            </div>
          </el-col
          >
          <el-col :span="8"
          >
            <div class="echarts-item" style="height: 265px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">维修费用</span>
              </div>
              <div style="width: 100%">
                <repair-money/>

              </div>
            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item" style="height: 265px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">时间</span>
              </div>
              <div style="width: 100%">
                <repair-date />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="table-box">
      <IconTitle title="月度统计" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box" style="height: 344px">
        <monthly-statistics style="margin: -70px 0 0 -45px"/>
      </div>
    </div>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
// import faultChart from "./echarts/faultChart";
import faultRadar from "./echarts/faultRadar";
import repairNum from "./echarts/repairNum";
import repairMoney from "./echarts/repairMoney";
import repairDate from "./echarts/repairDate";
import monthlyStatistics from "./echarts/monthlyStatistics";
import {
  getFailureLevelDistributeList,
  getBasicInformationList
} from "@/api/ems/statistical/maintain"

export default {
  name: "repair",
  components: {
    IconTitle,
    // faultChart,
    faultRadar,
    repairNum,
    repairMoney,
    repairDate,
    monthlyStatistics
  },
  data() {
    return {
      failureLevelData: [],
      basicInformation: []
    }
  },
  mounted() {
    // 当屏幕改变，图表重新动态改变并渲染
    this.getFailureLevelDistributeList();
    this.getBasicInformationList();
  },
  methods: {

    getBasicInformationList() {
      getBasicInformationList().then(res => {
        this.basicInformation = res.data.data;
      });
    },

    getFailureLevelDistributeList() {
      getFailureLevelDistributeList().then(res => {
        this.failureLevelData = res.data.data; let a = 0;
        this.failureLevelData.forEach(function (item) {
          a += item.gradeNum;
        });
        this.allNum = a;
      });
    },

    format(percentage) {
      return `${percentage}`;
    },
    goRanking() {
      this.$router.push('/ems/statistical/repair/ranking')
    }
  }
}
</script>

<style scoped lang="less">
.repair {
  font-size: 12px;
}
</style>
