import request from '@/utils/request'

export function getIndustryTree(params) {
    return request({
        url: '/system/sysIndustryType/tree',
        method: 'get',
        params
    })
}
//根据企业名称模糊搜索企业
export function searchEnterprise(params) {
    return request({
        url: '/platform/serviceProvider/getInfoByName',
        method: 'get',
        params
    })
}

//提交认证
export function submitIdentify(data, method) {
    return request({
        url: '/platform/product',
        method: method || 'post',
        data
    })
}

export function getParkList(params) {
    return request({
        url: '/platform/park/getListByName',
        method: 'get',
        params
    })
}

export function getSubstitutionTree(params) {
    return request({
        url: '/system/sysIndustryKind/getSubstitutionTree',
        method: 'get',
        params
    })
}
