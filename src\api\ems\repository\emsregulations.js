import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/ems/emsregulations/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/ems/emsregulations',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/ems/emsregulations/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/ems/emsregulations/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/ems/emsregulations',
    method: 'put',
    data: obj
  })
}

// 规章制度树状图
export function getTree(query) {
  return request({
    url: '/ems/emsknocategory/tree',
    method: 'get',
    params: query
  })
}

export function getCatList(id) {
  return request({
    url: '/ems/emsrepairexceptions/getCatListByDeviceId/' + id,
    method: 'get'
  })
}
