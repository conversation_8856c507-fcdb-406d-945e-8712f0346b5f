export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  editBtn:false,
  searchIcon: false,
  "searchShow": false,
  "column": [
    {
      "type": "input",
      "label": "制度编号",
      "prop": "regulationCode",
      "span": 12,
      //search:true
    },
    {
      "type": "input",
      "label": "制度名称",
      "prop": "regulationName",
      "span": 12,
      // search:true
    },
    {
      "type": "input",
      "label": "是否置顶",
      "prop": "isNoTop",
      "span": 12,
      dicUrl:'/system/dict/type/isNoTop'
    },
    {
      "type": "input",
      "label": "分类",
      "prop": "categories",
      "span": 12
    },
    {
      "type": "input",
      "label": "创建人",
      "prop": "createBy",
      "span": 12,
    },
    // {
    //   "type": "input",
    //   "label": "发布范围",
    //   "prop": "releaseScopeName",
    //   "span": 20
    // },
    {
      "type": "datetime",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12,
      //"search": true
    },
    {
      "type": "input",
      "label": "说明",
      "prop": "remark",
      "span": 12
    },

    // {
    //   "type": "datetime",
    //   "label": "更新时间",
    //   "prop": "updateTime",
    //   //search:true,
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "更新人",
    //   "prop": "updateBy",
    //   //search:true,
    //   "span": 12
    // },

    // {
    //   "type": "input",
    //   "label": "制度类别",
    //   "prop": "regulationCategory",
    //   //search:true,
    //   "span": 12
    // },


    // {
    //   "type": "input",
    //   "label": "新闻内容",
    //   "prop": "newsContent",
    //   "span": 12
    // },

    // {
    //   "type": "input",
    //   "label": "删除标志",
    //   "prop": "delFlag",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "租户id",
    //   "prop": "tenantId",
    //   "span": 12
    // }
  ]
}
