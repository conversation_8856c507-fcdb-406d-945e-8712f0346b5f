<template>
  <div id="histogramDb" :style="{width: '30px', height: '39px'}"></div>
</template>

<script>

import {
  statusList,
} from "@/api/ems/statistical/device";

export default {
  data() {
    return {
      status: [],
      value: '',
    }
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.getStatusList();
  },
  methods: {

    // 状态统计
    getStatusList() {
      statusList().then(res => {
        this.status = res.data.data;
        this.value = this.status.badRunCount;
        this.drawLine();
      })
    },

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let histogramDb = this.$echarts.init(document.getElementById('histogramDb'))

      // 绘制图表
      histogramDb.setOption({
        xAxis: {
          axisLabel: {
            color: '#c0c3cd',
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          type: 'category',
          show: false
        },
        yAxis: {
          show: false
        },
        series: [
          {
            data: [this.value****],
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 8,
            itemStyle: {
              color: '#f88210',
            },
          },
          {
            data: [this.status.sum],
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 8,
            barGap: '-100%',
            zlevel: -1,
            itemStyle: {
              color: '#f6f6f6',
            },
          },
        ],

      });
    }
  }
}

</script>
