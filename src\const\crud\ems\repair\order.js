export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "editBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": false,
    "column": [
        {
            "type": "input",
            "label": "主键ID",
            "prop": "id",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "设备编号",
            "prop": "deviceNum",
            "span": 12
        },    {
            "type": "input",
            "label": "设备名称",
            "prop": "deviceName",
            "span": 12
        },	  {
            "type": "input",
            "label": "工单编号",
            "prop": "repairNum",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "异常项ID",
            "prop": "exceptionId",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "设备ID",
            "prop": "deviceId",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "故障分类ID",
            "prop": "falutCategoriesId",
            "span": 12,
            "hide": true
        },	  {
            "type": "input",
            "label": "设备状态",
            "prop": "deviceStatus",
            "span": 12,
            "slot":true
        },	  {
            "type": "input",
            "label": "故障等级",
            "prop": "faultGrade",
            "span": 12,
            "slot":true
        },	  /*{
            "type": "input",
            "label": "故障描述",
            "prop": "faultRemark",
            "span": 12
        },*/	  {
            "type": "input",
            "label": "状态",
            "prop": "status",
            "span": 12,
            "slot":true
        },	  {
            "type": "input",
            "label": "处理人",
            "prop": "handler",
            "span": 12
        },	  {
            "type": "input",
            "label": "当前步骤",
            "prop": "currentStep",
            "span": 12,
            "slot":true,
        },	  {
            "type": "input",
            "label": "维修方式",
            "prop": "repairMethod",
            "span": 12,
            "slot":true
        },	  {
            "type": "input",
            "label": "验收人",
            "prop": "checkName",
            "span": 12
        },	  {
            "type": "input",
            "label": "验收时间",
            "prop": "checkTime",
            "span": 12
        },	  {
            "type": "input",
            "label": "评分",
            "prop": "score",
            "span": 12
        },	  {
            "type": "input",
            "label": "验收说明",
            "prop": "checkRemark",
            "span": 12
        }]
}
