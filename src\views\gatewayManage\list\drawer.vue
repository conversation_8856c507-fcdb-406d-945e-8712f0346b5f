<template>
    <div class="appealHandleDetail" v-loading="loading" element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading">
        <div class="title_header">
            <el-button @click="$router.back()" type="text">
                <svg-icon icon-class="back" style="margin-right: 5px" />
                返回
            </el-button>
            <h3 class="title">安装驱动</h3>
        </div>
        <div class="content_wrap">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px"
                @submit.native.prevent>
                <el-form-item label="协议名称" prop="agreementName">
                    <el-input v-model="queryParams.agreementName" placeholder="请输入" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="设备类型" prop="driveId">
                    <el-select v-model="queryParams.driveId" placeholder="请选择" clearable>
                          <el-option v-for="dict in driveIdArr" :key="dict.id" :label="dict.driveName" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="20" class="cardDemo" v-if="dataList && dataList.length > 0">
                <el-col :span="6" style="margin-bottom: 24px;" v-for="item in dataList">
                    <div class="cardDemoCols">
                        <div class="cardDemoCentre">
                            <img src="@/assets/images/login-background.jpg" />
                            <div class="cardDemoText">
                                <el-tooltip effect="dark" content="Siemens-S7" placement="top">
                                    <div class="cardDemoTitle">{{ item.agreementName }}</div>
                                </el-tooltip>
                                <el-tooltip effect="dark" :content="item.driveName" placement="bottom">
                                    <div class="cardDemoContent">{{ item.driveName }}</div>
                                </el-tooltip>
                            </div>
                        </div>
                        <div class="cardDemoFooter">
                            <el-button plain class="installSuccess" disabled v-if="item.installStatus === 1"
                                type="success">已安装</el-button>
                            <el-button :loading="false"  plain class="install" @click="setInstallFuc(item)" v-else>安装</el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
            <el-empty description="暂无数据" v-else />

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getList" />

        </div>

    </div>
</template>
<script>


import { gatDriveList, getDrivetypes,installManage } from "@/api/gatewayManage/list/index.js";



export default {
    dicts: ['guide_rod_status'],
    data() {
        return {
            loading: false,
            queryParams: {
                page: 1, //page
                limit: 12, //limit
                agreementName: undefined,
                driveId: undefined,
            },
            driveIdArr: [],
            dataList: [],
            total:0,
            installBtn:false
        };
    },

    created() {
        const { query } = this.$route;
        getDrivetypes(query.id).then(res => {
            this.driveIdArr = res.data;
        });
        this.getList()
    },
    methods: {
        handleQuery() { 
            this.queryParams.page = 1;
            this.getList();
        },

        getList() {
            const { query } = this.$route;
            this.loading = true;
            const params = { ...this.queryParams, gatewayId:  query.id };
            gatDriveList(params).then(response => {
                const { list, total } = response.data
                this.dataList = list;
                this.total = total;
            }).finally(() => this.loading = false)
        },

        resetQuery() { 
            this.queryParams= {
                page: 1, //page
                limit: 12, //limit
                agreementName: undefined,
                driveId: undefined,
            }
            this.resetForm("queryParams");
            this.handleQuery();
        },

        setInstallFuc() {
            this.installBtn=true;
            installManage().then(res=>{
                this.$modal.msgWarning("暂未开通");
                //  this.getList();
            }).finally(() => this.installBtn = false)
         }
    },
};
</script>
<style lang="scss" scoped>
.appealHandleDetail {
    height: calc(100vh - 152px);
    /* height: 100vh; */
    padding: 28px 0 20px;
    background-color: #fff;
    border-radius: 16px;
    display: flex;
    flex-direction: column;

    .title_header {
        padding: 0 24px 12px;
        border-bottom: 1px solid #dbdfe9;

        .el-button {
            padding: 0;
            margin-right: 16px;
        }

        .title {
            display: inline-block;
            font-size: 18px;
            margin: 0;
            vertical-align: middle;
            padding-left: 20px;
            color: #181f2d;
            font-weight: bold;
            border-left: 1px solid #dbdfe9;
        }
    }

    .content_wrap {
        flex: 1;
        overflow: auto;
        padding: 24px;
        display: flex;
        flex-direction: column;


        .cardDemo {
            max-height: calc(100vh - 350px);
            width: 100%;
            overflow-y: auto;

            .cardDemoCols {
                height: 160px;
                display: flex;
                flex-direction: column;
                /* background: red; */
                border: 1px solid #DCDFE8;
                box-shadow: 0px 2px 8px #F3F5F8;
                border-radius: 8px;

                .cardDemoCentre {
                    padding: 24px 24px 0px;
                    display: flex;
                    flex: 1;
                    background-image: url('../../../assets/img/gateway-dealbagrd.png');
                    /* background-size: cover; */
                    background-size: 100% 100%;
                    background-repeat: no-repeat;

                    img {
                        width: 50px;
                        height: 50px;
                    }

                    .cardDemoText {
                        margin-left: 16px;
                        flex: 1;
                        white-space: nowrap;
                        overflow-x: hidden;
                        text-overflow: ellipsis;

                        .cardDemoTitle {
                            font-family: PingFang SC;
                            font-weight: bold;
                            font-size: 16px;
                            color: #515A6E;
                            margin-bottom: 8px;
                            white-space: nowrap;
                            overflow-x: hidden;
                            text-overflow: ellipsis;
                        }

                        .cardDemoContent {
                            font-family: PingFangSC-Regular;
                            font-size: 13px;
                            color: #515A6E;
                            white-space: nowrap;
                            overflow-x: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }

            .cardDemoFooter {
                height: 40px;
                background: #F3F5F8;
                display: flex;
                justify-content: end;
                align-items: center;
                box-shadow: 0px 2px 8px #F3F5F8;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top: 1px solid #DCDFE8;

                .install {
                    margin-right: 24px;
                    height: 28px;
                    background: rgba(1, 71, 235, 0.05);
                    border-radius: 0px;
                    border: 1px solid #0147EB;
                    font-size: 12px;
                }

                .installSuccess {
                    margin-right: 24px;
                    height: 28px;
                    font-size: 12px;
                }
            }

        }

    }

}</style>
  
  
  