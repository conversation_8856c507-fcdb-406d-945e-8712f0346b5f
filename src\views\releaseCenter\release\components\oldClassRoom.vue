<template>
    <div class="pageMainTop" v-loading="flagloading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
        <div class="app-containerPage">
            <div class="tagsTilte" style="margin-bottom: 15px;">课堂信息</div>
            <div class="pageMain" label-position="right">
                <div style="padding: 0px 0px 20px 0px">
                    <el-form :disabled="detailFlag" v-show="step === 1" :model="formOne" :rules="formOneRules" ref="formOne"
                        label-width="130px" class="demo-ruleForm">
                        <el-row>
                            <template>
                                <el-col :span="18">
                                    <el-form-item label="课程名称" prop="title">
                                        <el-input v-model="formOne.title" placeholder="请输入课程名称"
                                            :style="{ 'width': pageFormItemStyle.width }" />
                                    </el-form-item>
                                </el-col>
                              <!--<el-col :span="18">
                                  <el-form-item label="细分类别" prop="category">
                                      <el-select v-model="formOne.category" placeholder="请选择细分类别"
                                          :style="{ 'width': pageFormItemStyle.width }">
                                          <el-option v-for="dict in dict.type.course_category" :key="dict.value"
                                              :label="dict.label" :value="dict.value"></el-option>
                                      </el-select>
                                  </el-form-item>
                              </el-col>
                              <el-col :span="18">
                                  <el-form-item label="所属环节" prop="link">
                                      <el-select v-model="formOne.link" placeholder="请选择所属环节"
                                          :style="{ 'width': pageFormItemStyle.width }">
                                          <el-option v-for="dict in dict.type.course_link" :key="dict.value"
                                              :label="dict.label" :value="dict.value"></el-option>
                                      </el-select>
                                  </el-form-item>
                              </el-col>-->
                                <el-col :span="18">
                                    <el-form-item class="uploadItem" label="课程封面" prop="file">
                                        <el-upload class="avatar-uploader" accept="image/*"
                                            :before-upload="beforeUploadImage" :headers="headers" :action="actionurl"
                                            :show-file-list="false" :on-success="handleAvatarSuccess">
                                            <img v-if="imageUrl" :src="ensureFullUrl(imageUrl)" class="avatar">
                                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                        </el-upload>
                                        <span style="font-size: 12px; color: #9EA5B6;">支持扩展名：.jpg .img .png</span>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="18">
                                    <el-form-item label="讲师名称" prop="teacherNickName">
                                        <el-input v-model="formOne.teacherNickName" placeholder="请输入讲师名称"
                                            :style="{ 'width': pageFormItemStyle.width }" />
                                    </el-form-item>
                                </el-col>
                            </template>
                        </el-row>
                    </el-form>

                    <template>
                        <el-form style="padding-bottom: 50px;" v-show="step === 2" :model="formTwo" ref="formTwo"
                            :disabled="detailFlag" class="demo-ruleForm">
                            <el-form-item required>
                                <div v-for="(t, i) in formTwo.taskDataList" :key="i">
                                    <el-col :span="20" style="margin-bottom: 18px;position: relative;margin-top: 20px;">
                                        <div style="margin-bottom: 24px;">
                                            <el-row>
                                                <el-col :span="19">
                                                    <div @click="foldClick(t, i, 'fold')"
                                                        style="cursor:pointer;font-size: 16px;">
                                                        <span
                                                            :class="!t.fold ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"></span>
                                                        第<span class="tableDelete">{{ changeNumToHan(i + 1) }}</span>章节
                                                        <span style="font-weight: bold;" v-if="t.title">{{
                                                            t.title }}</span>
                                                    </div>
                                                </el-col>
                                                <el-col :span="4" style="margin-left: 13px;">
                                                    <el-popconfirm title="确定是否删除此章节？" @onConfirm="removeDomain(t)"
                                                        @confirm="removeDomain(t)">
                                                        <el-button class="el-icon-delete-solid tableDelete" slot="reference"
                                                            type="text">删除章节</el-button>
                                                    </el-popconfirm>
                                                </el-col>
                                            </el-row>
                                        </div>
                                        <div v-show="t.fold">
                                            <el-form-item label="节点名称" :prop="'taskDataList.' + i + '.title'" :rules="{
                                                required: true,
                                                message: '请输入节点名称',
                                                trigger: ['blur', 'change'],
                                            }">
                                                <el-input v-model="t.title" placeholder="请输入节点名称" style="width: 80%;" />
                                            </el-form-item>
                                            <div :class="collapseCustom">
                                                <el-collapse v-model="t.activeNames"
                                                    style="padding: 20px 0px 20px 0px;border-top:0px;border-bottom: 0px;">
                                                    <el-collapse-item v-for="(ct, ci) in t.videoInfoVOList" :key="ci"
                                                        :name="ci"
                                                        :class="(ci % 2) === 0 ? collapseEvenlar : collapseSingular">
                                                        <template slot="title">
                                                            <div
                                                                style="width: 100%;display: flex;justify-content: space-between;">
                                                                <div>
                                                                    <i
                                                                        :class="t.activeNames && t.activeNames.indexOf(ci) !== -1 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i>
                                                                    <span
                                                                        style="font-size: 14px;font-weight: bold;margin-left: 8px;"
                                                                        v-if="ct.title">{{ ct.title }}</span>
                                                                </div>
                                                                <div style="margin-right: 20px;"
                                                                    @click.stop="(event) => { stopBubbling(event) }">
                                                                    <el-popconfirm title="确定是否删除此课程任务？"
                                                                        @onConfirm="removeCourseFlag(t, ct, ci, i)"
                                                                        @confirm="removeCourseFlag(t, ct, ci, i)">
                                                                        <el-button class="el-icon-delete-solid tableDelete"
                                                                            slot="reference" type="text">删除</el-button>
                                                                    </el-popconfirm>
                                                                </div>
                                                            </div>
                                                        </template>
                                                        <el-row>
                                                            <el-col :span="24"
                                                                style="margin-bottom: 18px;position: relative; ">
                                                                <el-form-item label="任务名称"
                                                                    :prop="'taskDataList.' + i + '.videoInfoVOList.' + ci + '.title'"
                                                                    :rules="{
                                                                        required: true,
                                                                        message: '请输入任务名称',
                                                                        trigger: ['blur', 'change'],
                                                                    }">
                                                                    <el-input v-model="ct.title" placeholder="请输入任务名称"
                                                                        style="width: 80%;" />
                                                                </el-form-item>
                                                            </el-col>
                                                        </el-row>
                                                        <el-col :span="24" style="margin-bottom: 18px; ">
                                                            <el-form-item label="关联视频上传"
                                                                :prop="'taskDataList.' + i + '.videoInfoVOList.' + ci + '.videoPath'"
                                                                :rules="{
                                                                    required: true,
                                                                    message: '请上传视频',
                                                                    trigger: ['blur', 'change'],
                                                                }">
                                                                <el-upload :headers="headers" class="avatar-uploader"
                                                                    :action="actionurl" accept="video/*"
                                                                    :show-file-list="false" :on-success="function (response, file) {
                                                                        return handleVideoSuccess(response, file, ci, t, 'url')
                                                                    }" :on-progress="(response, file) => {
    return handleVideoSuccess(response, file, ci, t)
}" :before-upload="beforeUploadVideo">
                                                                    <video
                                                                        v-if="ct.videoPath != '' && ct.videoFlag == false"
                                                                        :src="ct.videoPath" class="avatar"
                                                                        controls="controls">您的浏览器不支持视频播放</video>
                                                                    <i v-else-if="ct.videoPath == '' && ct.videoFlag == false"
                                                                        class="el-icon-plus avatar-uploader-icon"></i>
                                                                    <!-- 进度条 -->
                                                                    <el-progress v-if="ct.videoFlag == true" type="circle"
                                                                        :percentage="ct.videoUploadPercent"
                                                                        style="margin-top:30px;"></el-progress>

                                                                </el-upload>
                                                            </el-form-item>
                                                        </el-col>
                                                    </el-collapse-item>
                                                </el-collapse>
                                            </div>
                                        </div>
                                        <el-button style="color: #1B4596;margin-left: 75px;" type="text"
                                            @click="addlearningdity(t)">
                                            <i class="el-icon-circle-plus-outline"></i>
                                            添加学习任务
                                        </el-button>
                                        <el-divider></el-divider>
                                    </el-col>
                                </div>
                            </el-form-item>
                            <el-col :span="19">
                                <el-button class="addChapter" type="text" @click="addonecommodity">
                                    <i class="el-icon-circle-plus-outline"></i>
                                    新增章节
                                </el-button>
                            </el-col>

                            <el-empty style="width: 100%;" v-if="formTwo.taskDataList.length <= 0"
                                description="暂无课程数据"></el-empty>
                        </el-form>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import {
 getChapterVideoByCourseId, editChapterAndVideo
} from "@/api/industryclassroom/courseinfo";
import { changeNumToHan } from '@/utils/ruoyi'
import iconImg from '@/assets/images/矩形.png'
import { getToken } from "@/utils/auth";

export default {
    dicts: ['course_link', 'course_category'],
    name: "UpdatCourse",
    props: {
        footerWidth: {
            type: String,
            default: '0px',
        },
    },
    data() {
        return {
            headers: {
                Authorization: "Bearer " + getToken()
            },
            collapseCustom: 'collapseCustom',
            collapseSingular: 'collapseSingular',
            collapseEvenlar: 'collapseEvenlar',
            Icon: iconImg,
            step: 1,
            formOneId: '',
            height: document.documentElement.clientHeight - 94.5 + "px;",
            formOne: {
                // title: '',
                // category: '',
                // link: '',
                // teacherNickName: '',
                // file: null
            },
            formOneRules: {
                title: [
                    { required: true, message: '请输入课程名称', trigger: 'blur' },
                ],
                category: [
                    { required: true, message: '请选择细分类别', trigger: 'change' }
                ],
                link: [
                    { required: true, message: '请选择所属环节', trigger: 'change' }
                ],
                teacherNickName: [
                    { required: true, message: '请输入讲师名称', trigger: 'change' }
                ],
                file: [{ required: true, message: "请课程封面", trigger: 'change' }],

            },
            categoryList: [],
            linkList: [],
            courseServiceList: [],
            fileList: [],
            imageUrl: '',
            actionurl: process.env.VUE_APP_BASE_API + "/system/file/upload",
            cacheData: [],
            formTwo: {
                taskDataList: []
            },
            detailFlag: (this.$route.query.pageType == 'detail' || this.$route.query.pageType == 'check') || false,
            flagloading: false
        };
    },
    created() {
    },
    watch: {
        'formTwo.taskDataList'(newName, oldName) {
        }
    },
    computed: {
        pageFormItemStyle() {
            return {
                width: '100%'
            }
        }
    },
    methods: {
        changeNumToHan,

        stopBubbling(event) {
            event.stopPropagation();
        },

        foldClick(t, i, key) {
            const arr = [...this.formTwo.taskDataList].map((v, index) => {
                if (index === i) {
                    return {
                        ...v,
                        [key]: !t[key]
                    }
                }
                else {
                    return v
                }
            })
            this.formTwo.taskDataList = arr
        },

        async handleVideoSuccess(res, file, ci, t, type) {
            let isMp4success = await this.getMp4Time(file.raw);
            const index = this.formTwo.taskDataList.indexOf(t);
            const arr = [...this.formTwo.taskDataList].map((v, i) => {
                if (i === index) {
                    return {
                        ...v,
                        videoInfoVOList: v.videoInfoVOList.map((sonItem, sonIndex) => {
                            if (sonIndex === ci) {
                                if (type === 'url') {
                                    return {
                                        ...sonItem,
                                        title: sonItem.title,
                                        videoPath: res.data.url,
                                        videoLength: isMp4success ? parseInt(isMp4success) : 0,
                                        videoFlag: false,
                                        videoUploadPercent: 0
                                    }
                                }
                                else {
                                    return {
                                        ...sonItem,
                                        title: sonItem.title,
                                        videoPath: '',
                                        videoFlag: true,
                                        videoUploadPercent: parseInt(file.percentage.toFixed(0))
                                    }
                                }
                            }
                            else {
                                return sonItem
                            }
                        })
                    }
                }
                else {
                    return { ...v }
                }
            })
            this.formTwo.taskDataList = [...arr]
            if (file.length !== 0) {
                this.$refs.formTwo.validateField(`taskDataList.${index}.videoInfoVOList.${ci}.videoPath`)
            }
        },


        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (['jpg', 'img', 'png'].indexOf(fileType) == -1) {
                this.$message.error('请上传后缀为.jpg .img .png格式的图片文件');
                return false;
            }
        },

        getMp4Time(file) {
            return new Promise(async (resolve, reject) => {
                let url = URL.createObjectURL(file);
                let audioElement = new Audio(url);
                let durtaion = 0;
                audioElement.addEventListener("loadedmetadata", function () {
                    durtaion = audioElement.duration;
                    fun(parseFloat(durtaion).toFixed(1))
                });
                let fun = (s) => {
                    durtaion = s;
                    resolve(durtaion)
                };
            })
        },

        beforeUploadVideo(file) {
            const isLt10M = file.size / 1024 / 1024 <= 100;
            if (['video/mp4', 'video/ogg', 'video/flv', 'video/avi', 'video/wmv', 'video/rmvb'].indexOf(file.type) == -1) {
                this.$message.error('请上传正确的视频格式');
                return false;
            }
            if (!isLt10M) {
                this.$message.error('上传视频大小不能超过100MB哦!');
                return false;
            }
        },

        removeCourseFlag(t, item, ci) {
            const index = this.formTwo.taskDataList.indexOf(t);
            const arr = [...this.formTwo.taskDataList].map((v, i) => {
                if (i === index) {
                    return {
                        ...v,
                        activeNames: [],
                        videoInfoVOList: v.videoInfoVOList.filter((sonItem, sonIndex) => {
                            if (sonIndex === ci) {
                                return false
                            }
                            else {
                                return true
                            }
                        })
                    }
                }
                else {
                    return { ...v }
                }
            })
            this.formTwo.taskDataList = [...arr]
        },

        removeDomain(item) {
            const index = this.formTwo.taskDataList.indexOf(item);
            if (index !== -1) {
                this.quantity = this.formTwo.taskDataList.length - 1;
                this.formTwo.taskDataList.splice(index, 1);
            }
        },

        addonecommodity() {
            this.quantity = this.formTwo.taskDataList.length + 1;
            this.formTwo.taskDataList.push({
                title: "",
                fold: true,
                courseFlag: true,
                activeNames: [],
            });
        },

        addlearningdity(item) {
            const index = this.formTwo.taskDataList.indexOf(item);
            const arr = [...this.formTwo.taskDataList]
            let childarr = arr[index].videoInfoVOList ? arr[index].videoInfoVOList : []
            childarr.push({
                title: "",
                videoPath: "",
                videoFlag: false,
                videoUploadPercent: '',
            });
            let arrALL = arr.map((t, i) => {
                if (i == index) {
                    const newarr = t.activeNames ? [...t.activeNames] : []
                    newarr.push(childarr.length - 1)
                    return {
                        ...t,
                        activeNames: [...newarr],
                        fold: true,
                        courseFlag: true,
                        videoInfoVOList: childarr.map((v, i) => {
                            return {
                                ...v,
                                index: i
                            }
                        })
                    }
                }
                else {
                    return t
                }
            })
            this.formTwo.taskDataList = [...arrALL]
        },

        readNodes(nodes = [], arr = []) {
            for (let item of nodes) {
                arr.push(item);
                if (item.videoInfoVOList && item.videoInfoVOList.length) {
                    this.readNodes(item.videoInfoVOList, arr);
                }
            }
            return arr;
        },

        getFormData(data) {
            const json1 = JSON.stringify(this.cacheData)
            const json2 = JSON.stringify(this.formTwo.taskDataList)
            if (json1 != json2) {
                this.$confirm('当前课程有修改暂未提交,是否确定返回上一步？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.getCourseInfoAll(data)
                }).catch(() => {
                });
            }
            else {
                this.getCourseInfoAll(data)
            }
        },
        getCourseInfoAll(data) {
            this.step = 1;
            this.formOne = {
                ...data
            }
            this.imageUrl = data.imagePath;
            this.formOneId = data.id
        },

        handleAvatarSuccess(res, file) {
            const { data } = res;
            this.imageUrl = URL.createObjectURL(file.raw);
            this.formOne.file = {
                imagePath: data.url,
            }
            if (file.length !== 0) {
                this.$refs.formOne.validateField('file')
            }
        },

        getFormTwoData(type, item) {
            this.flagloading = true;
            const newId = this.formOneId;
            this.step = 2;
            getChapterVideoByCourseId(newId).then(response => {
                this.formTwo.taskDataList = response.data.map((v, i) => {
                    return {
                        ...v,
                        courseFlag: true,
                        fold: (type === 'foldr' && v.id == item.id) ? true : false,
                        videoInfoVOList: v.videoInfoVOList.map(t => {
                            return {
                                ...t,
                                videoFlag: false,
                                videoUploadPercent: ''
                            }
                        })
                    }
                })
                this.cacheData = JSON.parse(JSON.stringify(this.formTwo.taskDataList));

            }).finally(() => this.flagloading = false)
        },

        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (formName == 'formOne') {
                        // return
                        const icCourseInfo = {
                            ...this.formOne,
                            imagePath: this.formOne.file.imagePath,
                            // id: newId
                        }
                        delete icCourseInfo.file
                        this.$emit('submit', icCourseInfo, (res) => {
                            console.log(res, '123123');
                            if (res && res.code == 200) {
                                if (!this.formOne.id) {
                                    const { businessId } = res.data; //只有新增接口才会有businessId返回值
                                    this.formOneId = businessId;
                                }
                                this.$nextTick(() => {
                                    this.getFormTwoData();
                                    this.$modal.msgSuccess("提交成功");
                                })
                            }
                        })
                    }
                    else {
                        const { taskDataList } = this.formTwo
                        const params = {
                            chapterList: taskDataList.map((v, i) => {
                                return {
                                    ...v,
                                    sort: i,
                                    videoInfoVOList: v.videoInfoVOList && v.videoInfoVOList.map((sonV, sonI) => {
                                        return {
                                            ...sonV,
                                            sort: sonI
                                        }
                                    })
                                }
                            }),
                            id: this.formOneId
                        }
                        const apiSet = editChapterAndVideo;
                        apiSet(params).then(response => {
                            this.$modal.msgSuccess("提交成功");
                            this.$emit('back')
                        }).finally((e) => {
                            this.$emit('submitFormVideoFlag', false);
                        })
                    }
                }
            });
        },
    }
};
</script>


<style  scoped>
.pageMainTop {
    width: 70%;
}

.avatar-uploader /deep/ .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader /deep/ .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.loadingDiv {
    left: -100px !important;
    min-height: calc(100vh - 300px);
}

.cardFdone /deep/ .el-card__body {
    padding: 0px !important;
}

.collapseCustom {
    margin-left: 75px;
    margin-right: 140px
}

.collapseCustom /deep/ .el-icon-arrow-right {
    display: none !important
}

.collapseCustom /deep/ .el-collapse-item__header {
    /* background-color: #F3F5F8 !important; */
    padding-left: 20px !important;
}

.collapseSingular /deep/ .el-collapse-item__header {
    background-color: #FFFFFF !important;
}

.collapseEvenlar /deep/ .el-collapse-item__header {
    background-color: #F3F5F8 !important;
}


.collapseCustom /deep/ .el-collapse-item__content {
    padding-top: 25px !important;
}

.pageMainOrs /deep/ .avatar-uploader {
    line-height: 0;

}


.collapseCustom /deep/.el-collapse-item__wrap {
    border: 0px !important
}

.addChapter {
    width: 110px;
    height: 32px;
    border: 1px solid #1B4596;
    opacity: 1;
    border-radius: 0px;
    color: #1B4596
}
</style>


