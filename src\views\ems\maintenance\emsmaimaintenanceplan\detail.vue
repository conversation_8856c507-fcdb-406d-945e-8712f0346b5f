<template>
  <div>
    <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>基本信息</span></div>
      <div class="devTitle"><span>{{form.planName}}</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">计划编号</div>
          <div class="contentS">{{form.planNum}}</div>
          <div class="labelS">计划名称</div>
          <div class="contentS">{{form.planName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">所属部门</div>
          <div class="contentS">{{form.deptName}}</div>
          <div class="labelS">负责人</div>
          <div class="contentS">{{form.liableUserName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">创建人</div>
          <div class="contentS">{{form.createBy}}</div>
           <div class="labelS">创建时间</div>
          <div class="contentS">{{form.createTime}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">开始执行时间</div>
          <div class="contentS">{{form.beginTime}}</div>
           <div class="labelS">结束执行时间</div>
            <div class="contentS">{{form.endTime}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">保养类型</div>
          <div class="contentS">日</div>
           <div class="labelS">保养周期</div>
            <div class="contentS">{{form.inspectCycle}}日</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">任务有效期</div>
          <div class="contentS">{{form.effectiveTime}}日</div>
           <div class="labelS">提前提醒时间</div>
            <div class="contentS">{{form.noticeTime}}日</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">提前生成时间</div>
          <div class="contentS">{{form.generateTime}}日</div>
           <div class="labelS">审核人</div>
            <div class="contentS">{{form.auditUserName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">是否启用</div>
          <div class="contentS">
              {{form.enableName}}
          </div>
          <div class="labelS">下次生成任务</div>
          <div class="contentS">
              {{form.nextGenerateTime}}
          </div>
        </div>

        <div class="tableStyle">
          <div class="labelS">说明</div>
          <div class="contentS">
              {{form.remark}}
          </div>
        </div>
      </div>
    </el-card>
    <el-card shadow="always" class="box-card">
        <IconTitle title="负责人" imgUrl="yunwei"></IconTitle>
        <el-table v-loading="user.loading" :data="userList">
            <el-table-column
                    label="序号"
                    width="70px">
                <template slot-scope="scope">
                    {{scope.$index+1}}
                </template>
            </el-table-column>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="名称" align="center" prop="name"/>
            <el-table-column label="性别"  align="center" prop="sex"/>
            <el-table-column label="证书"  align="center" prop="zs"/>
        </el-table>
    </el-card>
    <el-card shadow="always" class="box-card">
        <IconTitle title="关联设备" imgUrl="yunwei"></IconTitle>
        <el-table v-loading="device.loading" :data="deviceList">
            <el-table-column
                    label="序号"
                    width="50px">
                <template slot-scope="scope">
                    {{scope.$index+1}}
                </template>
            </el-table-column>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备编号" align="center" prop="deviceNum"/>
            <el-table-column label="设备名称" align="center" prop="deviceName"/>
            <el-table-column label="品牌" align="center" prop="brandNewName"/>
            <el-table-column label="规格型号" align="center" prop="specification"/>
            <el-table-column label="保养项目" width="350px">
                <template slot-scope="scope">
                    <span style="font-size: 5px">{{scope.row.maintenanceStandardName}}</span>
                    <el-button
                            size="mini"
                            type="text"
                            v-if="scope.row.maiItemsListOnOFF==1"
                            @click="spread(scope.row)"
                    >
                        <span style="color: #2d8cf0;margin-left: 5px">展开</span>
                    </el-button>
                    <el-button
                            size="mini"
                            type="text"
                            @click="packUp(scope.row)"
                            v-else-if="scope.row.maiItemsListOnOFF==2"
                    ><span style="color: #2d8cf0;margin-left: 5px">收起</span>
                    </el-button>
                    <div :style="scope.row.css" class="itemCss" style="height: 90px;overflow: auto">
                        <p v-for="(item,i) in scope.row.maiItemsList ">{{item}}</p>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="queryParams.total>0"
                :total="queryParams.total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="deviceGetList"
        />
    </el-card>
      <el-card shadow="always" class="info-btn-box">
          <el-button @click="goBack">返回</el-button>
      </el-card>

<!--      <div class="info-btn-box">-->
<!--         -->
<!--      </div>-->
  </div>

</template>

<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import {planGetObj, planAddObj, planPutObj,
        deviceListAll,devicePlanIdList} from '@/api/ems/maintenance/emsmaimaintenanceplan'
    import { getUser } from "@/api/system/user"
    import { getDept } from '@/api/system/dept'
    export default {
        name: "detailPlanIndex",
        components: {
            IconTitle,
        },
        props: {
            id: {
                type: String,
            },
        },
        data() {
            return {
                // 用户证书
                userList: [],
                user: {
                    loading: false,
                },
                // 设备数据
                deviceList: [],
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                loading: false,
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                //点检项目
                checkList: [],
                check: {
                    title: "",
                    open: false,
                    loading: false,

                },
                queryParamsCheckList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                device: {
                    title: "",
                    open: false,
                    loading: false,
                    deviceForm: {
                        id: null,
                        standardNum: null,
                        standardName: null,
                        deptId: null,
                        requirement: null,
                        remark: null,
                        deviceId: [],
                    }
                },
                form: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    deptName:null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    deviceId:[],
                },
                oldForm: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    deptName:null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    deviceId:[],
                },
                userForm:{
                    name:null,
                    sex:null,
                }
            };
        },
        mounted() {
            if (this.id > 0) {
                planGetObj(this.id).then((res) => {
                    this.oldForm = res.data
                    this.oldForm.enable=parseInt(this.oldForm.enable);
                    this.oldForm.inspectSettings=parseInt(this.oldForm.inspectSettings);
                    this.user.loading=true;
                    if (this.oldForm.liableUserId!=null) {
                        getUser(this.oldForm.liableUserId).then(res => {
                            this.oldForm.liableUserName = res.data.userName;
                            this.userList = [];
                            var o = {};
                            o.name = this.oldForm.liableUserName;
                            o.sex = "未知"
                            o.zs = "未知"
                            this.userList.push(o);
                            this.user.loading = false;
                        })
                    }
                    if (this.oldForm.auditUserId!=null){
                        getUser(this.oldForm.auditUserId).then(res =>{
                            this.oldForm.auditUserName=res.data.userName
                        })
                    }
                    if(this.oldForm.deviceIdList!=null){
                        this.device.loading=true;
                        this.deviceIdNewList = [];
                        this.deviceIdNewList=  this.oldForm.deviceIdList;
                        for (var i = 0; i <  this.deviceIdNewList.length; i++) {
                            this.device.deviceForm.deviceId.push( this.deviceIdNewList[i]);
                        }
                        deviceListAll(Object.assign(
                            {
                                current: this.queryParams.pageNum,
                                size: this.queryParams.pageSize,
                            },
                            {deviceId: this.device.deviceForm.deviceId}
                            )
                        ).then(response => {
                            this.deviceList = response.data.records;
                            for(let i=0;i<this.deviceList.length;i++){
                                if (this.deviceList[i].maiItemsList!=null){
                                    let maiItemsOldList =this.deviceList[i].maiItemsList;
                                    let onAndoffList =[];
                                    for(let j=0;j<maiItemsOldList.length;j++){
                                        let name = maiItemsOldList[j].itemsName+"("+maiItemsOldList[j].itemsNum+")"
                                        onAndoffList.push(name)
                                    }
                                    this.deviceList[i].maiItemsList=onAndoffList;

                                }
                                this.deviceList[i].css="display:none"
                                if (this.deviceList[i].maintenanceStandardName!=null){
                                    this.deviceList[i].maintenanceStandardName=this.deviceList[i].maintenanceStandardName+this.deviceList[i].maiItemsList.length+"项"
                                }
                            }
                            this.queryParams.total = response.data.total;
                            this.device.loading= false;
                        });
                    }
                    if (this.oldForm.deptId!=null){
                        getDept(this.oldForm.deptId).then((res) => {
                            this.oldForm.deptName=res.data.deptName;
                        });
                    }
                    this.form=this.oldForm;
                    this.form.enableName=this.oldForm.enable=="0"?"不启用":"启用"
                });

            }
            // fetchTree().then((response) => {
            //     this.treeData = response.data;
            // });
        },
        methods: {
            deviceGetList() {
                this.device.loading = true;
                deviceListAll(Object.assign(
                    {
                        current: this.queryParams.pageNum,
                        size: this.queryParams.pageSize,
                    },
                    {deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    for(let i=0;i<this.deviceList.length;i++){
                        if (this.deviceList[i].maiItemsList!=null){
                            let maiItemsOldList =this.deviceList[i].maiItemsList;
                            let onAndoffList =[];
                            for(let j=0;j<maiItemsOldList.length;j++){
                                let name = maiItemsOldList[j].itemsName+"("+maiItemsOldList[j].itemsNum+")"
                                onAndoffList.push(name)
                            }
                            this.deviceList[i].maiItemsList=onAndoffList;

                        }
                        this.deviceList[i].css="display:none"
                        if (this.deviceList[i].maintenanceStandardName!=null){
                            this.deviceList[i].maintenanceStandardName=this.deviceList[i].maintenanceStandardName+this.deviceList[i].maiItemsList.length+"项"
                        }
                    }
                    this.queryParams.total = response.data.total;
                    this.device.loading = false;
                });
            },
            packUp(row) {
                row.maiItemsListOnOFF= 1;
                row.css="display:none"
            },
            spread(row) {
                row.maiItemsListOnOFF= 2;
                row.css=""
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";
    .info-btn-box {
        width: 100%;
        text-align: center;
    }
.tableTitle {
  color: #333;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}
.devTitle {
  color: #262626;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
  .el-card__body {
    padding-top: 10px;
  }
  .labelS {
    //display: inline-block;
    flex:0 0 150px;
    //height: 40px;
    // margin-right: 10px;
    text-align: left;
    color: #606266;
    padding: 10px;
    border: 1px solid rgba(236, 240, 244, 100);
    margin-bottom: -1px;
  }
  .contentS {
    border: 1px solid rgba(236, 240, 244, 100);
    // height: 40px;
    color: #606266;
    width: 100%;
    margin-left: -1px;
    margin-bottom: -1px;
    padding: 10px;
    // margin: 10px 0;
    // width: calc(100% - 120px);
    // display: inline-block;
  }
  .tableStyle {
    display: flex;
  }
  .number{
      font-weight: bold;
      margin-top: 5px;
      font-size: 16px;
  }
}
</style>
