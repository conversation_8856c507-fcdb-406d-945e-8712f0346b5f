import { getHeaders } from "@/const/crud/getHeaders"

const headers = getHeaders();

const frontLabel = process.env.VUE_APP_BASE_API;

export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "editBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
  "column": [
	  {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
        hide: true
    },	  {
      "type": "input",
      "label": "计划编号",
      "prop": "planNum",
      "span": 15,
      "search":true
    },	  {
      "type": "input",
      "label": "计划名称",
      "prop": "planName",
      "span": 12,
      "search":true
    },
    // {
    //   "type": "input",
    //   "label": "部门id",
    //   "prop": "deptId",
    //   "span": 12
    // },	  {
    //   "type": "input",
    //   "label": "责任人",
    //   "prop": "liableUserId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "审核状态",
      "prop": "status",
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/plan_check_status`,
      dicHeaders: headers
    },	  {
      "type": "input",
      "label": "巡检周期",
      "prop": "inspectCycle",
      "span": 12
    },	  	  {
      "type": "input",
      "label": "任务有效时间",
      "prop": "effectiveTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "提前生成时间",
      "prop": "generateTime",
      "span": 15
    },	   {
      "type": "input",
      "label": "提前提醒时间",
      "prop": "noticeTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "计划开始时间",
      "prop": "beginTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "计划结束时间",
      "prop": "endTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "上次生成时间",
      "prop": "lastGenerateTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "下次生成时间",
      "prop": "nextGenerateTime",
      "span": 15
    },	  {
      "type": "input",
      "label": "是否启用",
      "prop": "enable",
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/ems_inspect_plan_enable`,
      dicHeaders: headers
    },	 	  {
      "type": "input",
      "label": "创建者",
      "prop": "createBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12
    },	 ]
}
