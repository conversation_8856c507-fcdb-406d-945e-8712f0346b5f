<template>
  <div class="details_view">
    <el-card style="border-radius: 10px">
      <div class="details_data">
        <IconTitle class="selfTitle" title="资料信息" imgUrl="yunwei"/>
        <h3>{{ information.dataName }}</h3>
        <table class="table_details" border="1">
          <tr>
            <th>资料编号</th>
            <td>{{ information.dataNo }}</td>
            <th>资料名称</th>
            <td>{{ information.dataName }}</td>
          </tr>
          <tr>
            <th>上传人</th>
            <td>{{ information.createBy }}</td>
            <th>上传时间</th>
            <td>{{ information.createTime }}</td>
          </tr>
          <tr>
            <th>资料类型</th>
            <td>{{ information.categoryName }}</td>
            <th>文档密级</th>
            <td v-if="information.documentEncryptionLevel == 0">无</td>
            <td v-if="information.documentEncryptionLevel == 1">低</td>
            <td v-if="information.documentEncryptionLevel == 2">中</td>
            <td v-if="information.documentEncryptionLevel == 3">高</td>
          </tr>
          <tr>
            <th>说明</th>
            <td colspan="3">{{ information.remark }}</td>
          </tr>
        </table>
      </div>
    </el-card>

    <el-card style="border-radius: 10px;margin-top: 15px">
      <div class="details_attachment">
        <IconTitle class="selfTitle" title="相关附件" imgUrl="yunwei"/>
        <el-table
            :data="fileArray"
            border
            :header-row-style="{color: '#343141'}"
            style="width: 100%; margin-top: 10px">
          <el-table-column
              prop="original"
              label="文件名称"
              width="240">
          </el-table-column>
          <el-table-column
              prop="type"
              label="文件类型"
              width="180">
          </el-table-column>
          <el-table-column
              prop="fileSize"
              label="文件大小">
          </el-table-column>
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="查看次数">-->
          <!--          </el-table-column>-->
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="下载次数">-->
          <!--          </el-table-column>-->
          <el-table-column
              prop="createTime"
              label="上传时间">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-search"
                  @click="selectFile(scope.row)"
              >查看
              </el-button>
              <el-button
                  type="text"
                  size="small"
                  icon="el-icon-download"
                  @click="download(scope.row, scope.index)"
              >下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-dialog title="预览" :visible.sync="viewVisible" width="50%" height="30%">
          <pdf
              :src="url"
              :page="pdfPage"
              @num-pages="pdfPageCount = $event"
              @page-loaded="pdfPage = $event"
          ></pdf>
          <!-- 上下翻页 -->
          <button @click="previousPage" style="float: left;">上一页</button>
          <button @click="nextPage" style="float: right">下一页</button>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="viewVisibleImg" width="50%" height="30%">
          <img :src="url" style="width: 100%; height: 100%">
        </el-dialog>

      </div>
    </el-card>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getObj} from "@/api/ems/repository/emsdevicedata"
import {changeByte} from "@/util/changeByte"
import pdf from 'vue-pdf'

export default {
  name: "detail",
  data() {
    return {
      // 下载文件
      searchForm: {
        fileName: ''
      },
      // 资料信息数据
      information: [],
      // 相关附件
      fileArray: [],
      viewVisible: false,
      viewVisibleImg: false,
      pdfPage: 1,
      pdfPageCount: 1,
      url: ''
    }
  },
  components: {
    IconTitle,
    pdf
  },
  mounted() {
    this.getListData();
  },
  methods: {
    // 获取基本数据
    getListData() {
      let id = this.$route.query.id;
      getObj(id).then(res => {
        this.information = res.data.data;
        this.fileArray = res.data.data.fileArray;
        for (let i = 0; i <= this.fileArray.length; i++) {
          this.fileArray[i].fileSize = changeByte(this.fileArray[i].fileSize);
        }
      })
    },
    // 查看功能
    selectFile(row) {
      if (row.type == 'pdf') {
        this.viewVisible = true;
        this.url = row.url;
      }
      if (row.type == 'jpg' || row.type == 'jpeg' || row.type == 'png') {
        this.viewVisibleImg = true;
        this.url = row.url;
      }
    },

    // 上一页
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pdfPageCount;
      this.pdfPage = p;
    },
    // 下一页
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pdfPageCount ? p + 1 : 1;
      this.pdfPage = p;
    },

    // 下载功能
    download: function (row, index) {
      this.downBlobFile(
          "/admin/sys-file/" + row.bucketName + "/" + row.fileName,
          this.searchForm,
          row.fileName
      );
    },
  }
}
</script>

<style scoped lang="less">
.details_view {
  .details_data {
    .table_details {
      border: 1px solid #e8eef4;
      font-size: 12px;
      width: 1237px;
      height: 137px;

      td {
        padding-left: 10px;
      }
    }
  }

  .details_attachment {
    .selfTitle {
      margin-bottom: 10px;
    }
  }
}
</style>
