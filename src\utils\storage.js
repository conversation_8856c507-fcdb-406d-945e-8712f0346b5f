
export const Storage = {
	setItem(key, value) {
		if (value === undefined) {
			return
		}
		window.localStorage.setItem(key, JSON.stringify(value))
	},
	getItem(key) {
		let json = window.localStorage.getItem(key)
		return JSON.parse(json)
	},
	removeItem(key) {
		window.localStorage.removeItem(key)
	},
	clear() {
		window.localStorage.clear()
	}
}


export const SessionStorage = {
	setItem(key, value) {
		if (value === undefined) {
			return
		}
		window.sessionStorage.setItem(key, JSON.stringify(value))
	},
	getItem(key) {
		let json = window.sessionStorage.getItem(key)
		return JSON.parse(json)
	},
	removeItem(key) {
		window.sessionStorage.removeItem(key)
	},
	clear() {
		window.sessionStorage.clear()
	}
}
