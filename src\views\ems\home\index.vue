<!--
 * @Author: your name
 * @Date: 2021-11-12 10:24:16
 * @LastEditTime: 2021-12-16 16:32:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \gwcloud-ui\src\page\wel.vue
-->
<template>
  <div>
    <!-- 顶部 -->
    <div class="banner-text the_top">
      <avue-data-tabs :option="option" style="width: 50% ;  float: left;"></avue-data-tabs>
      <avue-data-tabs :option="options" style="width: 50% ;  float: left;"></avue-data-tabs>
    </div>
    <!-- 报修工单月曲线 -->
    <div class="repairs">
      <div class="repairs_tle">
        报修工单月曲线
        <img class="tle_icon" src="@/static/svg/icon-gwshujufenxi.svg">
      </div>
      <div id="workOrder"></div>
    </div>
    <!-- 设备状态分析 -->
    <div class="dev_stastus">
      <div class="dev_tle">巡检保养分析</div>

      <div class="dev_content">
        <div style="width: 100%; height: 175px;">
          <div style="width: 50% ;float: left ">
            <div style="margin-bottom: 30px">
              <span style="margin-left: 40px;margin-top: 10px">巡检任务总数</span>
              <span style="color: #5892ff; font-weight: 700 ; margin-left: 40px ; font-size: 16px">{{
                  inspectionMaintenance.insInspectTaskCount
                }}</span>
            </div>
            <div style="float: left;width: 170px">
              <statistical style="margin-left: 40px;margin-top: 15px" class="statistical_equipmentKanban"/>
            </div>
            <div style="display: flex">
              <!--<avue-data-icons :option="pollingData"></avue-data-icons>-->
              <div style="display: flex; flex-direction: column; margin-left: 10px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">未开始</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    inspectionMaintenance.noStartCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">待核验</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    inspectionMaintenance.toCheckCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">执行中</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    inspectionMaintenance.executionCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">已完成</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    inspectionMaintenance.completedCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">已过期</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    inspectionMaintenance.staleCount
                  }}</span>
              </div>
            </div>
          </div>
          <div style="width: 50% ; float: right">
            <div style="margin-bottom: 30px">
              <span style="margin-left: 40px;margin-top: 10px">保养任务总数</span>
              <span style="color: #5892ff; font-weight: 700 ; margin-left: 40px ; font-size: 16px">{{
                  maintenance.maintenanceTaskCount
                }}</span>
            </div>
            <div style="float: left;width: 170px;height: 200px">
              <maintenanceStatistical style="margin-left: 40px;margin-top: 15px"/>
            </div>
            <!--<div>-->
            <!--  &lt;!&ndash;<avue-data-icons :option="maintainData"></avue-data-icons>&ndash;&gt;-->
            <!--</div>-->
            <div style="display: flex">
              <!--<avue-data-icons :option="pollingData"></avue-data-icons>-->
              <div style="display: flex; flex-direction: column; margin-left: 10px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">未开始</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    maintenance.noStartCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">待核验</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    maintenance.toCheckCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">执行中</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    maintenance.executionCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">已完成</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    maintenance.completedCount
                  }}</span>
              </div>
              <div style="display: flex; flex-direction: column; margin-left: 50px; margin-top: 15px">
                <img src="../../../../public/ems/img/home/<USER>" height="40px" width="40px"/>
                <span style="color: #acacac; font-size: 12px; margin-top: 15px; margin-bottom: 15px">已过期</span>
                <span style="color: #3fa1ff; font-size: 20px; text-align: center; width: 30px;">{{
                    maintenance.staleCount
                  }}</span>
              </div>
            </div>
          </div>
        </div>

      </div>

    </div>
    <!-- 设备状态/设备完好率/设备利用率排行 -->
    <div class="dev_usage">
      <div class="availability">
        <div class="register-senoud">
          <div class="ratio_date">
            <div class="ratio_title">设备状态<img class="tle_icon" src="@/static/svg/icon-gwbeijianshiyongshuai-copy.svg">
            </div>
          </div>
          <div class="mallapp_content">
            <div class="mallapp_pro" style="margin-top: 40px">
              <span class="pro_tle availa_tle" style="width: 60px;">正常</span>
              <el-progress
                  :style="'width:60%;  margin-left: 10px'"
                  color="#1890FF"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.normalCount"
              />
              <span class="pro_num">{{ deviceStatusData.normalCount }}</span>
            </div>

            <div class="mallapp_pro">
              <span class="pro_tle availa_tle" style="width: 60px;">带病运行</span>
              <el-progress
                  color="#E7A23D"
                  :style="'width:60%; margin-left: 10px'"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.badRunCount"
              />
              <span class="pro_num">{{ deviceStatusData.badRunCount }}</span>
            </div>

            <div class="mallapp_pro">
              <span class="pro_tle availa_tle" style="width: 60px;">故障</span>
              <el-progress
                  color="#67C23A"
                  :style="'width:60%; margin-left: 10px'"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.malfunctionCount"
              />
              <span class="pro_num">{{ deviceStatusData.malfunctionCount }}</span>
            </div>

          </div>

        </div>
      </div>
      <div id="goodCondition" style="margin-top: 44px; border-top: 1px solid #e6e6e6"></div>
      <div class="usage">
        <div class="register-senoud">
          <div class="ratio_date">
            <div class="ratio_title">设备使用状态<img class="tle_icon" src="@/static/svg/icon-gwshujupaihangbang.svg"></div>

          </div>
<!--          <div>-->
<!--            <repair-num/>-->
<!--          </div>-->
          <div class="mallapp_content">

            <div class="mallapp_pro" style="margin-top: 40px">
              <span class="pro_tle availa_tle" style="width: 60px;">在用</span>
              <el-progress
                  :style="'width:60%;  margin-left: 10px'"
                  color="#1890FF"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.normalCount"
              />
              <span class="pro_num">{{ deviceStatusData.normalCount }}</span>
            </div>

            <div class="mallapp_pro">
              <span class="pro_tle availa_tle" style="width: 60px;">闲置</span>
              <el-progress
                  color="#E7A23D"
                  :style="'width:60%; margin-left: 10px'"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.badRunCount"
              />
              <span class="pro_num">{{ deviceStatusData.badRunCount }}</span>
            </div>

            <div class="mallapp_pro">
              <span class="pro_tle availa_tle" style="width: 60px;">出租</span>
              <el-progress
                  color="#67C23A"
                  :style="'width:60%; margin-left: 10px'"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="deviceStatusData.malfunctionCount"
              />
              <span class="pro_num">{{ deviceStatusData.malfunctionCount }}</span>
            </div>


            <!--            <div class="mallapp_pro">-->

            <!--              <el-progress-->
            <!--                :style="'width:' + 70 / (100 / 50) + '%'"-->
            <!--                color="#1890FF"-->
            <!--                :text-inside="true"-->
            <!--                :stroke-width="26"-->
            <!--                :percentage="50"-->
            <!--              />-->

            <!--              <span class="pro_tle">空压机LJ-100：87%</span>-->
            <!--            </div>-->

            <!--            <div class="mallapp_pro">-->

            <!--              <el-progress-->
            <!--                color="#E7A23D"-->
            <!--                :style="'width:' + 70 / (100 / 20) + '%'"-->
            <!--                :text-inside="true"-->
            <!--                :stroke-width="26"-->
            <!--                :percentage="20"-->
            <!--              />-->

            <!--              <span class="pro_tle">高温摩擦焊接JK093：60%</span>-->
            <!--            </div>-->

            <!--            <div class="mallapp_pro">-->

            <!--              <el-progress-->
            <!--                color="#67C23A"-->
            <!--                :style="'width:' + 70 / (100 / 80) + '%'"-->
            <!--                :text-inside="true"-->
            <!--                :stroke-width="26"-->
            <!--                :percentage="80"-->
            <!--              />-->

            <!--              <span class="pro_tle">马扎克机床l00931：43%</span>-->
            <!--            </div>-->

            <!--            <div class="mallapp_pro">-->

            <!--              <el-progress-->
            <!--                color="#00C0DE"-->
            <!--                :style="'width:' + 70 / (100 / 68) + '%'"-->
            <!--                :text-inside="true"-->
            <!--                :stroke-width="26"-->
            <!--                :percentage="68"-->
            <!--              />-->

            <!--              <span class="pro_tle">三菱机床u0021：43% </span>-->
            <!--            </div>-->

            <!--            <div class="mallapp_pro">-->

            <!--              <el-progress-->
            <!--                color="#101010"-->
            <!--                :style="'width:' + 70 / (100 / 30) + '%'"-->
            <!--                :text-inside="true"-->
            <!--                :stroke-width="26"-->
            <!--                :percentage="30"-->
            <!--              />-->

            <!--              <span class="pro_tle">三菱线切割32sd：43%</span>-->
            <!--            </div>-->
          </div>

        </div>
      </div>
    </div>
    <!-- 即将超期保养任务/即将超期巡检任务 -->
    <div class="immediately">
      <div class="maintain_task">
        <div class="task_tle">即将超期保养任务<img class="tle_icon" src="@/static/svg/icon-gwtask.svg"></div>
        <div class="task_table">
          <el-table
                  :data="maiTask"
                  style="width: 100%"
                  :header-cell-style="{ background: '#f8f8f9', color: '#262626' }"
          >
            <el-table-column prop="taskNum" label="任务编号" width="130">
            </el-table-column>
            <el-table-column prop="planNum" label="计划编号" width="130">
            </el-table-column>
            <el-table-column prop="liableUserId" label="负责人"></el-table-column>
            <el-table-column prop="planEndTime" label="超时日期"></el-table-column>
            <el-table-column prop="deptId" label="所属部门"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="polling_task">
        <div class="task_tle">即将超期巡检任务<img class="tle_icon" src="@/static/svg/icon-gwxunjianguiji.svg"></div>
        <div class="task_table">
          <el-table
                  :data="insTask"
                  style="width: 100%"
                  :header-cell-style="{ background: '#f8f8f9', color: '#262626' }"
          >
            <el-table-column prop="taskNum" label="任务编号" width="130">
            </el-table-column>
            <el-table-column prop="planNum" label="计划编号" width="130">
            </el-table-column>
            <el-table-column prop="liableUserId" label="负责人"></el-table-column>
            <el-table-column prop="planEndTime" label="超时日期"></el-table-column>
            <el-table-column prop="deptId" label="所属部门"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {
  statusList,
  useStatusList,
} from "@/api/ems/statistical/device";
import {
  getHomeStatisticsList,
  getHomeStatisticsList2,
  getInspectionMaintenance,
  getMaintenance,
  getMonOrder
} from "@/api/home/<USER>";

import echarts from "echarts";
import statistical from "./echarts/statistical";
import maintenanceStatistical from "./echarts/maintenanceStatistical";
import repairNum from "./echarts/repairNum";
import {RepairworkOrder, Servicestatus, Servicerice} from "@/util/echartsAll";
import {
  todayTasklist
} from "@/api/ems/inspection/task";
import {todayMaiTasklist}
  from "@/api/ems/maintenance/emsmaimaintenancetask";

export default {
  name: "Wel",
  components: {
    repairNum,
    statistical,
    maintenanceStatistical
  },
  data() {
    return {
      tableData: [],
      activeNames: ["1", "2", "3", "4"],
      DATA: [],
      homeData1: [],
      homeData2: [],
      deviceStatusData: [],
      inspectionMaintenance: [],
      maintenance: [],
      text: "",
      actor: "",
      monMainData: [],
      count: 0,
      isText: false,
      useStatusData: [],
      option: {
        data: [
          {
            title: '设备使用',
            count: 0,
            subtitle: '统计',
            allcount: 0,
            text: '当前设备总数',
            color: 'rgb(27, 201, 142)',
            key: '备'
          },
          {
            title: '已报修',
            subtitle: '统计',
            count: 0,
            allcount: 0,
            text: '故障次数',
            color: 'rgb(230, 71, 88)',
            key: '障'
          },
          {
            // click: function (item) {
            //   alert(JSON.stringify(item));
            // },
            title: '今日完成保养',
            subtitle: '统计',
            count: 0,
            allcount: 0,
            text: '今日需保养总数',
            color: 'rgb(178, 159, 255)',
            key: '养'
          }
        ]
      },
      options: {
        data: [
          {
            title: '今日完成巡检',
            subtitle: '统计',
            count: 0,
            width: "16%",
            allcount: 0,
            text: '今日需巡检总数',
            color: 'rgb(248,149,136)',
            key: '检'
          },
          {
            title: '已验收工单',
            subtitle: '统计',
            count: 0,
            allcount: 0,
            text: '工单总数',
            color: 'rgb(120,152,225)',
            key: '单'
          },
          {
            title: '实施人员',
            subtitle: '统计',
            count: 0,
            allcount: 0,
            text: '总人员',
            color: 'rgb(124,214,207)',
            key: '人'
          },

        ]
      },
      gateWayApp: {
        msg: "客户页面-获取集团下网关应用数量成功",
        code: 0,
        list: 28,
        offlinelist: 4,
        onlinelist: 14,
        online: "50%",
        offline: "14.285714285714285%",
        other: "35.714285714285715%",
      },
      maiTask:[],
      insTask:[],
    };
  },
  computed: {
    ...mapGetters(["website"]),
  },
  mounted() {
    this.getAmountList();
    this.getHomeStatistics();
    this.getHomeStatistics2();
    this.getInspectionMaintenanceList();
    this.getMaintenanceList();
    this.get();
    this.getToDayTask()
  },
  methods: {
    getToDayTask(){
      todayMaiTasklist().then(response => {
        this.maiTask = response.data.data;
      });
      todayTasklist().then(response => {
        this.insTask = response.data.data;
      });

    },

    getInspectionMaintenanceList() {
      getInspectionMaintenance().then(res => {
        this.inspectionMaintenance = res.data.data;
      });
    },
    getMaintenanceList() {
      getMaintenance().then(res => {
        this.maintenance = res.data.data;
      });
    },

    getHomeStatistics() {
      getHomeStatisticsList().then(res => {
        this.homeData1 = res.data.data;
        this.option.data[0].allcount = this.homeData1.deviceCount;
        this.option.data[0].count = this.homeData1.useCount;
        this.option.data[1].allcount = this.homeData1.malfunctionCount;
        this.option.data[1].count = this.homeData1.repairsCount;
        this.option.data[2].allcount = this.homeData1.finishedNum;
        this.option.data[2].count = this.homeData1.CompleteMaintenanceCount;
      });
    },
    getHomeStatistics2() {
      getHomeStatisticsList2().then(res => {
        this.homeData2 = res.data.data;
        this.options.data[0].allcount = this.homeData2.allNum;
        this.options.data[0].count = this.homeData2.todayInspectionCount;
        this.options.data[1].allcount = this.homeData2.maintenanceOrderCount;
        this.options.data[1].count = this.homeData2.statusCount;
        this.options.data[2].allcount = this.homeData2.allUserCount;
        this.options.data[2].count = this.homeData2.implementerCount;
      });
    },


    getAmountList() {
      statusList().then(res => {
        this.deviceStatusData = res.data.data;
      });
    },

    getData() {
      if (this.count < this.DATA.length - 1) {
        this.count++;
      } else {
        this.count = 0;
      }
      this.isText = true;
      this.actor = this.DATA[this.count];
    },
    get() {
      const mycharts = echarts.init(document.getElementById("workOrder"));
      getMonOrder().then(res => {
        this.monMainData = res.data.data;
        // console.log("111>>>", JSON.stringify(this.monMainData));
        let xData = [];
        let yData = [];
        let lastYData = [];
        let lastXData = [];

        this.monMainData.showNumList.forEach(item => {
          xData.push(Number(item.mon))
          yData.push(item.num)
        });

        this.monMainData.lastMonOrder.forEach(item => {
          lastXData.push(Number(item.mon))
          lastYData.push(item.num)
        });

        RepairworkOrder(mycharts, this.monMainData, xData, yData, lastXData, lastYData);
      });


      // const equipment = echarts.init(document.getElementById("equipment"));
      // Servicestatus(equipment, ["1"], [], []);

      const goodCondition = echarts.init(
          document.getElementById("goodCondition")
      );
      let data = [];
      statusList().then(res => {
        data = res.data.data;
        Servicerice(goodCondition, data, [], []);
      });

    },
    setData() {
      let num = 0;
      let count = 0;
      let active = false;
      const timeoutstart = 5000;
      const timeoutend = 1000;
      const timespeed = 10;
      setInterval(() => {
        if (this.isText) {
          if (count == this.actor.length) {
            active = true;
          } else {
            active = false;
          }
          if (active) {
            num--;
            this.text = this.actor.substr(0, num);
            if (num == 0) {
              this.isText = false;
              setTimeout(() => {
                count = 0;
                this.getData();
              }, timeoutend);
            }
          } else {
            num++;
            this.text = this.actor.substr(0, num);
            if (num == this.actor.length) {
              this.isText = false;
              setTimeout(() => {
                this.isText = true;
                count = this.actor.length;
              }, timeoutstart);
            }
          }
        }
      }, timespeed);
    },
  },
};
</script>

<style scoped="scoped" lang="scss">


.tle_icon {
  width: 24px;
  height: 24px;
  margin-left: 3px;
}

::v-deep .avue-tags {
  display: none !important;
}

@mixin top_icon($color1, $color2) {
  width: 28px;
  height: 22px;
  line-height: 18px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  color: $color1;
  background-color: $color2;
  border: 1px solid $color2;
}

@mixin top_title() {

  display: flex;
  align-items: center;
  padding: 8px 0;
  color: #87888a;
  font-size: 14px;
  border-bottom: 1px solid #e6e6ee;
  padding-left: 20px;
  margin-bottom: 12px;
}

.banner-text {
  //display: flex;
  justify-content: space-between;
  border-radius: 10px;
  background: #fff;

  .top_item {
    width: 20%;
    border-right: 1px solid #e6e6ee;
    padding: 10px 0px 15px 0px;
    box-sizing: border-box;

    .col_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #87888a;
      padding: 0 20px;
      box-sizing: border-box;

      img {
        width: 60px;
        height: 60px;
      }
    }

    .fulfilled {
      font-size: 14px;
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 10px;
      box-sizing: border-box;

      :last-child {
        @include top_icon(#389e0d, #6dbef8);
      }
    }

    .line {
      :last-child {
        @include top_icon(#f284ef, #fbd3f8);
      }
    }

    .day {
      :last-child {
        @include top_icon(#38a980, #adde91);
      }
    }

    .attendant {
      :last-child {
        @include top_icon(#5773e0, #cacaf3);
      }
    }

    .defect {
      :last-child {
        @include top_icon(#ff9502, #ffd59a);
      }
    }

    .number {
      padding: 33px 20px 23px 20px;

      :first-child {
        font-size: 24px;
        color: #282828;
      }

      :last-child {
        font-size: 50px;
      }
    }

    .device {
      font-size: 14px;

      :last-child {
        color: #282828;
      }
    }
  }

  .last_top_item {
    border: none;
  }
}

//
.repairs {
  background: #ffffff;
  margin-top: 20px;
  border-radius: 10px;
  padding-bottom: 10px;
  box-sizing: border-box;

  .repairs_tle {
    @include top_title;
  }

  #workOrder {
    height: 328px;
    width: 100%;
  }
}

.dev_stastus {
  background: #ffffff;
  margin-top: 20px;
  border-radius: 10px;

  .dev_tle {
    @include top_title;

  }

  .dev_content {
    display: flex;
    width: 100%;
    justify-content: space-between;
    padding-bottom: 10px;

    #equipment,
    #realTime {
      width: 49%;
      height: 248px;

      .right-app {
        width: 100%;
        margin-top: -12px;

        .right-apptitle {
          width: 100%;
          font-size: 14px;
          color: #87888a;
          line-height: 45px;
          text-align: center;
        }

        .app-zbs {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;

          .zbs-title {
            width: 100%;
            font-size: 18px;
            color: #aeaeb2;
            border: none;
          }

          .app-zbs-num {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .num-title {
              width: 60%;
              font-size: 28px;
              font-weight: bold;
            }

            .num-content {
              width: 18%;
              font-size: 15px;
              font-weight: 500;
              color: #3a3a3a;
              line-height: 45px;

              div {
                height: 30px;
                display: flex;
                justify-content: space-between;
                padding: 0;

                span {
                  height: 30px;
                  border: none;
                  font-size: 12px;
                  color: #000000;
                }
              }

              .app-jxz {
                background-size: 18px;
                display: flex;
                justify-content: space-between;
              }
            }
          }
        }

        .zbs-jdt {
          margin: 20px 0 14px 0;
          height: 10px;
          padding: 0;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          border: none;

          .jdt-jxz {
            width: 50%;
            background: #27b66a;
            border-radius: 12px;
          }

          .jdt-ytz {
            width: 30%;
            background: #eb5960;
            border-radius: 12px;
          }

          .jdt-qt {
            width: 20%;
            background: #ffb413;
            border-radius: 12px;
          }
        }

        .zbs-jsz {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: 12px;

          .icon-shijian {
            color: red;
          }

          :nth-child(1) {
            color: #27b66a;
          }

          :nth-child(2) {
            font-size: 16px;
            color: #58c68b;
            margin: 0 8px 0 18px;
          }

          :nth-child(3) {
            color: #b6b6b6;
            margin-right: 16px;
          }

          :nth-child(4) {
            color: #b6b6b6;
          }
        }
      }
    }

    #realTime {
      padding-right: 63px;
    }
  }
}

.dev_usage {
  display: flex;
  margin-top: 20px;
  padding: 25px 20px 20px 20px;
  justify-content: space-between;
  width: 100%;
  border-radius: 10px;
  height: 350px;
  background: #ffffff;

  .availability {
    width: 35% !important;
  }

  .availability,
  .usage {
    width: 40%;

    .ratio_title {
      font-size: 16px;
      color: #101010;
      padding-bottom: 20px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      align-items: center;

      i {
        font-size: 24px;
      }
    }

    .register-senoud {
      .mallapp_content {
        line-height: 50px;
        box-sizing: border-box;

        ::v-deep .el-progress-bar__outer {
          height: 14px !important;
          background-color: rgba(0, 0, 0, 0);
        }

        ::v-deep .el-progress-bar__innerText {
          display: none;
        }

        ::v-deep .el-progress-bar {
          background: #dce1ec;
          border-radius: 10px;
        }

        .el-progress {
          margin: 10px 0;
        }

        .mallapp_pro {
          display: flex;
          align-items: center;

          .pro_tle {
            font-size: 13px;
            color: #aeaeb2;
            margin-left: 5px;
          }

          .availa_tle {
            width: 10%;
          }

          .pro_num {
            color: #3e4144;
            font-size: 16px;
            margin-left: 10px;
          }
        }
      }
    }
  }

  #goodCondition {
    width: 30%;
    height: 250px;
    margin-top: 10px;
  }
}

.immediately {
  display: flex;
  margin-top: 20px;
  padding: 25px 32px 0px 32px;
  justify-content: space-between;
  width: 100%;
  border-radius: 10px;
  height: 400px;
  background: #ffffff;

  .maintain_task,
  .polling_task {
    width: 48%;

    .task_tle {
      @include top_title;
      border: none;
      padding-left: 0;

      i {
        font-size: 20px;
      }
    }
  }
}
</style>
