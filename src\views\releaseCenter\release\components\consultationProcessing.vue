<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-descriptions v-if="typeName === 'APPEAL'" title="诉求内容" direction="vertical" :column="2" border>
      <el-descriptions-item label="联系人">{{ consultationData.contacts }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ consultationData.phone }}</el-descriptions-item>
      <el-descriptions-item label="咨询时间">{{ consultationData.subTime }}</el-descriptions-item>
      <el-descriptions-item label="类型">
        {{ consultationData.appealTitle }}
      </el-descriptions-item>
      <el-descriptions-item label="内容" :span="2">{{ consultationData.appealContent }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions v-else title="咨询内容" direction="vertical" :column="2" border>
      <el-descriptions-item label="咨询人">{{ consultationData.consultName }}</el-descriptions-item>
      <el-descriptions-item label="咨询时间">{{ consultationData.consultTime }}</el-descriptions-item>
      <el-descriptions-item label="咨询内容" :span="2">{{ consultationData.content }}</el-descriptions-item>
      <el-descriptions-item label="咨询类型">
        {{
          (productTypeItem => productTypeItem ? productTypeItem.dictLabel : '无')(
            labelTypeList.find(item => consultationData.productType == item.dictValue)
          )
        }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getInfo } from "@/api/release/index.js";
import { getProductList } from "@/api/base/consultationProcessing.js";
export default {
  name: 'IndustrialBrainNewMgmtFrontConsultationProcessing',
  data() {
    return {
      submitDing: false,
      consultationData: {},
      labelTypeList: [],
      typeName: null
    };
  },
  created() {
    this.getProductListFtn();
    const { flowInstanceId, type } = this.$route.query;
    this.typeName = type;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },
  methods: {
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.consultationData = params
        this.submitDing = false;
      });
    },
    getProductListFtn() {
      getProductList().then((res) => {
        this.labelTypeList = res.data || []
      })
    }
  },
};
</script>
<style lang="scss">
.financialCommn {
  width: 80%;

  ::v-deep.el-descriptions {
    .el-descriptions-item__label.is-bordered-label {
      color: #0D162A;
      background-color: #F6F8FC;
    }
  }
}
</style>