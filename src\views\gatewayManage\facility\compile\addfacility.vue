<template>
    <div style="width: 100%;" class="addStep1">

        <div style="text-align: center;" class="searchDemo">
            <el-form :inline="true" :model="queryParams" @submit.native.prevent>
                <el-input @keyup.enter.native="getList" placeholder="搜索设备类型" size="large" clearable
                    v-model="queryParams.driveName">
                    <el-button slot="append" icon="el-icon-search" @click="getList">搜索</el-button>
                </el-input>
            </el-form>
        </div>
        <div class="cardDemoStep1" v-loading="dataListLoading">
            <el-row :gutter="20">
                <el-col :span="4" v-for="item in list" :key="item.id">
                    <img :src="item.description" class="cardImg" @click="cardImgClick(item)" />
                    <p class="cardTitle">{{ item.driveName }}</p>
                </el-col>
            </el-row>
        </div>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getList" />

        <el-dialog :visible.sync="dialogVisible" title="通信协议" width="800" custom-class="dealDialog" @close="dealCancl">
            <div v-loading="dealLoading" style="min-height: 200px;">
                <el-row :gutter="20" class="cardDemoDialog" v-if="driveIdArr.length > 0">
                    <el-col :span="8" style="margin-bottom: 24px;" v-for="(item, index) in driveIdArr">
                        <div class="cardDemoCols" :class="{ clickCols: radioValue.id == item.id }"
                            @click="cardDemoCols(item)">
                            <div class="cardDemoCentre">
                                <img src="@/assets/gateway-deal.png" />
                                <div class="cardDemoText">
                                    <el-tooltip effect="dark" :content="item.agreementName" placement="top">
                                        <div class="cardDemoTitle">{{ item.agreementName }}</div>
                                    </el-tooltip>
                                    <el-tooltip effect="dark" :content="item.driveName" placement="bottom">
                                        <div class="cardDemoContent">{{ item.driveName }}</div>
                                    </el-tooltip>
                                    <div class="radioFix" :class="{ clickRadio: radioValue.id == item.id }"></div>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-empty description="暂无数据" v-else />
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dealCancl">取消</el-button>
                <el-button type="primary" @click="nextStep">下一步</el-button>
            </span>
        </el-dialog>

    </div>
</template>




<script>
import { getStep1List, getAgreement } from "@/api/gatewayManage/facility/index.js";
import { SessionStorage } from '@/utils/storage'
export default {
    data() {
        return {
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                driveName: '',
            },
            list: [],
            dataListLoading: false,
            total: 0,
            dialogVisible: false,
            dealLoading: false,
            driveIdArr: [],
            radioValue: {}
        };
    },
    created() {
        this.getList();
    },

    methods: {

        getList() {
            this.dataListLoading = true
            getStep1List(this.queryParams).then(res => {
                const { list, total } = res.data;
                this.list = list;
                this.total = total
            }).finally(() => this.dataListLoading = false);
        },

        cardImgClick(row) {
            this.dealLoading = true;
            getAgreement(row.id).then(res => {
                this.dialogVisible = true;
                this.driveIdArr = res.data;
            }).finally(() => this.dealLoading = false);
        },

        nextStep() {
            const { radioValue } = this;
            if (JSON.stringify(radioValue) === '{}') {
                this.$parent.$message.warning("请进行通信协议选择!!!");
            }
            else {
                SessionStorage.setItem('facilityPams', {
                    ...SessionStorage.getItem('facilityPams'),
                    agreementId: this.radioValue.id,
                    deviceType: this.radioValue.agreementName,
                });
                this.dialogVisible = false;
                this.$emit("loadFuc", true);
                this.$emit("next");
            }
        },

        dealCancl() {
            this.radioValue = {}
            this.dialogVisible = false
        },

        cardDemoCols(val) {
            this.radioValue = val
        },

    },


};
</script>

<style lang="scss" scoped>
.addStep1 {
    margin-top: 40px;

    .searchDemo {
        padding-left: 250px;
        padding-right: 250px;

        ::v-deep .el-input-group__append {
            color: white;
            background-color: #0147EB;
            box-shadow: none !important;
        }

        ::v-deep .el-button--primary {
            color: white;
            background-color: #0147EB;
            box-shadow: none !important;
        }
    }

    .cardDemoStep1 {
        /* margin: 0; */
        padding-left: 100px;
        padding-right: 100px;
        margin-top: 40px;

        .cardImg {
            width: 100%;
            height: 62px;
            cursor: pointer;
            display: inline-block;
            /* background-color: red; */
        }

        .cardTitle {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #002251;
            margin-top: 8px;
            text-align: center;
        }

        ::v-deep {
            .el-row {
                margin-left: 0px !important;
                margin-right: 0px !important;

                .el-col-4 {
                    max-width: 14% !important;
                    margin-bottom: 30px;
                }
            }
        }

    }

    .cardDemoDialog {
        .clickCols {
            box-shadow: 0px 2px 8px #F3F5F8;
            border-radius: 8px;
            border: 2px solid #0147EB !important;
            box-sizing: border-box;
        }

        .clickRadio {
            background: #FFFFFF;
            border: 4px solid #0055E2 !important;
            box-sizing: border-box !important;
        }

        .cardDemoCols {
            height: 100px;
            display: flex;
            flex-direction: column;
            /* background: red; */
            border: 1px solid #DCDFE8;
            box-shadow: 0px 2px 8px #F3F5F8;
            border-radius: 8px;
            position: relative;
            cursor: pointer;

            .cardDemoCentre {
                padding: 24px 24px 0px;
                display: flex;
                flex: 1;
                /* background-image: url('@/assets/gateway-dealbagrd.png'); */
                background-image: url('../../../../assets/gateway-dealbagrd.png');
                background-size: cover;
                background-size: 100% 100%;
                background-repeat: no-repeat;

                img {
                    width: 50px;
                    height: 50px;
                }

                .cardDemoText {
                    margin-left: 16px;
                    flex: 1;
                    white-space: nowrap;
                    overflow-x: hidden;
                    text-overflow: ellipsis;

                    .cardDemoTitle {
                        font-family: PingFang SC;
                        font-weight: bold;
                        font-size: 16px;
                        color: #515A6E;
                        margin-bottom: 8px;
                        white-space: nowrap;
                        overflow-x: hidden;
                        text-overflow: ellipsis;
                    }

                    .cardDemoContent {
                        font-family: PingFangSC-Regular;
                        font-size: 13px;
                        color: #515A6E;
                        white-space: nowrap;
                        overflow-x: hidden;
                        text-overflow: ellipsis;
                    }

                    .radioFix {
                        width: 14px;
                        height: 14px;
                        background: #FFFFFF;
                        border-radius: 50%;
                        border: 1px solid #DBDFE9;
                        position: absolute;
                        top: 12px;
                        right: 20px;
                    }
                }
            }
        }

    }

}
</style>

<style>
.dealDialog {
    .el-dialog__body {
        max-height: 400px !important;
        overflow: auto !important;
    }
}
</style>