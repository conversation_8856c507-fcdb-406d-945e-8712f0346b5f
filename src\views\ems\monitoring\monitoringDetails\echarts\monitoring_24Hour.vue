<template>
  <div id="monitoring_24Hour" :style="{width: '780px', height: '100%'}"></div>
</template>

<script>
import echarts from 'echarts';

export default {
  data() {
    return {};
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // console.log(new Date().getTime())  当前时间戳

      // 基于准备好的dom，初始化echarts实例
      let monitoring_24Hour = this.$echarts.init(document.getElementById('monitoring_24Hour'))

      let startTime = 1525835791000; // 2018/5/9 11:16:31
      let categories = ['监控'].reverse();

      let data = [
        // value 第一个参数: 设备 index;
        //       第二个参数: 状态的开始时间;
        //       第三个参数: 状态的结束时间;
        //       第四个参数: 状态的持续时间;
        {
          name: '运行',
          value: [0, 1525835791000, 1525835791000 + 600000, 600000],
          itemStyle: {
            normal: {
              color: '#4bc34b'
            }
          }
        },
        {
          name: '停机',
          value: [0, 1525836391000, 1525836391000 + 600000, 600000],
          itemStyle: {normal: {color: '#e51c23'}}
        },
        {
          name: '运行',
          value: [0, 1525836991000, 1525836991000 + 600000, 600000],
          itemStyle: {normal: {color: '#4bc34b'}}
        },
        {
          name: '停机',
          value: [0, 1525837591000, 1525837591000 + 600000, 600000],
          itemStyle: {normal: {color: '#e51c23'}}
        },
        {
          name: '待机',
          value: [0, 1525838191000, 1525838191000 + 600000, 600000],
          itemStyle: {normal: {color: '#ff9800'}}
        },
        {
          name: '运行',
          value: [0, 1525838791000, 1525838791000 + 600000, 600000],
          itemStyle: {normal: {color: '#4bc34b'}}
        }
      ]

      function renderItem(params, api) {
        let categoryIndex = api.value(0);
        let start = api.coord([api.value(1), categoryIndex]);
        let end = api.coord([api.value(2), categoryIndex]);
        let height = api.size([0, 1])[1] * 0.6;

        return {
          type: 'rect',
          shape: echarts.graphic.clipRectByRect({
            x: start[0],
            y: start[1] - height / 2,
            width: end[0] - start[0],
            height: height
          }, {
            x: params.coordSys.x,
            y: params.coordSys.y,
            width: params.coordSys.width,
            height: params.coordSys.height
          }),
          style: api.style()
        };
      }


      // 绘制图表
      monitoring_24Hour.setOption({
        tooltip: {
          formatter: function (params) {
            // console.log(params)
            return params.marker + params.name + ': ' + new Date().getHours() + '时' + params.value[3] / 1000 + ' s';
          }
        },
        grid: {
          height: 50,
          top: 20,
          left: 20,
          right: 0
        },
        xAxis: {
          type: 'time',
          min: startTime,
          data: ["0时", "1时", "2时", "3时", "4时", "5时", "6时", "7时", "8时", "9时",
            "10时", "11时", "12时", "13时", "14时", "15时", "16时", "17时", "18时", "19时",
            "20时", "21时", "22时", "23时"],
          // show: false,
          axisLabel: {
            interval: 0,//代表显示所有x轴标签显示
          },
          axisTick: {       //x轴刻度线
            "show": false
          },
          splitLine: {     //网格线
            "show": false
          }
        },
        yAxis: {
          data: categories,
          show: false
        },
        series: [
          {
            type: 'custom',
            renderItem: renderItem,
            itemStyle: {
              normal: {
                opacity: 0.8
              }
            },
            encode: {
              x: [1, 2, 3],
              y: 0,
            },
            data: data
          }
        ]
      });
    }
  }
}

</script>
