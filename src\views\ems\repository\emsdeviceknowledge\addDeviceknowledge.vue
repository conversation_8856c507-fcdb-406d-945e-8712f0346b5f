<template>
  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box">
        <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>

        <div class="info-from">
          <el-form-item label="知识名称" prop="title">
            <el-input v-model="form.title" maxlength="20" show-word-limit placeholder="请输入知识名称" style="width: 450px"></el-input>
          </el-form-item>

          <el-form-item label="知识分类" prop="categoryIdCopy">
            <el-cascader :options="treeData"
                         v-model="form.categoryIdCopy"
                         :props="optionProps"
                         :show-all-levels="false"
                         style="width: 450px"
                         placeholder="请选择知识分类"></el-cascader>
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input type="textarea" v-model="form.description" maxlength="80" show-word-limit placeholder="请输入描述" style="width: 450px"></el-input>
          </el-form-item>

          <el-form-item label="简介" prop="briefIntroduction">
            <el-input type="textarea" v-model="form.briefIntroduction" maxlength="25" show-word-limit placeholder="请输入简介" style="width: 450px"></el-input>
          </el-form-item>

          <el-form-item label="说明" prop="remark">
            <el-input type="textarea" v-model="form.remark" maxlength="255" show-word-limit placeholder="请输入说明" style="width: 450px"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="info-box">
        <IconTitle title="设备封面图片(1张)" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <ImageUpload
              ref="fileImage"
              :fileListTem="imgArrayTem"
              :limit="1"
          />
        </div>
      </div>
      <!-- 富文本编辑器 -->
      <div class="info-box">
        <IconTitle title="设备知识内容" imgUrl="yunwei"></IconTitle>
        <div style="padding-top: 20px; height: 100%" >
          <AvueUeditor v-model="form.knowledgeContent"
                        :options="options"></AvueUeditor>
        </div>
      </div>

      <div class="info-btn-box">
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {getTree} from "@/api/ems/repository/emsregulations";
import {addObj, getObj, putObj} from "@/api/ems/repository/emsdeviceknowledge";
import deviceUpload from "./deviceUpload";
import {tableOption} from "@/const/crud/ems/repository/emsregulations";
import IconTitle from "@/components/ems/icon-title/index.vue";
import {mapGetters} from "vuex";
import Treeselect from "@riophae/vue-treeselect";
import ImageUpload from "@/components/ImageUpload/index.vue";
import AvueUeditor from "avue-plugin-ueditor/packages/ueditor/src/index.vue";
export default {
  name: "addRegulations",
  components: {
    IconTitle,
    Treeselect,
    ImageUpload,
    deviceUpload,
    AvueUeditor
  },
  data() {
    return {

      // 富文本编辑器
      options: {
        //普通图片上传
        action: "/admin/sys-file/upload",
        customConfig: {},//wangEditor编辑的配置
        props: {
          res: "data",
          url: "url"
        },
      },

      title: "",
      tableData: [],
      searchForm: {
        //制度名称
        regulationName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      // 树状结构
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        props: {
          label: "name",
          value: "id",
        },
      },
      coverImgTem: [],
      treeData: [],
      form: {
        id: "",
        title: "",  // 知识名称
        categoryId: "",  // 知识分类
        categoryIdCopy: [],
        description: "", // 描述
        briefIntroduction: "",  //简介
        imageId: "", // 图片路径id
        remark: "",  //说明
        imgShow: {},
        knowledgeContent: '', // 知识内容
      },
      rules: {
        title: [
          {required: true, message: '请输入知识名称', trigger: 'change'}
        ],
        categoryIdCopy: [
          {required: true, message: '请选择知识分类', trigger: 'change'}
        ],
        description: [
          {required: true, message: '请输入描述', trigger: 'change'}
        ],
        briefIntroduction: [
          {required: true, message: '请输入简介', trigger: 'change'}
        ]
      },
      imgArrayTem: [],
      isNoTopData: [
        {
          value: 0,
          label: "是"
        },
        {
          value: 1,
          label: "否"
        }
      ],
      optionProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      editId: 0
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsregulations_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsregulations_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsregulations_edit, false),
      };
    },
  },
  mounted() {
    this.getTreeData();
    this.getUpdata();
  },
  methods: {
    // 回显数据
    getUpdata(){
      if (this.$route.query.id != null){
        let id = this.$route.query.id;
        getObj(id).then(res => {
          this.imgArrayTem = [];
          if (res.data.data.imgShow != null) {
            this.imgArrayTem[0] = res.data.data.imgShow;
          }
          // 修改回显数据
          Object.keys(this.form).forEach((item, index) => {
            if (item !== "imageId" ) {
              this.form[item] = res.data.data[item];
            }
          });

          this.form.id = res.data.data.id;
          this.form.title = res.data.data.title;
          this.form.categoryIdCopy = res.data.data.categoryId;
          this.form.description = res.data.data.description;
          this.form.briefIntroduction = res.data.data.briefIntroduction;
          this.form.imageId = res.data.data.imageId;
          this.form.remark = res.data.data.remark;
          this.form.knowledgeContent = res.data.data.knowledgeContent;
        })
      }
    },

    // 返回按钮
    goBack() {
      this.$router.push({
        path: '/ems/repository/emsdeviceknowledge/index'
      })
    },

    // 提交表单
    submitForm(formName) {
      let data = JSON.parse(JSON.stringify(this.form));
      let list = this.$refs.fileImage.fileList;
      if (list.length == 0) {
        data.imageId = "";
      }else {
        data.imageId = list[list.length-1].id
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          data.categoryId = data.categoryIdCopy[data.categoryIdCopy.length - 1];
          if (data.id) {
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.$router.push({
                path: '/ems/repository/emsdeviceknowledge/index'
              })
            });
          } else {
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功!")
              this.$parent.listFlag = true;
              this.$router.push({
                path: '/ems/repository/emsdeviceknowledge/index'
              })
            });
          }

        }
      });
    },

    // 规章制度树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "设备知识类型") {
              common_table_info.push(treeDataList[index])
            }
          })
          this.treeData = common_table_info;
        }
      })
    },

  }
}
</script>

<style scoped lang="scss">
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.add-box {
  height: 950px;
}

.info-box {
  background: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  margin-bottom: 10px;
  padding: 10px 15px;
  overflow: hidden;

  .info-from {
    display: flex;
    flex-wrap: wrap;
    padding-top: 20px;
    position: relative;

    .el-form-item {
      width: 50%;
      padding-right: 10px;
    }


    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {

      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }
}

.info-btn-box {
  width: 100%;
  text-align: center;
}


</style>
