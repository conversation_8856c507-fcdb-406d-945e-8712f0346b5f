<template>
  <div class="mod-config">
    <basic-container>
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-button v-if="true" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button v-if="true" icon="el-icon-download" type="primary" plain @click="exportExcel()">导出</el-button>
        </el-form-item>
      </el-form>

      <div class="avue-crud">
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading">
                      <el-table-column
                    prop="id"
                    header-align="center"
                    align="center"
                    label="主键">
            </el-table-column>
                      <el-table-column
                    prop="planNum"
                    header-align="center"
                    align="center"
                    label="计划编号">
            </el-table-column>
                      <el-table-column
                    prop="planName"
                    header-align="center"
                    align="center"
                    label="计划名称">
            </el-table-column>
                      <el-table-column
                    prop="deptId"
                    header-align="center"
                    align="center"
                    label="部门id">
            </el-table-column>
                      <el-table-column
                    prop="liableUserId"
                    header-align="center"
                    align="center"
                    label="责任人id">
            </el-table-column>
                      <el-table-column
                    prop="inspectCycle"
                    header-align="center"
                    align="center"
                    label="巡检周期">
            </el-table-column>
                      <el-table-column
                    prop="cycleUnit"
                    header-align="center"
                    align="center"
                    label="巡检周期单位">
            </el-table-column>
                      <el-table-column
                    prop="effectiveTime"
                    header-align="center"
                    align="center"
                    label="有效时间">
            </el-table-column>
                      <el-table-column
                    prop="effectiveUnit"
                    header-align="center"
                    align="center"
                    label="有效时间单位">
            </el-table-column>
                      <el-table-column
                    prop="generateTime"
                    header-align="center"
                    align="center"
                    label="提前生成时间">
            </el-table-column>
                      <el-table-column
                    prop="generateUnit"
                    header-align="center"
                    align="center"
                    label="生成时间单位">
            </el-table-column>
                      <el-table-column
                    prop="noticeTime"
                    header-align="center"
                    align="center"
                    label="提醒时间">
            </el-table-column>
                      <el-table-column
                    prop="noticeUnit"
                    header-align="center"
                    align="center"
                    label="提醒时间单位">
            </el-table-column>
                      <el-table-column
                    prop="beginTime"
                    header-align="center"
                    align="center"
                    label="计划开始时间">
            </el-table-column>
                      <el-table-column
                    prop="endTime"
                    header-align="center"
                    align="center"
                    label="计划结束时间">
            </el-table-column>
                      <el-table-column
                    prop="lastGenerateTime"
                    header-align="center"
                    align="center"
                    label="上次生成时间">
            </el-table-column>
                      <el-table-column
                    prop="nextGenerateTime"
                    header-align="center"
                    align="center"
                    label="下次生成时间">
            </el-table-column>
                      <el-table-column
                    prop="enable"
                    header-align="center"
                    align="center"
                    label="是否启用(0不启用 1启用)">
            </el-table-column>
                      <el-table-column
                    prop="inspectSettings"
                    header-align="center"
                    align="center"
                    label="巡检设置(0无需扫码，1扫一个二维码，3只需填写异常项)">
            </el-table-column>
                      <el-table-column
                    prop="strategyId"
                    header-align="center"
                    align="center"
                    label="策略id">
            </el-table-column>
                      <el-table-column
                    prop="createBy"
                    header-align="center"
                    align="center"
                    label="创建者">
            </el-table-column>
                      <el-table-column
                    prop="createTime"
                    header-align="center"
                    align="center"
                    label="创建时间">
            </el-table-column>
                      <el-table-column
                    prop="updateBy"
                    header-align="center"
                    align="center"
                    label="更新者">
            </el-table-column>
                      <el-table-column
                    prop="updateTime"
                    header-align="center"
                    align="center"
                    label="更新时间">
            </el-table-column>
                      <el-table-column
                    prop="remark"
                    header-align="center"
                    align="center"
                    label="备注">
            </el-table-column>
                      <el-table-column
                    prop="delFlag"
                    header-align="center"
                    align="center"
                    label="删除标志(0正常 1删除)">
            </el-table-column>
                      <el-table-column
                    prop="tenantId"
                    header-align="center"
                    align="center"
                    label="租户Id">
            </el-table-column>
                    <el-table-column
                  header-align="center"
                  align="center"
                  label="操作">
            <template slot-scope="scope">
              <el-button v-if="permissions.ems_emsinsinspectplan_edit" type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
              <el-button v-if="permissions.ems_emsinsinspectplan_del" type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
    </basic-container>
  </div>
</template>

<script>
  import {planFetchList, planDelObj} from '@/api/ems/inspection/plan'
  import TableForm from './emsinsinspectplan-form'
  import {mapGetters} from 'vuex'
  export default {
    data () {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false
      }
    },
    components: {
      TableForm
    },
    created () {
      this.getDataList()
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        })).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        this.$confirm('是否确认删除ID为' + id, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.$message.success('删除成功')
          this.getDataList()
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/ems/emsinsinspectplan/export', this.searchForm,'emsinsinspectplan.xlsx')
      }
    }
  }
</script>
