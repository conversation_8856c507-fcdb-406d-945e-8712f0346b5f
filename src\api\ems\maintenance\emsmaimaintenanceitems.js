/*
 *    Copyright (c) 2018-2025, gewu All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: gewu
 */

import request from '@/utils/request'

export function  checkFetchList(query) {
  return request({
    url: '/platform/emsmaimaintenanceitems/page',
    method: 'get',
    params: query
  })
}

export function  checkAddObj(obj) {
  return request({
    url: '/platform/emsmaimaintenanceitems',
    method: 'post',
    data: obj
  })
}

export function  checkGetObj(id) {
  return request({
    url: '/platform/emsmaimaintenanceitems/' + id,
    method: 'get'
  })
}

export function  checkDelObj(id) {
  return request({
    url: '/platform/emsmaimaintenanceitems/' + id,
    method: 'delete'
  })
}

export function  checkPutObj(obj) {
  return request({
    url: '/platform/emsmaimaintenanceitems',
    method: 'put',
    data: obj
  })
}
