<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <div class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
            type="info"
            icon="el-icon-refresh-left"
            @click="refreshChange()"
          ></el-button>
          <el-button
            id="gwButton"
            type="primary"
            style="backgroundcolor: #e1b980"
            icon="el-icon-circle-plus-outline"
            v-if="true"
            @click="cateAdd"
            >新增</el-button
          >

<!--          <el-button type="check" icon="el-icon-download" @click="exportExcel"-->
<!--            >导出</el-button-->
<!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua" />
        </div>
      </div>
    </el-card>
    <el-table
      :data="tableData"
      :header-cell-style="{ background: '#f8f8f9', color: '#606266' }"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="类别名称" prop="name" />
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status === "0" ? "启用" : "禁用" }}
        </template>
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remark" >
        <template v-slot="scope">
          <el-tooltip class="item" effect="light" placement="top">
            <div v-html="scope.row.remark" slot="content" style="max-width:300px"></div>
            <div class="oneLine">{{scope.row.remark}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="cateEdit(scope.row)"
            v-if="true"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="true"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="title" :visible.sync="open" width="540px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="设备类别" prop="category">
          <el-input v-model="form.category"  maxlength="20"
                    show-word-limit placeholder="请输入设备类别" />
        </el-form-item>

        <el-form-item label="父级" prop="parentId">
          <treeselect
            v-model="form.parentId"
            :options="qyEnterpriseTypeOptionList"
            :normalizer="normalizer"
            placeholder="请选择父级"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            placeholder="请选择状态"
            v-model="form.status"
            clearable
            style="width: 100%"
          >
            <el-option label="启用" value="0"></el-option>
            <el-option label="禁用" value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            maxlength="255"
            show-word-limit
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  fetchList,
  getObj,
  addObj,
  putObj,
  delObj,
  fetchListTree,
} from "@/api/ems/equipment/category";
import { tableOption } from "@/const/crud/ems/equipment/category";
import { mapGetters } from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "emsdevicecategory",
  components: {
    IconTitle,
    Treeselect,
  },
  data() {
    return {
      tableData: [],
      qyEnterpriseTypeOptionList: [],
      searchForm: {}, // 查询参数
      page: {},
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      title: "新增",
      open: false,
      form: {
        id: null,
        category: null,
        parentId: '',
        status: null,
        remark: null,
      },
      rules: {
        parentId: [
          { required: true, message: "请选择设备类别", trigger: "change" },
        ],category: [
          { required: true, message: "请输入设备类别", trigger: "blur" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(
          this.permissions.ems_emsdevicecategory_add,
          false
        ),
        delBtn: this.vaildData(
          this.permissions.ems_emsdevicecategory_del,
          false
        ),
        editBtn: this.vaildData(
          this.permissions.ems_emsdevicecategory_edit,
          false
        ),
      };
    },
  },
  mounted() {
    this.getList();

    this.initElement();
    this.changeThme();
  },
  methods: {
    getTreeOption(id) {
      fetchListTree(id).then((res) => {
        this.qyEnterpriseTypeOptionList = [];
        this.menuOptions = [];
        const menu = { id: 0, name: "设备类别目录", children: [] };
        menu.children = res.data;
        this.qyEnterpriseTypeOptionList.push(menu);
      });
    },
    cateAdd() {
      this.title = "新增";
      this.open = true;
      this.getTreeOption("");
      this.reset();
    },
    cateEdit: function (row) {
      this.open = true;
      this.title = "修改";
      this.getTreeOption(row.id);
      getObj(row.id).then((res) => {
        Object.keys(this.form).forEach((item, index) => {
          this.form[item] = res.data[item];
        });

        if (this.form.parentId===0) {
          this.form.parentId = undefined;
        }
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.parentId) {
            this.form.parentId = 0;
          }
          if (!this.form.id) {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.getList();
              this.open = false;
            });
          } else {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    reset() {
      this.form = {
        id: null,
        category: null,
        parentId: undefined,
        status: null,
        remark: null,
      };
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow =
        !this.$refs.crud.$refs.headerSearch.searchShow;
    },
    // 列表查询
    getList() {
      this.tableLoading = true;
      fetchList(Object.assign({} ,this.searchForm))
        .then((response) => {
          this.tableData = response.data
          this.page.total = response.data.total;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    // 删除
    handleDelete: function (row, index) {
      // console.log(JSON.stringify(row))
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList();
        });
    },

    // 刷新事件
    refreshChange() {
      this.getList();
    },
    // 导出excel
    // exportExcel() {
    //   this.downBlobFile(
    //     "/ems/emsdevicecategory/export",
    //     this.searchForm,
    //     "emsdevicecategory.xlsx"
    //   );
    // },
    // 改变主题颜色
    changeThme() {
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor = this.theme;
    },
  },
};
</script>
<style scoped lang='scss'>
@import "@/styles/ems/avue.scss";
.el-table {
  padding: 10px;
  margin-top: 10px;
}

.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.clearfix {
    height: 40px;
    position: relative;
    .btn-box {
        position: absolute;
        top: 0;
        left: 0;
    }
    .icon-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 40px;
    }
}

</style>
