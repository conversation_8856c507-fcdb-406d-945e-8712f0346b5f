<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <div v-if="listFlag" class="user">

    <el-card class="box-card btn-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left" @click="resetBtn()"></el-button>
          <el-button type="goon" icon="el-icon-circle-plus-outline" @click="toAddOpen()"
          >新增
          </el-button
          >
          <el-button type="success" icon="el-icon-edit" @click="toEditOpen()">编辑</el-button>
          <el-button type="danger" icon="el-icon-circle-close"  @click.native="handleDel()"
                     >删除</el-button>
          <el-button style="margin-left: 30px" type="info" icon="icon-online" @click="toAdd()">关联设备</el-button>
        </div>
        <div class="icon-box">
<!--          <i class="el-icon-search"></i>-->
<!--          <i class="el-icon-goods"></i>-->
          <i class="el-icon-setting" @click="columnShow"></i>
<!--          <i class="icon-zuixiaohua"/>-->
        </div>
      </div>
      <div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="设备名称">
            <el-input placeholder="设备名称" v-model="searchForm.devciceName" clearable></el-input>
          </el-form-item>
          <!-- <el-form-item label="状态">
            <el-select placeholder="状态" v-model="form.status">
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input placeholder="手机号" v-model="form.phone"></el-input>
          </el-form-item> -->
          <el-form-item>
            <el-button type="goon" icon="el-icon-search" @click="searchChange">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <basic-container>
      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <!--                    <el-table v-loading="spot.loading" :data="deviceList">-->
          <!--                        <el-table-column label="id" align="center" prop="id" v-if="false"/>-->
          <!--                        <el-table-column label="设备编号" align="center" prop="deviceNum"/>-->
          <!--                    </el-table>-->
          <avue-tree
              :option="treeOption"
              :data="strategyNewDataList"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <IconTitle :title="title" imgUrl="yunwei" style="font-size: 24px"></IconTitle>
          <!--                    <span> {{ title }} </span>-->
          <avue-crud
              ref="crud"
              :option="option"
              v-model="form"
              :page.sync="page"
              :table-loading="listLoading"
              :before-open="handleOpenBefore"
              :data="list"
              @search-change="searchChange"
              @size-change="sizeChange"
              @current-change="currentChange"
              @row-click="rowClick"
          >
          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>
    <el-drawer
        class="drawerStyle"
        :title="title"
        :show-close="false"
        :visible.sync="detail"
        direction="rtl"
        size="45%"

        append-to-body
    >
      <div>
        <img style="float: right;margin-right: 180px" :src="require('@/assets/imagesAssets/xgfj.png')">
        <div>
          <span class="labelS">标准编号：</span>
          <span class="contentS">{{ rowCheck.standardNum }}</span>
        </div>
        <div>
          <span class="labelS">标准名称：</span>
          <span class="contentS">{{ rowCheck.standardName }}</span>
        </div>
        <div>
          <span class="labelS">所属部门：</span>
          <span class="contentS">{{ rowCheck.deptName }}</span>
        </div>
      </div>
      <div class="line"></div>
      <div>

        <el-card shadow="always" class="box-card">
          <IconTitle title="点检项目" imgUrl="yunwei"></IconTitle>
          <el-table v-loading="check.loading" :data="checkList">
            <el-table-column
                label="序号"
                width="70px">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="编号" align="center" prop="itemsNum"/>
            <el-table-column label="项目" align="center" prop="itemsName"/>
            <el-table-column label="方法及基准" align="center" prop="methodBenchmark"/>
            <el-table-column label="类型" align="center" prop="type">
              <template slot-scope="scope">
                      <span>{{
                          scope.row.type == 1
                              ? "数字"
                              : scope.row.type == 2
                              ? "选项"
                              : ""
                        }}</span>
              </template>
            </el-table-column>
            <el-table-column label="下限" align="center" prop="lowerLimit"/>
            <el-table-column label="上线" align="center" prop="upperLimit"/>
            <el-table-column label="可选项" align="center" prop="optional"/>
            <el-table-column label="正常选项" align="center" prop="normalOption"/>
            <el-table-column label="参考图片" align="center" prop="referencePicture"/>
          </el-table>

          <pagination
              v-show="queryParamsCheckList.total>0"
              :total="queryParamsCheckList.total"
              :page.sync="queryParamsCheckList.pageNum"
              :limit.sync="queryParamsCheckList.pageSize"
              @pagination="checkGetList"
          />
        </el-card>
      </div>
    </el-drawer>

    <el-dialog :title="spot.title" :visible.sync="spot.open" width="600px" append-to-body>
      <el-form :model="checkForm" :rules="dataRule" ref="checkForm"
               label-width="110px">
        <el-form-item label="策略名称" prop="strategyName">
          <el-input v-model="checkForm.strategyName" @blur="getstrateGyName" placeholder="请输入策略名称"/>
        </el-form-item>
        <el-form-item label="所属部门" prop="deptId">
          <treeselect
              v-model="checkForm.deptId"
              :options="treeDeptData1"
              :normalizer="normalizer"
              placeholder="所属部门"
          />
          <!--                    <el-input v-model="checkForm.strategyName" placeholder="请输入策略名称"/>-->
        </el-form-item>
        <el-form-item label="是否启用" prop="enable">
          <el-radio-group v-model="checkForm.enable">
            <el-radio :label="0">不启用</el-radio>
            <el-radio :label="1">启用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
                      <el-button @click="checkBack">取消</el-button>
                      <el-button type="primary" @click="checkFormSubmit('checkForm')">确定</el-button>
                    </span>
    </el-dialog>
  </div>
  <div v-else>
    <IndexAdd :id='addEditId'/>
  </div>
</template>

<script>
import {addObj, delObj, fetchList, putObj} from "@/api/admin/user";
import {deptRoleList} from "@/api/admin/role";
import {fetchTree} from "@/api/admin/dept";
import {
  strategyFetchList,
  strategyGetObj,
  strategyAddObj,
  strategyPutObj,
  strategyDelObj,
  strategyList,
  deviceListANDStrategyId,
  spotAndName
} from "@/api/ems/inspection/spot";
import {tableOption} from "@/const/crud/ems/inspection/spot";
import {mapGetters} from "vuex";
import ExcelUpload from "@/components/upload/excel";
import IconTitle from "@/components/icon-title/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IndexAdd from "./indexAdd.vue";
import {
  checkFetchListDeviceId,
} from '@/api/ems/inspection/emsinsinspectitems';

export default {
  name: "spot",
  components: {
    ExcelUpload,
    IconTitle,
    IndexAdd,
    Treeselect
  },
  data() {
    return {
      detail: false,
      title: "",

      listFlag: true,
      addEditId: 0,
      checkForm: {
        id: null,
        strategyName: null,
        deptId: null,
        enable: 1
      },
      dataRule: {
        strategyName: [
          {required: true, message: '策略名称不能为空', trigger: 'blur'}
        ],
        deptId: [
          {required: true, message: '部门不能为空', trigger: 'blur'}
        ],
        enable: [
          {required: true, trigger: "blur", message: "是否启用不能为空"},
        ],
      },
      treeDeptData1: [], //部门


      searchForm: {
        deviceName: null,
        deviceNum: null,
        id: null,
      },
      treeOption: {
        defaultExpandAll: true,
        nodeKey: "id",
        addBtn: false,
        menu: false,
        props: {
          label: "name",
          value: "id",
        },
      },
      strategyDataList: [],
      strategyNewDataList: [],
      strategyNewDataChildren: [],
      spot: {
        loading: false,
        open: false,
        title: "",
      },
      device: {
        loading: false,
      },


      treeData: [],
      option: tableOption,
      checkedKeys: [],
      roleProps: {
        label: "roleName",
        value: "roleId",
      },
      defaultProps: {
        label: "name",
        value: "id",
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
        isAsc: false, // 是否倒序
      },
      list: [],
      listLoading: false,
      role: [],
      form: {},
      rolesOptions: [],
      excelUpload: false,

      //以下为抽屉参数
      rowCheck: {
        standardNum: null,
        standardName: null,
        deptName: null,
        loading: false,
        deviceList: [],
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          total: 0,
        },
      },
      checkList: [],
      check: {
        title: "",
        open: false,
        loading: false,

      },
      queryParamsCheckList: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  watch: {
    role() {
      this.form.role = this.role;
    },
  },
  created() {
    this.init();
  },
  methods: {
    // 删除
    handleDel: function () {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
              .then(() => {
                return strategyDelObj(this.searchForm.id);
              })
              .then((data) => {
                this.$message.success("删除成功");
                this.title="";
                this.list=[];
                this.init();
              });
    },
    toAdd() {
      if (this.searchForm.id == null) {
        this.$notify.info("请先选择点检策略")
        return
      }
      this.addEditId = this.searchForm.id
      this.listFlag = false;

    },
    rowClick(row, column) {
      this.detail = true;
      // this.rowCheck.loading = true;
      this.rowCheck.standardNum = row.deviceNum
      this.rowCheck.standardName = row.deviceName
      this.rowCheck.deptName = row.deptName
      this.rowCheck.rowId = row.id;
      this.checkGetList()
    },
    toAddOpen() {
      this.checkReset();
      this.spot.open = true;
      this.spot.title = "新增巡检策略";
      fetchTree().then((response) => {
        this.treeDeptData1 = response.data;
      });
    },

    toEditOpen() {
      if (this.searchForm.id == null) {
        this.$notify.info("请先选择点检策略")
        return
      }
      this.checkReset();
      this.spot.open = true;
      this.spot.title = "编辑巡检策略";
      fetchTree().then((response) => {
        this.treeDeptData1 = response.data;
        strategyGetObj(this.searchForm.id).then((response) => {
          this.checkForm = response.data;
          if (this.checkForm.enable == "1") {
            this.checkForm.enable = 1
          } else {
            this.checkForm.enable = 0
          }
        });
      });
    },
    checkBack() {
      this.spot.open = false;
    },
    checkGetList() {
      this.check.loading = true;
      checkFetchListDeviceId(Object.assign(
          {
            current: this.queryParamsCheckList.pageNum,
            size: this.queryParamsCheckList.pageSize,
          },
          {deviceId: this.rowCheck.rowId}
          )
      ).then(response => {
        this.checkList = response.data.records;
        this.queryParamsCheckList.total = response.data.total;
        this.check.loading = false;
      });
    },

    getstrateGyName() {
      if (this.checkForm.strategyName != null) {
        spotAndName(this.checkForm.strategyName).then((res) => {
          let data = JSON.parse(JSON.stringify(res.data));
          if (data != null) {
            this.$notify.error("策略名称不能重复，请重新填写")
            this.checkForm.strategyName = null;
            return;
          }
        }).catch(() => {
          this.$notify.error("系统异常，请稍后重试")
          this.checkForm.strategyName = null;
          return;
        })
      }
    },
    checkFormSubmit(formName) {
      let data = JSON.parse(JSON.stringify(this.checkForm));
      // console.log(JSON.stringify(this.checkForm))
      // return;
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.canSubmit = false;
          if (this.checkForm.id) {
            strategyPutObj(this.checkForm).then(data => {
              this.$notify.success('修改成功')
              this.spot.open = false
              this.init()
            }).catch(() => {
              this.canSubmit = true;
            });
          } else {
            strategyAddObj(this.checkForm).then(data => {
              this.$notify.success('添加成功')
              this.spot.open = false
              this.init()
            }).catch(() => {
              this.canSubmit = true;
            });
          }
        }
      })
    },
    checkReset() {
      this.checkForm = {
        id: null,
        strategyName: null,
        deptId: null,
        enable: 1
      };
      // this.resetForm("checkForm");
    },

    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },


    resetBtn() {
      this.searchForm.devciceName = null;
      this.getList();
    },
    init() {
      fetchTree().then((response) => {
        this.treeData = response.data;
        // console.log(JSON.stringify(response.data))
      });
      strategyList().then((response) => {
        this.strategyDataList = response.data;
        this.strategyNewDataList = [];
        var o = new Object();
        o.id = -1;
        o.name = "所有";
        o.parentId = 0;
        this.strategyNewDataChildren = [];
        for (var i = 0; i < this.strategyDataList.length; i++) {
          var ob = new Object();
          ob.id = this.strategyDataList[i].id;
          ob.name = this.strategyDataList[i].strategyName;
          ob.parentId = -1;
          this.strategyNewDataChildren.push(ob);
        }
        o.children = this.strategyNewDataChildren;
        this.strategyNewDataList.push(o)
        // console.log(JSON.stringify(response.data))
      });

    },
    nodeClick(data) {
      if (data.id == -1) {
        return;
      }
      this.title = data.name + "适用设备"
      this.searchForm.id = data.id
      this.checkForm.id = data.id
      this.getList();
    },
    // 对象数组去重
    uniqueArray(arr, key) {
      const seen = new Set();
      return arr.filter(item => {
        const k = item[key];
        return seen.has(k) ? false : seen.add(k);
      });
    },
    getList() {
      this.listLoading = true;
      deviceListANDStrategyId(
          this.searchForm
      ).then((response) => {
        let arr = response.data;
        const uniqueData = this.uniqueArray(arr, 'id');  //去重
        this.list = uniqueData;
        this.listLoading = false;
      });
    },
    getNodeData() {
      deptRoleList().then((response) => {
        this.rolesOptions = response.data;
      });
    },
    searchChange(param, done) {
      this.getList();
    },
    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    handleOpenBefore(show, type) {
      window.boxType = type;
      // 查询部门树
      fetchTree().then((response) => {
        this.treeDeptData = response.data;
      });
      // 查询角色列表
      deptRoleList().then((response) => {
        this.rolesOptions = response.data;
      });

      if (["edit", "views"].includes(type)) {
        this.role = [];
        for (let i = 0; i < this.form.roleList.length; i++) {
          this.role[i] = this.form.roleList[i].roleId;
        }
      } else if (type === "add") {
        this.role = [];
        this.form.username = undefined;
        this.form.phone = undefined;
        this.form.password = undefined;
      }
      show();
    },
    handleUpdate(row, index) {
      this.$refs.crud.rowEdit(row, index);
      this.form.password = undefined;
    },


    exportExcel() {
      this.downBlobFile("/admin/user/export", this.searchForm, "user.xlsx");
    }
  }
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style lang="scss" scoped>

.el-button--goon.is-active,
.el-button--goon:active {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon:focus,
.el-button--goon:hover {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon {
  color: #FFF;
  background-color: #02b980;
  border-color: #02b980;
}

.labelS {
  display: inline-block;
  width: 100px;
  margin-right: 10px;
  text-align: right;
  color: #888888;
  font-size: 14px;
}

.contentS {
  font-weight: bold;
  color: #101010;
  margin: 10px 0;
  display: inline-block;
  font-size: 14px;
}

.line {
  border: 2px solid rgba(236, 240, 244, 100);
  margin: 30px 0;
}

.user {
  height: 100%;

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }
}

::v-deep.drawerStyle {
  .el-drawer__header {
    background-color: #F2F2F5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 10px;
  }

  .el-card__body {
    padding: 0 20px 10px;
  }

  .box-card {
    box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, .12);
    margin: 0 10px;

  }

  .tableTitle {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    display: inline-block;
  }
}
.clearfix {
    height: 40px;
    position: relative;
    .btn-box {
        position: absolute;
        top: 0;
        left: 0;
    }
    .icon-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 40px;
    }
}
</style>

