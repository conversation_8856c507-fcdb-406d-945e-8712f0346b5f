<template>
  <div class="allocation">
        <el-card class="box-card btn-search page-search">
      <div slot="header">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left"></el-button>
          <el-button type="check" icon="el-icon-download">导出</el-button>
        </div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="备件编号">
            <el-input
              placeholder="备件编号"
              v-model="searchForm.roleName"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="备件名称">
            <el-input
              placeholder="备件名称"
              v-model="searchForm.roleCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障类型">
            <el-select
              placeholder="故障类型"
              v-model="searchForm.dsType"
              clearable
              style="width: 120px"
            >
              <el-option label="你好" value="1"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search">查询</el-button>
            <el-button icon="el-icon-refresh-right">重置</el-button>
          </el-form-item>
        </el-form>
        <div></div>
      </div>
    </el-card>
    <div class="table-box">
      <IconTitle title="巡检记录" imgUrl="yunwei">
        <span class="slot">巡检记录表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="deviceData"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="date" label="设备编号" align="center">
        </el-table-column>
          <el-table-column prop="date" label="发生日期" align="center">
        </el-table-column>
        <el-table-column prop="name" label="创建人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="开销" align="center">
        </el-table-column>
        <el-table-column prop="address" label="价值变动" align="center">
        </el-table-column>
         <el-table-column prop="name" label="变动值" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调入部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调入地点" align="center">
        </el-table-column>
        <el-table-column prop="name" label="新负责人" align="center">
        </el-table-column>
        <el-table-column prop="address" label="备注" align="center">
        </el-table-column>
      </el-table>
    </div>
        <el-card class="box-card btn-search page-search">
      <div slot="header">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left"></el-button>
          <el-button type="check" icon="el-icon-download">导出</el-button>
        </div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="备件编号">
            <el-input
              placeholder="备件编号"
              v-model="searchForm.roleName"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="备件名称">
            <el-input
              placeholder="备件名称"
              v-model="searchForm.roleCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="故障类型">
            <el-select
              placeholder="故障类型"
              v-model="searchForm.dsType"
              clearable
              style="width: 120px"
            >
              <el-option label="你好" value="1"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search">查询</el-button>
            <el-button icon="el-icon-refresh-right">重置</el-button>
          </el-form-item>
        </el-form>
        <div></div>
      </div>
    </el-card>
    <div class="table-box">
      <IconTitle title="更换记录" imgUrl="yunwei">
        <span class="slot">更换记录表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="deviceData"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="date" label="设备编号" align="center">
        </el-table-column>
          <el-table-column prop="date" label="发生日期" align="center">
        </el-table-column>
        <el-table-column prop="name" label="创建人" align="center">
        </el-table-column>
        <el-table-column prop="name" label="开销" align="center">
        </el-table-column>
        <el-table-column prop="address" label="价值变动" align="center">
        </el-table-column>
         <el-table-column prop="name" label="变动值" align="center">
        </el-table-column>
        <el-table-column prop="name" label="调入部门" align="center">
        </el-table-column>
        <el-table-column prop="address" label="调入地点" align="center">
        </el-table-column>
        <el-table-column prop="name" label="新负责人" align="center">
        </el-table-column>
        <el-table-column prop="address" label="备注" align="center">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";

export default {
  name: "spare",
  components: {
    IconTitle,
  },
  data() {
    return {
        searchForm:{},
         deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区",
        },
      ],
    };
  },
  methods: {
       deviceSelectionChange(){}
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
.allocation {
  
}
</style>
