import request from '@/utils/request'
import { requestPlatForm } from "@/utils/requestBase";


// 查询课程信息列表
export function listCourseInfo(query) {
    return request({
        url: '/industryClassroom/courseInfo/list',
        method: 'post',
        params: query
    })
}
// 查询课程信息列表 parentId is null
export function getCourseList(query) {
    return request({
        url: '/industryClassroom/courseInfo/getCourseList',
        method: 'post',
        params: query
    })
}

//上下架产品
export function upDownCourse(data) {
    return request({
        url: '/industryClassroom/courseInfo/batchUpdateStatus',
        method: 'post',
        data
    })
}

// 查询课程信息列表 不分页
export function getCourseInfoList() {
    return request({
        url: '/industryClassroom/courseInfo/getCourseInfoList',
        method: 'get'
    })
}

// 查询课程信息详细
export function getCourseInfo(id) {
    return request({
        url: '/industryClassroom/courseInfo/getInfo/' + id,
        method: 'get'
    })
}

// 新增课程信息
export function addCourseInfo(data) {
    return request({
        url: '/industryClassroom/courseInfo/add',
        method: 'post',
        data: data
    })
}

// 修改课程信息
export function updateCourseInfo(data) {
    return request({
        url: '/industryClassroom/courseInfo/edit',
        method: 'put',
        data: data
    })
}

// 删除课程信息
export function delCourseInfo(id) {
    return request({
        url: '/industryClassroom/courseInfo/remove/' + id,
        method: 'delete'
    })
}

// 获取课程类别 课程所属环节
export function getCourseDictData() {
    return request({
        url: '/industryClassroom/courseInfo/getCourseDictData',
        method: 'get'
    })
}

// 新增课程信息
export function addCourse(data) {
    return request({
        url: '/industryClassroom/courseInfo/addCourse',
        method: 'post',
        data: data
    })
}

// 新增章节信息
export function addChapter(data) {
    return request({
        url: '/industryClassroom/courseInfo/addChapter',
        method: 'post',
        data: data
    })
}

// 新增视频信息
export function addVideo(data) {
    return request({
        url: '/industryClassroom/courseInfo/addVideo',
        method: 'post',
        data: data
    })
}

// 获取课程类别 课程所属环节
export function getCourseServiceList() {
    return request({
        url: '/industryClassroom/courseInfo/getCourseServiceList',
        method: 'get'
    })
}


export function addChapterAndVideo(data) {
    return requestPlatForm({
        url: '/industryClassroom/courseInfo/addChapterAndVideo',
        method: 'post',
        data: data
    })
}


export function getChapterVideoByCourseId(id) {
    return requestPlatForm({
        url: '/industryClassroom/courseInfo/getChapterVideoByCourseId/' + id,
        method: 'get'
    })
}

export function deletemapping(id) {
    return request({
        url: `/industryClassroom/courseInfo/remove/` + id,
        method: 'delete'
    })
}

export function editChapterAndVideo(data) {
    return requestPlatForm({
        url: '/industryClassroom/courseInfo/editChapterAndVideo',
        method: 'post',
        data: data
    })
}