<template>
  <div id="transverse" :style="{width: '400px', height: '250px'}"></div>
</template>

<script>
import {getQuarterCount} from "@/api/ems/inspection/maintenanceStatistical"

let numberEchartsOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    containLabel: true
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      interval:0,//代表显示所有y轴标签显示
    }
  },
  series: [
    {
      name: '故障次数',
      type: 'bar',
      barWidth: 10,
      stack: '数量',
      color: '#7898e1',
      data: []
    },
    {
      name: '未保养',
      type: 'bar',
      barWidth:10,
      stack: '数量',
      color: '#f89588',
      data:  [],
    }
  ]
};
export default {
  data() {
    return {
      quarterCountData: [],
      noMaintenanceData: [],
      failureNumberData: [],
      numberEchartsOptions,
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let transverse = this.$echarts.init(document.getElementById('transverse'))
      getQuarterCount().then(res => {
        this.quarterCountData = res.data.data;
        this.noMaintenanceData = this.quarterCountData.noMaintenanceList;
        let yData = [];
        this.noMaintenanceData.forEach(function (item) {
          if (Number(item.mon) === 1) {
            yData.push("第一季")
          } else if (Number(item.mon) === 2) {
            yData.push("第二季")
          } else if (Number(item.mon)=== 3) {
            yData.push("第三季")
          } else if (Number(item.mon) === 4) {
            yData.push("第四季")
          }
          numberEchartsOptions.yAxis.data = yData;
          numberEchartsOptions.series[1].data.push(item.num)
        })
        this.failureNumberData = this.quarterCountData.failureNumberList;
        this.failureNumberData.forEach(function (item) {
          numberEchartsOptions.series[0].data.push(item.num);
        })
        // 绘制图表
        transverse.setOption(this.numberEchartsOptions);
      });


    }
  }
}

</script>
