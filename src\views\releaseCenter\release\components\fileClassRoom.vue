<template>
  <div
    class="pageMainTop"
    v-loading="flagloading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
  >
    <div class="app-containerPage">
      <div class="tagsTilte" style="margin-bottom: 15px">课堂信息</div>
      <div class="pageMain" label-position="right">
        <div style="padding: 0px 0px 20px 0px">
          <el-form
            :disabled="detailFlag"
            v-show="step === 1"
            :model="formOne"
            :rules="formOneRules"
            ref="formOne"
            label-width="130px"
            class="demo-ruleForm"
          >
            <el-row>
              <template>
                <!-- <el-col :span="18">
                  <el-form-item label="课程名称" prop="title">
                    <el-input
                      v-model="formOne.title"
                      placeholder="请输入课程名称"
                      :style="{ width: pageFormItemStyle.width }"
                    />
                  </el-form-item>
                </el-col> -->
                <el-col :span="18">
                  <el-form-item label="文件名称" prop="title">
                    <el-input
                      v-model="formOne.title"
                      placeholder="请输入文件名称"
                      :style="{ width: pageFormItemStyle.width }"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="18">
                  <el-form-item
                    class="uploadItem"
                    label="文档封面"
                    prop="imagePath"
                  >
                    <NewImageUpload
                      :disabled="detailFlag"
                      v-model="formOne.imagePath"
                      :limit="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    class="uploadItem"
                    label="附件"
                    prop="policyUrl"
                  >
                    <el-upload
                      ref="upload"
                      :limit="upload.limit"
                      :accept="upload.accept"
                      :headers="upload.headers"
                      :action="upload.url"
                      :disabled="upload.isUploading"
                      :on-progress="handleFileUploadProgress"
                      :on-success="handleFileSuccess"
                      :before-remove="beforeRemove"
                      :on-exceed="handleExceed"
                      :file-list="formOne.policyUrl"
                    >
                      <el-button size="small" type="primary"
                        >点击上传</el-button
                      >
                      <div class="el-upload__tip" slot="tip">
                        只能上传{{ upload.accept }}后缀文件(<span
                          style="color: red"
                          >多文件请上传压缩包</span
                        >)
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { changeNumToHan } from "@/utils/ruoyi";
import iconImg from "@/assets/images/矩形.png";
import { getToken } from "@/utils/auth";
import NewImageUpload from "@/components/NewImageUpload";
export default {
  dicts: ["course_link", "course_category"],
  name: "FileClassRoom",
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  components: {
    NewImageUpload,
  },
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      Icon: iconImg,
      step: 1,
      formOneId: "",
      height: document.documentElement.clientHeight - 94.5 + "px;",
      formOne: {
        policyUrl: [],
      },
      formOneRules: {
        title: [
          { required: true, message: "请输入文件名称", trigger: "change" },
        ],
        imagePath: [
          { required: true, message: "请上次文档封面", trigger: "change" },
        ],
        policyUrl: [
          { required: true, message: "请上传文件附件", trigger: "change" },
        ],
      },
      fileList: [],
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/file/upload",
        accept: ".pdf,.docx,.xls,.xlsx,.zip,.rar",
        limit: 1,
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
      flagloading: false,
    };
  },
  created() {},
  watch: {
    "formTwo.taskDataList"(newName, oldName) {},
  },
  computed: {
    pageFormItemStyle() {
      return {
        width: "100%",
      };
    },
  },
  methods: {
    changeNumToHan,
    init(data) {
      const { title, imagePath, remark } = data;
      this.formOne = {
        title,
        imagePath,
        policyUrl: [
          {
            name: remark.split("/").pop(),
            url: remark,
            uid: this.generateUniqueId(),
          },
        ],
      };
    },
    // 文件上传进度处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      const data = {
        ...response.data,
        uid: this.generateUniqueId(),
      };
      this.formOne.policyUrl.push(data);
    },
    // 生成唯一ID的函数
    generateUniqueId() {
      // 这里可以使用例如UUID库或其他方式生成唯一ID
      return Math.random().toString(36).substring(7);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.upload.limit} 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      );
    },
    // 文件移除事件
    beforeRemove(file, fileList) {
      // 手动在fileList中删除文件
      const index = this.formOne.policyUrl.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        this.formOne.policyUrl.splice(index, 1);
      }
      return true;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const icCourseInfo = {
            ...this.formOne,
            remark: this.formOne.policyUrl[0].url,
            // id: newId
          };
          this.$emit("submit", icCourseInfo, (res) => {});
        }
      });
    },
  },
};
</script>

<style scoped>
.pageMainTop {
  width: 70%;
}

.avatar-uploader /deep/ .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader /deep/ .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.loadingDiv {
  left: -100px !important;
  min-height: calc(100vh - 300px);
}

.cardFdone /deep/ .el-card__body {
  padding: 0px !important;
}

.collapseCustom {
  margin-left: 75px;
  margin-right: 140px;
}

.collapseCustom /deep/ .el-icon-arrow-right {
  display: none !important;
}

.collapseCustom /deep/ .el-collapse-item__header {
  /* background-color: #F3F5F8 !important; */
  padding-left: 20px !important;
}

.collapseSingular /deep/ .el-collapse-item__header {
  background-color: #ffffff !important;
}

.collapseEvenlar /deep/ .el-collapse-item__header {
  background-color: #f3f5f8 !important;
}

.collapseCustom /deep/ .el-collapse-item__content {
  padding-top: 25px !important;
}

.pageMainOrs /deep/ .avatar-uploader {
  line-height: 0;
}

.collapseCustom /deep/.el-collapse-item__wrap {
  border: 0px !important;
}

.addChapter {
  width: 110px;
  height: 32px;
  border: 1px solid #1b4596;
  opacity: 1;
  border-radius: 0px;
  color: #1b4596;
}
</style>
