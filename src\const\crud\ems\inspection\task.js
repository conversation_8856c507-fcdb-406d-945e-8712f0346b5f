import { getHeaders } from "@/const/crud/getHeaders"

const headers = getHeaders();

const frontLabel = process.env.VUE_APP_BASE_API;

export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "delBtn": false,
  editBtn:false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
  menu:true,
  "column": [
	  {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
        hide: true
    },	  {
      "type": "input",
      "label": "任务编号",
      "prop": "taskNum",
      "span": 12,
      "search":true,
    },
    // {
    //   "type": "input",
    //   "label": "责任人id",
    //   "prop": "liableUserId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "计划编号",
      "prop": "planNum",
      "span": 12
    },{
      "type": "input",
      "label": "计划名称",
      "prop": "planName",
      "span": 12,
      "search":true,
    },		  {
      "type": "input",
      "label": "任务开始时间",
      "prop": "planBeginTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "任务结束时间",
      "prop": "planEndTime",
      "span": 12
    },
    {
      "type": "select",
      "label": "任务状态",
      "prop": "status",
      "span": 12,
      slot:true,
      "search":true,
      dicUrl:`${frontLabel}/system/dict/data/type/ems_ins_inspect_task_status`,
      dicHeaders: headers
    },
    {
      "type": "select",
      "label": "转让状态",
      "prop": "transferStatus",
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/ems_ins_inspect_task_transfer_status`,
      dicHeaders: headers
    },
    {
      "type": "select",
      "label": "验收状态",
      "prop": "checkStatus",
      "span": 12,
      dicUrl:`${frontLabel}/system/dict/data/type/task_check_status`,
      dicHeaders: headers
    },{
      "type": "input",
      "label": "开始执行时间",
      "prop": "executeTime",
      "span": 12
    },
    {
      "type": "input",
      "label": "结束执行时间",
      "prop": "executeEndTime",
      "span": 12
    }
    // {
    //   "type": "input",
    //   "label": "应检设备数",
    //   "prop": "shouldCheckNum",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "漏检设备数",
    //   "prop": "leakDetectionNum",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "异常设备数",
    //   "prop": "abnormalNum",
    //   "span": 12
    // }
    ]
}
