<template>
    <div class="app-container workshopPage">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
            <el-form-item label="名称" prop="searchValue">
                <el-input v-model="queryParams.searchValue" placeholder="请输入菜单名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd(1)"
                    v-hasPermi="['system:menu:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
        </el-row>

        <el-table  v-if="refreshTable"  v-loading="loading" :data="menuList" row-key="uniqueFlag" :default-expand-all="expandAll"
            :tree-props="{ children: 'childrenList', hasChildren: 'children' }">
            <el-table-column prop="tname" label="菜单名称" :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="sort" label="排序"></el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime"></el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button style="color:#67c23a" v-if="scope.row.level!==3" size="mini" type="text" @click="handleAdd(scope.row)">新增</el-button>
                    <el-button size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button style="color:#f56c6c" size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item :label="dialogMap[levelKey]+'名称'" prop="name">
                    <el-input style="width: 80%;" v-model="form.name" placeholder="请输入菜单名称" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number style="width: 28%;" placeholder="请输入菜单名称" v-model="form.sort" controls-position="right" :min="1" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>

import { getWorkShopList,addWorkShop,editWorkShop,deleteWorkShop} from "@/api/gatewayManage/workshop/index.js";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
    name: "workshopPage",
    data() {
        return {
            loading: true,
            showSearch: true,
            menuList: [],
            title: "",
            open: false,
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                searchValue: '',
            },
            total: 0,
            form: {

            },
            rules: {
                menuName: [
                    { required: true, message: "菜单名称不能为空", trigger: "blur" }
                ],
                orderNum: [
                    { required: true, message: "菜单顺序不能为空", trigger: "blur" }
                ],
                path: [
                    { required: true, message: "路由地址不能为空", trigger: "blur" }
                ]
            },
            levelKey:null,
            dialogMap:{
                1:'公司',
                2:"工厂",
                3:'车间'
            },
            expandAll:false,
            refreshTable:true
        };
    },
    created() {
        this.getList();
    },
    methods: {

        getList() {
            this.loading = true;
            getWorkShopList(this.queryParams).then(response => {
                const { list, total } = response.data
                this.menuList = list;
                this.total = total;
                this.toggleExpandAll(this.queryParams.searchValue?true:false)
            }).finally(() => this.loading = false)
        },

        cancel() {
            this.open = false;
            this.reset();
        },
        reset() {
            this.form = {
                name:undefined,
                sort:undefined
            };
            this.resetForm("form");
        },

        toggleExpandAll(flag) {
            this.refreshTable = false;
            this.expandAll = flag;
            this.$nextTick(() => {
                this.refreshTable = true;
            });
        },

        handleQuery() {
            this.getList();
        },
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleAdd(row) {
            this.reset();
            if(row==1){
                this.levelKey=row;
            }
            else{
                this.levelKey=row.level+1;
                this.form={...this.form,upId:row.id}
            }
            this.open = true;
            this.title = "新增";
        },
        handleUpdate(row) {
            this.reset();
            this.levelKey=row.level;
            this.form={
                id:row.id,
                name:row.tname,
                sort:row.sort,
            };
                this.open = true;
                this.title = "修改";
        },
        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    const pamres ={
                        ...this.form,
                        level:this.levelKey
                    }
                    if (pamres.id != undefined) {
                        editWorkShop(pamres).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWorkShop(pamres).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const pamres={ids:row.id,level:row.level}
            this.$modal.confirm('是否确认删除？').then(function () {
                return deleteWorkShop(pamres);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        }
    }
};
</script>
  



<style lang="scss" scoped>
.workshopPage {}
</style>