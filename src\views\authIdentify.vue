<template>
    <div class="authIdentify app-container">
        <el-form ref="baseForm" :model="baseForm" :rules="rules" label-position="right" label-width="150px">
            <div class="tagsTilte" style="margin-bottom: 15px;">{{ authType == 'ENTERPRISE' ? '企业' : '服务商' }}基本信息</div>
            <el-row style="padding:20px 40px 20px 20px" :gutter="50">
                <el-col :span="12">
                    <el-form-item prop="serviceProviderName" :label="authType == 'ENTERPRISE' ? '企业名称' : '服务商名称'">
                        <el-autocomplete :readonly="allDisabled" @input="enterpriseChange" :trigger-on-focus="false"
                            value-key="serviceProviderName" style="width:100%" v-model="baseForm.serviceProviderName"
                            :fetch-suggestions="querySearchAsync" placeholder="企业关键字"
                            @select="enterpriseSelect"></el-autocomplete>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="uscc" label="统一社会信用代码">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.uscc"
                            placeholder="统一社会信用代码"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="legalPerson" label="法人">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.legalPerson"
                            placeholder="请输入法人"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="industryCode" label="行业">
                        <el-cascader :disabled="disabled || allDisabled" style="width:100%"
                            v-model="baseForm.industryCode" placeholder="请选择行业" :options="industryDict" :props="{
                                children: 'childrenList',
                                label: 'name',
                                value: 'industryCode',
                            }" :show-all-levels="true"></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="area" label="省市区">
                        <el-cascader :disabled="disabled || allDisabled" style="width:100%" v-model="baseForm.area"
                            placeholder="请选择省市区" :options="areaDict" :props="{
                                children: 'children',
                                label: 'name',
                                value: 'code',
                            }" :show-all-levels="true"></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="officeAddress" label="办公地址">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.officeAddress"
                            placeholder="办公地址"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="organizationCode" label="组织代码">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.organizationCode"
                            placeholder="组织代码"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="所属园区" prop="parkId">
                        <el-select :disabled="disabled || allDisabled" v-model="baseForm.parkId" placeholder="请选择所属园区"
                            style="width:100%" clearable>
                            <el-option v-for="t in parkDataList" :key="t.parkId" :label="t.name" :value="t.parkId" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="标签名称" prop="labelIds">
                        <el-select :disabled="disabled || allDisabled" style="width: 100%;" multiple filterable
                            v-model="baseForm.labelIds" placeholder="请选择标签">
                            <el-option v-for="item in labelList" :key="item.id" :label="item.labelName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="产业" prop="industrialIdJson">
                        <el-cascader :disabled="disabled || allDisabled" style="width:100%"
                            v-model="baseForm.industrialIdJson" :options="treeDataList" :props="{
                                children: 'children',
                                label: 'vueName',
                                value: 'id',
                            }" placeholder="请选择产业" clearable>
                        </el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="telephone" label="联系方式">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.telephone"
                            placeholder="联系方式"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="registerAddress" label="注册地址">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.registerAddress"
                            placeholder="注册地址"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="registerDate" label="注册日期">
                        <el-date-picker :readonly="allDisabled" style="width:100%" value-format="yyyy-MM-dd"
                            :disabled="disabled" v-model="baseForm.registerDate" placeholder="注册日期"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="registerCapital" label="注册资本">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.registerCapital"
                            placeholder="注册资本"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="recordDep" label="登记机关">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.recordDep"
                            placeholder="登记机关"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="postalCode" label="邮政编码">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.postalCode"
                            placeholder="邮政编码"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="business" label="经营范围">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.business"
                            placeholder="经营范围"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="mainBusiness" label="主营范围">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.mainBusiness"
                            placeholder="主营范围"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="email" label="电子邮箱">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.email"
                            placeholder="电子邮箱"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="url" label="公司URL地址">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.url"
                            placeholder="公司url地址"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="briefIntroduction" label="简介">
                        <el-input :readonly="allDisabled" :disabled="disabled" v-model="baseForm.briefIntroduction"
                            placeholder="简介"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="logo" :label="authType == 'ENTERPRISE' ? '企业logo' : '服务商logo'">
                        <el-upload :show-file-list="false" :disabled="disabled || allDisabled"
                            accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                            :headers="headers" :on-success="handleLogoSuccess">
                            <img style="width:100%;height:100%" v-if="baseForm.logo" :src="ensureFullUrl(baseForm.logo)"
                                class="avatar" />
                            <i v-else class="el-icon-plus"></i>
                            <div class="el-upload__tip" slot="tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-form ref="identifyForm" style="margin-bottom:30px" :model="identifyForm" :rules="rules"
            label-position="right" label-width="130px">
            <div class="tagsTilte" style="margin-bottom: 15px;">认证信息</div>
            <el-row style="padding:20px 40px 20px 20px" :gutter="50">
                <el-col :span="24">
                    <el-form-item prop="operatorName" label="经办人姓名">
                        <el-input :readonly="allDisabled" style="width:50%" v-model="identifyForm.operatorName"
                            placeholder="经办人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="operatorTel" label="经办人手机号">
                        <el-input :readonly="allDisabled" style="width:50%" v-model="identifyForm.operatorTel"
                            placeholder="经办人手机号"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="operatorCardImageJust" label="经办人身份证">
                        <div class="upload-img" style="display:flex">
                            <el-upload :disabled="allDisabled" :show-file-list="false" accept=".jpg,.png,.img,.jpeg"
                                :action="uploadUrl" list-type="picture-card" :headers="headers"
                                :on-success="operatorCardImageJustSuccess">
                                <img style="width:100%;height:100%" v-if="identifyForm.operatorCardImageJust"
                                    :src="ensureFullUrl(identifyForm.operatorCardImageJust)" class="avatar" />
                                <i v-else class="el-icon-plus"></i>
                            </el-upload>
                            <el-upload :disabled="allDisabled" style="margin-left:10px" :show-file-list="false"
                                accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                                :headers="headers" :on-success="operatorCardImageBackSuccess">
                                <img style="width:100%;height:100%" v-if="identifyForm.operatorCardImageBack"
                                    :src="ensureFullUrl(identifyForm.operatorCardImageBack)" class="avatar" />
                                <i v-else class="el-icon-plus"></i>
                            </el-upload>
                        </div>
                        <div class="el-upload__tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="idCardImageJust" label="法人身份证">
                        <div class="upload-img" style="display:flex">
                            <el-upload :disabled="allDisabled" :show-file-list="false" accept=".jpg,.png,.img,.jpeg"
                                :action="uploadUrl" list-type="picture-card" :headers="headers"
                                :on-success="idCardImageJustSuccess">
                                <img style="width:100%;height:100%" v-if="identifyForm.idCardImageJust"
                                    :src="ensureFullUrl(identifyForm.idCardImageJust)" class="avatar" />
                                <i v-else class="el-icon-plus"></i>
                            </el-upload>
                            <el-upload :disabled="allDisabled" style="margin-left:10px" :show-file-list="false"
                                accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                                :headers="headers" :on-success="idCardImageBackSuccess">
                                <img style="width:100%;height:100%" v-if="identifyForm.idCardImageBack"
                                    :src="ensureFullUrl(identifyForm.idCardImageBack)" class="avatar" />
                                <i v-else class="el-icon-plus"></i>
                            </el-upload>
                        </div>
                        <div class="el-upload__tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="businessLicenseImage" label="营业执照">
                        <el-upload :disabled="allDisabled" :show-file-list="false" accept=".jpg,.png,.img,.jpeg"
                            :action="uploadUrl" list-type="picture-card" :headers="headers"
                            :on-success="businessLicenseImageSuccess">
                            <img style="width:100%;height:100%" v-if="identifyForm.businessLicenseImage"
                                :src="ensureFullUrl(identifyForm.businessLicenseImage)" class="avatar">
                            <i v-else class="el-icon-plus"></i>
                            <div class="el-upload__tip" slot="tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="12">
                    <el-form-item prop="authorizationLetter" label="授权函地址">
                        <el-input :readonly="allDisabled" v-model="identifyForm.authorizationLetter" placeholder="授权函地址"></el-input>
                    </el-form-item>
                </el-col> -->
            </el-row>
        </el-form>
        <div class="pageFooter">
            <el-button @click="$router.go(-1)">返回</el-button>
            <el-button v-if="!allDisabled" :loading="loading" type="primary" @click="submitBtn">提交</el-button>
        </div>
    </div>
</template>

<script>
import { getIndustryTree, searchEnterprise, submitIdentify, getParkList, getSubstitutionTree } from '@/api/index'
import {
    getListByProductType,
} from "@/api/release/index";
import { getCityData } from "@/api/release/financial";
import { getToken } from '@/utils/auth'
import { validPhone, validEmail } from '@/utils/validate'
import { getInfo } from "@/api/release/index.js";
export default {
    name: "authIdentify",
    data() {
        return {
            headers: {
                Authorization: "Bearer " + getToken()
            },
            loading: false,
            disabled: false,
            allDisabled: false,
            flowInstanceId: '',
            baseForm: {},
            identifyForm: {},
            rules: {
                uscc: [
                    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
                ],
                telephone: [
                    { required: false, validator: validPhone, trigger: 'change' }
                ],
                operatorTel: [
                    { required: false, validator: validPhone, trigger: 'change' }
                ]
            },
            uploadUrl: process.env.VUE_APP_BASE_API + "/system/file/upload",
            industryDict: [],
            areaDict: [],
            parkDataList: [],
            treeDataList: [],
            labelList: []
        };
    },
    created() {
        this.getLabelData()
        this.getSubstitutionTreeData()
        this.getParkData()
        this.getIndustryTree()
        this.getAreaDict()
        const { flowInstanceId, isEdit } = this.$route.query;
        if (flowInstanceId) {
            this.flowInstanceId = flowInstanceId
            isEdit != '1' && (this.allDisabled = true)
            this.getFormDataFtn(flowInstanceId)
        }
    },
    computed: {
        authType() {
            return this.$route.query.authType || this.identifyForm.stype
        }
    },
    watch: {
        'baseForm.serviceProviderId': {
            handler(newVal) {
                if (newVal) {
                    this.disabled = true
                }
            },
            deep: true
        }
    },
    methods: {
        getFormDataFtn(flowInstanceId) {
            getInfo({ flowInstanceId }).then(res => {
                const { enterpriseServiceDTO, enterpriseServiceInfoDTO } = res.data.params;
                if (enterpriseServiceInfoDTO) {
                    this.$set(this, 'baseForm', {
                        ...enterpriseServiceInfoDTO,
                        area: [enterpriseServiceInfoDTO.provinceCode, enterpriseServiceInfoDTO.cityCode, enterpriseServiceInfoDTO.districtCode],
                        industrialIdJson: [enterpriseServiceInfoDTO.industrialKindId, enterpriseServiceInfoDTO.industrialTypeId]
                    })
                }
                if (enterpriseServiceDTO) {
                    this.$set(this, 'identifyForm', {
                        ...enterpriseServiceDTO
                    })
                }
            })
        },
        submitBtn() {
            this.$refs.baseForm.validate((valid) => {
                if (valid) {
                    this.$refs.identifyForm.validate(valid1 => {
                        if (valid1) {
                            this.loading = true
                            const { industrialIdJson, ...baseForm } = this.baseForm;
                            const enterpriseServiceInfoDTO = {
                                ...baseForm,
                                industrialKindId: industrialIdJson ? industrialIdJson[0] : null,
                                industrialTypeId: industrialIdJson ? industrialIdJson[1] : null,
                            };
                            if (enterpriseServiceInfoDTO.area && enterpriseServiceInfoDTO.area.length) {
                                const [provinceCode, cityCode, districtCode] = enterpriseServiceInfoDTO.area
                                enterpriseServiceInfoDTO.provinceCode = provinceCode
                                enterpriseServiceInfoDTO.cityCode = cityCode
                                enterpriseServiceInfoDTO.districtCode = districtCode
                            }
                            delete enterpriseServiceInfoDTO.area
                            const params = {
                                enterpriseServiceInfoDTO, enterpriseServiceDTO: {
                                    ...this.identifyForm, stype: this.authType,
                                }
                            }
                            submitIdentify({ params, productType: this.authType + "_AUTH", flowInstanceId: this.flowInstanceId }, this.identifyForm.id ? 'put' : 'post').then(res => {
                                if (res.code == 200) {
                                    this.$message.success(`认证提交成功,请等待审核!`);
                                    this.$router.push({ path: '/index', query: { reIdentify: '1' } })
                                }
                            }).finally(() => {
                                this.loading = false
                            })
                        }
                    })
                }
            })
        },
        enterpriseSelect(v) {
            this.$set(this, 'baseForm', { ...v, area: new Array(v.provinceCode, v.cityCode, v.districtCode) })
            // this.disabled = true
        },
        getLabelData() {
            const params = { productType: 'ENTERPRISE' }
            getListByProductType(params).then(res => {
                this.labelList = res.data || []
            })
        },
        enterpriseChange(v) {
            if (!v) {
                this.disabled = false
                this.$set(this, 'baseForm', {})
            }
        },
        querySearchAsync(queryString, cb) {
            searchEnterprise({ serviceProviderName: queryString }).then(res => {
                cb(res.data || [])
            })
        },
        getParkData() {
            getParkList().then(res => {
                this.parkDataList = res.data || []
            })
        },
        getSubstitutionTreeData() {
            getSubstitutionTree().then(res => {
                this.treeDataList = res.data || []
            })
        },
        getIndustryTree() {
            getIndustryTree().then(res => {
                this.industryDict = res.data || []
            })
        },
        getAreaDict() {
            getCityData().then(res => {
                this.areaDict = res.data || []
            })
        },
        idCardImageJustSuccess(res, file) {
            this.$set(this.identifyForm, 'idCardImageJust', res.data.url || '')
        },
        businessLicenseImageSuccess(res, file) {
            this.$set(this.identifyForm, 'businessLicenseImage', res.data.url || '')
        },
        idCardImageBackSuccess(res, file) {
            this.$set(this.identifyForm, 'idCardImageBack', res.data.url || '')
        },
        operatorCardImageBackSuccess(res, file) {
            this.$set(this.identifyForm, 'operatorCardImageBack', res.data.url || '')
        },
        operatorCardImageJustSuccess(res, file) {
            this.$set(this.identifyForm, 'operatorCardImageJust', res.data.url || '')
        },
        handleLogoSuccess(res, file) {
            this.$set(this.baseForm, 'logo', res.data.url || '')
        },
        handleAvatarSuccess(res, file, key, index) {
            if (res.code == 500) {
                this.$message.error('上传失败');
                return
            }
            const { data } = res;
            let formkey;
            if (key == 'applicationHighlights') {
                const appData = this.form.applicationHighlights;
                appData[index].imgUrlHighlights = data.url;
                this.form = {
                    ...this.form,
                    applicationHighlights: [...appData]
                };
                formkey = key + '.' + index + '.imgUrlHighlights';
            }
            else if (key == 'applicationScenario') {
                this.form = {
                    ...this.form,
                    applicationScenario: { ...this.form.applicationScenario, imgUrlScenario: data.url }
                };
                formkey = 'applicationScenario.imgUrlScenario';
            }
            else {
                this.form = { ...this.form, imgUrl: data.url };
                formkey = 'imgUrl';
            }
            if (res.code === 200) {
                this.$refs.form.clearValidate(formkey)
            }
        },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (['jpg', 'img', 'png'].indexOf(fileType) == -1) {
                this.$message.error('请上传后缀为.jpg .img .png格式的图片文件');
                return false;
            }
        },

        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    const params = {
                        ...this.form,
                        // industrialTypes: this.form.industrialTypes.join(','),
                        appCases: this.form.appCases.join(','),
                        industrialType: 1,
                    }
                    this.$emit('submitFtn', params, (res) => {
                        // 相应结束后的其他逻辑
                    });
                }
            });
        },
    },

};
</script>



<style lang="scss" scoped>
.authIdentify {
    padding: 30px 20px 20px 20px;

    .pageFooter {
        margin-top: 30px;
        width: 100%;
        position: fixed;
        padding: 0 20px;
        bottom: 0px;
        background: #FFFFFF;
        box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
        opacity: 1;
        border-radius: 0px;
        height: 60px;
        display: flex;
        justify-content: end;
        align-items: center;
        right: 20px
    }
}
</style>