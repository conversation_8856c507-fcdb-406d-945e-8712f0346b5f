<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="资源类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择资源类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.sd_supply_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="标签" prop="label">
            <el-select
              v-model="form.label"
              placeholder="请选择标签"
              style="width: 100%"
              multiple
              clearable
            >
              <el-option
                v-for="dict in dict.type.supply_lable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="资源名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入资源名称"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="产品数量" prop="num">
            <el-input-number
              style="width: 100%"
              v-model="form.num"
              :min="0"
              label="产品数量"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="行业" prop="industrialSubstitutionIdList">
            <el-cascader
              @change="industryChange"
              v-model="form.industrialSubstitutionIdList"
              :options="industrys"
              :props="{
                children: 'children',
                label: 'vueName',
                value: 'id',
              }"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="产品" prop="productTypeId">
            <el-select
              v-model="form.productTypeId"
              placeholder="请先选择行业"
              style="width: 100%"
            >
              <el-option
                v-for="item in specsData"
                :key="item.id"
                :label="item.productName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!--        <el-col :span="10">
          <el-form-item label="行业" prop="industryTypeCodes">
            <el-cascader
              v-model="form.industryTypeCodes"
              :options="industryTypes"
              :props="{
                children: 'childrenList',
                label: 'name',
                value: 'industryCode',
              }"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>-->
        <el-col :span="10">
          <el-form-item label="联系人" prop="linkmen">
            <el-input
              v-model="form.linkmen"
              placeholder="请输入联系人"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="电话咨询号码" prop="telephoneCounseling">
            <el-input
              v-model="form.telephoneCounseling"
              placeholder="请输入电话咨询号码"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="交易地点" prop="area">
            <el-cascader
              v-model="form.area"
              :options="cityData"
              :props="{
                checkStrictly: true,
                children: 'children',
                label: 'name',
                value: 'code',
              }"
              clearable
              placeholder="请选择交易地点"
              style="width: 100%"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="包装说明" prop="packing">
            <el-input
              v-model="form.packing"
              placeholder="请输入包装说明"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="资源描述内容" prop="description">
            <el-input
              v-model="form.description"
              placeholder="请输入资源描述内容"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="最高预算类型" prop="maximumBudgetType">
            <el-radio-group v-model="form.maximumBudgetType">
              <el-radio :label="0">面议</el-radio>
              <el-radio :label="1">具体最高预算</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="form.maximumBudgetType == 1">
          <el-form-item label="最高预算（元）" prop="maximumBudget">
            <el-input-number
              type="number"
              v-model="form.maximumBudget"
              controls-position="right"
              :precision="2"
              :min="0"
              placeholder="请输入最高预算"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item class="uploadItem" label="资源缩略图" prop="picture">
            <UploadImage
              :disabled="detailFlag"
              :fileList="form.picture"
              @addUpload="addUpload"
              @removeUpload="removeUpload"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="20">
          <el-form-item label="详情介绍内容" prop="detailWord">
            <editor
              :readOnly="detailFlag"
              v-model="form.detailWord"
              :min-height="192"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="20">
          <el-form-item label="详情介绍内容" prop="otherDescription">
            <editor
              :readOnly="detailFlag"
              v-model="form.otherDescription"
              :min-height="192"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="过期时间" prop="expireTime">
            <el-date-picker
              v-model="form.expireTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              :picker-options="pickerOptions"
              placeholder="过期时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getCityData } from "@/api/release/financial";
import { getSysIndustry, getSysIndustryType } from "@/api/release/policy";
import { getInfo } from "@/api/release/index.js";
import { listByIndustrialSubstitutionId } from "@/api/release/resource.js";
import { getToken } from "@/utils/auth";
import UploadImage from "@/components/UploadImage";
export default {
  name: "declarationEnterprise",
  dicts: ["supply_lable", "sd_supply_type"],
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  data() {
    var validateURL = (rule, value, callback) => {
      const urlRegex = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
      const reg = /^(\d{3,4}-)?\d{7,8}$/;
      if (!urlRegex.test(value) && !reg.test(value)) {
        callback(new Error("请输入正确的电话号码"));
      } else {
        callback();
      }
    };
    return {
      form: {
        name: undefined,
        telephoneCounseling: undefined,
        type: undefined,
        label: undefined,
        industryTypeCodes: undefined,
        industrialSubstitutionIdList: undefined,
        description: undefined,
        picture: [],
        detailWord: "",
        linkmen: undefined,
        productTypeTreeNames: undefined, //产业id
        productTypeId: undefined, //所属规格id
        num: 0, //产品数量
        packing: undefined, //包装
        area: undefined, //交易地点
        expireTime: undefined, //过期时间
        otherDescription: undefined, //其他说明
        maximumBudgetType: 0, //交易类型
        maximumBudget: 0, //交易额
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 如果当前时间之前的时间都禁用，则减去一天的毫秒数
        },
      },
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/file/upload",
      },
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        telephoneCounseling: [
          { required: true, message: "请输入电话咨询号码", trigger: "blur" },
          { validator: validateURL, trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择类型", trigger: "change" }],
        label: [{ required: true, message: "请选择标签", trigger: "change" }],
        // industryTypeCodes: [
        //   { required: true, message: "请选择行业", trigger: "change" },
        // ],
        industrialSubstitutionIdList: [
          { required: true, message: "请选择行业", trigger: "change" },
        ],
        description: [{ required: true, message: "请输入描述", trigger: "blur" }],
        picture: [{ required: true, message: "请上传图片", trigger: "change" }],
        productTypeTreeNames: [
          { required: true, message: "请选择规格", trigger: "change" },
        ],
        linkmen: [{ required: true, message: "请输入联系人", trigger: "blur" }],
        productTypeId: [{ required: true, message: "请选择规格", trigger: "change" }],
        num: [{ required: true, message: "请输入数量", trigger: "blur" }],
        packing: [{ required: true, message: "请输入包装", trigger: "blur" }],
        area: [{ required: true, message: "请选择交易地点", trigger: "change" }],
        expireTime: [{ required: true, message: "请选择过期时间", trigger: "change" }],
        otherDescription: [
          { required: true, message: "请输入其他说明", trigger: "blur" },
        ],
        maximumBudgetType: [
          { required: true, message: "请选择交易类型", trigger: "change" },
        ],
        detailWord: [{ required: true, message: "请输入详情介绍内容", trigger: "blur" }],
        maximumBudget: [{ required: true, message: "请输入交易额", trigger: "blur" }],
      },
      industryTypes: [],
      industrys: [],
      cityData: [],
      specsData: [],
      submitDing: false,
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },
  components: {
    UploadImage,
  },
  created() {
    this.getCityDataFtn();
    this.getSysIndustryTypeFtn(); //行业
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },
  methods: {
    //行业变化事件
    industryChange(res) {
      if (res[0] === undefined) {
        return;
      }
      let industrialSubstitutionId = res[1];
      if (industrialSubstitutionId === undefined) {
        industrialSubstitutionId = res[0];
      }
      this.getSpecsData(industrialSubstitutionId); //产品
    },
    //新增图片事件
    addUpload(res) {
      this.form.picture = [...this.form.picture, res.url];
    },
    //删除图片事件
    removeUpload(file) {
      const index = this.form.picture.indexOf(file);
      if (index > -1) {
        this.form.picture.splice(index, 1);
      }
    },
    //详情接口回显
    getFormDataFtn(flowInstanceId) {
      // this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.form = {
          ...params,
          picture: params.picture.split(","),
          type: params.type.toString(),
          area: JSON.parse(params.area),
          label: JSON.parse(params.label),
        };
        if (this.form.industrialSubstitutionIdList) {
          this.industryChange(this.form.industrialSubstitutionIdList);
        }
        // this.submitDing = false;
      });
    },
    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },
    getSysIndustryTypeFtn() {
      // getSysIndustryType().then((res) => {
      //   this.industryTypes = res.data;
      // });
      getSysIndustry().then((res) => {
        this.industrys = res.data;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const form = this.form;
          const productTypeId = form.productTypeId;
          // const picture = this.form.picture.map((item) => {
          //   return item.url;
          // });
          const foundItem = this.specsData.find((item) => item.id === productTypeId);
          let specifications = foundItem ? foundItem.productIndex : undefined;
          const params = {
            ...form,
            picture: this.form.picture.join(","),
            specifications,
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
    getSpecsData(industrialSubstitutionId) {
      listByIndustrialSubstitutionId(industrialSubstitutionId).then((res) => {
        this.specsData = res.data;
      });
    },
  },
};
</script>

<style lang="scss">
.financialCommn {
  width: 80%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }
}
</style>
