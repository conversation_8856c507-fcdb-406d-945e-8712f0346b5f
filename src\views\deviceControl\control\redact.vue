<template>
    <div class="app-container controlRedactPage">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
        <el-form-item label="控制策略" prop="warnName">
          <el-input v-model="queryParams.warnName" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:post:add']">新增</el-button>
        </el-col>
      </el-row>
  
      <el-table v-loading="loading" :data="postList">
        <el-table-column label="策略名称" prop="warnName" width="200"/>
        <el-table-column label="有效时间" prop="validTime"   width="400"/>
        <el-table-column label="规则描述" prop="description" />
        <el-table-column label="状态" prop="status" width="200">
          <template slot-scope="scope">
            <el-switch  :value="scope.row.status==1?true:false" @change="switchClick(scope.row)" active-color='#13ce66' ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              style="color: #FF4D4F;"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            </template>
        </el-table-column>
      </el-table>
  
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
        @pagination="getList" />
  
      <!-- 添加或修改岗位对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="60%" append-to-body custom-class="dynamicDemo">
        <div class="sketch_content" :style="{ height: sizeList['--dynamicHeight'] }">
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="设备名称">
              <!-- <span style="color: #FF4D4F;font-weight: bold;">{{ this.$route.query.name||'暂无' }}</span> -->
              <el-tag class="nameTag" effect="dark" type="danger">{{ this.$route.query.name||'暂无' }}</el-tag>
              <!-- <el-tag class="nameTag" effect="dark" type="danger">你是大帅比!</el-tag> -->
            </el-form-item>
            <el-form-item label="规则名称" prop="warnName">
              <el-input style="width: 80%;" v-model="form.warnName" placeholder="请输入规则名称" />
            </el-form-item>
            <div class="spanItem">
                <el-form-item label="有效时间" prop="validTimeType">
                <el-radio-group v-model="form.validTimeType">
                  <el-radio  :label="0">一直有效</el-radio>
                  <el-radio  :label="1" >指定时间</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item  prop="dateRange" class="colPicker" v-if="form.validTimeType===1">
                <el-date-picker
                v-model="form.dateRange"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="规则描述" prop="description">
              <el-input  style="width: 80%;" v-model="form.description" type="textarea" placeholder="请输入规则描述" />
            </el-form-item>
            <el-form-item label="触发器逻辑" prop="logic">
              <el-select style="width: 80%;" v-model="form.logic" placeholder="请选择">
                      <el-option label="或" value="or"></el-option>
                      <el-option label="且" value="and"></el-option>
                    </el-select>
            </el-form-item>
  
            <!-- 触发器 -->
            <div class="hintTitle" style="margin-top: 30px;">触发条件</div>
            <div class="dynaCard" v-for="(item, index) in form.warningTriggerVos" :key="item.key">
              <div class="dynaCardHead">
                <div class="hintTitleSamil">触发器{{ index + 1 }} </div>
                <el-popconfirm title="确认是否删除" @confirm="removeFormItem(item, 'warningTriggerVos')" v-if="form.warningTriggerVos&&form.warningTriggerVos.length>1">
                  <template #reference>
                    <i style="color: red;font-size: 18px;" class="el-icon-delete"></i>
                  </template>
                </el-popconfirm>
              </div>
              <el-row>
                <el-col :span="cloSpanFnt(item)">
                  <el-form-item label-width="70px" label='参数' :prop="'warningTriggerVos.' + index + '.field'" :rules="{
                    required: true, message: '请选择', actions: 'change'
                  }">
                    <el-select style="width: 90%;" v-model="item.field" placeholder="请选择参数">
                      <el-option v-for="t in parameList" :key="t.id" :label="t.propertyName" :value="t.keyValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="cloSpanFnt(item)">
                  <el-form-item label='触发条件' label-width="90px" :prop="'warningTriggerVos.' + index + '.relation'" :rules="{
                    required: true, message: '请选择', actions: 'change'
                  }">
                    <el-select style="width: 90%;" v-model="item.relation" placeholder="请选择触发条件">
                      <el-option v-for="dict in dict.type.trigger_relation" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="cloSpanFnt(item)">
                  <el-form-item label-width="70px" label='值' :prop="'warningTriggerVos.' + index + '.filterCondition'" :rules="{
                    required: true, message: '域名不能为空', actions:['blur','change'],type:'number'
                  }">
                    <el-input-number style="width: 90%;" v-model="item.filterCondition"  :min="1"></el-input-number>
                  </el-form-item>
                </el-col>

              </el-row>
            </div>
            <el-button icon="el-icon-circle-plus-outline" type="text" @click="addFormItem('warningTriggerVos')">新增触发器</el-button>
  
            <!-- 执行动作 -->
            <div class="hintTitle" style="margin-top: 30px;">执行动作（满足触发条件后执行所有动作）</div>
            <div class="dynaCard" v-for="(item, index) in form.actionList" :key="item.key">
              <div class="dynaCardHead">
                <div class="hintTitleSamil">执行动作{{ index + 1 }} </div>
                <el-popconfirm title="确认是否删除" @confirm="removeFormItem(item, 'actionList')" v-if="form.actionList&&form.actionList.length>1">
                  <template #reference>
                    <i style="color: red;font-size: 18px;" class="el-icon-delete"></i>
                  </template>
                </el-popconfirm>
              </div>
              <el-row>
                <el-col :span="10">
                  <el-form-item  label-width="100px" label='执行动作' :prop="'actionList.' + index + '.val'" :rules="{
                    required: true, message: '域名不能为空', actions: 'change'
                  }">
                      <el-select style="width: 90%;" v-model="item.val" placeholder="请选择执行动作">
                        <el-option 
                        v-for="dict in dict.type.execute_action" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-button icon="el-icon-circle-plus-outline" type="text" @click="addFormItem('actionList')">新增执行动作</el-button>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
    
  <script>
  import { redactList, setTtatus,getParameFtn,getStrategy,addRule,editRule,getRuleItem,delRule} from "@/api/deviceControl/control";
  
  export default {
    name: "controlRedactPage",
    dicts: ['trigger_relation','execute_action'],
    data() {
      return {
        screenHeight: null,
        sizeList: {
          '--dynamicHeight': 410,
        },
        loading: true,
        ids: [],
        single: true,
        multiple: true,
        showSearch: true,
        total: 0,
        postList: [],
        title: "",
        open: false,
        queryParams: {
          
          page: 1,
          limit: 10,  
          warnName: undefined,
        },
        strategys:[],
        form: {},
        dateRange: [],
        rules: {
          description: [
            { required: true, message: "请输入", trigger: "blur" }
          ],
          warnName: [
            { required: true, message: "请输入", trigger: "blur" }
          ],
          validTimeType: [
            { required: true, message: "请选择", trigger: "change"}
          ],
          dateRange: [
            { required: true, message: "请选择", trigger: "change"}
          ],
          logic: [
            { required: true, message: "请输入", trigger: "change"}
          ],
        },
        parameList:[]
      };
    },
    created() {
      this.getList();
      // getStrategy().then(res=>this.strategys=res.data);
      //弹动态高
      this.screenHeight = document.body.clientHeight;
      let that = this;
      window.onresize = function () {
        that.$set(that, 'screenHeight', document.body.clientHeight);
      }
    },
    watch: {
      screenHeight(val, oldVal) {
        let num = val - 300;
        this.$set(this.sizeList, "--dynamicHeight", num + 'px');
      }
    },
    methods: {

      getList() {
      const {query} = this.$route
        this.loading = true;
        const params ={...this.queryParams,id:query.id }
        redactList(params).then(response => {
          const {list,total} = response.data;
          this.postList = list;
          this.total = total;
          this.loading = false;
        }).catch(()=>this.loading = false);
        getParameFtn(query.id).then(res=>this.parameList = res.data);
      },

      switchClick(row){
        const newStatus = row.status==0?1:0
        const params ={
          id:row.id,
          status:newStatus
        }
        setTtatus(params).then(res=>{
          this.getList();
          this.$modal.msgSuccess(newStatus?"已开启":'已停止');
        })
      },
  
      cloSpanFnt(row) {
        return 8
      },
  
      removeFormItem(item, key) {
        var index = this.form[key].indexOf(item)
        if (index !== -1) {
          this.form[key].splice(index, 1)
        }
      },
  
      addFormItem(key) {
        let dataOp = key === 'warningTriggerVos' ? {
          field: undefined,
          relation: undefined,
          filterCondition: 1,
          key: Date.now()
        } : {
          val:undefined,
          key: Date.now()
        }
        this.form[key].push(dataOp);
      },

      cancel() {
        this.open = false;
      },

      reset() {
        this.form = {
          warningTriggerVos:[{field:undefined,relation:undefined,filterCondition:1, key: Date.now()}],
          actionList: [{val:undefined}],
          warnName:'',
          validTimeType:undefined,
          dateRange:undefined,
          description:'',
          logic:undefined
        };
        this.resetForm("form");
      },

      handleQuery() {
        this.queryParams.page = 1;
        this.getList();
      },

      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加控制策略";
      },

      handleUpdate(row) {
        this.reset();
        const id = row.id 
        getRuleItem(id).then(response => {
          const {actionList=[],validBeginTime,validEndTime} = response.data;
          this.form={
            ...response.data,
           actionList:actionList.map((v,i)=>{
            return {
              val:v,
              key: v+i
            }
           }),
           dateRange:validBeginTime?[validBeginTime,validEndTime]:[]
          }
          this.open = true;
          this.title = "修改控制策略";
        });
      },

      submitForm: function () {
        this.$refs["form"].validate(valid => {
          if (valid) {
            const {query} = this.$route;
            const {dateRange} = this.form;
            const params = {
                ...this.form,
                actionList:this.form.actionList.map(v=>v.val),
                validBeginTime:dateRange&&dateRange[0],
                validEndTime:dateRange&&dateRange[1],
                deviceId : query.id,
            }
            delete params.dateRange
            if (this.form.id != undefined) {
              editRule(params).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              addRule(params).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },

      handleDelete(row) {
        const data =[row.id]
        this.$modal.confirm('是否确认删除此数据？').then(function () {
          return delRule(data);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      },
    }
  };
  </script>
  
  <style lang="scss" >
  .controlRedactPage {

  }
  


  @keyframes color-change {
     0% {background-color: white;color: red;left: 10%}
    10%{background-color: red;color: black;left: 30%}
    20%{background-color: black;color: yellow;left: 50%}
    50% {background-color: green;color: blue;left: 70%}
    60%{background-color: blue;color: gold;left: 40%}
    70%{background-color: hotpink;color: white;left: 30%}
    90%{background-color: lightblue;color: black;left: 10%}
   100%{background-color: red;color: burlywood;left: 0px}
}
  
  .dynamicDemo {
    position: relative;
    .nameTag{
      font-size: 16px !important;
      font-weight: bold !important ;
      //1
      /* animation-name: color-change;
      animation-duration: 0.1s;
      animation-iteration-count: infinite;
      position: absolute; */
    }
    .spanItem{
      position: relative;
      .colPicker{
        width: 220px;
        position: absolute;
        top: 0;
        left: 350px;
        .el-form-item__content{
          margin-left: 0px !important;
        }
      }

    }

    ::v-deep .el-dialog__body {
      padding: 24px !important;
    }
    .sketch_content {
      // 必须有高度 overflow 为自动
      overflow: auto;
      // 滚动条的样式,宽高分别对应横竖滚动条的尺寸
      &::-webkit-scrollbar {
        width: 3px;
      }
      // 滚动条里面默认的小方块,自定义样式
      &::-webkit-scrollbar-thumb {
        background: #8798AF;
        border-radius: 2px;
      }
      // 滚动条里面的轨道
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }
  
  
    .dynaCard {
      /* width: 100%; */
      background: #F6F8FC;
      border-radius: 5px;
      padding: 12px 24px;
      margin-bottom: 20px;
      .dynaCardHead {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        margin-top: 12px;
        margin-left: 22px;
  
        .hintTitleSamil {
          font-weight: bold;
          font-size: 15px;
          color: #0D162A;
  
          &:before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 10px;
            background: #6FC342;
            border-radius: 0px;
            margin-right: 6px;
          }
        }
      }
    }  
  
  }
  </style>
    
    