<template>
  <!-- 设备列表 -->
  <div class="app-container">
    <el-form :inline="true" :model="searchForm" size="small">
      <el-form-item label="商品名称">
        <el-input
          placeholder="请输入商品名称"
          v-model="searchForm.deviceName"
          clearable
        />
      </el-form-item>
      <el-form-item label="种类">
        <el-select
          clearable
          v-model="searchForm.grade"
          placeholder="请选择种类"
          style="width: 160px"
        >
          <el-option
            v-for="item in dict.type.goods_grade"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="品类">
        <el-select
          clearable
          v-model="searchForm.category"
          placeholder="请选择品类"
          style="width: 160px"
        >
          <el-option
            v-for="item in dict.type.goods_category"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" icon="el-icon-search" type="primary" @click="getDataList()"
          >查询</el-button
        >
        <el-button size="mini" icon="el-icon-refresh" @click="handleReset"
          >重置</el-button
        >
        <el-button
          size="mini"
          icon="el-icon-plus"
          type="primary"
          @click="handleUpdate(1, null)"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="deviceList" v-loading="loading" style="width: 100%">
      <el-table-column
        align="center"
        type="index"
        label="序号"
        width="50"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="deviceName"
        label="商品名称"
      ></el-table-column>
      <el-table-column align="center" prop="specification" label="产地"></el-table-column>
      <el-table-column
        align="center"
        prop="practicalRadius"
        label="品类"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="abrasionRadius"
        label="种类"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="manufacturer"
        label="规格明细"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="manufactureTime"
        label="商品价格"
      ></el-table-column>
      <el-table-column align="center" prop="lifeSpan" label="商品库存"></el-table-column>
      <el-table-column align="center" prop="lifeSpan" label="商品重量"></el-table-column>
      <el-table-column align="center" prop="lifeSpan" label="商品货号"></el-table-column>
      <el-table-column align="center" prop="lifeSpan" label="商品图片"></el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(3, scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text" @click="handleUpdate(2, scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            style="color: red"
            type="text"
            @click="handleDelete(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      :page="queryPage.pageNum"
      :limit="queryPage.pageSize"
      :total="total"
      @pagination="onPageChange"
    />
  </div>
</template>

<script>
import { getGoodsManageList, deleteGoodsManage } from "@/api/goods/list";
export default {
  name: "DeviceManagerList",
  dicts: ["goods_grade", "goods_category"],
  data() {
    return {
      searchForm: {},
      deviceList: [],
      total: 0,
      options: [],
      queryPage: {
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 重置搜索条件
    handleReset() {
      this.searchForm = {};
      this.getDataList();
    },
    // 获取砂轮列表
    getDataList() {
      this.loading = true;
      getGoodsManageList({ ...this.queryPage, ...this.searchForm })
        .then((res) => {
          this.deviceList = res.rows;
          this.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetSearch() {
      this.searchForm = {
        deviceName: "",
        deviceCode: "",
        deviceStatus: "",
      };
      this.getDataList();
    },
    // 编辑 2 新增1  3查看
    handleUpdate(type, data) {
      this.$router.push({
        path: "/goods/goods_details",
        query: {
          id: null,
          type,
        },
      });
    },
    // 删除砂轮
    handleDelete(id) {
      console.log(id, "id");
      this.$confirm("确认删除该条砂轮信息吗？", "提示", {
        type: "warning",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            // 开启loading
            instance.confirmButtonLoading = true;
            deleteGoodsManage(id)
              .then((res) => {
                instance.confirmButtonLoading = false; // 关闭loading
                done(); // 关闭对话框
                this.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                });
                this.getDataList();
              })
              .catch(() => {
                instance.confirmButtonLoading = false; // 关闭loading
              });
          } else {
            done(); // 取消操作直接关闭对话框
          }
        },
      });
    },
    toggleDeviceStatus(row) {
      // 实现切换设备状态逻辑
    },
    onPageChange(data) {
      this.queryPage.pageNum = data.page;
      this.queryPage.pageSize = data.limit;
      this.getDataList();
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.search-conditions {
  margin-bottom: 20px;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
</style>
