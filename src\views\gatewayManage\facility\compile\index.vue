<template>
    <div class="app-container facilityCompile" v-resize="resize">
        <div class="title_header">
            <!-- <el-button @click="$router.back()" type="text"> -->
            <el-button @click="leaveFuc" type="text">
                <svg-icon icon-class="back" style="margin-right: 5px" />
                返回
            </el-button>
            <h3 class="title">{{ queryConfig.id ? '编辑设备' : '添加设备' }}</h3>
        </div>

        <div class="audit-detail" ref="wrap" v-loading="compileLoading">
            <div class="stepsDemo">
                <el-steps :active="active" align-center finish-status="success">
                    <template v-if="!queryConfig.id">
                        <el-step title="设备基本信息">
                        </el-step>
                        <el-step title="自定义采集" />
                        <el-step title="下发配置文件" />
                    </template>
                    <template v-else>
                        <el-step title="设备类型/协议"></el-step>
                        <el-step title="设备信息配置" />
                        <el-step title="下发" />
                    </template>
                </el-steps>
            </div>

            <template v-if="queryConfig.id">
                <form-data v-if="active === 0" @loadFuc="loadFuc" @next="next" :footerWidth="footerWidth" />
                <table-facility v-else-if="active === 1" @loadFuc="loadFuc" @next="next" @lastnext="lastnext"
                    :footerWidth="footerWidth" />
            </template>
            <template v-else>
                <add-facility v-if="active === 0" @loadFuc="loadFuc" @next="next" />
                <form-data v-else-if="active === 1" @loadFuc="loadFuc" @next="next" :footerWidth="footerWidth" />
            </template>

            <div v-if="active === 2">
                <div v-if="queryConfig.id">
                    <div class="addTableTilte" style="margin-top: 30px;margin-bottom: 20px;">下发当前的配置文件到设备</div>
                    <div style="margin-left: 15px;">
                        <el-button type="primary" plain icon="el-icon-upload" @click="issueFuc">下发配置文件到网关</el-button>
                        <el-button type="danger" plain icon="el-icon-video-play"
                            @click="setRestartFuc">重启改设备所在网关</el-button>
                    </div>
                </div>

                <div class="tips">
                    温馨提示：<span class="tipsText" style="color: #f56c6c;">设备信息下发保存后，需重启网关才可生效！</span>
                </div>
                <div v-show="footerWidth && footerWidth != '0px'" class="pageFooter" :style="{ width: `${footerWidth}` }">
                    <div style="margin-right: 20px;">
                        <el-button v-if="!btnFlag" @click="lastStepFuc">上一步</el-button>
                        <el-button type="primary" @click="leaveFuc" v-if="btnFlag">立刻前往(重启)</el-button>
                        <el-button type="primary" :loading="btnLoading" @click="submitAllFuc" v-else>下发并保存</el-button> 
                        <!-- <el-button type="primary" :loading="btnLoading" @click="leaveFuc" v-else>下发并保存</el-button> -->

                    </div>
                </div>
            </div>

        </div>

    </div>
</template>
  
<script>

import AddFacility from './addfacility';
import FormData from './formdata';
import TableFacility from './tablefacility';

import { submitAll, submitPutAll, setRestart, issueFile } from "@/api/gatewayManage/facility/index.js";
import { SessionStorage } from '@/utils/storage'


export default {
    name: "facilityCompile",
    components: { AddFacility, FormData, TableFacility },
    data() {
        return {
            queryConfig: {},
            active: 0,
            compileLoading: false,
            footerWidth: '',
            btnLoading: false,
            btnFlag: false
        };
    },
    created() {
        this.queryConfig = this.$route.query;
        SessionStorage.setItem('facilityPams', { ...this.$route.query, ...SessionStorage.getItem('facilityPams') });
    },

    destroyed() {
        SessionStorage.removeItem('facilityPams');
    },

    methods: {

        resize(val) {
            this.footerWidth = val ? val.width : '0px'
        },

        issueFuc() {
            const data = SessionStorage.getItem('facilityPams');
            this.compileLoading = true;
            issueFile(data).then(res => {
                this.$message.success("下发配置成功！");
            }).finally(() => this.compileLoading = false)
        },

        setRestartFuc() {
            const data = SessionStorage.getItem('facilityPams')
            this.compileLoading = true
            setRestart({ id: data.gatewayId }).then(res => {
                this.$message.success("重启网关成功！");
            }).finally(() => this.compileLoading = false)
        },

        loadFuc(done) {
            if (done) {
                this.compileLoading = done;
                setTimeout(() => {
                    this.compileLoading = false;
                }, 500)
            }
        },

        next() {
            if (this.active++ > 2) this.active = 0;
            SessionStorage.setItem('facilityPams', {
                ...SessionStorage.getItem('facilityPams'),
                nextStep: this.active
            });
        },

        lastnext() {
            if (--this.active < 0) this.active = 0;
            SessionStorage.setItem('facilityPams', {
                ...SessionStorage.getItem('facilityPams'),
                nextStep: this.active
            });
        },

        lastStepFuc() {
            this.loadFuc(true);
            if (this.active-- < 0) this.active = 0;
            SessionStorage.setItem('facilityPams', {
                ...SessionStorage.getItem('facilityPams'),
                nextStep: this.active
            });
        },

        submitAllFuc() {
            this.btnLoading = true
            const data = SessionStorage.getItem('facilityPams');
            const apiFlag = !data.id ? true : false
            const api = apiFlag ? submitAll(data) : submitPutAll(data);
            api.then(res => {
                this.$message({
                    message: apiFlag ? '添加设备成功！！！' : '编辑设备成功！！！',
                    type: 'success',
                    duration: 800,
                    onClose: () => {
                        this.btnFlag = true;
                    }
                });
            }).finally(() => {
                this.btnLoading = false;
            })
        },

        leaveFuc() {
            this.$router.push({ path: `/gatewayManage/facility`, query: { ...this.queryConfig } })
        }

    },

};
</script>



<style lang="scss" >
.facilityCompile {
    height: calc(100vh - 152px);
    /* height: 100vh; */
    padding: 28px 0 20px;
    background-color: #fff;
    border-radius: 16px;
    display: flex;
    flex-direction: column;

    .title_header {
        padding: 0 24px 12px;
        border-bottom: 1px solid #dbdfe9;

        .el-button {
            padding: 0;
            margin-right: 16px;
        }

        .title {
            display: inline-block;
            font-size: 18px;
            margin: 0;
            vertical-align: middle;
            padding-left: 20px;
            color: #181f2d;
            font-weight: bold;
            border-left: 1px solid #dbdfe9;
        }
    }

    .audit-detail {
        margin-top: 40px;
        flex: 1;
        overflow: auto;
        padding: 0 20px;

        .tips {
            text-align: center;
            margin-top: 52px;
            font-size: 18px;
            font-weight: bold;

            .tipsText {}
        }



        .addTableTilte {
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 18px;
            color: #0D162A;

            &:before {
                content: '';
                width: 4px;
                height: 12px;
                background: #0147EB;
                display: inline-block;
                margin-right: 10px;
            }
        }

        .stepsDemo {
            padding-left: 200px;
            padding-right: 200px;

            .is-process {
                color: #0147EB;
                border-color: #0147EB;
            }
        }


    }

    .pageFooter {
        position: fixed;
        bottom: 0px;
        background: #FFFFFF;
        box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
        opacity: 1;
        border-radius: 0px;
        height: 60px;
        display: flex;
        justify-content: end;
        align-items: center;
        right: 24px
    }

}
</style>