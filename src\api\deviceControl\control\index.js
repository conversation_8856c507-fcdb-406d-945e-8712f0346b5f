import { requestPlatForm } from "@/utils/requestBase";




export function controlList(query) {
  return requestPlatForm({
    url: '/device/control/device/list',
    method: 'get',
    params: query
  })
}


//设备字典
export function getDeviceDict() {
  return requestPlatForm({
    url: '/produce/device/dict',
    method: 'get',
  })
}





//控制策略list
export function redactList(query) {
  return requestPlatForm({
    url: '/device/control/strategy',
    method: 'get',
    params: query
  })
}
//控制策略字典
export function getStrategy() {
  return requestPlatForm({
    url: '/device/control/strategy/dict',
    method: 'get',
  })
}
//启停
export function setTtatus(query) {
  return requestPlatForm({
    url: '/device/control/change/strategy/status',
    method: 'get',
    params: query
  })
}

//参数字典
export function getParameFtn(id) {
  return requestPlatForm({
    url: '/produce/device/register/data/' + id,
    method: 'get',
  })
}
//添加策略
export function addRule(data) {
  return requestPlatForm({
    url: '/warn/rule',
    method: 'post',
    data: data
  })
}
//修改策略
export function editRule(data) {
  return requestPlatForm({
    url: '/warn/rule',
    method: 'put',
    data: data
  })
}
//策略详情回显
export function getRuleItem(id) {
  return requestPlatForm({
    url: '/warn/rule/' + id,
    method: 'get',
  })
}
//策略删除
export function delRule(data) {
  return requestPlatForm({
    url: '/warn/rule',
    method: 'delete',
    data
  })
}