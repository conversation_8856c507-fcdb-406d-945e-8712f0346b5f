import { requestPlatForm } from "@/utils/requestBase";

// 查询列表
export function getProductList(query) {
  return requestPlatForm({
    url: "/productLibrary/list",
    method: "get",
    params: query,
  });
}
// 新增流程
export function addList(data) {
  return requestPlatForm({
    url: "/productLibrary",
    method: "post",
    data: data,
  });
}

// 修改流程
export function updateList(data) {
  return requestPlatForm({
    url: "/productLibrary",
    method: "PUT",
    data: data,
  });
}

// 删除流程
export function deList(id) {
  return requestPlatForm({
    url: "/productLibrary/" + id,
    method: "DELETE",
  });
}
