<template>
  <div class="execution">
    <el-card class="gzzd">
      <div class="sbtp">
        <img :src="require('@/assets/imagesAssets/sbzl.png')"/>
      </div>
      <div class="tbzl">
        <div class="anzhuo">
          <img src="@/assets/svg/anzhuo.svg"/>
        </div>
        <div class="zl">
          <span>故障库</span>
        </div>
      </div>
      <div class="sm">
        设备故障定义
      </div>
    </el-card>
    <el-card class="box-card btn-search page-search crud">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     style="backgroundColor:#E1b980"
                     icon="el-icon-circle-plus-outline"
                     v-if="permissions.ems_emsfaultlibrary_add"
                     @click="addRegulations"
          >新增
          </el-button
          >
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="permissions.ems_emsfaultlibrary_edit"
              :disabled="single"
              @click="deviceEdit"
          >编辑
          </el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="permissions.ems_emsfaultlibrary_del"
              @click.native="handleDel()"
              :disabled="multiple"
          >删除
          </el-button
          >
          <!--          <el-button-->
          <!--              type="check"-->
          <!--              icon="el-icon-download"-->
          <!--              @click="exportExcel"-->
          <!--          >导出-->
          <!--          </el-button-->
          <!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="form">
        <el-form :inline="true">
          <el-form-item label="故障名称">
            <el-input placeholder="请输入故障名称" v-model="searchForm.faultName" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChangeU">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <basic-container>
      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <avue-crud
              ref="crud"
              :page.sync="page"
              :data="tableData"
              :permission="permissionList"
              :table-loading="tableLoading"
              :option="tableOption"
              :cell-style="cellStyle"
              @selection-change="selectionChange"
              @on-load="getList"
              @search-change="searchChange"
              @refresh-change="refreshChange"
              @size-change="sizeChange"
              @current-change="currentChange"
              @cell-click="cellClick"
              @row-del="handleDel"
          >
            <template slot="header">
              <IconTitle class="selfTitle" title="故障库" imgUrl="yunwei"/>
            </template>
            <template slot-scope="scope" slot="menu">
              <el-button type="text" @click="deviceEdit(scope.row)">
                <i class="icon-bianji" style="font-size: 13px"></i>编辑
              </el-button>
            </template>

          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>

    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-tabs tab-position="left" v-model="tabVal" @tab-click="tabClickFun">
        <el-tab-pane name="1" label="基本信息">
          <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-row>
              <el-col :span="12">
                <el-form-item label="故障名称" :label-width="formLabelWidth" prop="faultName">
                  <el-input v-model="form.faultName" maxlength="20" show-word-limit autocomplete="off"
                            placeholder="请输入故障名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部位" :label-width="formLabelWidth" prop="position">
                  <el-input v-model="form.position" maxlength="20" show-word-limit autocomplete="off"
                            placeholder="请输入部位"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="故障类别" :label-width="formLabelWidth" prop="falutCategoriesId">
              <el-cascader
                  style="width: 100%"
                  :options="treeData"
                  v-model="form.falutCategoriesId"
                  :props="optionTree"
                  :show-all-levels="false"
                  placeholder="请选择资料类别"></el-cascader>
            </el-form-item>
            <el-form-item label="故障描述" :label-width="formLabelWidth" prop="faultDescription">
              <el-input type="textarea" maxlength="255" show-word-limit v-model="form.faultDescription"
                        autocomplete="off" placeholder="请输入故障描述"></el-input>
            </el-form-item>
            <el-form-item label="故障现象" :label-width="formLabelWidth" prop="faultPhenomenon">
              <el-input type="textarea" maxlength="255" show-word-limit v-model="form.faultPhenomenon"
                        autocomplete="off" placeholder="请输入故障现象"></el-input>
            </el-form-item>
            <el-form-item label="解决办法" :label-width="formLabelWidth" prop="solution">
              <el-input type="textarea" maxlength="255" show-word-limit v-model="form.solution" autocomplete="off"
                        placeholder="请输入解决办法"></el-input>
            </el-form-item>
            <el-form-item label="预防对策" :label-width="formLabelWidth" prop="prevention">
              <el-input type="textarea" maxlength="255" show-word-limit v-model="form.prevention" autocomplete="off"
                        placeholder="请输入预防对策"></el-input>
            </el-form-item>

            <el-form-item label="说明" :label-width="formLabelWidth" prop="remark">
              <el-input type="textarea" maxlength="255" show-word-limit v-model="form.remark" autocomplete="off"
                        placeholder="请输入说明"></el-input>
            </el-form-item>
            <el-form-item label="文件" :label-width="formLabelWidth" prop="fileIdArray">
              <device-upload ref="fileupload"
                             :fileListTem="imgArrayTem"
              ></device-upload>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane name="2" label="适用设备">
          <el-form :model="form" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
            <el-button style="float: left;margin-bottom: 10px"
                       type="primary"
                       icon="el-icon-plus"
                       size="mini"
                       @click="addDeviceId"
            >新增
            </el-button>
            <el-table :data="deviceList">
              <el-table-column label="id" align="center" prop="id" v-if="false"/>
              <el-table-column label="设备编号" align="center" prop="deviceNum"/>
              <el-table-column label="设备名称" align="center" prop="deviceName"/>
              <el-table-column label="品牌" align="center" prop="brandNewName"/>
              <el-table-column label="规格型号" align="center" prop="specification"/>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="deviceListDele(scope.row)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="device.title" :visible.sync="device.open" width="900px" append-to-body>
      <div class="mainAnalysis">
        <div style="float: left;width: 20%">
          <div class="tree">
            <el-tree
                style="width: 100%;"
                accordion
                :data="cat.data"
                :props="cat.defaultProps"
                :normalizer="normalizer"
                @node-click="catHandleNodeClick">
            </el-tree>
          </div>
        </div>
        <div style="float: right;width: 80%">
          <el-table v-loading="device.loading" :data="deviceNewList"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" align="center"/>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备名称" align="center" prop="deviceName"/>
            <el-table-column label="规格型号" align="center" prop="specification"/>
          </el-table>
          <el-pagination
              @size-change="sizeChangeHandle"
              @current-change="currentChangeHandle"
              :current-page="pageIndex"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              :total="totalPage"
              background
              layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDeiceList">确 定</el-button>
        <el-button @click="cancelDeviceList">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getTree} from "@/api/ems/repository/emsregulations";
import {fetchList, getObj, addObj, putObj, delObj} from "@/api/ems/repository/emsfaultlibrary";
import {getAccountList, emsDevicefetchList} from "@/api/ems/equipment/account";
import deviceUpload from "../emsdevicedata/deviceUpload"
import {tableOption} from "@/const/crud/ems/repository/emsfaultlibrary";
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
export default {
  name: 'emsregulations',
  components: {
    IconTitle,
    deviceUpload
  },
  data() {
    return {
      title: "",
      activeName: 'first',
      tabVal: '1',
      tableData: [],
      deviceList: [],
      //所有的设备数据
      deviceNewList: [],
      searchForm: {
        faultName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      // 树状结构
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        defaultExpandAll: true,
        props: {
          label: "name",
          value: "id",
        },
      },
      treeData: [],
      // 新增弹出框
      dialogFormVisible: false,
      formLabelWidth: "120px",
      form: {
        faultName: '',  // 故障名称
        position: '',   // 部位
        faultDescription: '',   // 故障描述
        faultPhenomenon: '',   // 故障现象
        faultCause: '',   // 故障原因
        solution: '',  // 解决办法
        prevention: '',  // 预防对策
        fileIdArray: '',   // 文件id
        remark: '',   // 说明
        deviceIds: [],
        deviceId: "",
        falutCategoriesId: '',  // 故障类别
      },
      device: {
        title: "",
        open: false,
        loading: false,
        deviceForm: {
          id: null,
          deptId: null,
          requirement: null,
          remark: null,
          deviceId: [],
        }
      },
      imgArrayTem: [],
      rules: {
        faultName: [
          {required: true, message: '请输入故障名称', trigger: 'blur'},
        ],
        faultDescription: [
          {required: true, message: '请输入故障描述', trigger: 'blur'},
        ],
        faultPhenomenon: [
          {required: true, message: '请输入故障现象', trigger: 'blur'},
        ],
        solution: [
          {required: true, message: '请输入解决办法', trigger: 'blur'},
        ],
        falutCategoriesId: [
          {required: true, message: '请选择资料类别', trigger: 'blur'},
        ],
      },
      //分类的树形数据
      cat: {
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
      editId: 0,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      pageIndex2: 1,
      pageSize2: 1000,
      totalPage2: 0,
      equipmentData: [],
      optionProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        tags: true,
        multiple: true
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataForm: {},
      optionTree: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsfaultlibrary_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsfaultlibrary_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsfaultlibrary_edit, false),
      };
    },
  },
  created() {
    this.getTreeData();
    // this.changeThme();
  },
  mounted() {
    this.initElement();
  },
  methods: {
    deviceListDele(row) {
      let index = this.deviceList.findIndex(item => item.id === row.id);
      this.deviceList.splice(index, 1)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //分类节点点击事件
    catHandleNodeClick(data) {
      //categoryId
      // console.log(data.id)
      this.deviceGetList(data.id);
    },
    tabClickFun(val) {
    },
    submitDeiceList(parm) {
      if (parm.length > 0) {
        for (var i = 0; i < parm.length; i++) {
          this.device.deviceForm.deviceId.push(parm[i]);
        }
      } else {
        this.ids = [...new Set(this.ids)];
        if (this.ids.length === 0) {
          this.device.open = false;
          this.applyLoading = false;
          return;
        }
        for (var i = 0; i < this.ids.length; i++) {
          this.device.deviceForm.deviceId.push(this.ids[i]);
        }
      }
      deviceListAll(
          Object.assign({
            current: this.pageIndex2,
            size: this.pageSize2,
            ...this.device.deviceForm
          })
      ).then(response => {
        this.deviceList = response.data.data.records;
        this.device.open = false;
      });
    },
    cancelDeviceList() {
      this.device.title = "";
      this.device.open = false;
    },
    addDeviceId() {
      this.device.title = "设备台账";
      this.device.open = true;
      this.deviceGetList();
      this.getCatTree()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.deviceGetList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.deviceGetList()
    },
    deviceGetList(params) {
      this.dataForm.categoryId = params
      this.device.loading = true;
      emsDevicefetchList(
          Object.assign({
            current: this.queryParams.pageNum,
            size: this.queryParams.pageSize,
            ...this.dataForm
          })
      ).then(response => {
        this.deviceNewList = response.data.data.records;
        this.queryParams.total = response.data.data.total;
        this.totalPage = response.data.data.total;
        this.device.loading = false;
      });
    },

    //获取分类的tree数据
    getCatTree() {
      catTree().then((response) => {
        this.cat.data = response.data.data;
      })
    },
    // 清空表格
    empty() {
      this.form = {
        id: '',
        faultName: '',
        position: '',
        faultDescription: '',
        faultPhenomenon: '',
        faultCause: '',
        solution: '',
        prevention: '',
        remark: '',
        fileIdArray: '',
        equipment: [],
        deviceId: '',
        deviceIds: [],
        falutCategoriesId: null,
      }
      this.imgArrayTem = []
      this.deviceList = []
      this.device.deviceForm.deviceId = [];
      this.deviceNewList = []
    },

    // 新增按钮
    addRegulations() {
      this.title = "新增";
      this.dialogFormVisible = true;
      this.empty();
    },

    // 修改
    deviceEdit(row) {
      this.title = "编辑";
      this.tabVal= '1',
          this.dialogFormVisible = true;
      this.empty();
      let id = row.id || this.selectionList[0].id
      // 回显数据
      getObj(id).then(res => {
        this.imgArrayTem = [];
        if (res.data.data.fileArray != null) {
          res.data.data.fileArray.forEach((element) => {
            this.imgArrayTem.push({
              name: element.original,
              id: element.id,
              url: element.url,
              temUrl: element.url,
            });
          });
        }
        // 修改回显数据
        Object.keys(this.form).forEach((item, index) => {
          if (item !== "fileArray") {
            this.form[item] = res.data.data[item];
          }
        });
        this.form = res.data.data;
        this.submitDeiceList(res.data.data.deviceIds)
      })
    },

    // 部门树
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },

    // 提交表单
    submitForm(formName) {
      let device = [];
      this.deviceList.forEach(item => {
        device.push(item.id)
      });
      this.form.deviceIds = device;
      let data = JSON.parse(JSON.stringify(this.form));
      if (typeof data.falutCategoriesId !== "string") {
        if (data.falutCategoriesId.length > 0) {
          data.falutCategoriesId = data.falutCategoriesId.slice(-1)[0];
        }
      }
      // console.log(">>>>>>>>>>>>>>>" , JSON.stringify(data))
      // return;



      if (this.$refs.fileupload.fileList != null) {
        data.fileIdArray = this.$refs.fileupload.fileList.map((item) =>
            item.id
        );
      } else {
        data.fileIdArray = [];
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (data.id) {
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.deviceList = [];
              this.getList(this.page);
            });
          } else {
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功!")
              this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.deviceList = [];
              this.getList(this.page);
            });
          }
        }
      });
    },

    // 搜索框
    searchChangeU(param, done) {
      //console.log(this.$refs.crud)
      this.page.currentPage = 1
      this.getList(this.page, this.searchForm)
      //done()
    },
    resetBtn() {
      this.searchForm.faultName = '';
    },

    // 改变制度编号的颜色
    cellStyle(data) {
      if (data.columnIndex === 2) {
        return "color:#02b980;cursor:pointer";
      }
    },

    // 点击制度编号跳转页面
    cellClick(row, column) {
      if (column.property === "faultNumber") {
        this.$router.push({
          path: "/ems/repository/emsfaultlibrary/faultlibraryDetails", //详情页面
          query: {
            id: row.id
          }
        });
      } else {
        return;
      }
    },

    // 故障库树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "故障库类型") {
              common_table_info.push(treeDataList[index])
            }
          })
          this.treeData = common_table_info;
        }
      })
    },

    // 树状图每次点击重新搜索
    nodeClick(data) {
      this.page.page = 1;
      this.getList(this.page, {falutCategoriesId: data.id});
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
    },
    //编辑
    // handleEdit() {
    //   var refsDate = this.$refs
    //   refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
    // },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },

    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.downBlobFile(
          "/ems/emsregulations/export",
          this.searchForm,
          "emsregulations.xlsx"
      );
    },
    // 改变主题颜色
    // changeThme() {
    //   //"#02b980"
    //   document.getElementById("gwButton").style.backgroundColor = this.theme;
    // },

  },
};
import {deviceList, deviceListAll} from "@/api/ems/inspection/criterion";

import {fetchList as catTree} from "@/api/ems/equipment/category";
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="less">
.execution {

  .crud {
    .form {
      float: top;
    }
  }

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }

  .gzzd {
    margin-bottom: 10px;
    height: 120px;

    .sbtp {
      width: 147px;
      height: 100px;
      float: right;
      margin-right: 24px;
      margin-top: -10px;
    }

    .tbzl {
      display: flex;
      flex-direction: row;

      .anzhuo {
        left: 262px;
        top: 100px;
        width: 15px;
        height: 15px;
        color: rgba(89, 89, 89, 100);
        margin-right: 13px;
      }

      .zl {
        left: 295px;
        top: 95px;
        width: 72px;
        height: 27px;
        color: rgba(89, 89, 89, 100);
        font-size: 18px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: bold;
      }
    }

    .sm {
      left: 262px;
      top: 152px;
      width: 700px;
      height: 20px;
      color: rgba(134, 129, 129, 100);
      font-size: 12px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
      margin-top: 40px;
    }
  }
}
</style>
