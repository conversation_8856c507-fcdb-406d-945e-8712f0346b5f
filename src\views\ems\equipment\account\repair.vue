<template>
  <div class="repair">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="6"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>故障统计</span>
              </div>
              <div class="total-tj-gz">
                  <div class="tj">
                    <span class="top">维修费用</span>
                    <b class="color">{{ basicInformation.upkeepCosts }}</b>
                  </div>
                <div class="tj">
                  <span class="top">故障次数</span>
                  <b>{{ basicInformation.failureNumber }}</b>
                </div>
              </div>
              <div class="line"></div>
              <div class="sort" @click="goRanking()">
                <div class="sort-left">
                  设备故障排行：第 <span class="color"> {{ Number(basicInformation.rank) }} </span> 名
                </div>
                <div class="sort-right">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div></div
          ></el-col>
          <el-col :span="9"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>故障等级分布</span>
              </div>
              <div class="progress-box" v-if="failureLevelData.length != 0"  v-for="item in failureLevelData">
                <span class="left"
                  v-if="item.faultGrade == '0'">一般
                  <p>{{ ((item.gradeNum / allNum) * 100).toFixed(2) }}%</p>
                </span>
                <span class="left"
                      v-if="item.faultGrade == '1'">严重
                  <p>{{ ((item.gradeNum / allNum) * 100).toFixed(2)   }}%</p>
                </span>
                <span class="left"
                      v-if="item.faultGrade == '2'">紧急
                  <p>{{ ((item.gradeNum / allNum) * 100).toFixed(2)   }}%</p>
                </span>
                <span class="left"
                      v-if="item.faultGrade == '3'">其他
                  <p>{{ ((item.gradeNum / allNum) * 100 ).toFixed(2)  }}%</p>
                </span>
                <div class="right">
                  <el-progress v-if="item.faultGrade == '0'" :percentage="item.gradeNum" :format="format"
                               color="#02b980"></el-progress>
                  <el-progress v-if="item.faultGrade == '1'" :percentage="item.gradeNum" :format="format"
                               color="#f29c38"></el-progress>
                  <el-progress v-if="item.faultGrade == '2'" :percentage="item.gradeNum" :format="format"
                               color="#D75746"></el-progress>
                  <el-progress v-if="item.faultGrade == '3'" :percentage="item.gradeNum" :format="format"
                               color="#D75746"></el-progress>
                </div>
              </div>
            <div class="progress-box" style="margin-left: 165px ; margin-top: 55px" v-if="failureLevelData.length == 0">
              <p style="color: #9d9d9d; font-size: 14px">暂无数据</p>
            </div>
            </div>
          </el-col>
          <el-col :span="9"
            ><div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>故障类型分布</span>
              </div>
              <div id="typeEcharts" style="height: 140px"></div>
            </div>
          </el-col>
<!--          <el-col :span="9"-->
<!--            ><div class="echarts-item">-->
<!--              <div class="item-title">-->
<!--                <i class="icon-ziliao"></i>-->
<!--                <span>故障类型分布</span>-->
<!--                <p>fault</p>-->
<!--              </div>-->
<!--              <div id="faultEcharts" style="height: 140px"></div>-->
<!--            </div>-->
<!--          </el-col>-->
        </el-row>
      </div>
    </div>
    <el-card class="box-card btn-search page-search">
      <div slot="header">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left"></el-button>
          <!-- <el-button type="check" icon="el-icon-download">导出</el-button> -->
        </div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="报修编号">
            <el-input
              placeholder="请输入报修编号"
              v-model="searchForm.repairNum"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="报障人">
            <el-select
                placeholder="请选择报障人"
                v-model="searchForm.reportName"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.userName"
                  :value="item.userId"
                  :key="item.userId"
                  v-for="item in userAll"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="处理人">
            <el-select
                placeholder="请选择处理人"
                v-model="searchForm.handlerId"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.userName"
                  :value="item.userId"
                  :key="item.userId"
                  v-for="item in userAll"
              ></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item label="故障类型">-->
<!--            <el-select-->
<!--                placeholder="请选择故障类型"-->
<!--                v-model="searchForm.status"-->
<!--                clearable-->
<!--                style="width: 200px"-->
<!--            >-->
<!--              <el-option-->
<!--                  :label="item.label"-->
<!--                  :value="item.value"-->
<!--                  :key="item.id"-->
<!--                  v-for="item in taskStatusSelect"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item label="维修方式">
            <el-select
                placeholder="请选择维修方式  "
                v-model="searchForm.repairMethod"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in repairMethodSelect"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChange">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
        <div></div>
      </div>
    </el-card>
    <div class="table-box">
      <IconTitle title="维修记录" imgUrl="yunwei">
<!--        <span class="slot">维修列表</span>-->
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
        :data="maintenanceRecord"
        border
        style="width: 100%"
        @selection-change="deviceSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column prop="repairNum" label="报修编号" align="center">
        </el-table-column>
        <el-table-column prop="reportTime" label="报修时间" align="center">
        </el-table-column>
        <el-table-column prop="reportName" label="报障人" align="center">
        </el-table-column>
<!--        <el-table-column prop="status" label="设备状态" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.status == 0"><el-tag type="info" effect="dark">故障</el-tag></span>-->
<!--            <span v-if="scope.row.status == 1"><el-tag effect="dark">正常</el-tag></span>-->
<!--            <span v-if="scope.row.status == 2"><el-tag type="warning" effect="dark">带病运行</el-tag></span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="category" label="故障类型" align="center">
        </el-table-column>
        <el-table-column prop="faultGrade" label="故障等级" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.faultGrade == 0"><el-tag type="info" effect="dark">一般</el-tag></span>
            <span v-if="scope.row.faultGrade == 1"><el-tag effect="dark">严重</el-tag></span>
            <span v-if="scope.row.faultGrade == 2"><el-tag type="warning" effect="dark">紧急</el-tag></span>
            <span v-if="scope.row.faultGrade == 3"><el-tag type="danger" effect="dark">其他</el-tag></span>
          </template>
        </el-table-column>
        <el-table-column prop="faultRemark" label="故障描述" align="center">
        </el-table-column>
<!--        <el-table-column prop="address" label="预防措施" align="center">-->
<!--        </el-table-column>-->
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0"><el-tag type="info" effect="dark">待分配</el-tag></span>
            <span v-if="scope.row.status == 1"><el-tag effect="success">已分配</el-tag></span>
            <span v-if="scope.row.status == 2"><el-tag type="warning" effect="dark">维修中</el-tag></span>
            <span v-if="scope.row.status == 3"><el-tag type="danger" effect="dark">已解决</el-tag></span>
            <span v-if="scope.row.status == 4"><el-tag type="dark" effect="dark">已验收</el-tag></span>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="处理人" align="center">
        </el-table-column>
        <el-table-column prop="checkName" label="验收人" align="center">
        </el-table-column>
        <el-table-column prop="checkTime" label="验收时间" align="center">
        </el-table-column>
        <el-table-column prop="repairMethod" label="维修方式" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.repairMethod == 0"><el-tag type="info" effect="dark">自修</el-tag></span>
            <span v-if="scope.row.repairMethod == 1"><el-tag effect="success">外协</el-tag></span>
          </template>
        </el-table-column>

      </el-table>
    </div>
  </div>
</template>
<script>
import echarts from "echarts";
import {
  getFailureLevel,
  getBasicInformation,
  getFailureType,
  getSelectrepairMethod
} from "@/api/ems/equipment/account";
import { listUser } from '@/api/system/user';
import {getMaintenanceRecord} from "@/api/ems/repair/order";
import IconTitle from "@/components/icon-title/index.vue";
let typeOption = {

  color: ['#63b2ee', '#76da91', '#f8cb7f' ,'#f89588', '#7cd6cf','#9192ab', '#7898e1' , '#efa666','#eddd86', '#9987ce' ,'#63b2ee', '#76da91'],
  series: [
    {
      type: "pie",
      radius: ["30%", "55%"],
      center: ['50%', '50%'],
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      data: [
        { value: 3, name: "管道故障：3" },
        { value: 2, name: "电器故障：2" },
        { value: 23, name: "油路故障：23" },
      ],
    },
  ],
};
// let faultOption = {
//   xAxis: {
//     type: "category",
//     data: ["误操作", "油路堵塞", "磨损老化", "螺丝松动"],
//     axisLabel: {
//       rotate: 40,
//     },
//   },
//   yAxis: {
//     type: "value",
//     show: false,
//   },
//
//   series: [
//     {
//       data: [0, 0, 2, 3],
//       type: "bar",
//       label: {
//         show: true,
//         position: "top",
//       },
//       color: "#63b6f8",
//     },
//   ],
// };
export default {
  name: "basic",
  components: {
    IconTitle,
  },
  data() {
    return {
      typeOption,
      // faultOption,
      failureLevelData: [],
      allNum: 0,
      repairMethodSelect: [],
      searchForm: {},
      basicInformation: [],
      userAll: [],
      failureTypeData: [],
      maintenanceRecord: [],
      deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
      ],
    };
  },
  mounted() {

    // var faultDom = document.getElementById("faultEcharts");
    // var faultEcharts = echarts.init(faultDom);
    // faultEcharts.setOption(this.faultOption);
    this.getFailureLevelList();
    this.getBasicInformationList();
    this.getFailureTypeList();
    this.getListUser();
    this.getSelect();
    this.getMaintenanceRecordList();
  },
  methods: {
    // 跳转到排行页
    goRanking(){
      this.$router.push("/ems/statistical/repair/ranking")
    },
    deviceSelectionChange() {
    },
    resetBtn(){
      this.searchForm.repairNum='';
      this.searchForm.reportName='';
      this.searchForm.handlerId='';
      this.searchForm.repairMethod='';
    },
    searchChange() {
      this.getMaintenanceRecordList(this.searchForm);
    },
    getMaintenanceRecordList(params) {
      let id = this.$route.query.id;
      this.searchForm.deviceId = id;
      getMaintenanceRecord(this.searchForm).then(res => {
        this.maintenanceRecord = res.data.records;
        // this.total = res.data.total;
      });
    },

    getBasicInformationList() {
      getBasicInformation(this.$route.query.id).then(res => {
        this.basicInformation = res.data;
        // console.log("111>>" , JSON.stringify(this.basicInformation))
      });
    },
    // 获取负责人
    getListUser() {
      listUser().then(res => {
        this.userAll = res.rows
      })
    },

    getSelect() {
      getSelectrepairMethod().then((res) => {
        this.repairMethodSelect = res.data;
      });
    },

    getFailureTypeList() {
      getFailureType(this.$route.query.id).then(res => {
        this.failureTypeData = res.data;
        var chartDom = document.getElementById("typeEcharts");
        var myChart = echarts.init(chartDom);
        if (this.failureTypeData.length>0) {
          this.typeOption.series[0].data =  this.failureTypeData
          myChart.setOption(this.typeOption);
        }else {
          myChart.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }

      });
    },

    getFailureLevelList() {
      getFailureLevel(this.$route.query.id).then(res => {
        this.failureLevelData = res.data;
        let a = 0;
        this.failureLevelData.forEach(function (item) {
          a += item.gradeNum;
        });
        this.allNum = a;

      });
    },


    format(percentage) {
      return `${percentage}`;
    }


  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.repair {
  .table-box {
    .echarts-box {
      .echarts-item {
        .total-tj-gz {
          padding: 25px 0 20px 0;
          display: flex;
          .tj {
            width: 50%;
            .top {
              display: block;
              margin-bottom: 10px;
            }
            b {
              font-size: 20px;
            }
            b.color {
              color: #c9c184;
            }
          }
        }
        .line {
          height: 1px;
          background: #ccc;
          margin: 0 -15px 0 -15px;
        }
        .sort {
          margin-top: 15px;
          display: flex;
          justify-content: space-between;
          .sort-left {
            display: flex;
            align-items: center;
            .color {
              color: $theme;
            }
          }
          .sort-right {
            i {
              cursor: pointer;
            }
          }
        }
        //第二快
        .progress-box {
          margin-top: 20px;
          display: flex;
          align-items: center;
          p {
            display: inline-block;
            color: #606266;
            margin: 0 10px;
          }
          .right {
            width: 70%;
          }
        }
      }
    }
  }


}
</style>
