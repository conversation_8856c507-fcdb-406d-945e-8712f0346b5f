<template>
  <div class="maintain-box">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>设备等级分布</span>
              </div>
              <div class="progress-box" style="margin-top: 20px" v-for="item in deviceData">
                <span class="width: 30% ; float: left" >
                  {{ item.deviceLevel }}
                  <!--<span class="pro_nums">{{ item.divided * 100 }}%</span>-->
                  <span class="pro_nums">{{ (item.divided * 100).toFixed(2) }}%</span>
                </span>
                <div style="width: 50% ; float: right; margin-top: 2px">
                  <el-progress v-if="item.deviceLevel == 'A(关键)'" :percentage="item.gradeNum" :format="format"
                               color="#02b980"></el-progress>
                  <el-progress v-if="item.deviceLevel == 'B(重要)'" :percentage="item.gradeNum" :format="format"
                               color="#f29c38"></el-progress>
                  <el-progress v-if="item.deviceLevel == 'C(一般)'" :percentage="item.gradeNum" :format="format"
                               color="#D75746"></el-progress>
                </div>
              </div>
            </div>
          </el-col
          >
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>设备使用状态</span>
              </div>
              <div  id="useStatusEcharts" style="margin:auto;height: 150px;width: 260px;"></div>
            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>设备年龄分布</span>
              </div>
              <div id="faultEcharts" style="height: 140px"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="table-box">
      <div class="echarts-box">
        <el-row :gutter="12">
          <el-col :span="24">
            <IconTitle title="状态统计" imgUrl="yunwei"></IconTitle>
            <div class="statusEcharts" style="width: 320px; float: left; margin-top: 10px; height: 160px;">
              <!-- 水球图 -->
              <statistical class="statistical_equipmentKanban" :status="status"/>

              <div class="histogram_tow">

                <!-- 带病运行 -->
                <div class="histogram_div">
                  <!-- 柱状图 -->
                  <histogramDb />
                  <div class="span_text_div">
                    <div class="span_text">
                      <span style="color: #606266; font-weight: 700">{{ status.badRunCount }}</span>
                    </div>
                    <div class="dbyx">
                      <span style="color:#b0b1b3;">带病运行</span>
                    </div>
                  </div>
                </div>

                <!-- 故障数 -->
                <div class="histogram_div">
                  <!-- 柱状图 -->
                  <histogramGz/>
                  <div class="span_text_div">
                    <div class="span_text">
                      <span style="color: #606266; font-weight: 700;" >{{ status.malfunctionCount }}</span>
                    </div>
                    <div class="dbyx">
                      <span style="color:#b0b1b3;">故障数</span>
                    </div>
                  </div>
                </div>

              </div>
              <div class="sbzs">
                <span style="color: #606266; font-weight: 700">{{ this.sum }}</span>
                <span style="font-size: 14px ; width: 60px">设备总数</span>
              </div>
            </div>
            <div
                style="float:left; float: left; margin:10px 80px 0px 50px ; width: 1px; height: 160px; background: #d2d6db;">
            </div>
            <div>
              <div class="images_status">
                <img class="images_s" :src="require('@/assets/imagesAssets/normal.png')">
                <span style="padding-left: 10px">正常<span style="font-size: 30px"> {{ status.normalCount }} </span>台</span>
              </div>
              <div class="images_status">
                <img class="images_s" :src="require('@/assets/imagesAssets/vector.png')">
                <span style="padding-left: 10px">带病运行<span style="font-size: 30px"> {{ status.badRunCount }} </span>台</span>
              </div>
              <div class="images_status">
                <img class="images_s" :src="require('@/assets/imagesAssets/malfunction.png')">
                <span style="padding-left: 10px">故障<span style="font-size: 30px"> {{ status.malfunctionCount }} </span>台</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="table-box" style="height: 100%">
      <IconTitle title="价值排行" imgUrl="yunwei"></IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 100%">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>采购金额</span>
                <p>original</p>
              </div>

              <div class="procurement_data" style="height:400px;overflow-y:auto">
                <div class="data" v-for="item in amountData">
                  <img :src="headIMG(item.coverImgFile)" alt="" class="img">
                  <div class="data_original">
                    <div class="data_top">
                      <div class="deviceName">
                        <span style="color:#343334; font-weight: 700; margin: 0 10px 0 20px">{{ item.deviceName }}</span>
                        <span>(</span><span style="color:#cac383; font-weight: 600;">¥{{
                          item.purchaseAmount
                        }}</span><span>)</span>
                      </div>

                      <div class="purchaseDate">
                        <span>{{ item.purchaseDate != null ? item.purchaseDate.toString().substring(0,10) : "暂无购置日期" }}</span>
                      </div>

                    </div>
                    <div class="data_bottom">
                      <el-tooltip content="A(关键)" placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'A'" style="background-color: #259b24;"/>
                      </el-tooltip>
                      <el-tooltip content="B(重要) " placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'B'" style="background-color: #ff9800;"/>
                      </el-tooltip>
                      <el-tooltip content="C(一般)" placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'C'" style="background-color: #e51c23;"/>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="echarts-item" style="height: 100%" >
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>资产净值</span>
                <p>price</p>
              </div>

              <div class="procurement_data" style="height:400px;overflow-y:auto">
                <div class="data" v-for="item in netAssetValueData">
                  <img :src="headIMG(item.coverImgFile)" alt="" class="img">
                  <div class="data_original">
                    <div class="data_top">
                      <div class="deviceName">
                        <span style="color:#343334; font-weight: 700; margin: 0 10px 0 20px">{{ item.deviceName }}</span>
                        <span>(</span><span style="color:#cac383; font-weight: 600;">¥{{
                          item.purchaseAmount
                        }}</span><span>)</span>
                      </div>

                      <div class="purchaseDate">
                        <span>{{item.purchaseDate != null ? item.purchaseDate.toString().substring(0,10) : "暂无日期" }}</span>
                      </div>

                    </div>
                    <div class="data_bottom">
                      <el-tooltip content="A(关键)" placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'A'" style="background-color: #259b24;"/>
                      </el-tooltip>
                      <el-tooltip content="B(重要) " placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'B'" style="background-color: #ff9800;"/>
                      </el-tooltip>
                      <el-tooltip content="C(一般)" placement="bottom" effect="light">
                        <div class="cj_div" v-if="item.deviceLevel == 'C'" style="background-color: #e51c23;"/>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import echarts from "echarts";
import statistical from "./echarts/statistical";
import histogramDb from "../../../../echarts/histogramDb";
import histogramGz from "../../../../echarts/histogramGz";

import {
  deviceStatusList,
  statusList,
  amountList,
  useStatusList,
  getEquipmentAge,
  getNetAssetValue
} from "@/api/ems/statistical/device";

import IconTitle from "@/components/ems/icon-title/index.vue";

let faultOption = {
  grid: {
    top: "20%",
    left: "0%",
    right: "10%",
    bottom: "0%",
    containLabel: true,
  },
  xAxis: {
    axisLabel: {
      interval:0,
      rotate:0
    },
    data: []
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      data: [],
      type: "bar",
      barWidth: '20px',
      label: {
        show: true,
        position: "top",
      },
      color: "#63b2ee",
    },
  ],
};
let numsEchartsOptions = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    x: "right",
    y: 'center',
  },
  series: [
    {
      type: 'pie',
      radius: ['35%', '60%'],
      center: ["43%", "50%"],
      label:{
        show:true,
        formatter:'{b} : {c} ({d}%)'
      },
      color: [
        "#63b2ee",
        "#76da91",
        "#f8cb7f",
        "#f89588",
        "#7cd6cf",
        "#9192ab",
        "#7898e1",
        "#efa666",
        "#eddd86",
        "#9987ce",
        "#63b2ee",
        "#76da91"
      ],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      data: []
    }
  ]
};

export default {
  name: "account-detail",
  components: {
    IconTitle,
    statistical,
    histogramDb,
    histogramGz
  },
  data() {
    return {
      deviceStatusList: {},
      searchForm: {},
      deviceData: [],
      sum: '',
      status: [],  // 设备状态
      amountData: [], // 采购金额
      netAssetValueData: [],
      numsEchartsOptions,
      faultOption,
      numsOptions: [],  //设备使用状态
      equipmentAge: []
    };
  },
  mounted() {
    this.deviceStatusShow();
    this.getStatusList();
    this.getAmountList();
    this.getUseStatusList();
    this.getEquipmentAgeList();
    this.getNetAssetValueList();
  },
  methods: {
    deviceSelectionChange() {
    },

    headIMG(img) {
      const imgData = require('@/assets/imagesAssets/kong.png')
      return img != null ? img.url : imgData
    },

    getEquipmentAgeList() {
      getEquipmentAge().then(res => {
        this.equipmentAge = res.data.data;
        let faultDom = document.getElementById("faultEcharts");
        let faultEcharts = echarts.init(faultDom);
        let arrNum = [];
        let yearNum = [];
        this.equipmentAge.forEach(function(value,index){
          yearNum.push(value.name);
          arrNum.push(value.value);
        })
        faultOption.xAxis.data = yearNum;
        faultOption.series[0].data = arrNum;
        faultEcharts.setOption(this.faultOption);
      });
    },

    // 采购金额
    getAmountList() {
      amountList().then(res => {
        this.amountData = res.data.data;
      })
    },

    getNetAssetValueList() {
      getNetAssetValue().then(res => {
        this.netAssetValueData = res.data.data;
      });
    },

    // 状态统计
    getStatusList() {
      statusList().then(res => {
        this.status = res.data.data;
      })
    },

    // 设备使用状态
    getUseStatusList(){
      useStatusList().then(res => {
        this.numsOptions = res.data.data;

        console.log("this.value>>>>>>>>>" , JSON.stringify(this.numsOptions.length))

        let numsEcuseStatusEchartsDom = document.getElementById("useStatusEcharts");
        let numsEcharts = echarts.init(numsEcuseStatusEchartsDom);

        if (this.numsOptions.length > 0) {
          let arrNum = [];
          for (let key in this.numsOptions) {
            let obj = new Object();
            obj.value = this.numsOptions[key].useStatusNum;
            let status = Number(this.numsOptions[key].useStatus);
            if (status === 0) {
              obj.name = "在用";
            } else if (status === 1) {
              obj.name = "闲置";
            } else if (status === 2) {
              obj.name = "出租";
            } else if (status === 3) {
              obj.name = "禁用";
            } else if (status === 4) {
              obj.name = "报废";
            }
            arrNum.push(obj);
          }
          numsEchartsOptions.series[0].data = arrNum;
          numsEcharts.setOption(this.numsEchartsOptions);
        } else {
          numsEcharts.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }


      })
    },

    // 基本信息 ：设备等级分布
    deviceStatusShow() {
      deviceStatusList().then(res => {
        this.deviceData = res.data.data;


        for (let a = 0; a < this.deviceData.length; a++) {
          this.deviceData[a].gradeNum = parseInt(this.deviceData[a].gradeNum);
        }

        let array = this.deviceData.map(item => {
          return item.gradeNum;
        })

        //获取数组元素和
        this.sum = array.reduce(function (a, b) {
          return a + b;
        }, 0)

        for (let a = 0; a < this.deviceData.length; a++) {
          this.deviceData[a].divided = this.deviceData[a].gradeNum / this.sum;
          this.deviceData[a].divided = this.deviceData[a].divided.toFixed(2);
        }
      })
    },

    format(percentage) {
      return `${percentage}`;
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.maintain-box {

  .table-box {
    .echarts-box {

      .echarts-item {
        .procurement_data {
          margin: 20px 0 0 30px;
          font-size: 12px;

          .data {
            display: flex;
            margin-bottom: 15px;

            .data_original {
              .data_top {
                margin-top: 5px;
                display: flex;
                .deviceName{
                  width: 350px;
                }
                .purchaseDate{

                }
              }

              .data_bottom {
                .cj_div {
                  width: 60px;
                  height: 4px;
                  background-color: red;
                  border-radius: 50px;
                  margin: 10px 0 0 20px;
                }
              }
            }

            .img {
              width: 40px;
              height: 40px;
            }
          }
        }
      }

      .statusEcharts {
        display: flex;

        .sbzs {
          display: flex;
          flex-direction: column;
          margin: 55px 0 0 30px;
        }

        .histogram_tow {
          display: flex;
          flex-direction: column;
          margin: 0 18px;

          .histogram_div {
            display: flex;
            margin: 20px 0 0 0;

            .span_text_div {
              display: flex;
              flex-direction: column;

              .span_text {
                display: flex;
                flex-direction: column;
                margin-top: 15px;
                font-size: 12px;
              }

              .dbyx {
                font-size: 12px;
                width: 60px;
              }
            }

          }
        }

        .statistical_equipmentKanban {
          margin: 20px 0 0 20px;
        }
      }
    }
  }


  .main-item {
    margin-top: 15px;

    .flex {
      padding: 5px;
      background: #f3f5fb;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .flex.green {
      background: #effcf4;
    }

    .progress-box {
      display: flex;
      align-items: center;
    }

  }
}

.pro_nums {
  padding-left: 8px;
}

.images_status {
  line-height: 160px;
  width: 200px;
  float: left;
}

.images_s {
  width: 30%;
  display: inline-block;
  vertical-align: middle;
}
</style>
