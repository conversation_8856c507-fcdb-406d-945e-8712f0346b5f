<template>
    <div class="enterpriseSettle app-container">
        <el-form ref="baseForm" :model="baseForm" :rules="rules" label-position="right" label-width="150px">
            <div class="tagsTilte" style="margin-bottom: 15px;">企业入驻</div>
            <el-row style="padding: 20px" :gutter="50">
                <el-col :span="24">
                    <el-form-item prop="enterpriseName" label="企业名称">
                        <el-input v-model="baseForm.enterpriseName" placeholder="企业名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="enterpriseIntroduction" label="企业介绍">
                        <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5}" v-model="baseForm.enterpriseIntroduction" placeholder="企业介绍"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="logo" label="企业Logo">
                        <el-upload :show-file-list="false" accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                            :headers="headers" :on-success="handleLogoSuccess">
                            <img style="width:100%;height:100%" v-if="baseForm.logo" :src="ensureFullUrl(baseForm.logo)"
                                class="avatar" />
                            <i v-else class="el-icon-plus"></i>
                            <div class="el-upload__tip" slot="tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="contacts" label="联系人">
                        <el-input v-model="baseForm.contacts" placeholder="联系人"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="contactsTelephone" label="联系电话">
                        <el-input v-model="baseForm.contactsTelephone" placeholder="联系电话"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="area" label="省市区">
                        <el-cascader style="width:100%" v-model="baseForm.area"
                            placeholder="请选择省市区" :options="areaDict" :props="{
                                children: 'children',
                                label: 'name',
                                value: 'code',
                            }" :show-all-levels="true"></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="detailAddress" label="详细地址">
                        <el-input v-model="baseForm.detailAddress" placeholder="详细地址"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="logo" label="上传营业执照">
                        <el-upload :show-file-list="false" accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                            :headers="headers" :on-success="handleBusinessSuccess">
                            <img style="width:100%;height:100%" v-if="baseForm.businessLicense" :src="ensureFullUrl(baseForm.businessLicense)"
                                class="avatar" />
                            <i v-else class="el-icon-plus"></i>
                            <div class="el-upload__tip" slot="tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="businessLicenseNum" label="营业执照号">
                        <el-input v-model="baseForm.businessLicenseNum" placeholder="营业执照号"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="dateRange" label="营业执照有效期">
                        <el-date-picker
                            v-model="baseForm.dateRange"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item prop="legalBusinessScope" label="法定经营范围">
                        <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5}" v-model="baseForm.legalBusinessScope"
                            placeholder="法定经营范围"></el-input>
                    </el-form-item>
                </el-col>
                
                <el-col :span="24">
                    <el-form-item prop="logo" label="经营资质">
                        <el-upload :show-file-list="false" accept=".jpg,.png,.img,.jpeg" :action="uploadUrl" list-type="picture-card"
                            :headers="headers" :on-success="handleQualiSuccess">
                            <img style="width:100%;height:100%" v-if="baseForm.businessQualification" :src="ensureFullUrl(baseForm.businessQualification)"
                                class="avatar" />
                            <i v-else class="el-icon-plus"></i>
                            <div class="el-upload__tip" slot="tip">只能上传jpg,png,img,jpeg文件，且不超过1Mb</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="legalPersonDelegate" label="法人代表">
                        <el-input v-model="baseForm.legalPersonDelegate" placeholder="法人代表"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="legalPersonTelephone" label="法人手机号码">
                        <el-input v-model="baseForm.legalPersonTelephone" placeholder="法人手机号码"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="pageFooter">
            <el-button @click="$router.go(-1)">退出</el-button>
            <el-button :loading="loading" type="primary" @click="submitBtn">提交</el-button>
        </div>
    </div>
</template>

<script>
import { getIndustryTree, searchEnterprise, submitIdentify, getParkList, getSubstitutionTree } from '@/api/admin/user'
import {
    getListByProductType,
} from "@/api/admin/user.js";
import { getToken } from '@/utils/auth'
import { validPhone, validEmail } from '@/utils/validate'
import { getInfo } from "@/api/admin/user.js";
import { getCityData } from '@/api/system/dept.js'
export default {
    name: "enterpriseSettle",
    data() {
        return {
            headers: {
                Authorization: "Bearer " + getToken()
            },
            loading: false,
            disabled: false,
            allDisabled: false,
            flowInstanceId: '',
            baseForm: {},
            rules: {
                enterpriseName: [
                    { required: true, message: '请输入企业名称', trigger: 'blur' }
                ],
                contacts: [
                    { required: true, message: '请输入联系人', trigger: 'blur' }
                ],
                contactsTelephone: [
                    { required: true, validator: validPhone, trigger: 'change' }
                ],
                area: [
                    { required: true, message: '请选择省市区', trigger: 'change' }
                ],
                detailAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' }
                ],
                legalPersonTelephone: [
                    { required: false, validator: validPhone, trigger: 'change' }
                ]
            },
            uploadUrl: process.env.VUE_APP_BASE_API + "/system/file/upload",
            industryDict: [],
            areaDict: [],
            parkDataList: [],
            treeDataList: [],
            labelList: []
        };
    },
    created() {
        this.getAreaDict()
    },
    computed: {
    },
    watch: {
    },
    methods: {
        submitBtn() {
            this.$refs.baseForm.validate((valid) => {
                if (valid) {
                    this.loading = true
                    console.log(444,this.baseForm)
                    const params = { ...this.baseForm }
                    if (params.area && params.area.length) {
                        const [provinceCode, cityCode, districtCode] = params.area
                        params.provinceCode = provinceCode
                        params.cityCode = cityCode
                        params.districtCode = districtCode
                    }
                    if(params.dateRange && params.dateRange.length){
                        params.businessLicenseStartDate = params.dateRange[0] || ''
                        params.businessLicenseEndDate = params.dateRange[1] || ''
                    }
                    return
                    submitIdentify({ params, productType: this.authType + "_AUTH", flowInstanceId: this.flowInstanceId }, this.identifyForm.id ? 'put' : 'post').then(res => {
                        if (res.code == 200) {
                            this.$message.success(`认证提交成功,请等待审核!`);
                            this.$router.push({ path: '/index', query: { reIdentify: '1' } })
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        getAreaDict() {
            getCityData().then(res => {
                this.areaDict = res.data || []
            })
        },
        handleLogoSuccess(res, file) {
            this.$set(this.baseForm, 'logo', res.data.url || '')
        },
        handleBusinessSuccess(res, file){
            this.$set(this.baseForm, 'businessLicense', res.data.url || '')
        },
        handleQualiSuccess(res){
            this.$set(this.baseForm, 'businessQualification', res.data.url || '')
        },
    },

};
</script>



<style lang="scss" scoped>
.enterpriseSettle {
    padding: 30px 20px 20px 20px;
    .el-form{
        width: 90%;
    }
    .tagsTilte {
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 18px;
        color: #0D162A;
        &:before {
            content: '';
            width: 4px;
            height: 12px;
            background: #0147EB;
            display: inline-block;
            margin-right: 10px;
        }
    }
    .pageFooter {
        margin-top: 30px;
        width: 100%;
        position: fixed;
        padding: 0 20px;
        bottom: 0px;
        background: #FFFFFF;
        box-shadow: 0px -6px 6px rgba(81, 90, 110, 0.1);
        opacity: 1;
        border-radius: 0px;
        height: 60px;
        display: flex;
        justify-content: end;
        align-items: center;
        right: 20px
    }
}
</style>