<template>
  <div class="fixed-container checkCenterPage">
    <el-row>
      <el-col :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="queryParams.productName" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="产品类型" prop="productType">
            <el-select v-model="queryParams.productType" placeholder="请选择产品类型" clearable>
              <el-option v-for="dict in productList" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tableList" @selection-change="handleSelectionChange" style="width: 100%" height="calc(100vh - 280px)">
          <el-table-column label="产品名称" prop="productName" :show-overflow-tooltip="true" min-width="100" />
          <el-table-column label="产品类型" prop="productType" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{
                productList.length > 0
                ? productList.filter(
                  (v) => v.dictValue === scope.row.productType
                )[0].dictLabel
                : ""
              }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="280">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发布人" prop="createBy" />
          <el-table-column label="状态" prop="examineStatus">
            <template slot-scope="scope">
              <el-tag type="success" effect="dark" v-if="scope.row.examineStatus == 'PASS'">通过</el-tag>
              <el-tag type="danger" effect="dark" v-else-if="scope.row.examineStatus == 'REFUSE'">拒绝</el-tag>
              <el-tag effect="dark" v-else>待审核</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" class-name="small-padding fixed-width" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleLook(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
      </el-col>
    </el-row>

    <el-dialog title="新增产品" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :rules="rules" :model="form" label-width="120px" size="mini">
        <el-form-item label="产品类型" prop="productType">
          <el-select style="width: 90%" v-model="form.productType" placeholder="请选择产品类型" clearable>
            <el-option v-for="dict in productList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getProductList,
  getTableList,
  groundUpIng,
  groundHot,
} from "@/api/check/index.js";

export default {
  name: "checkCenterPage",
  dicts: ["sys_normal_disable", "sys_user_sex"],
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      tableList: [],
      open: false,
      form: {},
      rules: {
        productType: [
          { required: true, message: "产品类型不能为空", trigger: "change" },
        ],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      productList: [],
    };
  },

  created() {
    this.getList();
    this.getProductListFtn();
  },
  methods: {

    getProductListFtn() {
      getProductList(1).then((res) => {
        this.productList = res.data;
      });
    },

    getList() {
      this.loading = true;
      getTableList(this.queryParams)
        .then((response) => {
          this.tableList = response.rows;
          this.total = response.total;
        })
        .finally(() => (this.loading = false));
    },

    cancel() {
      this.open = false;
      this.reset();
    },

    reset() {
      this.form = {
        productType: undefined,
      };
      this.resetForm("form");
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    handleUpdate(row) {
      const { productType, id } = row;
      this.$router.push({
        path: "/examine/checkCenter/release",
        query: { type: productType, id },
      });
    },

    handleLook(row) {
      const { productType, flowInstanceId } = row;
      this.$router.push({
        path: "/examine/checkCenter/release",
        query: { type: productType, flowInstanceId, pageType: 'check' },
      });
    },

    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { productType } = this.form;
          this.$router.push({
            path: "/examine/checkCenter/release",
            query: { type: productType },
          });
        }
      });
    },

  },
};
</script>
