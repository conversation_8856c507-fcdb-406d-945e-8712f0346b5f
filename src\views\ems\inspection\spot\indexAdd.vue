<template>
  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box">
        <IconTitle title="策略" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <el-table v-loading="strateg.loading" :data="strategyList"
                    :show-header="false">
            <el-table-column prop="id" v-if="false"/>
            <el-table-column
                label="strategyName"
                prop="deviceNum"
                align="right"
                width="100"
            >
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >
                  <div>
                    <img style="width: 35px;height: 35px" :src="require('@/assets/images/yunwei.png')"/>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
                label="strategyName"
                prop="deviceNum"
                align="left"
                width="250"
            >
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >
                  <!--                                    <IconTitle imgUrl="Moneymanagement"></IconTitle>-->

                  <div>
                    <span>{{ scope.row.strategyName }}</span>
                    <br/>
                    <span style="color: #958181">{{
                        scope.row.strategyNum
                      }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <!--                        <el-table-column label="策略编号" align="center" key="strategyNum" prop="strategyNum"/>-->
            <!--                        <el-table-column label="策略名称" align="center" key="username" prop="strategyName"/>-->
            <el-table-column label="状态" align="center" width="100" key="enable" prop="enable">
              <template slot-scope="scope">
                                          <span>{{
                                              scope.row.enable == 0
                                                  ? "不启用"
                                                  : scope.row.enable == 1
                                                      ? "启用" : ""
                                            }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" align="center" width="100" key="deptId" prop="deptId">
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >

                  <!--                                    <img icon="icon-Moneymanagement" />-->
                  <div>
                    <span>{{ scope.row.deptId }}</span>
                    <br/>
                    <span style="color: #958181"></span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createBy"
                             width="220">
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >

                  <!--                                    <img icon="icon-Moneymanagement" />-->
                  <div>
                    <span>{{ scope.row.createBy }}</span>
                    <br/>
                    <span style="color: #958181">创建人</span>
                  </div>
                </div>
              </template>

            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime"
                             width="220">
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >

                  <!--                                    <img icon="icon-Moneymanagement" />-->
                  <div>
                    <span>{{ scope.row.createTime }}</span>
                    <br/>
                    <span style="color: #958181">创建时间</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" align="center" prop="updateTime"
                             width="220">
              <template slot-scope="scope">
                <div
                    class="img-text"
                    style="cursor: pointer"
                >

                  <!--                                    <img icon="icon-Moneymanagement" />-->
                  <div>
                    <span>{{ scope.row.updateTime }}</span>
                    <br/>
                    <span style="color: #958181">更新时间</span>
                  </div>
                </div>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>
      <div class="info-box">
        <IconTitle title="关联操作" imgUrl="yunwei"></IconTitle>
        <el-button style="float: right"
                   type="primary"
                   icon="el-icon-plus"
                   size="mini"
                   @click="addDeviceId"
        >新增
        </el-button>
        <el-table v-loading="loading" :data="deviceList">
          <el-table-column label="id" align="center" prop="id" v-if="false"/>
          <el-table-column label="设备编号" align="center" prop="deviceNum"/>
          <el-table-column label="设备名称" align="center" prop="deviceName"/>
          <el-table-column label="品牌" align="center" prop="brandNewName"/>
          <el-table-column label="规格型号" align="center" prop="specification"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="deviceListDele(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="queryParams.total>0"
            :total="queryParams.total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="deviceGetList"
        />
        <!-- 用于添加设备的 -->
        <el-dialog :title="device.title" :visible.sync="device.open" width="800px" append-to-body>
          <el-table v-loading="device.loading" :data="deviceNewList"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" align="center"/>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备编号" align="center" prop="deviceNum"/>
            <el-table-column label="设备名称" align="center" prop="deviceName"/>
            <el-table-column label="品牌" align="center" prop="brandNewName"/>
            <el-table-column label="规格型号" align="center" prop="specification"/>
          </el-table>

          <pagination
              v-show="queryParamsDeviceList.total>0"
              :total="queryParamsDeviceList.total"
              :page.sync="queryParamsDeviceList.pageNum"
              :limit.sync="queryParamsDeviceList.pageSize"
              @pagination="deviceGetList"
          />
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitDeiceList">确 定</el-button>
            <el-button @click="cancelDeviceList">取 消</el-button>
          </div>
        </el-dialog>
      </div>
      <div class="info-btn-box">
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>

  </div>

</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";
import ImageUpload from "@/components/ImageUpload/index.vue";
import {fetchListTree} from "@/api/ems/equipment/category";
import {getBrandList} from "@/api/ems/equipment/brand";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {mapGetters} from "vuex";
import {fetchTree} from "@/api/admin/dept";
import {
  strategyFetchList,
  strategyGetObj,
  strategyAddObj,
  strategyPutObj,
  strategyDelObj,
  strategyList,
  deviceListANDStrategyId,
  spotAndName,
  deviceListAllPageStrategy
} from "@/api/ems/inspection/spot";
import {
  deviceList,
  deviceListAll,
  deviceListStrategy,
  selectDeviceNotStandard
} from "@/api/ems/inspection/criterion";
import {strategyDeviceAddObj, strategyDevicePutObj} from "@/api/ems/inspection/strategyDevice";


export default {
  name: "AddIndex",
  components: {
    IconTitle,
    Treeselect,
    ImageUpload,
  },
  props: {
    id: {
      type: String,
    },
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "name",
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
      },

      strategyList: [],
      strateg: {
        title: "",
        open: false,
        loading: false,
      },
      form: {
        id: null,
        deviceIdList: [],
        strategyId: null,
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        total: 0,
      },
      deviceList: [],
      deviceQueryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      device: {
        title: "",
        open: false,
        loading: false,
        deviceForm: {
          id: null,
          standardNum: null,
          standardName: null,
          deptId: null,
          requirement: null,
          remark: null,
          deviceId: [],
        }
      },
      deviceNewList: [],
      list: [],
      treeData: [],
      loading: false,
      categoryList: [], //设备类别
      brandList: [], //设备品牌
      treeDeptData: [], //部门

      queryParamsDeviceList: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataRule: {
        // inspectionType: [
        //     {required: true, message: '巡检类型不能为空', trigger: 'blur'}
        // ],
      },
      coverImgTem: [],
      imgArrayTem: [],
      rules: {},
      dialogVisible: false,
    };
  },
  created() {
    this.getSelect();
  },
  computed: {},
  mounted() {
    // alert(this.id)
    if (this.id > 0) {
      this.strateg.loading = true
      this.strategyList = [];
      strategyGetObj(this.id).then((res) => {
        // this.strategyList=res.data;
        this.strategyList.push(res.data)
        deviceListAllPageStrategy(Object.assign(
                {
                  current: this.queryParams.pageNum,
                  size: this.queryParams.pageSize,
                },
                {id: this.id}
            )
        ).then(response => {
          
          let arr = response.data.records;
          const uniqueList = this.uniqueArray(arr, 'id');  // 去重
          this.deviceList = uniqueList;

          this.queryParams.total = response.data.total;
          for (var i = 0; i < this.deviceList.length; i++) {
            this.device.deviceForm.deviceId.push(this.deviceList[i].id);
          }
          this.loading = false;
          this.device.open = false;
          this.strateg.loading = false;
        });

      });

    }
  },
  methods: {
    // 对象数组去重
    uniqueArray(arr, key) {
      const seen = new Set();
      return arr.filter(item => {
        const k = item[key];
        return seen.has(k) ? false : seen.add(k);
      });
    },
    submitDeiceList() {
      this.loading = true;
      for (var i = 0; i < this.ids.length; i++) {
        this.device.deviceForm.deviceId.push(this.ids[i]);
      }
      // selectDeviceNotStandard({deviceId:this.ids}).then(res => {
      //   console.log(">>>>data1>>>>>>",JSON.stringify(res.data))
      //   console.log(">>>>data2>>>>>>",JSON.stringify(res.data))
      // });
      deviceListAll(Object.assign(
              {
                current: this.queryParams.pageNum,
                size: this.queryParams.pageSize,
              },
              {deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceList = response.data.records;
        this.queryParams.total = response.data.total;
        this.loading = false;
        this.device.open = false;
      });
    },
    addDeviceId() {
      this.device.title = "设备台账";
      this.device.open = true;
      this.deviceGetList();
    },
    deviceListDele(row) {
      for (var i = 0; i < this.deviceList.length; i++) {
        if (this.deviceList[i].id == row.id) {
          this.deviceList.splice(i, 1)
          this.queryParams.total = this.queryParams.total - 1;
        }
      }
      for (var i = 0; i < this.device.deviceForm.deviceId.length; i++) {
        if (this.device.deviceForm.deviceId[i] == row.id) {
          this.device.deviceForm.deviceId.splice(i, 1)
        }
      }
    },
    deviceGetList() {
      this.device.loading = true;
      deviceListStrategy(Object.assign(
              {
                current: this.queryParamsDeviceList.pageNum,
                size: this.queryParamsDeviceList.pageSize,
              },
              {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
          )
      ).then(response => {
        this.deviceNewList = response.data.records;
        this.queryParamsDeviceList.total = response.data.total;
        this.device.loading = false;
      });
    },
    cancelDeviceList() {
      this.device.title = "";
      this.device.open = false;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    submitForm(formName) {
      this.form.deviceIdList = this.device.deviceForm.deviceId;
      this.form.strategyId = this.id;

      // console.log("data>>>>>>>>>>>>>>>>>>>", JSON.stringify(this.form));
      // return;
      let data = JSON.parse(JSON.stringify(this.form));
      this.$refs[formName].validate((valid) => {
        if (data.id) {
          strategyDevicePutObj(data).then((res) => {
            this.reset();
            this.$parent.listFlag = true;
            this.$parent.addllistFlag = false;
            this.$message.success("修改成功");
          });
        } else {
          strategyDeviceAddObj(data).then((res) => {
            this.reset();
            this.$parent.listFlag = true;
            this.$parent.addllistFlag = false;
            this.$message.success("新增成功");
          });
        }
      });
    },
    reset() {
      this.form = {
        id: null,
        deviceIdList: [],
        strategyId: null,
      };
      //this.resetForm("form");
    },


    getSelect() {
      // remote("ins_inspect_items_type").then(response => {
      //     this.typeList = response.data;
      // });
      // fetchListTree("").then((res) => {
      //     this.categoryList = res.data ? res.data : [];
      // });
      // getBrandList().then((res) => {
      //     this.brandList = res.data;
      // });
      //部门
      fetchTree().then((response) => {
        this.treeDeptData = response.data;
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    goBack() {
      this.$parent.listFlag = true;
      //  this.$parent.refreshChange()
    },
  },
};
</script>
<style lang="scss">
.add-box {
  .el-dialog__body {
    height: 80vh;
  }

  .table-box {
    height: 100%;

    .table-big-box {
      overflow: auto;
      height: 80%;

    }

  }
}
</style>

<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.info-from .el-table td,
.info-from .el-table th.is-leaf,
.info-from .el-table--border,
.info-from .el-table--group {
  border: none;
  cursor: pointer;
}

.info-from .el-table::before {
  height: 0;
}

.add-box {
  margin-bottom: 50px;

  .info-box {
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 10px 15px;
    overflow: hidden;

    .info-from {
      display: flex;
      flex-wrap: wrap;
      padding-top: 20px;
      position: relative;

      .el-form-item {
        width: 50%;
        padding-right: 10px;
      }
    }

    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {
      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }

  .info-btn-box {
    width: 100%;
    text-align: center;
  }

  .user {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
