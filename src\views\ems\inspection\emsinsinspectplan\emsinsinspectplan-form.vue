<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="计划编号" prop="planNum">
      <el-input v-model="dataForm.planNum" placeholder="计划编号"></el-input>
    </el-form-item>
    <el-form-item label="计划名称" prop="planName">
      <el-input v-model="dataForm.planName" placeholder="计划名称"></el-input>
    </el-form-item>
    <el-form-item label="部门id" prop="deptId">
      <el-input v-model="dataForm.deptId" placeholder="部门id"></el-input>
    </el-form-item>
    <el-form-item label="责任人id" prop="liableUserId">
      <el-input v-model="dataForm.liableUserId" placeholder="责任人id"></el-input>
    </el-form-item>
    <el-form-item label="巡检周期" prop="inspectCycle">
      <el-input v-model="dataForm.inspectCycle" placeholder="巡检周期"></el-input>
    </el-form-item>
    <el-form-item label="巡检周期单位" prop="cycleUnit">
      <el-input v-model="dataForm.cycleUnit" placeholder="巡检周期单位"></el-input>
    </el-form-item>
    <el-form-item label="有效时间" prop="effectiveTime">
      <el-input v-model="dataForm.effectiveTime" placeholder="有效时间"></el-input>
    </el-form-item>
    <el-form-item label="有效时间单位" prop="effectiveUnit">
      <el-input v-model="dataForm.effectiveUnit" placeholder="有效时间单位"></el-input>
    </el-form-item>
    <el-form-item label="提前生成时间" prop="generateTime">
      <el-input v-model="dataForm.generateTime" placeholder="提前生成时间"></el-input>
    </el-form-item>
    <el-form-item label="生成时间单位" prop="generateUnit">
      <el-input v-model="dataForm.generateUnit" placeholder="生成时间单位"></el-input>
    </el-form-item>
    <el-form-item label="提醒时间" prop="noticeTime">
      <el-input v-model="dataForm.noticeTime" placeholder="提醒时间"></el-input>
    </el-form-item>
    <el-form-item label="提醒时间单位" prop="noticeUnit">
      <el-input v-model="dataForm.noticeUnit" placeholder="提醒时间单位"></el-input>
    </el-form-item>
    <el-form-item label="计划开始时间" prop="beginTime">
      <el-input v-model="dataForm.beginTime" placeholder="计划开始时间"></el-input>
    </el-form-item>
    <el-form-item label="计划结束时间" prop="endTime">
      <el-input v-model="dataForm.endTime" placeholder="计划结束时间"></el-input>
    </el-form-item>
    <el-form-item label="上次生成时间" prop="lastGenerateTime">
      <el-input v-model="dataForm.lastGenerateTime" placeholder="上次生成时间"></el-input>
    </el-form-item>
    <el-form-item label="下次生成时间" prop="nextGenerateTime">
      <el-input v-model="dataForm.nextGenerateTime" placeholder="下次生成时间"></el-input>
    </el-form-item>
    <el-form-item label="是否启用(0不启用 1启用)" prop="enable">
      <el-input v-model="dataForm.enable" placeholder="是否启用(0不启用 1启用)"></el-input>
    </el-form-item>
    <el-form-item label="巡检设置(0无需扫码，1扫一个二维码，3只需填写异常项)" prop="inspectSettings">
      <el-input v-model="dataForm.inspectSettings" placeholder="巡检设置(0无需扫码，1扫一个二维码，3只需填写异常项)"></el-input>
    </el-form-item>
    <el-form-item label="策略id" prop="strategyId">
      <el-input v-model="dataForm.strategyId" placeholder="策略id"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createBy">
      <el-input v-model="dataForm.createBy" placeholder="创建者"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updateBy">
      <el-input v-model="dataForm.updateBy" placeholder="更新者"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    <el-form-item label="删除标志(0正常 1删除)" prop="delFlag">
      <el-input v-model="dataForm.delFlag" placeholder="删除标志(0正常 1删除)"></el-input>
    </el-form-item>
    <el-form-item label="租户Id" prop="tenantId">
      <el-input v-model="dataForm.tenantId" placeholder="租户Id"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
    import {planGetObj, planAddObj,planPutObj} from '@/api/ems/inspection/plan'

    export default {
    data () {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          id: 0,
          planNum: '',
          planName: '',
          deptId: '',
          liableUserId: '',
          inspectCycle: '',
          cycleUnit: '',
          effectiveTime: '',
          effectiveUnit: '',
          generateTime: '',
          generateUnit: '',
          noticeTime: '',
          noticeUnit: '',
          beginTime: '',
          endTime: '',
          lastGenerateTime: '',
          nextGenerateTime: '',
          enable: '',
          inspectSettings: '',
          strategyId: '',
          createBy: '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          remark: '',
          delFlag: '',
          tenantId: ''
        },
        dataRule: {
          planNum: [
            { required: true, message: '计划编号不能为空', trigger: 'blur' }
          ],
          planName: [
            { required: true, message: '计划名称不能为空', trigger: 'blur' }
          ],
          deptId: [
            { required: true, message: '部门id不能为空', trigger: 'blur' }
          ],
          liableUserId: [
            { required: true, message: '责任人id不能为空', trigger: 'blur' }
          ],
          inspectCycle: [
            { required: true, message: '巡检周期不能为空', trigger: 'blur' }
          ],
          cycleUnit: [
            { required: true, message: '巡检周期单位不能为空', trigger: 'blur' }
          ],
          effectiveTime: [
            { required: true, message: '有效时间不能为空', trigger: 'blur' }
          ],
          effectiveUnit: [
            { required: true, message: '有效时间单位不能为空', trigger: 'blur' }
          ],
          generateTime: [
            { required: true, message: '提前生成时间不能为空', trigger: 'blur' }
          ],
          generateUnit: [
            { required: true, message: '生成时间单位不能为空', trigger: 'blur' }
          ],
          noticeTime: [
            { required: true, message: '提醒时间不能为空', trigger: 'blur' }
          ],
          noticeUnit: [
            { required: true, message: '提醒时间单位不能为空', trigger: 'blur' }
          ],
          beginTime: [
            { required: true, message: '计划开始时间不能为空', trigger: 'blur' }
          ],
          endTime: [
            { required: true, message: '计划结束时间不能为空', trigger: 'blur' }
          ],
          lastGenerateTime: [
            { required: true, message: '上次生成时间不能为空', trigger: 'blur' }
          ],
          nextGenerateTime: [
            { required: true, message: '下次生成时间不能为空', trigger: 'blur' }
          ],
          enable: [
            { required: true, message: '是否启用(0不启用 1启用)不能为空', trigger: 'blur' }
          ],
          inspectSettings: [
            { required: true, message: '巡检设置(0无需扫码，1扫一个二维码，3只需填写异常项)不能为空', trigger: 'blur' }
          ],
          strategyId: [
            { required: true, message: '策略id不能为空', trigger: 'blur' }
          ],
          createBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          delFlag: [
            { required: true, message: '删除标志(0正常 1删除)不能为空', trigger: 'blur' }
          ],
          tenantId: [
            { required: true, message: '租户Id不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0;
        this.visible = true;
        this.canSubmit = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
                this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false;
            if (this.dataForm.id) {
                putObj(this.dataForm).then(data => {
                    this.$notify.success('修改成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            } else {
                addObj(this.dataForm).then(data => {
                    this.$notify.success('添加成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            }
          }
        })
      }
    }
  }
</script>
