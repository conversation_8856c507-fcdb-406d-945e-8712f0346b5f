<template>
  <div class="login">
    <div class="login-container">
      <!-- <div class="logo-name">
        <img src="../assets/images/login/name.jpg" alt="">
      </div> -->
      <div class="login-left">
        <img src="../assets/images/login/welcome.jpg" alt="">
      </div>
      <div class="login-right">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <h3 class="title">登录</h3>
          <el-form-item prop="username" class="form-list-item">
            <img src="../assets/images/login/user.jpg" alt="" class="user-icon">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="请输入手机号"
            >
              <!-- <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" /> -->
            </el-input>
          </el-form-item>
          <el-form-item prop="password" class="form-list-item">
            <img src="../assets/images/login/password.jpg" alt="" class="password-icon">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="请输入密码"
              @keyup.enter.native="handleLogin"
            >
              <!-- <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" /> -->
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaEnabled" class="form-list-item">
            <img src="../assets/images/login/Captcha.jpg" alt="" class="Captcha-icon">
            <el-input
              v-model="loginForm.code"
              auto-complete="off"
              placeholder="图形验证码"
              style="width: 63%"
              @keyup.enter.native="handleLogin"
            >
              <!-- <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" /> -->
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img"/>
            </div>
          </el-form-item>
          <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
          <el-form-item style="width:100%;">
            <el-button
              :loading="loading"
              size="medium"
              type="primary"
              style="width:100%;"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right;" v-if="register">
              <router-link class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2024 ruoyi.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        redirectUri: '',
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: true,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      let urlSearchParams = new URLSearchParams(window.location.search)
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        redirectUri: urlSearchParams.get('redirectUri')
      };
      console.log('333',this.loginForm)
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            if(this.loginForm.redirectUri){
              location.href = this.loginForm.redirectUri
            }else{
              // this.$router.push({ path: "enterpriseSettle" })
              this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
            }
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login/background.jpg");
  background-size: cover;
}
.login-container{
  width: 1200px;
  height: 600px;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #fff;
  box-shadow: 0px 4px 26px rgba(0,85,226,0.1);
  border-radius: 10px
}
.logo-name{
  position: absolute;
  width: 198px;
  top: -58px;
  left: 0;
}
.logo-name>img{
  display: inline-block;
  width: 100%;
}
.login-left{
  width: 600px;
}
.login-left>img{
  width: 600px;
  vertical-align: middle;
}
.login-right{
  width: 600px;
  height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.title {
  margin: 0px auto 36px;
  text-align: center;
  color: #181F2D;
  font-size: 30px;
  font-weight: bold;
}

.login-form {
  border-radius: 4px;
  background: #ffffff;
  width: 340px;
  /* padding: 127px 130px; */
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 40px;
    width: 14px;
    margin-left: 2px;
  }
}
.form-list-item{
  position: relative;
  height: 40px;
  .el-input__inner{
    padding: 0 15px 0 34px;
  }
}
.user-icon,.password-icon,.Captcha-icon{
  position: absolute;
  z-index: 999;
  height: 14px;
  margin: 13px 10px 13px 12px;
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    float: right;
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
}
</style>
