<template>
  <div class="execution">

  </div>
</template>
<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import FormTitle from "@/components/edge/form-title";

export default {
  name: 'template',
  components: {
    IconTitle,
    FormTitle
  },
  data() {
    return {
      imageUrl: '',
    };
  },
  mounted() {
  },
  methods: {


  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";

.execution {
  padding: 10px 10px 10px 10px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 10px;
  margin-left: 1%;
  height: 550px;
}
</style>
