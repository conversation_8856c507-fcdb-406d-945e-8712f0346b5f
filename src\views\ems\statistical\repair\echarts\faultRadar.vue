<template>
  <div id="faultRadar" :style="{width: '400px', height: '130px'}"></div>
</template>

<script>
import {
  getFailureTypeDistributeList,
} from "@/api/ems/statistical/maintain"
export default {
  data() {
    return {
      failureTypeDistribute: []
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let faultRadar = this.$echarts.init(document.getElementById('faultRadar'))
      getFailureTypeDistributeList().then(res => {
        this.failureTypeDistribute = res.data.data;
        if (this.failureTypeDistribute.length > 0) {
          let result = [];
          let dataResult = [];
          this.failureTypeDistribute.forEach(item => {
            let obj = new Object();
            obj.name = item.name;
            obj.max = 20;
            result.push(obj);
            dataResult.push(item.value);
          });

          // 绘制图表
          faultRadar.setOption({
            tooltip: {
              trigger: 'item'
            },
            series: [
              {
                type: 'pie',
                radius: ['35%', '60%'],
                center: ["45%", "53%"],
                label: {
                  show: true,
                  formatter: '{b} : {c} ({d}%)'
                },
                color: [
                  "#63b2ee",
                  "#76da91",
                  "#f8cb7f",
                  "#f89588",
                  "#7cd6cf",
                  "#9192ab",
                  "#7898e1",
                  "#efa666",
                  "#eddd86",
                  "#9987ce",
                  "#63b2ee",
                  "#76da91"
                ],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 50,
                  borderColor: '#fff',
                  borderWidth: 2,
                },
                data: this.failureTypeDistribute
              }
            ]
          });
        } else {
          faultRadar.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }

      });
    }
  }
}

</script>
