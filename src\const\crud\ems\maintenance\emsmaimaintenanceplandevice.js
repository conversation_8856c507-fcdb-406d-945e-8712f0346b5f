export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": false,
  "column": [
	  {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12
    },	  {
      "type": "input",
      "label": "保养计划id",
      "prop": "maintenancePlanId",
      "span": 12
    },	  {
      "type": "input",
      "label": "设备台账id",
      "prop": "deviceId",
      "span": 12
    },	  {
      "type": "input",
      "label": "租户id",
      "prop": "tenantId",
      "span": 12
    },	  {
      "type": "input",
      "label": "createBy",
      "prop": "createBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "createTime",
      "prop": "createTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "updateBy",
      "prop": "updateBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "updateTime",
      "prop": "updateTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "remark",
      "prop": "remark",
      "span": 12
    },	  {
      "type": "input",
      "label": "delFlag",
      "prop": "delFlag",
      "span": 12
    }  ]
}
