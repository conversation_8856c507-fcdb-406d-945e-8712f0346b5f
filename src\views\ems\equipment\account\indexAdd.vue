<template>
  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box">
        <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <el-form-item label="资产编号" prop="assetNum">
            <el-input v-model="form.assetNum" placeholder="请输入资产编号" maxlength="40" show-word-limit/>
          </el-form-item>
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="form.deviceName" placeholder="请输入设备名称" maxlength="20" show-word-limit/>
          </el-form-item>
          <el-form-item label="设备状态" prop="status">
            <el-select
                placeholder="请选择设备状态"
                v-model="form.status"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in dict.type.device_status"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备使用状态" prop="useStatus">
            <el-select
                placeholder="请选择设备使用状态"
                v-model="form.useStatus"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in dict.type.device_use_status"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="设备等级" prop="deviceLevel">
            <el-select
                placeholder="请选择设备等级"
                v-model="form.deviceLevel"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in dict.type.device_level"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="设备类别" prop="categoryId">
            <treeselect
                v-model="form.categoryId"
                :options="categoryList"
                :normalizer="normalizer"
                placeholder="请选择设备类别"
            />
          </el-form-item>
          <el-form-item label="设备品牌" prop="brandId">
            <el-select
                placeholder="请选择设备品牌"
                v-model="form.brandId"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.brandName"
                  :value="item.id"
                  :key="item.id"
                  v-for="item in brandList"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="负责人" prop="liableUser">
            <el-input v-model="form.liableUser" placeholder="请输入负责人" maxlength="5" show-word-limit/>
            <!--            <el-input v-model="form.liableUser" @focus="dialogVisible=true" placeholder="请选择负责人"/>-->
          </el-form-item>

          <el-form-item label="计划运行时间" prop="expectedRunTime">
            <el-input-number controls-position="right" style="width: 100%" v-model="form.expectedRunTime"
                             label="请输入计划运行时间(min为单位)" :min="0"></el-input-number>
          </el-form-item>
          <!--          <el-form-item label="正常运行时间" prop="runTime" class='runTime'>-->
          <!--            <el-input-number controls-position="right" style="width: 50%" v-model="form.minRunTime"-->
          <!--                             label="请输入计划运行时间(mrunTimein为单位)"></el-input-number>-->
          <!--            <span>至</span>-->
          <!--            <el-input-number controls-position="right" style="width: 50%" v-model="form.maxRunTime"-->
          <!--                             label="请输入计划运行时间(min为单位)"></el-input-number>-->

          <!--          </el-form-item>-->
          <el-form-item label="规格型号" prop="specification">
            <el-input
                v-model="form.specification"
                placeholder="请输入规格型号"
                maxlength="40" show-word-limit
            />
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-select
                placeholder="请选择单位"
                v-model="form.unit"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in dict.type.unit"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="供应商" prop="supplierId">
            <el-select
                placeholder="请选择供应商"
                v-model="form.supplierId"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.supplierName"
                  :value="item.id"
                  :key="item.id"
                  v-for="item in supplierList"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
              label="购置日期" prop="purchaseDate">
            <el-date-picker
                style="width: 100%"
                v-model="form.purchaseDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择购置日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="购置金额"
                        :rules="form.depreciationOpen == 1 ? rules.purchaseAmount : [{required:false}]"
                        prop="purchaseAmount">
            <el-input
                v-model="form.purchaseAmount"
                onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g, '$1')"
                placeholder="请输入购置金额"
                maxlength="20" show-word-limit
            />
          </el-form-item>

          <el-form-item
              label="保修日期" prop="warrantyDate">
            <el-date-picker
                style="width: 100%"
                v-model="form.warrantyDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择保修日期">
            </el-date-picker>

          </el-form-item>
          <el-form-item
              :rules="form.depreciationOpen == 1 ? rules.introductionDate : [{required:false}]"
              label="投产日期" prop="introductionDate">

            <el-date-picker
                style="width: 100%"
                v-model="form.introductionDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择投产日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item
              :rules="form.depreciationOpen == 1 ? rules.expectedScrapDate : [{required:false}]"
              label="预计报废日期" prop="expectedScrapDate">
            <el-date-picker
                style="width: 100%"
                v-model="form.expectedScrapDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择预计报废日期">
            </el-date-picker>

          </el-form-item>
          <el-form-item label="电子标签码" prop="electronicLabel">
            <el-input
                v-model="form.electronicLabel"
                placeholder="请输入电子标签码"
                maxlength="40" show-word-limit
            />
          </el-form-item>

          <el-form-item label="部门" prop="deptId" >
            <treeselect
                v-model="form.deptId"
                :options="treeDeptData"
                :normalizer="normalizerDept"
                placeholder="请选择部门"
            />
          </el-form-item>

          <el-form-item label="位置" prop="storageLocationId">
            <treeselect
                v-model="form.storageLocationId"
                :options="storageList"
                :normalizer="normalizer"
                placeholder="请选择位置"
                :appendToBody="true"
            />
          </el-form-item>
          <el-form-item label="是否开启折旧" prop="depreciationOpen">
            <el-radio-group v-model="form.depreciationOpen">
              <el-radio label="0">否</el-radio>
              <el-radio label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.depreciationOpen == 1" label="折旧方法" prop="depreciationMethod">
            <el-select
                placeholder="请选择设备折旧方法"
                v-model="form.depreciationMethod"
                clearable
                style="width: 100%"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="item.id"
                  v-for="item in dict.type.depreciation_method"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.depreciationOpen == 1" label="开启折旧日期" prop="depreciationDate">
            <el-date-picker
                style="width: 100%"
                v-model="form.depreciationDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择开启折旧日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item v-if="form.depreciationOpen === 1" label="净残率" prop="residualRate">
            <el-input v-model="form.residualRate" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g, '$1')" maxlength="5" show-word-limit  placeholder="请输入净残率(0.05)"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="info-box">
        <IconTitle title="父设备" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <div>
            <!-- 所属分类 TODO -->
            <el-form-item label="父设备" prop="parentId" style="width: 100%">
              <!--一级分类-->
              <el-select
                  style="float: left"
                  v-model="form.parentCategoryId"
                  placeholder="设备分类" @change="accountOneChanged">
                <el-option
                    v-for="account in accountOneList"
                    :key="account.id"
                    :label="account.category"
                    :value="account.id"/>
              </el-select>

              <!-- 二级分类 -->
              <el-select
                  style="float: right ; margin-left: 20px"
                  v-model="form.parentId"
                  placeholder="请选择设备">
                <el-option
                    v-for="account in accountTwoList"
                    :key="account.value"
                    :label="account.deviceName"
                    :value="account.id"/>
              </el-select>
            </el-form-item>

          </div>
        </div>
      </div>

      <div class="info-box">
        <IconTitle title="设备封面图片(1张)" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <ImageUpload
              ref="fileCoverImg"
              :fileListTem="coverImgTem"
              :limit="1"
          />
        </div>
      </div>
      <div class="info-box">
        <IconTitle title="设备图片(最多10张)" imgUrl="yunwei"></IconTitle>
        <div class="info-from">
          <CoverUpload
              ref="fileupload"
              :fileListTem="imgArrayTem"
              :limit="10"
          />
        </div>
      </div>
      <div class="info-btn-box">
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";
import ImageUpload from "@/components/ems/ImageUpload/index.vue";
import CoverUpload from "@/components/ems/ImageUpload/coverUpload.vue";
import {
  getSelectUseStatus,
  addObj,
  getObj,
  putObj,
  getAccountList,
  getAccountId
} from "@/api/ems/equipment/account";
import {fetchListTree} from "@/api/ems/equipment/category";
import {fetchPositionListTree} from "@/api/ems/equipment/position";
import {getBrandList} from "@/api/ems/equipment/brand";
import {getList} from "@/api/ems/equipment/supplier";
import {fetchTree} from "@/api/admin/dept";
import {fetchList} from "@/api/admin/user";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import form from "../../../gen/form";

export default {
  name: "AddIndex",
  components: {
    IconTitle,
    Treeselect,
    ImageUpload,
    CoverUpload
  },
  props: ['id', 'showStatus'],
  dicts: ['device_level', 'device_use_status', 'device_status', 'depreciation_method', 'unit'],
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "name",
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
      },
      list: [],
      deviceLevelSelect: [],//设备等级
      useStatusSelect: [], //使用状态
      depreciationMethodSelect: [], //折旧方法
      deviceStatusSelect: [], //设备状态
      unitSelect: [], //单位
      categoryList: [], //设备类别
      storageList: [], //存放位置
      brandList: [], //和设备品牌
      supplierList: [], //供应商
      treeDeptData: [], //部门
      accountOneList: [],
      accountTwoList: [],
      form: {
        id: null,
        deviceNum: null,
        assetNum: null,
        deviceName: null,
        status: null,
        useStatus: null,
        categoryId: null,
        brandId: null,
        liableUserId: null,
        liableUser: null,
        expectedRunTime: null,
        specification: null,
        unit: null,
        supplierId: null,
        purchaseDate: null,
        purchaseAmount: null,
        warrantyDate: null,
        introductionDate: null,
        expectedScrapDate: null,
        electronicLabel: null,
        deptId: null,
        storageLocationId: null,
        currentNetWorth: null,
        coverImgId: 0,
        imgIdArray: [],
        minRunTime: 0,
        maxRunTime: 0,
        depreciationOpen: "0",
        controlOpen: "0",
        depreciationMethod: '',
        depreciationDate: null,
        residualRate: '',
        serviceLife: null,
        parentId: null,
        parentName: null,
        // 父设备数据
        parentCategoryId: undefined,//类别id
        fsbOptions1: []
      },
      coverImgTem: [],
      imgArrayTem: [],
      rules: {

        deviceNum: [
          {required: true, message: '请输入设备编号', trigger: 'blur'}
        ],
        assetNum: [
          {required: true, message: '请输入资产编号', trigger: 'blur'}
        ],
        deviceName: [
          {required: true, message: '请输入设备名称', trigger: 'blur'}
        ],
        purchaseAmount: [
          {required: true, message: '请输入购置金额', trigger: 'blur'}
        ],
        residualRate: [
          {required: true, message: '请输入净残率', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '请选择设备状态', trigger: 'blur'}
        ],
        useStatus: [
          {required: true, message: '请选择设备使用状态', trigger: 'blur'}
        ],
        introductionDate: [
          {required: true, message: '请选择投产日期', trigger: 'blur'}
        ],
        expectedScrapDate: [
          {required: true, message: '预计报废日期', trigger: 'blur'}
        ],
        categoryId: [
          {required: true, message: '请选择设备类别', trigger: 'blur'}
        ],
        brandId: [
          {required: true, message: '请选择设备品牌', trigger: 'blur'}
        ],
        depreciationMethod: [
          {required: true, message: '请选择折旧方法', trigger: 'blur'}
        ],
        depreciationDate: [
          {required: true, message: '请选择开始折旧日期', trigger: 'blur'}
        ],
        liableUser: [
          {required: true, message: '请选择负责人', trigger: 'change'}
        ],
        // currentNetWorth: [
        //   {required: true, message: '请输入当前净值', trigger: 'blur'},
        //   {pattern: /^[0-9]{1}([0-9]|[.])*$/, message: '当前净值只能为数字值', trigger: 'change'},
        // ],
      },
      dialogVisible: false,
    };
  },
  created() {
    this.getSelect();
    this.getOneAccount()
  },
  mounted() {
    if (this.showStatus === 1) {   //打开编辑页面
      if (this.id > 0) {
        getObj(this.id).then((res) => {
          //数据回显
          this.form = res.data;
          getAccountList().then(response=>{
            this.accountOneList = response.data;
            for (let i = 0; i <this.accountOneList.length; i++) {
              //每个一级分类
              let oneSubject = this.accountOneList[i];
              //判断：所有一级分类id和点击一级分类id是否一样
              if (this.form.parentCategoryId === oneSubject.id) {  //===即比较值 还要比较类型
                //从一级分类中获取所有的二级分类
                this.accountTwoList = oneSubject.children;
                //把二级分类Id值清空
              }
            }
          })
          if (res.data.coverImgId) {
            this.coverImgTem.push({url: res.data.coverImgId});
          } else {
            this.coverImgTem = [];
          }
          if (res.data.imgArray != null) {
              this.imgArrayTem = res.data.imgArray.map((element) => {
                return {
                  url: element
                }
              }
            );
          }
          // 修改回显数据
          Object.keys(this.form).forEach((item, index) => {
            if (item !== "coverImgId" && item !== "imgIdArray") {
              this.form[item] = res.data[item];
            }
          });
        });
      }
    }
  },
  methods: {

    //查询所有的一级分类
    getOneAccount(){
      getAccountList().then(response=>{
        this.accountOneList = response.data;
      })
    },

    accountOneChanged() {
      for (let i = 0; i <this.accountOneList.length; i++) {
        //每个一级分类
        let oneSubject = this.accountOneList[i];
        //判断：所有一级分类id和点击一级分类id是否一样
        if (this.form.parentCategoryId === oneSubject.id) {  //===即比较值 还要比较类型
          //从一级分类中获取所有的二级分类
          this.accountTwoList = oneSubject.children;
          //把二级分类Id值清空
          this.form.parentId = '';
        }
      }
    },

    handleChange(value) {
    },

    // 在获得父设备类别之后查询父设备
    getFuListDate(id) {
      if (id != "" || id != null) {
        getAccountId(id).then(res => {
          this.form.fsbOptions1 = res.data;
        })
      }
    },
    
    submitForm(formName) {
      let data = JSON.parse(JSON.stringify(this.form));
      let list = this.$refs.fileCoverImg.fileList;
      const imgList = this.$refs.fileupload.fileList;
      const regex = /^http:\/\/.*\.png$/;
      if (list.length == 0) {
        data.coverImgId = "";
      }else {
        for (let k in list[0]) {
          if (regex.test(list[0][k])) {
            data.coverImgId = list[list.length-1][k];
          }
        }
      }

      data.imgIdArray = [];
      if (imgList.length > 0) {
        imgList.forEach(item => {
            if (Object.keys(item).includes('temUrl')) {
              data.imgIdArray.push(item.temUrl);
            } else if ( Object.keys(item).includes('url')) {
              data.imgIdArray.push(item.url);
            }
          }
        )
      } else {
        data.imgIdArray = [];
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (data.id) {
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.form = res.data;
              this.reset();
            });
          } else {
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功！")
              this.$parent.listFlag = true;
              this.reset();
            });
          }
        }
      });
    },

    // 清空数据
    reset() {
      this.form = {
        id: null,
        deviceNum: null,
        assetNum: null,
        factoryNumber: null,
        deviceName: null,
        status: null,
        useStatus: null,
        categoryId: null,
        brandId: null,
        liableUserId: null,
        liableUser: null,
        expectedRunTime: null,
        specification: null,
        unit: null,
        supplierId: null,
        purchaseDate: null,
        purchaseAmount: null,
        warrantyDate: null,
        introductionDate: null,
        expectedScrapDate: null,
        electronicLabel: null,
        deptId: null,
        storageLocationId: null,
        currentNetWorth: null,
        productionRhythm: null,
        coverImgId: "",
        imgIdArray: [],
        minRunTime: 0,
        maxRunTime: 0,
        depreciationOpen: "0",
        controlOpen: "0",
        parentCategoryId: null,
        parentId: null
      };
      this.coverImgTem = [];
      this.imgArrayTem = [];
    },
    getSelect() {
      getSelectUseStatus().then((res) => {
        this.useStatusSelect = res.data;
      });
      fetchListTree("").then((res) => {
        this.categoryList = res.data ? res.data : [];
      });

      fetchPositionListTree("").then((res) => {
        this.storageList = res.data ? res.data : [];
      });
      getBrandList().then((res) => {
        this.brandList = res.data;
      });
      getList().then((res) => {
        this.supplierList = res.data;
      });
      //部门
      fetchTree().then((response) => {
        this.treeDeptData = response.data;
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    normalizerDept(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    goBack() {
      this.$parent.listFlag = true;
      //  this.$parent.refreshChange()
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.add-box {
  margin-bottom: 50px;

  .info-box {
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 10px 15px;
    overflow: hidden;

    .info-from {
      display: flex;
      flex-wrap: wrap;
      padding-top: 20px;
      position: relative;

      .el-form-item {
        width: 50%;
        padding-right: 10px;
      }
    }

    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {
      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }

  .info-btn-box {
    width: 100%;
    text-align: center;
  }

  .user {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
</style>
