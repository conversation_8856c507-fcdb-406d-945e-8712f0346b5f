import { requestPlatForm } from "@/utils/requestBase";


// 查询流程列表
export function listFlow(query) {
  return requestPlatForm({
    url: '/base/flow/list',
    method: 'get',
    params: query
  })
}

// 查询流程详细
export function getFlow(id) {
  return requestPlatForm({
    url: '/base/flow/' + id,
    method: 'get'
  })
}

// 新增流程
export function addFlow(data) {
  return requestPlatForm({
    url: '/base/flow',
    method: 'post',
    data: data
  })
}

// 修改流程
export function updateFlow(data) {
  return requestPlatForm({
    url: '/base/flow/update',
    method: 'post',
    data: data
  })
}

// 删除流程
export function delFlow(id) {
  return requestPlatForm({
    url: '/base/flow/delete/' + id,
    method: 'get'
  })
}


// 查询文档类型管理列表
export function listDocument(query) {
    return requestPlatForm({
      url: '/base/document/list',
      method: 'get',
      params: query
    })
  }

  // 查询文档类型管理详细
  export function getDocument(id) {
    return requestPlatForm({
      url: '/base/document/' + id,
      method: 'get'
    })
  }

  // 新增文档类型管理
  export function addDocument(data) {
    return requestPlatForm({
      url: '/base/document',
      method: 'post',
      data: data
    })
  }

  // 修改文档类型管理
  export function updateDocument(data) {
    return requestPlatForm({
      url: '/base/document/update',
      method: 'post',
      data: data
    })
  }

  // 删除文档类型管理
  export function delDocument(id) {
    return requestPlatForm({
      url: '/base/document/delete/' + id,
      method: 'get'
    })
  }



  //流程
  export function getSelectList(id) {
    return requestPlatForm({
      url: '/base/flow/queryRelatesInfo',
      method: 'get'
    })
  }

  //流程提交
  export function saveFlowData(data) {
    return requestPlatForm({
      url: '/base/flow/flowDesign',
      method: 'post',
      data: data
    })
  }

  //流程回显
  export function queryFlowNodesByFlowId(id) {
    return requestPlatForm({
      url: '/base/flow/queryFlowNodesByFlowId/' + id,
      method: 'get'
    })
  }

  //流程置顶
  export function topping(id) {
    return requestPlatForm({
      url: '/base/flow/topping/' + id,
      method: 'get'
    })
  }

  //流程类型
  export function getFlowTypeList() {
    return requestPlatForm({
      url: '/base/flow/getFlowTypeList',
      method: 'get'
    })
  }