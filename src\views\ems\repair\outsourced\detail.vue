<template>
    <div>

        <el-card shadow="always" class="box-card">
            <div class="tableTitle"><span>申请信息</span></div>
            <div class="devTitle"><span>{{outOrder.deviceName}}</span></div>
            <div>
                <div class="tableStyle">
                    <div class="labelS">申请单号</div>
                    <div class="contentS">{{outOrder.repairNum}}</div>
                    <div class="labelS">申请人</div>
                    <div class="contentS">{{outOrder.createBy}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">设备编号</div>
                    <div class="contentS">{{outOrder.deviceNum}}</div>
                    <div class="labelS">设备名称</div>
                    <div class="contentS">{{outOrder.deviceName}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">委外单位</div>
                    <div class="contentS">{{outOrder.outName}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">申请时间</div>
                    <div class="contentS">{{outOrder.createTime}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">预计金额</div>
                    <div class="contentS">{{outOrder.expectMoney}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">工期要求</div>
                    <div class="contentS">{{outOrder.duration}}</div>
                </div>
                <div class="tableStyle">
                    <div class="labelS">委外理由</div>
                    <div class="contentS">{{outOrder.outReason}}</div>
                </div>
            </div>
        </el-card>

        <el-card shadow="always" class="box-card">
            <div class="tableTitle"><span>关联工单</span></div>
            <div>
                <el-table
                        :data="tableData"
                        style="width: 100%">
                    <el-table-column
                            prop="repairNum"
                            label="编号">
                    </el-table-column>
                    <el-table-column
                            prop="createBy"
                            label="申请人">
                    </el-table-column>
                    <el-table-column
                            prop="falutType"
                            label="故障类型">
                    </el-table-column>
                    <el-table-column
                            prop="faultGrade"
                            label="故障等级">
                    </el-table-column>
                    <el-table-column
                            prop="createTime"
                            label="报修时间">
                    </el-table-column>
                </el-table>

            </div>
        </el-card>


    </div>
</template>
<script>
    import {getObj} from "@/api/ems/repair/outorder";
    export default {
        data() {
            return {
                outOrder:{},
                tableData:[]
            };
        },
        mounted() {
            this.getOrde()
        },
        methods :{
            getOrde(){
                getObj(this.$route.query.id).then(res => {
                    this.outOrder = res.data.data.outOrder
                    this.tableData = res.data.data.list
                })
            }
        }
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";

    .headerStyle {
        margin-bottom: 20px;

    }

    .tableTitle {
        color: #333;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;

    }

    .smallTitle {
        color: rgba(153, 153, 153, 100);
        font-size: 12px;
        margin-left: 10px;
    }

    .devTitle {
        color: #262626;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .box-card {
        margin-bottom: 20px;

        .el-card__body {
            padding-top: 10px;
        }

        .labelS {
            //display: inline-block;
            flex: 0 0 150px;
            //height: 40px;
            // margin-right: 10px;
            text-align: left;
            color: #606266;
            padding: 10px;
            border: 1px solid rgba(236, 240, 244, 100);
            margin-bottom: -1px;
        }

        .contentS {
            border: 1px solid rgba(236, 240, 244, 100);
            // height: 40px;
            color: #606266;
            width: 100%;
            margin-left: -1px;
            margin-bottom: -1px;
            padding: 10px;
            // margin: 10px 0;
            // width: calc(100% - 120px);
            // display: inline-block;
        }

        .tableStyle {
            display: flex;
        }

        .number {
            font-weight: bold;
            margin-top: 5px;
            font-size: 16px;
        }

        .nocomplate {
            color: white;
            background-color: #E83672;
            padding: 3px 5px;
            border-radius: 3px;
            font-size: 12px;
        }

        .complate {
            background-color: #78BF34;
        }
    }

</style>
