<template>
  <div class="execution">
    <el-card class="gzzd">
      <div class="sbtp">
        <img :src="require('@/assets/imagesAssets/sbzl.png')"/>
      </div>
      <div class="tbzl">
        <div class="anzhuo">
          <img src="@/assets/svg/anzhuo.svg"/>
        </div>
        <div class="zl">
          <span>规章制度</span>
        </div>
      </div>
      <div class="sm">
        可以管理平台菜单和应用菜单，采用统一管理方式实现对所有系统菜单的统一增删改查，方便用户操作及统一管控
      </div>
    </el-card>
    <el-card class="box-card btn-search page-search crud">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     style="backgroundColor:#E1b980"
                     icon="el-icon-circle-plus-outline"
                     v-if="permissions.ems_emsregulations_add"
                     @click="addRegulations"
          >新增
          </el-button
          >
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="permissions.ems_emsregulations_edit"
              :disabled="single"
              @click="deviceEdit"
          >编辑
          </el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="permissions.ems_emsregulations_del"
              @click.native="handleDel()"
              :disabled="multiple"
          >删除
          </el-button
          >
<!--          <el-button-->
<!--              type="check"-->
<!--              icon="el-icon-download"-->
<!--              @click="exportExcel"-->
<!--          >导出-->
<!--          </el-button-->
<!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="form">
        <el-form :inline="true">
          <el-form-item label="制度名称">
            <el-input placeholder="请输入制度名称" v-model="searchForm.regulationName" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChangeU">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <basic-container>
      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <avue-crud
              ref="crud"
              :page.sync="page"
              :data="tableData"
              :permission="permissionList"
              :table-loading="tableLoading"
              :option="tableOption"
              :cell-style="cellStyle"
              @selection-change="selectionChange"
              @on-load="getList"
              @search-change="searchChange"
              @refresh-change="refreshChange"
              @size-change="sizeChange"
              @current-change="currentChange"
              @cell-click="cellClick"

              @row-del="handleDel"

          >
<!--            @row-update="handleUpdate"-->
<!--            @row-save="handleSave"-->
            <template slot="header">
              <IconTitle class="selfTitle" title="规章制度" imgUrl="yunwei"/>
            </template>
            <template slot-scope="scope" slot="menu">
              <el-button type="text" @click="deviceEdit(scope.row)">
                <i class="icon-bianji" style="font-size: 13px"></i>编辑
              </el-button>
            </template>

          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>
  </div>
</template>
<script>
import {fetchList, getObj, addObj, putObj, delObj, getTree} from "@/api/ems/repository/emsregulations";

import {tableOption} from "@/const/crud/ems/repository/emsregulations";
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";


export default {
  name: 'emsregulations',
  components: {
    IconTitle,
  },
  data() {
    return {
      title: "",
      tableData: [],
      searchForm: {
        //制度名称
        regulationName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      // 树状结构
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        defaultExpandAll:true,
        props: {
          label: "name",
          value: "id",
        },
      },
      treeData: [],
      // 新增弹出框
      dialogFormVisible: false,
      form: {
        id: "",
        releaseScope: undefined,
        regulationName: "",
        isNoTop: "",
        regulationCategory: "",
        regulationCategoryCopy: [],
        fileIdArray: "",
        remark: ""
      },
      rules: {},
      imgArrayTem: [],
      treeDeptData: [], //部门
      isNoTopData: [
        {
          value: 0,
          label: "是"
        },
        {
          value: 1,
          label: "否"
        }
      ],
      optionProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      editId: 0
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsregulations_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsregulations_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsregulations_edit, false),
      };
    },
  },
  mounted() {
    this.initElement();
    this.changeThme();
    this.getTreeData();
    // this.getSelect();
  },
  methods: {

    // 新增按钮
    addRegulations() {
      this.$router.push({
        path: "/ems/repository/emsregulations/addRegulations",
      });
    },

    // 修改
    deviceEdit(row) {
      this.$router.push({
        path: "/ems/repository/emsregulations/editRegulations",
        query: {
          id: row.id,
          editId: row.id || this.selectionList[0].id
        }
      });
    },

    // 部门树
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },

    // 提交表单
    submitForm(formName) {
      let data = JSON.parse(JSON.stringify(this.form));

      if (this.$refs.fileupload.fileList != null) {
        data.fileIdArray = this.$refs.fileupload.fileList.map((item) =>
            item.id
        );
      } else {
        data.fileIdArray = [];
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          data.regulationCategory = data.regulationCategoryCopy[data.regulationCategoryCopy.length - 1];
          if (data.id) {
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.form = res.data.data;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          } else {
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功!")
              this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          }
        }
      });
    },

    // 搜索框
    searchChangeU(param, done) {
      //console.log(this.$refs.crud)
      this.page.currentPage = 1
      this.getList(this.page, this.searchForm)
      //done()
    },
    resetBtn() {
      this.searchForm.regulationName = '';
    },

    // 改变制度编号的颜色
    cellStyle(data) {
      if (data.columnIndex === 2) {
        return "color:#02b980;cursor:pointer";
      }
    },

    // 点击制度编号跳转页面
    cellClick(row, column) {
      // console.log("111111111111111111111",column.property);
      if (column.property === "regulationCode") {
        this.$router.push({
          path: "/ems/repository/emsregulations/systemDetails", //制度详情页面
          query: {
            id: row.id
          }
        });
      } else {
        return;
      }
    },

    // 规章制度树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "规章制度类型") {
              common_table_info.push(treeDataList[index])
            }
          })
          this.treeData = common_table_info;
          // console.log(JSON.stringify(this.treeData));
        }
      })
    },

    // 树状图每次点击重新搜索
    nodeClick(data) {
      this.page.page = 1;
      this.getList(this.page, {regulationCategory: data.id});
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
    },
    //编辑
    // handleEdit() {
    //   var refsDate = this.$refs
    //   refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
    // },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 更新
    // handleUpdate: function (row, index, done, loading) {
    //   putObj(row)
    //       .then((data) => {
    //         this.$message.success("修改成功");
    //         done();
    //         this.getList(this.page);
    //       })
    //       .catch(() => {
    //         loading();
    //       });
    // },
    // 保存
    // handleSave: function (row, done, loading) {
    //   addObj(row)
    //       .then((data) => {
    //         this.$message.success("添加成功");
    //         done();
    //         this.getList(this.page);
    //       })
    //       .catch(() => {
    //         loading();
    //       });
    // },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.downBlobFile(
          "/ems/emsregulations/export",
          this.searchForm,
          "emsregulations.xlsx"
      );
    },
    // 改变主题颜色
    changeThme() {
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor = this.theme;
    },

    // getSelect() {
    //   //部门
    //   fetchTree().then((response) => {
    //     this.treeDeptData = response.data.data;
    //   });
    // },

  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="less">
.execution {

  .crud {
    .form {
      float: top;
    }
  }

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }

  .gzzd {
    margin-bottom: 10px;
    height: 120px;

    .sbtp {
      width: 147px;
      height: 100px;
      float: right;
      margin-right: 24px;
      margin-top: -10px;
    }

    .tbzl {
      display: flex;
      flex-direction: row;

      .anzhuo {
        left: 262px;
        top: 100px;
        width: 15px;
        height: 15px;
        color: rgba(89, 89, 89, 100);
        margin-right: 13px;
      }

      .zl {
        left: 295px;
        top: 95px;
        width: 72px;
        height: 27px;
        color: rgba(89, 89, 89, 100);
        font-size: 18px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: bold;
      }
    }

    .sm {
      left: 262px;
      top: 152px;
      width: 700px;
      height: 20px;
      color: rgba(134, 129, 129, 100);
      font-size: 12px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
      margin-top: 40px;
    }
  }
}
</style>
