<template>
  <div class="drawer-box">
    <div style="position: absolute;margin-left: 50%;margin-top: -73px;">
      <img src="../../../../../public/ems/images/sbtzxq.png">
    </div>
    <!--    <div style="position: absolute;margin-left: 80%;margin-top: 110px">-->
    <!--      <el-button size="mini" @click="print_dy" type="primary">打印<i class="el-icon-printer el-icon&#45;&#45;right"></i>-->
    <!--      </el-button>-->
    <!--    </div>-->
    <div class="info-box" ref="print">
      <div style="float: right; margin-right: 15%;position: relative;">
        <div style="position: absolute; ">
          <img style="width: 110px; height: 110px"  :src="headIMG(deviceData.qrCode)">
        </div>
        <div style="position: absolute;margin-top: 110px;margin-left: 18px" >
          <!-- <el-button size="mini" id="showBtn" @click="print_dy" type="primary">打印<i class="el-icon-printer el-icon--right"></i> -->
          </el-button>
        </div>
      </div>

      <div class="info-item">
        <span class="labelS">设备名称：</span>
        <span class="contentS">{{ deviceData.deviceName }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">设备编号：</span>
        <span class="contentS">{{ deviceData.deviceNum }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">资产编号：</span>
        <span class="contentS">{{ deviceData.assetNum }}</span>
      </div>
      <!--      <div class="info-item" id="addressData" style="display: none">-->
      <!--        <span class="labelS">设备地址:</span>-->
      <!--        <div class="contentS">-->
      <!--          &lt;!&ndash;<icon class="icon-map1"/>&ndash;&gt;-->
      <!--          {{ deviceData.location }}-->
      <!--        </div>-->
      <!--      </div>-->

    </div>
    <div class="info-box">
      <div class="info-item">
        <span class="labelS">描述信息</span>
        <span class="content-status">设备状态:
             <div v-if="deviceData.status == 0" class="sbzt">
              <span class="circle-green"></span>
              <p>正常</p>
            </div>
            <div v-if="deviceData.status == 1" class="sbzt">
              <span class="circle-red"></span>
              <p>故障</p>
            </div>
            <div v-if="deviceData.status == 2" class="sbzt">
              <span class="circle-yellow"></span>
              <p>带病运行</p>
            </div>
        </span>
        <span class="content-status">使用状态:
          <div v-if="deviceData.useStatus == 0" class="sbzt">
            <span class="circle-blue"></span>
            <p>在用</p>
          </div>
          <div v-if="deviceData.useStatus == 1" class="sbzt">
            <span class="circle-green"></span>
            <p>闲置</p>
          </div>
          <div v-if="deviceData.useStatus == 2" class="sbzt">
            <span class="circle-blue"></span>
            <p>出租</p>
          </div>
          <div v-if="deviceData.useStatus == 3" class="sbzt">
            <span class="circle-purple"></span>
            <p>禁用</p>
          </div>
          <div v-if="deviceData.useStatus == 4" class="sbzt">
            <span class="circle-yellow"></span>
            <p>报废</p>
          </div>
        </span>
        <span class="content-status"
        >设备等级:
          <div v-if="deviceData.deviceLevel == 'A'" class="sbzt">
            <span class="circle-blue"></span>
            <p>A(关键)</p>
          </div>
          <div v-if="deviceData.deviceLevel == 'B'" class="sbzt">
            <span class="circle-green"></span>
            <p>B(重要)</p>
          </div>
          <div v-if="deviceData.deviceLevel == 'C'" class="sbzt">
            <span class="circle-purple"></span>
            <p>C(一般)</p>
          </div>
        </span>
      </div>
    </div>
    <div class="tab-box">
      <el-tabs tab-position="left" style="font-size: 12px">
        <el-tab-pane label="基本信息" style="font-size: 12px">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="基本信息" imgUrl="yunwei">
                </IconTitle>
                <!-- 背景图片-->
                <img class="bjq" :src="require('@/assets/imagesAssets/bjq.png')">
                <div class="card-item">
                  <span>设备类别:</span>
                  <div class="card-con">{{ deviceData.category }}</div>
                </div>
                <div class="card-item">
                  <span>供应商:</span>
                  <div class="card-con">
                    <!--<icon class="icon-gongsi"/>-->
                    {{ deviceData.supplierName }}
                  </div>
                </div>
                <div class="card-item">
                  <span>规格型号:</span>
                  <div class="card-con">{{ deviceData.specification }}</div>
                </div>
                <div class="card-item">
                  <span>品牌:</span>
                  <div class="card-con">{{ deviceData.brandName }}</div>
                </div>
                <div class="card-item">
                  <span>保修期至:</span>
                  <div class="card-con">{{ deviceData.warrantyDate }}</div>
                </div>
                <div class="card-item">
                  <span>启用期至:</span>
                  <div class="card-con">{{ deviceData.introductionDate }}</div>
                </div>
                <div class="card-item">
                  <span>预计报废日期:</span>
                  <div class="card-con">{{ deviceData.expectedScrapDate }}</div>
                </div>
                <div class="card-item">
                  <span>负责人:</span>
                  <div class="card-con">{{ deviceData.liableUser }}</div>
                </div>
                <div class="card-item">
                  <span>所属部门:</span>
                  <div class="card-con">{{ deviceData.deptName }}</div>
                </div>
                <div class="card-item">
                  <span>设备地址:</span>
                  <div class="card-con">
                    <!--<icon class="icon-map1"/>-->
                    {{ deviceData.location }}
                  </div>
                </div>
              </div>
              <div class="box-card">
                <IconTitle title="设备图片" imgUrl="yunwei">
                </IconTitle>
                <div class="card-image">
                  <span v-for="item in imgArray" :key="item.id" @click="image_tar(item)">
                    <img :src="item" class="image">
                  </span>
                  <el-dialog title="预览" :visible.sync="dialogVisible" width="50%" height="30%"
                             :modal="false" @close="dialogVisibleClose" :close-on-click-modal="false">
                    <img :src="url" style="width: 100%; height: 100%">
                  </el-dialog>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="关联信息">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="关联的子设备" imgUrl="yunwei">
                </IconTitle>
                <!-- 设备文档 -->
                <el-table
                    :data="childDevice"
                    border
                    style="width: 100%"
                    :header-cell-style="{
                    background: '#f8f8f9',
                    color: '#606266',
                  }"
                >
                  <el-table-column type="selection" align="center" width="55"/>
                  <el-table-column prop="deviceNum" label="设备编号" align="center">
                  </el-table-column>
                  <el-table-column prop="deviceName" label="设备名称" align="center">
                  </el-table-column>
                  <el-table-column prop="brandName" label="品牌" align="center">
                  </el-table-column>
                  <el-table-column prop="specification" label="规格型号" align="center">
                  </el-table-column>
                </el-table>
              </div>
              <!--              <div class="box-card">-->
              <!--                <IconTitle title="设备关联备件" imgUrl="yunwei">-->
              <!--                  <span class="slot">品牌管理</span>-->
              <!--                </IconTitle>-->
              <!--                &lt;!&ndash; 设备文档 &ndash;&gt;-->
              <!--                <el-table-->
              <!--                    :data="FDeviceData"-->
              <!--                    border-->
              <!--                    style="width: 100%"-->
              <!--                    :header-cell-style="{-->
              <!--                    background: '#f8f8f9',-->
              <!--                    color: '#606266',-->
              <!--                  }"-->
              <!--                    @selection-change="deviceSelectionChange"-->
              <!--                >-->
              <!--                  <el-table-column type="selection" align="center" width="55"/>-->
              <!--                  <el-table-column prop="date" label="备件编号" align="center">-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column prop="name" label="备件名称" align="center">-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column prop="address" label="品牌" align="center">-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column prop="address" label="类别" align="center">-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column prop="address" label="需求" align="center">-->
              <!--                  </el-table-column>-->
              <!--                </el-table>-->
              <!--              </div>-->
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="维保排名">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="维修" imgUrl="yunwei"/>
                <div style="display:flex;">
                  <div class="weixiuNum">
                    <img :src="require('@/assets/imagesAssets/wxNum.png')" height="50px" width="100px"/>
                    <span style="font-weight: 600; font-size: 25px; margin-left: 45px">{{
                        maintainRankingData.failureNumber
                      }}</span>
                    <span style="margin-left: 25px; color: #385693">维修次数</span>
                  </div>
                  <div class="weixiuNum">
                    <img :src="require('@/assets/imagesAssets/pjwxNum.png')" height="50px" width="50px"/>
                    <span style="font-weight: 600; font-size: 25px;  text-align: center; width: 52px;">{{
                        maintainRankingData.averageMaintenanceNum == 0.00 ? 0 : maintainRankingData.averageMaintenanceNum
                      }}</span>
                    <span style="margin-left: -25px; color: #385693">同类设备平均维修次数</span>
                  </div>
                  <div class="weixiuNum">
                    <img :src="require('@/assets/imagesAssets/qingyuan.png')" height="50px" width="50px"
                         style="margin-top: 10px;"/>
                    <span style="font-weight: 600; font-size: 25px; margin: -41px 0 0 19px; color: #fcfdfa">{{
                        maintainRankingData.rank == null ? "0" : maintainRankingData.rank
                      }}</span>
                    <span style="margin: 31px 0 0 -8px; color: #385693">同类设备排名</span>
                  </div>
                </div>
              </div>
              <div class="box-card">
                <IconTitle title="保养" imgUrl="yunwei"/>
                <div style="display: flex; flex-direction: column">
                  <div style="display:flex;">
                    <div class="weixiuNum">
                      <img :src="require('@/assets/imagesAssets/byNum.png')" height="50px" width="100px"/>
                      <span style="font-weight: 600; font-size: 25px; margin-left: 45px">{{
                          maintenanceRankingData.maintainNumber
                        }}</span>
                      <span style="margin-left: 25px; color: #385693">保养次数</span>
                    </div>
                    <div class="weixiuNum">
                      <img :src="require('@/assets/imagesAssets/pjbyNum.png')" height="50px" width="60px"/>
                      <span style="font-weight: 600; font-size: 25px; text-align: center; width: 57px;">{{
                          maintenanceRankingData.averageMaintenanceNum == 0.00 ? 0 : maintenanceRankingData.averageMaintenanceNum
                        }}</span>
                      <span style="margin-left: -25px; color: #385693">同类设备平均保养次数</span>
                    </div>
                    <div class="weixiuNum">
                      <img :src="require('@/assets/imagesAssets/qingyuan.png')" height="50px" width="50px"
                           style="margin-top: 10px;"/>
                      <span style="font-weight: 600; font-size: 25px; margin: -41px 0 0 19px; color: #fcfdfa">{{
                          maintenanceRankingData.rank == null ? "0" : maintenanceRankingData.rank
                        }}</span>
                      <span style="margin: 31px 0 0 -8px; color: #385693">同类设备排名</span>
                    </div>
                  </div>
                  <!--                  <div style="display:flex;">-->
                  <!--                    <div class="weixiuNum">-->
                  <!--                      <img :src="require('@/assets/imagesAssets/byfy.png')" height="50px" width="100px"/>-->
                  <!--                      <span style="font-weight: 600; font-size: 25px; margin-left: 45px">5</span>-->
                  <!--                      <span style="margin-left: 25px; color: #385693">维修次数</span>-->
                  <!--                    </div>-->
                  <!--                    <div class="weixiuNum">-->
                  <!--                      <img :src="require('@/assets/imagesAssets/tlbyfy.png')" height="50px" width="60px"/>-->
                  <!--                      <span style="font-weight: 600; font-size: 25px; margin-left: -9px">158.67</span>-->
                  <!--                      <span style="margin-left: -25px; color: #385693">同类设备平均维修次数</span>-->
                  <!--                    </div>-->
                  <!--                    <div class="weixiuNum">-->
                  <!--                      <img :src="require('@/assets/imagesAssets/qingyuan.png')" height="50px" width="50px" style="margin-top: 10px;"/>-->
                  <!--                      <span style="font-weight: 600; font-size: 25px; margin: -41px 0 0 19px; color: #fcfdfa">7</span>-->
                  <!--                      <span style="margin: 31px 0 0 -8px; color: #385693">同类设备排名</span>-->
                  <!--                    </div>-->
                  <!--                  </div>-->
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="最新点检">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="巡检信息" imgUrl="yunwei">
                </IconTitle>
                <!-- 背景图片-->
                <img class="bjq" :src="require('@/assets/imagesAssets/bjq.png')">
                <div class="card-item">
                  <span class="labelS">检验时间:</span>
                  <div class="contentS">{{ latestTallyData.provingTime }}</div>
                </div>
                <div class="card-item">
                  <span class="labelS">检验人:</span>
                  <div class="contentS">{{ latestTallyData.surveyor }}</div>
                </div>
                <div class="card-item">
                  <span class="labelS">异常个数:</span>
                  <div class="contentS">{{ latestTallyData.abnormalNum }}</div>
                </div>
              </div>
              <div class="box-card">
                <IconTitle title="巡检结果" imgUrl="yunwei">
                </IconTitle>
                <div class="box-card">
                  <template>
                    <el-table
                        :data="latestTallyData.insInspectTaskList"
                        stripe>
                      <el-table-column
                          prop="itemsName"
                          label="点检项"
                          align="center"
                          width="200">
                      </el-table-column>
                      <el-table-column
                          prop="inspectValue"
                          label="点检值"
                          align="center"
                          width="200">
                      </el-table-column>
                      <el-table-column
                          prop="isAbnormal"
                          align="center"
                          width="200"
                          label="状态">
                        <template slot-scope="scope">
                            <span>{{
                                scope.row.isAbnormal == 0
                                    ? "正常"
                                    : scope.row.isAbnormal == 1
                                        ? "异常" : ""
                              }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue"
import {
  getMaintenanceRanking,
  getMaintainRanking
} from "@/api/ems/equipment/account";


export default {
  name: "drawerCon",
  components: {
    IconTitle,
  },
  data() {
    return {
      dialogVisible: false,
      url: '',
      cData: [],
      searchForm: {
        deviceId: '',
        executeEndTime: ''
      },
      maintenanceRankingData: [],
      maintainRankingData: [],
    };
  },
  props: {
    latestTallyData: {
      type: Object
    },
    deviceData: {
      type: Object
    },
    imgArray: {
      type: Array
    },
    childDevice: {
      type: Array
    }
  },
  created() {


    // this.getMaintenanceRankingList();
  },
  watch: {
    deviceData: function (newVal, oldVal) {
      this.cData = newVal;  //newVal即是chartData
      let id = this.cData.id;
      this.getMaintenanceRankingList(id);
      this.getMaintainRankingList(id);
      // this.deviceSelectionChange(id);
    }
  },
  methods: {
    print_dy: function () {
      let s = document.getElementById("showBtn");
      // let a = document.getElementById("addressData");
      s.style.display = "none";
      // a.style.display = "block";
      this.$print(this.$refs.print);
      s.style.display = "block";
      // a.style.display = "none";
    },

    headIMG(img) {
      const imgData = require('@/assets/imagesAssets/kong.png')
      return img != null ? img : imgData
    },

    getMaintenanceRankingList(id) {
      this.searchForm.deviceId = id;
      getMaintenanceRanking(this.searchForm).then(res => {
        this.maintenanceRankingData = res.data;
      });
    },
    getMaintainRankingList(id) {
      this.searchForm.deviceId = id;
      getMaintainRanking(this.searchForm).then(res => {
        this.maintainRankingData = res.data;
      });
    },

    image_tar(url) {
      this.url = url;
      this.dialogVisible = true;
    },
    dialogVisibleClose() {
      this.dialogVisible = false;
      this.url = '';
    }
  },
};
</script>
<style lang="scss" media="print" scoped>
@import "@/styles/ems/mixin.scss";
@import "@/styles/color.scss";
@page {
  size: auto; /* auto is the initial value */
  margin: 3mm; /* this affects the margin in the printer settings */
}
::v-deep .el-drawer__body {
  overflow: auto;
}

.drawer-box {
  .info-box {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #f2f2f5;
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    margin-bottom: 10px;
    margin-left: 30px;

    .sbtzxq {
      float: left;
      margin-top: -143px;
      margin-left: 250px;
    }

    .labelSMS {
      width: 15%;
      color: rgba(193, 200, 210, 100);
      font-size: 12px;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
      margin-left: -30px;
    }

    .labelS {
      width: 15%;
      color: #888;
      font-size: 12px;
    }

    .contentS {
      font-weight: bold;
      color: #101010;
      display: inline-block;
      font-size: 12px;
      margin-top: 0px;
    }

    .content-status {
      margin-right: 50px;
      font-size: 12px;
      display: flex;
      align-items: center;

      .sbzt {
        height: 10px;
        display: flex;
        align-items: center;
      }

      .circle-lan {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-lv {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, $theme);
      }

      .circle-zi {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-blue {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-green {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #5ec28e);
      }
      .circle-blue {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #5ebbff);
      }
      .circle-orange {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #fdaf5b);
      }

      .circle-purple {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-red {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ff0000);
      }

      .circle-yellow {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ffff00);
      }
    }
  }

  .tab-box {
    .basic-box-con {
      overflow: auto;
      height: 500px;

      .basic-big {
        .box-card {
          padding: 10px;
          box-shadow: 0px 0px 7px 0px #eff2f5;
          margin: 5px;

          .weixiuNum {
            display: flex;
            flex-direction: column;
            margin: 20px 0 20px 85px;
          }

          .card-image {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            margin-left: 10px;

            .image {
              left: 1435px;
              top: 776px;
              width: 124px;
              height: 113px;
              margin-top: 10px;
              margin-left: 5px;
              border-radius: 5px;
            }

            //.image_tar{
            //  width: 50%;
            //  height: 50%;
            //}
          }

          .bjq {
            position: absolute;
            left: 310px;
            top: 100px;
          }

          .card-item {
            display: flex;
            align-items: center;
            margin-top: 10px;

            .icon-gongsi {
              color: #128bed;
            }

            .icon-map1 {
              color: #128bed;
            }

            span {
              display: inline-block;
              width: 11%;
              color: #888;
              text-align: right;
              margin-right: 10px;
            }

            .card-con {
              color: #101010;
            }
          }

          .image {
            left: 1435px;
            top: 776px;
            width: 116px;
            height: 83px;
          }

          .el-table {
            margin-top: 20px;
          }
        }
      }
    }
  }
}

</style>
