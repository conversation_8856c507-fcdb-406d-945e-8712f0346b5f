<template>
  <div class="execution">
    <el-card class="sbzl">
      <div class="sbtp">
        <img :src="require('@/assets/imagesAssets/sbzl.png')"/>
      </div>
      <div class="tbzl">
        <div class="anzhuo">
          <img src="@/assets/svg/anzhuo.svg"/>
        </div>
        <div class="zl">
          <span>设备资料</span>
        </div>
      </div>
      <div class="sm">
        可以管理平台菜单和应用菜单，采用统一管理方式实现对所有系统菜单的统一增删改查，方便用户操作及统一管控
      </div>
    </el-card>
    <el-card class="box-card btn-search page-search crud">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     style="backgroundColor:#E1b980"
                     icon="el-icon-circle-plus-outline"
                     v-if="permissions.ems_emsdevicedata_add"
                     @click="deviceAdd"
          >新增
          </el-button
          >
          <!--          @click="$refs.crud.rowAdd()"-->
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="permissions.ems_emsdevicedata_edit"
              :disabled="single"
              @click="deviceEdit"
          >编辑
          </el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="permissions.ems_emsdevicedata_del"
              @click.native="handleDel()"
              :disabled="multiple"
          >删除
          </el-button
          >
          <!--          <el-button-->
          <!--              type="check"-->
          <!--              icon="el-icon-download"-->
          <!--              @click="exportExcel"-->
          <!--          >导出-->
          <!--          </el-button-->
          <!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="form">
        <el-form :inline="true">
          <el-form-item label="资料名称">
            <el-input placeholder="请输入资料名称" v-model="searchForm.dataName" maxlength="20" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChangeU">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <basic-container class="container">

      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <avue-crud
              ref="crud"
              :page.sync="page"
              :data="tableData"
              :permission="permissionList"
              :table-loading="tableLoading"
              :option="tableOption"
              :cell-style="cellStyle"
              @selection-change="selectionChange"

              @search-change="searchChange"
              @refresh-change="refreshChange"
              @size-change="sizeChange"
              @current-change="currentChange"
              @row-update="handleUpdate"
              @row-save="handleSave"
              @row-del="handleDel"
              @cell-click="cellClick"
          >
            <template slot="header">
              <IconTitle class="selfTitle" title="设备资料" imgUrl="yunwei"/>
            </template>
            <template slot-scope="scope" slot="menu">
              <el-button type="text" @click="deviceEdit(scope.row)">
                <i class="icon-bianji" style="font-size: 13px"></i>编辑
              </el-button
              >
              <el-button type="text" @click="goLook(scope.row)">
                <i class="el-icon-view"></i>相关附件
              </el-button
              >
            </template>
          </avue-crud>
        </el-col>
      </el-row>
    </basic-container>
    <!-- 相关附件 -->
    <el-drawer
        class="drawerStyle"
        title="相关附件"
        :visible.sync="drawer"
        direction="rtl"
        size="50%"
        append-to-body
    >
      <drawer-con :drawerData="drawerData" :fileArray="fileArray"/>
    </el-drawer>

    <!--  新增和修改  -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="50%" >

      <div>
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="资料名称" prop="dataName">
                <el-input v-model="form.dataName" maxlength="20" show-word-limit placeholder="请输入资料名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文档密级" prop="documentEncryptionLevel">
                <el-select style="width: 100%" v-model="form.documentEncryptionLevel" placeholder="请选择文档密级">
                  <el-option
                      v-for="item in documentData"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="资料类别" prop="dataCategoryId">
                <el-cascader :options="treeData"
                             v-model="form.dataCategoryId"
                             :props="optionProps"
                             :show-all-levels="false"
                             style="width: 100%"
                             placeholder="请选择资料类别"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联设备" prop="deviceIds">
                <el-select style="width: 100%" v-model="form.deviceIds" multiple placeholder="请选择关联设备">
                  <el-option
                      v-for="item in devices"
                      :key="item.id"
                      :label="item.deviceName"
                      :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="文件" prop="fileIdArray">
                <device-upload ref="fileupload"
                               :fileListTem="imgArrayTem"
                ></device-upload>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="说明" prop="remark">
                <el-input type="textarea" v-model="form.remark" maxlength="255" show-word-limit placeholder="请输入说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item style="float: right" v-if="title == '新增'">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="dialogFormVisible = false">返回</el-button>
              </el-form-item>
              <el-form-item style="float: right" v-if="title == '修改'">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="dialogFormVisible = false">返回</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {fetchList, getObj, addObj, putObj, delObj, getTree} from "@/api/ems/repository/emsdevicedata";
import {tableOption} from '@/const/crud/ems/repository/emsdevicedata'
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
import Tenant from "@/views/admin/tenant";
import DrawerCon from "./drawerCon"
import {changeByte} from "@/util/changeByte"
import deviceUpload from "./deviceUpload";
import { getAccountId,getAllAccountList } from "@/api/ems/equipment/account";
import {fetchListTree} from "@/api/ems/equipment/category";
import Treeselect from "@riophae/vue-treeselect";

export default {
  name: 'emsdevicedata',
  components: {
    Tenant,
    IconTitle,
    DrawerCon,
    deviceUpload,
    Treeselect
  },
  data() {
    return {
      form: {
        id: '',
        dataName: '',
        documentEncryptionLevel: '',
        dataCategoryId: '',
        dataCategoryIds: null,
        deviceIds: null,
        fileIdArray: [],
        remark: '',
      },
      devices: [],
      categoryList: [], //设备类别
      imgArrayTem: [],
      rules: {
        dataName: [
          {required: true, message: '请输入资料名称', trigger: 'blur'},
          {min: 1, max: 15, message: '长度在 1 到 15 个字符', trigger: 'blur'}
        ],
        documentEncryptionLevel: [
          {required: true, message: '请选择文档密级', trigger: 'blur'},
        ],
        dataCategoryId: [
          {required: true, message: '请选择资料类别', trigger: 'blur'},
        ],
        deviceIds: [
          {required: true, message: '请选择关联设备', trigger: 'blur'},
        ],
      },
      documentData: [
        {
          value: '0',
          label: '无'
        },
        {
          value: '1',
          label: '低'
        },
        {
          value: '2',
          label: '中'
        },
        {
          value: '3',
          label: '高'
        }
      ],
      optionProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },

      title: "",
      tableData: [],
      searchForm: {
        dataName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      editId: 0,
      // 树状图
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        props: {
          label: "name",
          value: "id",
        },
        filterText: "搜索关键字自定义",
        defaultExpandAll: true
      },
      treeData: [],

      // 详情页
      drawer: false,
      drawerData: {},
      fileArray: [],

      // 新增和修改弹出框
      dialogFormVisible: false,
      rowsData: {}
    };
  },

  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsdevicedata_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsdevicedata_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsdevicedata_edit, false),
      };
    },
  },
  created() {
    this.getSelect();
  },
  mounted() {
    this.initElement();
    this.changeThme();
    this.getTreeData();
  },
  methods: {

    // 新增
    deviceAdd() {
      this.title = "新增";
      this.reset();
      this.dialogFormVisible = true;
    },

    // 修改
    deviceEdit(row) {
      this.title = "修改";
      this.reset();
      this.dialogFormVisible = true;
      this.editId = row.id || this.selectionList[0].id;
      // alert(this.editId)
      getObj(this.editId).then(res =>{

        this.imgArrayTem = [];
        if (res.data.data.fileArray != null) {
          res.data.data.fileArray.forEach((element) => {
            this.imgArrayTem.push({
              name: element.original,
              id: element.id,
              url: element.url,
              temUrl: element.url,
            });
          });
        }
        // 修改回显数据
        Object.keys(this.form).forEach((item, index) => {
          if (item !== "fileArray") {
            this.form[item] = res.data.data[item];
          }
        });
        this.form = res.data.data;
        this.form.deviceIds = res.data.data.deviceIds;

      })
    },

    cellStyle(data) {
      // 改变资料编号的颜色
      if (data.columnIndex === 2) {
        return "color:#02b980;cursor:pointer";
      }
    },

    // 点击资料编号跳转页面
    cellClick(row, column) {
      if (column.property === "dataNo") {
        this.$router.push({
          // path: "/ems/equipment/account/detail",
          path: "/ems/repository/emsdevicedata/details",
          query: {
            id: row.id
          }
        });
      } else {
        return;
      }
    },

    // 相关附件
    goLook(row) {
      this.drawer = true;
      getObj(row.id).then(res => {
        this.drawerData = res.data.data;
        this.fileArray = res.data.data.fileArray;
        for (let i = 0; i <= this.fileArray.length; i++) {
          this.fileArray[i].fileSize = changeByte(this.fileArray[i].fileSize);
        }
      })
    },

    // 设备类别树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "设备资料类型") {
              common_table_info.push(treeDataList[index])
              // console.log(JSON.stringify(treeDataList[index]))
            }
          })
          this.treeData = common_table_info;
        }
      })
    },

    nodeClick(data) {
      // console.log(data)
      this.page.page = 1;
      this.getList(this.page, {dataCategoryId: data.id});
    },

    searchChangeU(param, done) {
      //console.log(this.$refs.crud)
      this.page.currentPage = 1
      this.getList(this.page, this.searchForm)
      //done()
    },
    resetBtn() {
      this.searchForm.dataName = '';
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      }).catch(() => {
        this.tableLoading = false;
      });
    },
    //编辑
    handleEdit() {
      var refsDate = this.$refs
      refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
    },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 更新
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      addObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.downBlobFile(
          "/ems/emsdevicedata/export",
          this.searchForm,
          "设备资料.xlsx"
      );
    },
    // 改变主题颜色
    changeThme() {
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor = this.theme;
    },

    submitForm(formName) {

      let data = JSON.parse(JSON.stringify(this.form));
      if (this.$refs.fileupload.fileList != null) {
        // alert(1111)
        data.fileIdArray = this.$refs.fileupload.fileList.map((item) =>
            item.id
        );
      } else {
        data.fileIdArray = [];
      }
      if (data.dataCategoryId.length > 0) {
        data.dataCategoryId = data.dataCategoryId.slice(-1)[0];
      }
      // console.log(">>>>>>>>>>>>>>>>>",JSON.stringify(data))
      // return;
      this.$refs[formName].validate((valid) => {
        if (valid) {

          if (data.id) {
            // if (data.dataCategoryIds.length > 0) {
            //   data.dataCategoryId = data.dataCategoryIds.slice(-1)[0];
            // }
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.form = res.data.data;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          } else {
            // data.dataCategoryId = data.dataCategoryIds.pop();
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功!")
              // this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          }
        }
      });
    },
    // 表单重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 清空数据
    reset() {
      this.form = {
        id: null,
        dataName: null,
        documentEncryptionLevel: null,
        dataCategoryId: null,
        dataCategoryIdCopy: null,
        dataCategoryName: null,
        fileIdArray: null,
        remark: null
      };
      this.imgArrayTem = [];
    },
    getSelect() {
      fetchListTree("").then((res) => {
        this.categoryList = res.data.data ? res.data.data : [];
      });
      getAllAccountList().then(res => {
        this.devices = res.data.data;
      });
      this.getList(this.page, this.searchForm)
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    // 在获得父设备类别之后查询父设备
    getFuListDate(id) {
      // console.log("=============>",id)  //有值
      if (id != "" || id != null) {
        getAccountId(id).then(res => {
          this.form.fsbOptions1 = res.data.data;
        })
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="scss">

.drawerStyle {
  ::v-deep .el-drawer__header {
    background-color: #f2f2f5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 20px;
  }
}

.execution {

  .crud {
    .form {
      float: top;
    }
  }

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }
}

.sbzl {
  margin-bottom: 10px;
  height: 120px;

  .sbtp {
    width: 147px;
    height: 100px;
    float: right;
    margin-right: 24px;
    margin-top: -10px;
  }

  .tbzl {
    display: flex;
    flex-direction: row;

    .anzhuo {
      left: 262px;
      top: 100px;
      width: 15px;
      height: 15px;
      color: rgba(89, 89, 89, 100);
      margin-right: 13px;
    }

    .zl {
      left: 295px;
      top: 95px;
      width: 72px;
      height: 27px;
      color: rgba(89, 89, 89, 100);
      font-size: 18px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
    }
  }

  .sm {
    left: 262px;
    top: 152px;
    width: 700px;
    height: 20px;
    color: rgba(134, 129, 129, 100);
    font-size: 12px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
    margin-top: 40px;
  }

}
</style>
