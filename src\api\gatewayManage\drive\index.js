import { requestPlatForm } from "@/utils/requestBase";



export function gatDriveList(query) {
    return requestPlatForm({
      url: '/drive/manage',
      method: 'get',
      params: query
    })
  } 
  export function deleteDrive(id) {
    return requestPlatForm({
      url: '/drive/manage',
      method: 'delete',
      data: id
    })
  }

  export function addDrive(data) {
    return requestPlatForm({
      url: '/drive/manage',
      method: 'post',
      data: data
    })
}
export function editDrive(data) {
  return requestPlatForm({
    url: '/drive/manage',
    method: 'put',
    data: data
  })
}



