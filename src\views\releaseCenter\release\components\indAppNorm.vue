<template>
    <div class="indAppNormPage">
        <el-form :disabled="detailFlag" ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="标准应用名称" prop="industrialName">
                <el-input v-model="form.industrialName" placeholder="请输入标准应用名称" style="width: 100%" />
            </el-form-item>


            <el-form-item label="应用ID" prop="appId">
                <el-input style="width: 85%;margin-right: 2px;" disabled v-model="form.appId" placeholder="生成应用ID" />
                <el-button @click="generateIdKey()" size="small" type="info">点击生成</el-button>
            </el-form-item>
            <el-form-item label="应用密钥" prop="appKey">
                <el-input disabled style="width: 85%;margin-right: 2px;" v-model="form.appKey" placeholder="生成应用密钥" />
                <el-button @click="generateAppKey()" size="small" type="info">点击生成</el-button>
            </el-form-item>
            <el-form-item label="简介" prop="profiles">
                <el-input v-model="form.profiles" placeholder="请输入简介" style="width: 100%" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="认证类型" prop="authType">
                <el-select style="width: 100%" v-model="form.authType" placeholder="请选择认证类型">
                    <el-option v-for="dict in dict.type.user_app_apply_auth_type" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="授权类型" prop="grantType">
                <el-select style="width: 100%" v-model="form.grantType" placeholder="请选择授权类型">
                    <el-option v-for="(item, index) in dict.type.app_user_apply_grant_type" :key="index" :value="item.value"
                        :label="item.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="退出登录地址" prop="loginOutUrl">
                <el-input style="width: 100%" v-model="form.loginOutUrl" placeholder="请输入退出登录地址" />
            </el-form-item>
            <el-form-item label="回调地址" prop="redirectUrl">
                <el-input style="width: 100%" v-model="form.redirectUrl" placeholder="请输入回调地址" />
            </el-form-item>
            <el-form-item label="是否是单租户" prop="isSingleTenant">
                <el-select style="width: 100%" v-model="form.isSingleTenant" placeholder="请选择授权类型">
                    <el-option value="YES" label="是" />
                    <el-option value="NO" label="否" />
                </el-select>
            </el-form-item>
            <el-form-item label="用户接口" prop="userInterface">
                <el-input v-for="(item, index) in userInterfaceList" :key="index" style="width: 100%;margin-bottom: 10px;"
                    :placeholder="'请输入' + item.label + '接口'" v-model="item.requestUrl">
                    <template slot="prepend">{{ item.label }}</template>
                    <el-tooltip slot="append" effect="dark" :content="item.content" placement="top-start">
                        <i style="font-size: 16px;cursor: pointer;" class="el-icon-question"></i>
                    </el-tooltip>
                </el-input>
            </el-form-item>


            <el-form-item label="应用案例" prop="appCases">
                <el-select v-model="form.appCases" multiple filterable remote reserve-keyword placeholder="请选择应用案例"
                    :remote-method="getAllCaseByNameFtn" :loading="apploading" style="width: 100%" clearable>
                    <el-option v-for="item in appList" :key="item.caseId" :label="item.caseName" :value="item.caseId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="使用企业规模" prop="enterpriseScale">
                <el-select v-model="form.enterpriseScale" placeholder="请选择企业规模" style="width: 100%" clearable>
                    <el-option v-for="dict in dict.type.enterprisescale" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="价格" prop="price">
                <el-select v-model="form.price" placeholder="请选择价格" style="width: 100%" clearable>
                    <el-option v-for="dict in dict.type.price" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="评级" prop="grade">
                <el-select v-model="form.grade" placeholder="请选择评级" style="width: 100%" clearable>
                    <el-option v-for="dict in dict.type.grade" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="应用亮点" prop="produceHights">
                <editor v-model="form.produceHights" :min-height="192" :readOnly="detailFlag" />
            </el-form-item>
            <el-form-item label="应用详情" prop="productDetails">
                <editor v-model="form.productDetails" :min-height="192" :readOnly="detailFlag" />
            </el-form-item>
            <el-form-item label="应用场景" prop="applicationScenarios">
                <editor v-model="form.applicationScenarios" :min-height="192" :readOnly="detailFlag" />
            </el-form-item>
            <el-form-item class="uploadItem" label="图片" prop="imgUrl">
                <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                    :http-request="uploadApiFtn" action="#" :show-file-list="false">
                    <img v-if="form.imgUrl" :src="ensureFullUrl(form.imgUrl)" class="appImage" />
                    <i v-else class="el-icon-plus avatar-uploader-icon appIcon"></i>
                    <div slot="tip" style="font-size: 12px; color: #9ea5b6">
                        支持扩展名：.jpg .img .png
                    </div>
                </el-upload>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { getAllCaseByName } from "@/api/release/indApp";
import { getInfo, uploadApi } from "@/api/release/index.js";

export default {
    name: "indAppNormPage",
    dicts: ["enterprisescale", "price", "grade", 'app_user_apply_grant_type', 'user_app_apply_auth_type'],
    props: {},
    data() {
        var validateInterface = (rule, value, callback) => {
            const isFlag = this.userInterfaceList.find(item => !item.requestUrl)
            if (isFlag) {
                callback(new Error('请填写完增删改接口'))
            } else {
                callback()
            }
        }
        var validateUrl = (rule, value, callback) => {
            const reg = /^https?:\/\//
            if (reg.test(value)) {
                callback()
            } else {
                callback(new Error())
            }
        }
        return {
            form: {
                appCases: [],
                produceHights: '',
                productDetails: '',
                applicationScenarios: '',
                isSingleTenant: undefined,
                authType: undefined,
                grantType: undefined
            },
            rules: {
                industrialName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                profiles: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                imgUrl: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                // appCases: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                produceHights: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                productDetails: [{ required: false, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                applicationScenarios: [{ required: true, message: "必填项不能为空", trigger: 'blur' }],
                userInterface: [
                    { validator: validateInterface, trigger: "blur" }
                ],
                appId: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                appKey: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                authType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                grantType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                loginOutUrl: [
                    { required: true, message: "必填项不能为空", trigger: "blur" },
                    { validator: validateUrl, trigger: "blur", message: "地址应以http://或https://开头" }
                ],
                redirectUrl: [
                    { required: true, message: "必填项不能为空", trigger: "blur" },
                    { validator: validateUrl, trigger: "blur", message: "地址应以http://或https://开头" }
                ],
                isSingleTenant: [{ required: true, message: "必填项不能为空", trigger: "change" }],
            },
            detailFlag:
                this.$route.query.pageType == "detail" ||
                this.$route.query.pageType == "check" ||
                false,
            appList: [],
            apploading: false,
            userInterfaceList: [
                { interfaceType: 'add', label: '新增', requestUrl: '', content: '平台侧新建用户时，选择了相对应的应用后，保存时会向应用侧调用新增接口（接口请求方式为POST，表单参数为accountNum（用户名）和tenantId（租户id））' },
                { interfaceType: 'delete', label: '删除', requestUrl: '', content: '平台侧删除用户时，会向用户对应的应用去调用删除接口（接口请求方式为DELETE，示例：http://127.0.0.1/delete/{accountNum}/{tenantId}，accountNum为用户名，tenantId为租户id' }
            ],
        };
    },

    created() {
        this.getAllCaseByNameFtn(); //应用案例
        const { flowInstanceId } = this.$route.query;
        flowInstanceId && this.getFormDataFtn(flowInstanceId)
    },

    methods: {
        generateRandomString(length) {
            var result = '';
            var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            var charactersLength = characters.length;
            for (var i = 0; i < length; i++) {
                result += characters.charAt(Math.floor(Math.random() * charactersLength));
            }
            return result;
        },
        generateAppKey() {
            this.$set(this.form, 'appKey', this.generateRandomString(12))
        },
        generateIdKey() {
            this.$set(this.form, 'appId', this.generateRandomString(12))
        },
        editorChangeFn() {
            this.$refs.form.validateField('applicationScenarios')
        },

        getFormDataFtn(flowInstanceId) {
            getInfo({ flowInstanceId }).then(res => {
                const { params } = res.data;
                this.form = {
                    ...params,
                    appCases: params.appCases && params.appCases.split(',').map(v => v - 0),
                    isSingleTenant: params.isSingleTenant || undefined,
                    authType: params.authType || undefined,
                    grantType: params.grantType || undefined,
                }
                const userInterface = params.userInterface ? JSON.parse(params.userInterface) : []
                this.userInterfaceList.forEach(item => {
                    let requestUrl = "";
                    userInterface.forEach(ii => {
                        if (ii.interfaceType === item.interfaceType) {
                            requestUrl = ii.requestUrl;
                        }
                    })
                    item.requestUrl = requestUrl;
                })
            })
        },

        getAllCaseByNameFtn(val = "") {
            this.apploading = true;
            const params = { caseName: val || undefined };
            getAllCaseByName(params)
                .then((res) => {
                    this.appList = res.data;
                })
                .finally(() => (this.apploading = false));
        },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (["jpg", "img", "png"].indexOf(fileType) == -1) {
                this.$message.error("请上传后缀为.jpg .img .png格式的图片文件");
                return false;
            }
        },

        uploadApiFtn(event) {
            let fileData = new FormData();
            fileData.append("file", event.file);
            uploadApi(fileData).then((res) => {
                this.form = { ...this.form, imgUrl: res.data.url };
                this.$refs.form.clearValidate("imgUrl");
            });
        },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (["jpg", "img", "png"].indexOf(fileType) == -1) {
                this.$message.error("请上传后缀为.jpg .img .png格式的图片文件");
                return false;
            }
        },

        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    const userInterface = this.userInterfaceList.map(item => {
                        return {
                            interfaceType: item.interfaceType,
                            requestUrl: item.requestUrl
                        }
                    })
                    const params = {
                        ...this.form,
                        appCases: this.form.appCases.join(','),
                        industrialType: 2,
                        userInterface: JSON.stringify(userInterface)
                    }
                    this.$emit("submitFtn", params, (res) => {
                        // 相应结束后的其他逻辑
                    });
                }
            });
        },
    },
};
</script>

<style lang="scss">
.indAppNormPage {
    width: 55%;

    .uploadItem {
        .el-form-item__content {
            line-height: normal;
        }
    }

    .dynaCard {
        /* width: 100%; */
        background: #f6f8fc;
        border-radius: 5px;
        padding: 12px 24px;
        margin-bottom: 20px;

        .dynaCardHead {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            margin-top: 12px;
            margin-left: 22px;

            .hintTitleSamil {
                font-weight: bold;
                font-size: 15px;
                color: #0d162a;

                &:before {
                    content: "";
                    display: inline-block;
                    width: 4px;
                    height: 10px;
                    background: #6fc342;
                    border-radius: 0px;
                    margin-right: 6px;
                }
            }
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        text-align: center;
    }

    .avatar-uploader {
        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
    }

    .appIcon {
        width: 160px;
        height: 160px;
        line-height: 160px;
    }

    .appImage {
        width: 160px;
        height: 160px;
    }
}
</style>
