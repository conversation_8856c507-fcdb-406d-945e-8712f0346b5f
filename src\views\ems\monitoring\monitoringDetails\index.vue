<template>
  <div class="mainAnalysis">
    <div class="left_card">
      <el-card class="box-card">
        <IconTitle class="selfTitle" title="数据采集/设备列表" imgUrl="yunwei"/>
        <div class="tabs">
          <el-tabs v-model="activeName" @tab-click="handleClick" stretch>
            <el-tab-pane label="类别" name="first">
              <div class="tree">
                <el-tree
                        style="width: 130px;"
                        accordion
                        :data="cat.data"
                        :props="cat.defaultProps"
                        :default-expanded-keys="catExpandDefault"
                        @node-click="catHandleNodeClick">
                </el-tree>
                <!-- 分割线 -->
                <el-divider style="margin-left: 20px" direction="vertical"/>
                <div class="right_data">
                  <div class="data_top">
                    <span style="color: #E29836">温馨提示：</span>
                    <div>
                      <div style="padding-top: 10px; display: flex">
                        <div>
                          <img src="@/assets/svg/yxtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">运行中</span>

                        <div>
                          <img src="@/assets/svg/gjtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">关机</span>

                        <div>
                          <img :src="require('@/assets/imagesAssets/jgtp.png')"
                               style="margin: 1px 5px 0 0; color: #8c939d; width: 18px">
                        </div>
                        <span>告警</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="data_body">
                      <div>
                        <!-- 遍历出来的数据 -->
                        <div @click="catClick(item.id)" v-for="(item,index) in listByCat" :key="index" style="display: flex;margin: 10px 0 15px 25px">
                          <div>
                            <img src="@/assets/svg/yxtb.svg" style="margin-right: 5px;">
                          </div>
                          <div  v-bind:class="{first:catCurrentIndex===item.id}">{{item.deviceName}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </el-tab-pane>
            <el-tab-pane label="位置" name="second">
              <!--位置-->
              <div class="tree">
                <el-tree
                        style="width: 130px;"
                        accordion
                        :data="posi.data"
                        :props="posi.defaultProps"
                        :default-expanded-keys="posiExpandDefault"
                        @node-click="handleNodeClick">
                </el-tree>
                <!-- 分割线 -->
                <el-divider style="margin-left: 20px" direction="vertical"/>
                <div class="right_data">
                  <div class="data_top">
                    <span style="color: #E29836">温馨提示：</span>
                    <div>
                      <div style="padding-top: 10px; display: flex">
                        <div>
                          <img src="@/assets/svg/yxtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">运行中</span>

                        <div>
                          <img src="@/assets/svg/gjtb.svg" style="margin-right: 5px;">
                        </div>
                        <span style="margin-right: 10px">关机</span>

                        <div>
                          <img :src="require('@/assets/imagesAssets/jgtp.png')"
                               style="margin: 1px 5px 0 0; color: #8c939d; width: 18px">
                        </div>
                        <span>告警</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="data_body">
                      <div>
                        <!-- 遍历出来的数据 -->
                        <div v-for="(item,index) in listByPosi" :key="index" style="display: flex;margin: 10px 0 15px 25px">
                          <div>
                            <img src="@/assets/svg/yxtb.svg" style="margin-right: 5px;">
                          </div>
                          <div v-bind:class="{first:catCurrentIndex===item.id}">{{item.deviceName}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <div class="right_card">
      <div id="box-card">
        <div class="gk">
          <div class="tp">
            <img src="@/assets/svg/gktb.svg" alt="">
          </div>
          <span style="font-weight: 700; text-align: center; margin-top: 5px">概况</span>
        </div>
        <div v-if="tabName == 'collect'">
          <div class="details_top">
            <div class="details_one">
              <img class="details_img" :src="require('@/assets/img/logo1.png')">
              <div style="display: flex; flex-direction: column">
                <div class="div_text">
                  <img class="yuan" :src="require('@/assets/imagesAssets/lvYuan.png')">
                  <div>
                    <span style="font-weight: 600">{{device.deviceName}}</span>
                    <div style="display: flex; margin-top: 15px">
                      <div class="A" style="margin: 0px 15px 0 -15px;">{{device.deviceLevel}}</div>
                      <span class="bh">{{device.deviceNum}}</span>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>

        <div v-else>
          <div class="details_top">
            <div class="details_one">
              <img class="details_img" :src="require('@/assets/img/logo1.png')">
              <div style="display: flex; flex-direction: column">
                <div class="div_text">
                  <img class="yuan" :src="require('@/assets/imagesAssets/lvYuan.png')">
                  <span style="font-weight: 600">{{device.deviceName}}</span>
                  <div class="A">{{device.deviceLevel}}</div>
                  <span class="bh">{{device.deviceNum}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tab">
          <div class="tab_model">
            <span>模型点：</span>
            <img class="model_img" :src="require('@/assets/imagesAssets/wenHao.png')">
            <span style="color: #F1AD4E; margin-left: 15px">温馨提示：数据采集通过边缘网关对设备进行数据采集</span>
          </div>
          <IconTitle class="selfTitle" title="【实时数据】" imgUrl="yunwei">
            <span style="font-size: 14px;font-weight: 600">数据采集</span>
          </IconTitle>
          <div class="table_data">
            <div class="btn-box">
              <el-button
                  type="info"
                  icon="el-icon-refresh-left"
                  @click="refreshChange()"
              ></el-button>
            </div>
            <div>
              <el-table
                  v-loading="loading"
                  border
                  highlight-current-row
                  :data="monitoringList"
                  :header-cell-style="{background: '#f4f7fc'}"
                  @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" min-width="2%" align="center"/>
                <el-table-column
                    type="index"
                    label="编号"
                    width="50">
                </el-table-column>
                <el-table-column
                    label="属性名称"
                    :show-overflow-tooltip="true"
                    align="center"
                    prop="proName"
                    min-width="7%"
                />
                <el-table-column
                    label="属性KEY"
                    align="center"
                    prop="proKey"
                    min-width="9%"
                    :show-overflow-tooltip="true"
                />
                <el-table-column
                    label="数据类型"
                    align="center"
                    prop="dataType"
                    min-width="9%"
                />
                <el-table-column
                    label="单位"
                    align="center"
                    prop="faultTypeName"
                    min-width="9%"
                />
                <el-table-column
                    label="实时数据"
                    align="center"
                    :show-overflow-tooltip="true"
                    prop="data"
                    min-width="9%"
                />
                <el-table-column
                    label="是否监控"
                    align="center"
                    :show-overflow-tooltip="true"
                    prop="solveWay"
                    min-width="9%"
                />
                <el-table-column
                    label="监控指标"
                    align="center"
                    :show-overflow-tooltip="true"
                    prop="solveWay"
                    min-width="9%"
                />
              </el-table>
              <pagination
                  v-show="total > 0"
                  :total="total"
                  :page.sync="queryParams.pageNum"
                  :limit.sync="queryParams.pageSize"
                  @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import IconTitle from "@/components/ems/icon-title/index.vue";
  import elapsedTime from "./echarts/elapsedTime";
  import monitoring_24Hour from "./echarts/monitoring_24Hour";
  import status from './echarts/status'
  import {fetchList as positionTree} from "@/api/ems/equipment/position";
  import {getListByPosition,getListByCat,getAllDynamicPro,getDataPreview,getObj} from "@/api/ems/equipment/account";
  import {fetchList as catTree} from "@/api/ems/equipment/category";
  import Pagination from "@/components/Pagination/index.vue"

  export default {
    name: "index",
    components: {
      IconTitle,
      elapsedTime,
      monitoring_24Hour,
      status,
      Pagination
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        monitoringList: [],
        total: 0,
        queryParams: {
          pageNum: 1,
          pageSize: 10
        },
        insertOpen: false,
        insertData: {
          id: ''
        },
        rules: {},
        deviceData: [],  // 进度条数据
        tabName: 'realTime',

        percentage: 72,
        customColor: '#42a7ff',


        activeName: 'first',
        //位置的树形数据
        posi:{
          data:[],
          defaultProps: {
            children: 'children',
            label: 'name'
          }
        },
        posiExpandDefault:[],
        posiCurrentIndex:undefined,
        listByPosi:[],
        //分类的树形数据
        cat:{
          data:[],
          defaultProps: {
            children: 'children',
            label: 'name'
          }
        },
        catExpandDefault:[],
        catCurrentIndex:undefined,
        listByCat:[],
        device:{

        }
      };
    },
    mounted() {
      this.getPostionTree()
      this.getCatTree()
      /* this.getAllDynamic("oil_filter1")*/
    },
    watch: {
      catExpandDefault(newVal, oldVal) {
        if (newVal) {
          this.$nextTick(() => {
            document.querySelector('.el-tree-node__content').click();
          });
        }
      }
    },
    methods: {
      // 设置 ‘ 采集模型 ’ tab页的高度
      handleClick(tab, event) {
        if (this.tabName == 'collect') {
          document.getElementById('box-card').style.height = '1200px';
        } else {
          document.getElementById('box-card').style.height = '1000px';
        }
      },
      //位置节点点击事件
      handleNodeClick(data) {
        getListByPosition({"storageLocationId":data.id}).then((res)=>{
          this.listByPosi = res.data.data
        })
      },
      //分类节点点击事件
      catHandleNodeClick(data) {
        getListByCat({"categoryId":data.id}).then((res)=>{
          this.listByCat = res.data.data

          if(this.catCurrentIndex == undefined){
            this.catCurrentIndex = this.listByCat[0].id
            this.getDevInfo(this.listByCat[0].id)
          }
        })
      },
      //获取设备动态属性以及实时数据
      getAllDynamic(code){
        getAllDynamicPro({"deviceNum":code}).then((response)=>{
          if(response.data.code == 200){
            var list = response.data.data
            //获取设备实时数据
            getDataPreview({"deviceNum":code}).then((res)=>{
              if(res.data.code == 200){
                if(res.data.data != '暂无数据'){
                  var obj = res.data.data.data
                  list.forEach(m => {
                    var name = m.proKey;
                    m['data'] = obj[name]
                  })
                }
                this.monitoringList = list
              }
            })
          }
        })
      },
      getDevInfo(id){
        getObj(id)
                .then((response) => {
                  this.device = response.data.data
                  this.getAllDynamic(this.device.deviceNum)
                })

      },
      //获取位置的tree数据
      getPostionTree(){
        positionTree()
                .then((response) => {
                  this.posi.data = response.data.data;
                  this.posiExpandDefault.push(this.posi.data[0].id);
                })
      },
      //获取分类的tree数据
      getCatTree(){
        catTree()
                .then((response) => {
                  this.cat.data = response.data.data;
                  this.catExpandDefault.push(this.cat.data[0].id);
                })
      },
      catClick(id){
        this.catCurrentIndex = id
        this.getDevInfo(id)
      },
      /**
       * ***************************** 实时数据 *****************************
       */

      // 刷新事件
      refreshChange() {
        this.getList(this.page);
      },

      // 多选框选中
      handleSelectionChange(val) {
        this.multipleSelection = val;
        //把选中数据的id提取到一个新的数组newArray中
        this.newArray = this.multipleSelection.map((item) => {
          return item.id
        })
      },

      // 新增和修改对话框关闭之后，重置表单
      insertClosed() {
        this.reset();
      },

      // 表单重置
      reset() {
        this.insertData = {}
      },

      // 查询数据
      getList() {

      },

      // 新增数据
      toAdd() {
        this.reset();
        this.insertOpen = true;
      },

      // 修改数据
      handleEdit() {
        this.reset();
        this.insertOpen = true;
      },

      // 提交表单
      submitForm(insertData) {
        this.$refs[insertData].validate((valid) => {
          if (valid) {
            this.insertData.id = this.seleteId;
            if (this.insertData.id > 0) {
              // 修改
              update(this.insertData).then(res => {
                let {code, msg} = res;
                if (code == 200) {
                  this.$message.success(msg);
                  this.insertOpen = false;
                  this.getList();
                }
              })
            } else {
              // 新增
              insert(this.insertData).then(res => {
                let {code, msg} = res;
                if (code == 200) {
                  this.$message.success(msg);
                  this.insertOpen = false;
                  this.getList();
                }
              })
            }

          } else {
            this.$message.error("你输入的信息不完整，不允许提交！")
            return false;
          }
        });
      },

      // 删除数据
      async handleDel(id) {
        const confirmResult = await this.$confirm('请问是否要永久删除该数据', '删除提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(err => err)
        //如果用户点击确认，则confirmResult 为'confirm'
        //如果用户点击取消, 则confirmResult获取的就是catch的错误消息'cancel'
        if (confirmResult != "confirm") {
          return this.$message.info("已经取消删除")
        }
        //发送请求根据id完成删除操作
        deleteInfo(id).then(res => {
          let {code, msg} = res;
          if (code != 200) {
            //判断如果删除失败，就做提示
            this.$message.error(msg);
          }
          //删除成功的提示
          this.$message.success(msg);
          //重新请求最新的数据
          this.getList();
        })
      },

      /**
       * ***************************** 状态分析 *****************************
       */

      format(percentage) {
        return `${percentage}`;
      },

      // 状态分解
      getState() {
        getTime().then(res => {
          if (res.code === 200) {
            var objStr = JSON.stringify(res.data)
            if (objStr === '{}') {
              this.allData = {
                加工: {},
                待机: {},
                关机: {}
              }
            } else {
              // 状态分解
              this.allData = res.data.proportion
            }
            // 当天24小时监控
            const details = res.data.details
            const details2 = details.slice(0, 40)
            const newdeta = []
            details2.filter(item => {
              let duration = Math.round(Math.random() * 1000000)
              // 开始时间
              const start_time = new Date(item.start_time)
              item.start_time = Date.parse(start_time)
              // 结束时间
              const end_time = new Date(item.end_time)
              item.end_time = Date.parse(end_time)
              const staObj = {}
              if (item.status === '关机') {
                staObj.itemStyle = {normal: {color: '#e51c23'}}
                staObj.name = '关机'
                staObj.value = [0, item.start_time, item.end_time, duration]
              } else if (item.status === '加工') {
                staObj.itemStyle = {normal: {color: '#4bc34b'}}
                staObj.name = '加工'
                staObj.value = [0, item.start_time, item.end_time, duration]
              } else if (item.status === '待机') {
                staObj.itemStyle = {normal: {color: '#ff9800'}}
                staObj.name = '待机'
                staObj.value = [0, item.start_time, item.end_time, duration]
              }
              newdeta.push(staObj)
            })
            const twenty_four = echarts.init(document.getElementById('twenty_four'))
            statisticdata(twenty_four, newdeta, newdeta[0].value[2])
            // 获取状态统计
            const statistical = res.data.statistical
            delete statistical.on_time
            this.accounting = statistical
            const getStatistics = {}
            let allTime = 0
            for (const key in statistical) {
              allTime += HoursSec(statistical[key])
              getStatistics[key] = HoursSec(statistical[key]) // foo, bar
            }
            for (const key in getStatistics) {
              getStatistics[key] = Number(((getStatistics[key] / allTime) * 100).toFixed(1))// foo, bar
            }
            this.getStatistics = getStatistics
            const status_echarts = echarts.init(document.getElementById('status_echarts'))
            statusEcharts(status_echarts, getStatistics)
          }
        }).catch(error => {
          console.log(error)
        })
      },

      /**
       * ***************************** 采集模型 *****************************
       */

      formatCollect(percentage) {
        return '';
      }

    }

  }
</script>

<style scoped lang="less">
  .first{
    color: #26AE61;
  }

  .mainAnalysis {
    font-size: 12px;
    display: flex;
    height: 100%;

    .right_card {
      width: 900px;
      height: 1000px;
      margin-left: 15px;

      #box-card {
        height: 1000px;
        border-radius: 10px;
        background-color: #ffffff;

        .gk {
          display: flex;
          padding: 10px 0 0 10px;

          .tp {
            background-color: #009DFF;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            padding: 3px 0 0 3px;
            margin-right: 5px;
          }
        }

        .details_top {
          margin: 25px 20px;
          display: flex;

          .details_one {
            display: flex;

            .details_img {
              width: 50px;
              height: 50px;
            }

            .div_text {
              margin-left: 20px;
              display: flex;

              .yuan {
                width: 12px;
                height: 12px;
                margin: 2px 5px 0 0;
              }

              .A {
                margin: 0 15px 0 30px;
                border-bottom: 2px solid #000c17;
                width: 10px;
                height: 15px;
                text-align: center;
                font-size: 13px;
                font-weight: 600;
              }

              .bh {
                font-size: 10px;
                font-weight: 600;
              }
            }

            .tag-box {
              margin-left: 20px;

              .el-tag {
                margin: 10px 15px 0 0;
              }
            }
          }
        }

        .tab {
          margin: 0 10px;

          .right_status{
            .div-card {
              padding: 10px 10px 0 10px;
              margin: 15px;
              height: 220px;
              box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
              border-radius: 10px;

              .book_status{
                margin: -35px 0 0 -50px
              }

              .run_status{
                margin: -35px 0 0 -10px;
              }
            }
          }

          .tab_model {
            margin: 20px 0;

            .model_img {
              width: 10px;
              height: 10px;
            }
          }

          .table_data {
            margin-top: 20px;

            .btn-box {
              margin-bottom: 20px;
            }
          }

          .caozuo {
            display: flex;

            .caoZuoTop {
              width: 160px;
              height: 80px;
              border: 1px solid #ECF0F4;
              margin: 20px 0px 0 15px;

              .divImg {
                display: flex;
                margin-bottom: 15px;

                .caoZuoImg {
                  width: 20px;
                  height: 20px;
                  font-weight: 700;
                  margin: 5px 5px 0 15px;
                }

                span {
                  color: #26AE61;
                  font-weight: 600;
                  margin: 5px 0px 0 10px;
                }
              }

              span {
                color: #B6B6B6;
                font-size: 10px;
                margin-left: 15px;
              }
            }

          }

          .tableData {
            .table {
              width: 700px;
              height: 200px;

              th {
                color: #B6B6B6;
              }

              td {
                color: #101010;

                div {
                  width: 80px;
                  height: 20px;
                  background-color: #00C0DE;
                  color: #fff;
                  text-align: center;
                }
              }
            }
          }

          .model_div {
            display: flex;
            margin: 15px 0 0 15px;
            .model_table{
              margin-top: -13px;
              table{
                th{
                  color: #B6B6B6;
                }
                td{
                  color: #101010;
                }
              }
            }
          }

        }

      }
    }
  }

  .el-tabs__nav-wrap::after {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #E4E7ED;
    z-index: 1;
  }

  .el-tabs__item {
    font-size: 12px;
  }

  .el-divider--vertical {
    display: inline-block;
    width: 1px;
    height: 71em;
    margin: 0 8px;
    vertical-align: middle;
    position: relative;
  }

  // 改变el-progress的背景颜色
  //.el-progress-bar__outer {
  //  height: 6px;
  //  border-radius: 100px;
  //  background-color: #ffb413;
  //  overflow: hidden;
  //  position: relative;
  //  vertical-align: middle;
  //}

</style>
