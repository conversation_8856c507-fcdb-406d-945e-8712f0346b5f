<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <div v-if="listFlag" class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     icon="el-icon-circle-plus-outline"
                     v-if="true"
                     @click="toAdd()"
          >新增
          </el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="true"
              @click.native="handleDel()"
              :disabled="single"
          >删除
          </el-button
          >
          <el-button
              type="check"
              icon="el-icon-download"
              @click="exportExcel"
          >导出
          </el-button
          >
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
<!--          <i class="el-icon-goods"></i>-->
          <i class="el-icon-setting" @click="columnShow"></i>
<!--          <i class="icon-zuixiaohua"/>-->
        </div>
      </div>

        </el-card>
        <basic-container>
            <avue-crud
                    ref="crud"
                    :page.sync="page"
                    :data="tableData"
                    :permission="permissionList"
                    :table-loading="tableLoading"
                    :option="tableOption"
                    @selection-change="selectionChange"
                    @on-load="getList"
                    :cell-style="cellStyle"
                    @row-click="rowClick"
                    @cell-click="cellClick"
                    @search-change="searchChange"
                    @refresh-change="refreshChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    @row-update="handleUpdate"
                    @row-save="handleSave"
                    @row-del="handleDel"
            >
                <template slot="header">
                    <IconTitle class="selfTitle" title="保养计划" imgUrl="yunwei"/>
                </template>
                <template slot-scope="scope" slot="menu">
                    <el-button type="text" v-if="scope.row.enable==0" @click="handleEdit(scope.row)">
                        <i class="el-icon-edit"></i>编辑
                    </el-button
                    >
                    <el-button type="text" v-if="scope.row.status==0&&scope.row.auditUserId==userId" @click="handleShEdit(scope.row)">
                        <i class="el-icon-edit"></i>审核
                    </el-button
                    >
                    <el-button type="text" @click="toDetail(scope.row)">
                        <i class="el-icon-view"></i>查看
                    </el-button
                    >
                </template>
            </avue-crud>
            <!-- 用于添加设备的 -->
            <el-dialog :title="check.title" :visible.sync="check.open" width="600px" append-to-body>
                <el-form :model="checkForm" :rules="checkFormRule" ref="checkForm"
                         label-width="110px">
                    <el-form-item label="审核结果" prop="status">
                        <el-radio-group v-model="checkForm.status" @change="changeDiaType()">
                            <el-radio :label="1">通过</el-radio>
                            <el-radio :label="2">驳回</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="驳回原因" v-if="checkForm.status==2" prop="checkRemark" style="width: 100%;">
                        <el-input type="textarea" v-model="checkForm.checkRemark" placeholder="请输入驳回原因"/>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                      <el-button @click="checkBack">取消</el-button>
                      <el-button type="primary" @click="checkFormSubmit('checkForm')">确定</el-button>
                    </span>
      </el-dialog>
    </basic-container>
    <el-drawer
        class="drawerStyle"
        title="计划信息"
        :show-close="false"
        :visible.sync="detail"
        direction="rtl"
        size="50%"
        append-to-body
    >
      <div>
        <img style="float: right;margin-right: 180px" :src="require('@/assets/imagesAssets/xgfj.png')">
        <div>
          <span class="labelS">计划名称：</span>
          <span class="contentS">{{ rowCheck.planName }}</span>
        </div>
        <div>
          <span class="labelS">计划编号：</span>
          <span class="contentS">{{ rowCheck.planNum }}</span>
        </div>
        <div>
          <span class="labelS">负责人：</span>
          <span class="contentS">{{ rowCheck.liableUserName }}</span>
        </div>
      </div>
      <div class="statusList">
        <span class="labelS">描述信息</span>
        <span v-if="tableNewTask" class="detailInfo">任务状态</span>
        <span v-if="tableNewTask" class="dottedStyle" :style="taskStatusStyle"></span>
        <span class="statusContent">{{ rowCheck.taskObject.statusName }}</span>
      </div>
      <div class="line"></div>
      <div>
        <div class="icon-style">
          <div class="con-right">
            <div class="my-steps-box">
              <div v-for="(item, index) in menulist" :key="index">
                <div
                    :class="['step-item', activeMenu == index ? 'step-active' : '']"
                >
                  <div class="top-title">
                    <span class="text" @click="boxClick(index)">{{ item.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="con-left" id="scrollBox" v-if="rowCheck.boxIndexOne">
            <el-card shadow="always" class="box-card">
              <div class="tableTitle">审核信息</div>
              <div>
                <span class="labelS">任务状态：</span>
                <span class="contentS">待执行</span>
              </div>
              <div>
                <span class="labelS">执行时间：</span>
                <span class="contentS">无</span>
              </div>
              <div>
                <span class="labelS">异常项个数：</span>
                <span class="contentS">0</span>
              </div>
              <div>
                <span class="labelS">漏保项个数：</span>
                <span class="contentS">0</span>
              </div>
            </el-card>
          </div>
          <div class="con-left" v-else-if="rowCheck.boxIndexTwo">
            <el-card shadow="always" class="box-card">
              <div class="tableTitle">计划详情</div>
              <div>
                <span class="labelS">计划编号：</span>
                <span class="contentS">{{ rowCheck.planNum }}</span>
              </div>
              <div>
                <span class="labelS">计划名称：</span>
                <span class="contentS">{{ rowCheck.planName }}</span>
              </div>
              <div>
                <span class="labelS">负责人：</span>
                <span class="contentS">{{ rowCheck.liableUserName }}</span>
              </div>
              <div>
                <span class="labelS">执行时间：</span>
                <span class="contentS">{{ rowCheck.beginTime }} ~ {{ rowCheck.endTime }}</span>
              </div>
              <div>
                <span class="labelS">保养周期：</span>
                <span class="contentS">{{ rowCheck.inspectCycle }}</span>
              </div>
              <div>
                <span class="labelS">创建人：</span>
                <span class="contentS">{{ rowCheck.createBy }}</span>
              </div>
              <div>
                <span class="labelS">创建时间：</span>
                <span class="contentS">{{ rowCheck.createTime }}</span>
              </div>
            </el-card>
            <el-card shadow="always" v-if="tableNewTask" class="box-card">
              <div class="tableTitle">最新任务</div>
              <div>
                <span class="labelS">任务状态：</span>
                <span class="contentS">{{ rowCheck.taskObject.statusName }}</span>
              </div>
              <div>
                <span class="labelS">执行时间：</span>
                <span class="contentS">{{ rowCheck.taskObject.beginTime }} ~ {{ rowCheck.taskObject.endTime }}</span>
              </div>
              <div>
                <span class="labelS">异常项个数：</span>
                <span class="contentS">{{ rowCheck.taskObject.abnormalNum }}</span>
              </div>
              <div>
                <span class="labelS">漏保项个数：</span>
                <span class="contentS">{{ rowCheck.taskObject.leakDetectionNum }}</span>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
  <div v-else-if="addllistFlag">
    <IndexAdd :id='addEditId'/>
  </div>
  <div v-else-if="detaillistFlag">
    <IndexDetail :id='detailId'/>
  </div>
</template>
<script>
    import {
        planGetObj,
        planAddObj,
        planPutObj,
        planFetchList,
        planDelObj,
        updateCheckById,
    } from '@/api/ems/maintenance/emsmaimaintenanceplan'
    // taskObjectPlanId
    import {tableOption} from '@/const/crud/ems/maintenance/emsmaimaintenanceplan'
    import {mapGetters} from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/icon-title/index.vue";
    import IndexAdd from "./indexAdd.vue";
    import IndexDetail from "./detail.vue";
    import {getUserId} from '@/util/auth'

    export default {
        name: 'plan',
        components: {
            IconTitle,
            IndexAdd,
            IndexDetail,
        },
        data() {
            return {
                userId:null,
                tableData: [],
                searchForm: {}, // 查询参数
                single: true,  // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableNewTask: false,

      check: {
        title: "",
        open: false,
      },
      checkForm: {
        id: null,
        status: 1,
        checkRemark: null,
        enable: null,
      },
      checkFormRule: {
        status: [
          {required: true, message: '审核结果不能为空', trigger: 'blur'}
        ],
        checkRemark: [
          {required: false, message: '驳回原因不能为空', trigger: 'blur'}
        ],
      },


      listFlag: true,
      addllistFlag: false,
      detaillistFlag: false,

      detailId: 0,
      addEditId: 0,

      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      detail: false,
      activeMenu: 0,
          // {
          //   label: '审批信息'
          // },
      menulist: [

        {
          label: '计划详情'
        },
      ],


      taskStatusStyle: "",
      //以下为抽屉参数
      rowCheck: {
        planName: null,
        planNum: null,
        liableUserId: null,
        beginTime: null,
        endTime: null,
        inspectCycle: null,
        createBy: null,
        createTime: null,


        loading: false,
        taskObject: {
          status: null,
          statusName: null,
          beginTime: null,
          endTime: null,
          abnormalNum: 0,
          leakDetectionNum: 0,

        },

                    rowId: null,
                    boxIndexOne: false,
                    boxIndexTwo: true,
                    queryParams: {
                        pageNum: 1,
                        pageSize: 10,
                        total: 0,
                    },
                }
            };
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinsinspectplan_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinsinspectplan_del, false),
                    editBtn: this.vaildData(this.permissions.ems_emsinsinspectplan_edit, false),
                };
            },
        },
        mounted() {
            this.initElement();
            this.userId =getUserId()
        },
        methods: {
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList = list
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },
            toAdd() {
                this.addEditId = 0
                this.listFlag = false;
                this.addllistFlag = true;
            },
            toDetail(row) {
                this.detailId = row.id
                this.listFlag = false;
                this.detaillistFlag = true;
            },
            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
            },
            boxClick(key) {
                // alert(key)
                this.activeMenu = key;
                if (key == 0) {
                    this.rowCheck.boxIndexOne = true;
                    this.rowCheck.boxIndexTwo = false;
                } else {

        this.rowCheck.boxIndexOne = false;
        this.rowCheck.boxIndexTwo = true;
        this.getboxIndexTwo(this.rowCheck.rowId);
      }

    },
    cellStyle(data) {
      if (data.columnIndex == 2) {
        return "color:#02b980;cursor:pointer";
      }
    },
    cellClick(row, column) {
      if (column.property === "planNum") {
        this.toDetail(row);
      } else {
        return;
      }
    },
    rowClick(row, column) {
      if (column.property === "planNum" || column.property == undefined) {
        return;
      } else {
        this.detail = true;
        this.rowCheck.planName = row.planName;
        this.rowCheck.planNum = row.planNum;
        this.rowCheck.liableUserName = row.liableUserName;
        this.rowCheck.inspectCycle = row.inspectCycle;
        this.rowCheck.beginTime = row.beginTime;
        this.rowCheck.endTime = row.endTime;
        this.rowCheck.createBy = row.createBy;
        this.rowCheck.createTime = row.createTime;
        this.rowCheck.standardNum = row.standardNum
        this.rowCheck.standardName = row.standardName
        this.rowCheck.deptName = row.deptName
        this.rowCheck.rowId = row.id
        this.getboxIndexTwo(row.id);

      }
    },
    checkBack() {
      this.check.open = false;
    },
    changeDiaType() {
      if (this.checkForm.status == 2) {
        this.checkFormRule.checkRemark[0].required = true;
      } else {
        this.checkFormRule.checkRemark[0].required = false;
      }

    },


    checkFormSubmit(formName) {
      let data = JSON.parse(JSON.stringify(this.checkForm));
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.canSubmit = false
          updateCheckById(this.checkForm).then(data => {
            this.$notify.success('审核成功')
            this.visible = false
            this.check.open = false;
            this.refreshChange()
          }).catch(() => {
            this.canSubmit = true;
            this.check.open = false;
          });
        }
      })
    },

    getboxIndexTwo(id) {
      this.rowCheck.loading = true;
      taskObjectPlanId(id).then(response => {
        if (response.data != null) {
          this.tableNewTask = true;
          var taskStatus = ["未开始", "执行中", "待验收", "已完成", "已过期"]
          var taskStatusColor = [
            "#002aff",
            "#00f5e2",
            "#F7CA60",
            "#40f500",
            "#ff0000"]
          this.rowCheck.taskObject.statusName = taskStatus[Number(response.data.status)]
          this.taskStatusStyle = "background-color:" + taskStatusColor[Number(response.data.status)]
          this.rowCheck.taskObject.beginTime = response.data.planBeginTime;
          this.rowCheck.taskObject.endTime = response.data.planEndTime;
          this.rowCheck.taskObject.abnormalNum = response.data.abnormalNum == null ? 0 : response.data.abnormalNum;
          this.rowCheck.taskObject.leakDetectionNum = response.data.leakDetectionNum == null ? 0 : response.data.leakDetectionNum;
        } else {
          this.tableNewTask = false;
          this.rowCheck.taskObject.statusName = "暂未生成任务"
        }

        this.rowCheck.loading = false;
      });
    },
    // getboxIndexTwo(id){
    //
    // },


    // 列表查询
    getList(page) {
      this.tableLoading = true;
      planFetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, this.searchForm)).then((response) => {
        this.tableData = response.data.records;
        for (var i = 0; i < this.tableData.length; i++) {
          this.tableData[i].inspectCycle = this.tableData[i].inspectCycle + "日"
          this.tableData[i].effectiveTime = this.tableData[i].effectiveTime + "日"
          this.tableData[i].generateTime = this.tableData[i].generateTime + "日"
          this.tableData[i].noticeTime = this.tableData[i].noticeTime + "日"
        }
        this.page.total = response.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
    },
    //编辑
    handleEdit(row) {
      this.addEditId = row.id || this.selectionList[0].id;
      this.listFlag = false;
      this.addllistFlag = true;
      this.detaillistFlag = false;
    },
    //巡检计划审核
    handleShEdit(row) {
      this.checkForm.enable = row.enable
      this.checkForm.id = row.id
      this.check.open = true;
    },

    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }

            return planDelObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },

    // 更新
    handleUpdate: function (row, index, done, loading) {
      planPutObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      planAddObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsinsinspectplan/export",
          this.searchForm,
          "保养计划.xlsx"
      );
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/ems/avue.scss";
@import "@/styles/ems/public-styles.scss";

.statusList {
  margin-top: 20px;

  .labelS {
    color: #C1C8D2;
  }

  .dottedStyle {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
    background-color: #F7CA60;
  }

  .detailInfo {
    color: #5D5D5D;
    font-size: 14px;
    margin-right: 10px;
  }

  .statusContent {
    font-size: 14px;
  }
}

.labelS {
  display: inline-block;
  width: 100px;
  font-size: 14px;
  margin-right: 10px;
  text-align: right;
  color: #888888;

}

.contentS {
  font-weight: bold;
  font-size: 14px;
  color: #101010;
  margin: 10px 0;
  display: inline-block;
}

.line {
  border: 2px solid rgba(236, 240, 244, 100);
  margin: 30px 0;
}

::v-deep.drawerStyle {
  .el-drawer__header {
    background-color: #F2F2F5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 10px;
  }

  .box-card {
    box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, .12);
    margin: 0 20px 10px;
  }

  .tableTitle {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    display: inline-block;
  }
}

.icon-style {
  display: flex;

}

.con-left {
  width: 80%;
  height: 400px;
  overflow-y: scroll;

  .contentS {
    font-weight: normal;
  }

  .small-title {
    font-size: 12px;
    font-weight: 600;
    margin: 10px 15px;
    display: block;
  }
}

.con-right {
  width: 20%;
  border-right: 1px solid #ECF0F4;
  padding-left: 23px;
  text-align: center;
  box-sizing: border-box;

  .my-steps-box {
    font-size: 12px;
    margin-top: 50px;

    .step-item {
      margin-bottom: 15px;
      padding: 6px 0;
      position: relative;

      .text {
        cursor: pointer;
      }
    }

    .step-item.step-active {
      border-right: 2px solid #26AE61;

      .text {
        color: $theme;
      }
    }

    .step-item:nth-last-child(1) .line {
      display: none;
    }
  }
}
</style>
