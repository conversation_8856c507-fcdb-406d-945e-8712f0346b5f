<template>
  <div id="ball" :style="{width: '100px', height: '100px'}"></div>
</template>

<script>
import echarts from 'echarts'

export default {
  data() {
    return {};
  },

  props: {
    checkingTodayCompletionData:{
      type: Object
    }
  },

  watch: {
    checkingTodayCompletionData: function(newVal,oldVal){
      let a, b;
      this.cData = newVal;  //newVal即是chartData
      a = this.cData.num;
      b = this.cData.allNum;
      this.drawLine(a,b);
    }
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    // this.drawLine();
  },
  methods: {

    drawLine(a,b) {

      // 基于准备好的dom，初始化echarts实例
      let ball = this.$echarts.init(document.getElementById('ball'))

      // 绘制图表
      ball.setOption({
        title: [
          {
            text: (a === 0 ? 0 : ((a / b).toFixed(2) * 100)) + '%',
            x: 'center',
            top: '40%',
            textStyle: {
              fontSize: 14,
              color: '#7680a9',
              fontWeight: '500',
            },
          },
        ],
        polar: {
          radius: ['44%', '50%']
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            center: ['50%', '50%'],
            itemStyle: {
              normal: {
                labelLine: {
                  show: false,
                },
                color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                  {
                    offset: 1,
                    color: 'rgba(55,70,130, 1)',
                  },
                  {
                    offset: 0,
                    color: 'rgba(55,70,130, 0)',
                  },
                ]),
                shadowBlur: 10,
              },
            },
            data: [
              {
                value: 100,
              },
            ],
          },
        ]
      });
    }
  }
}

</script>
