<template>
  <div id="myRun" :style="{width: '205px', height: '100px'}"></div>
</template>

<script>

export default {
  data() {
    return {}
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let myRun = this.$echarts.init(document.getElementById('myRun'))

      let datas = [
        [
          {name: '运行', value: 5},
          {name: '故障', value: 2},
          {name: '待机', value: 3},
        ],
      ]


      // 绘制图表
      myRun.setOption({
        title: {
          left: 'center',
          textStyle: {
            color: '#999',
            fontWeight: 'normal',
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}'
        },
        color: ["#27ad61", "#fd0003", "#f88210"],
        series: datas.map(function (data, idx) {
          let top = idx * 33.3;
          return {
            type: 'pie',
            radius: [20, 30],
            top: top + '%',
            height: '100%',
            left: 'center',
            width: 300,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            label: {
              alignTo: 'edge',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 15,
              rich: {
                time: {
                  fontSize: 10,
                  color: '#999'
                }
              }
            },
            labelLine: {
              length: 15,
              length2: 0,
              maxSurfaceAngle: 80
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft
                  ? params.labelRect.x
                  : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points
              };
            },
            data: data
          };
        })


      });
    }
  }
}

</script>
