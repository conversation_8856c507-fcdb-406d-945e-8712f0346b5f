<template>
  <div class="icon-title">
    <img :src="'/img/ali/' + imgUrl + '.png'" alt />
    <b>{{ title }} </b>
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: "IconTitle",
  props: {
    imgUrl: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.icon-title {
  b {
    font-size: 14px;
  }
  .slot {
    font-size: 12px;
    color: #666;
    margin-left: 5px;
  }
  img {
    width: 18px;
    height: 18px;
  }
  display: flex;
  align-items: center;
}
</style>
