<template>
  <div class="offline-detail app-container">
    <h3 class="container-title">基本信息</h3>
    <el-descriptions
      :labelStyle="labelStyle"
      class="margin-top"
      :column="2"
      border
    >
      <el-descriptions-item v-for="item in labeColumn" :label="item.label">
        <dict-tag
          v-if="item.dict"
          :options="dict.type[item.dict]"
          :value="baseInfo[item.value]"
        />
        <span v-else>{{ baseInfo[item.value] }}</span>
      </el-descriptions-item>
    </el-descriptions>
    <h3 class="container-title node-wrap">节点列表</h3>
    <offline
      :tableData="tableData"
      @refresh="getEnterpriseInfo"
      offlineType="examine"
    />
    <div class="footer">
      <el-button type="primary" @click="$router.back()">返回</el-button>
    </div>
  </div>
</template>
<script>
import { getOfflineDetail } from "@/api/diagnosis";
import offline from "./offline.vue"; //线下诊断的表格弹窗
import { getDiagnosisType } from "@/api/diagnosis";

export default {
  // dicts: [],
  components: { offline },
  data() {
    return {
      labeColumn: [
        { label: "企业名称", value: "deptName" },
        // { label: '诊断等级', value: 'leval', dict: 'diagnostic_level_status' },
        { label: "诊断类型", value: "diagnosisTypeName" },
        { label: "诊断开始时间", value: "startDate" },
        { label: "诊断结束时间", value: "endDate" },
        { label: "附加条件", value: "additionalCondition" },
        { label: "第一联系人", value: "relationUser" },
        { label: "联系电话", value: "relationPhone" },
        { label: "第二联系人", value: "twoRelationUser" },
        { label: "联系电话", value: "twoRelationPhone" },
      ],
      baseInfo: {},
      tableData: [],
      diagnosisTypeList: [],
    };
  },
  async created() {
    await getDiagnosisType().then((res) => {
      const data = res.data || [];
      this.diagnosisTypeList = data;
    });
    await this.getEnterpriseInfo();
  },
  computed: {
    labelStyle() {
      return { width: "200px" };
    },
  },
  methods: {
    async getEnterpriseInfo() {
      const { flowInstanceId } = this.$route.query;
      const { data } = await getOfflineDetail({ flowInstanceId });
      data.params.ddiagnosticOfflineByIdVO.diagnosisTypeName =
        this.diagnosisTypeList.find(
          (item) =>
            item.id == data.params.ddiagnosticOfflineByIdVO.diagnosisType
        )?.labelName || "";
      this.$set(
        this,
        "baseInfo",
        data.params ? data.params.ddiagnosticOfflineByIdVO : {}
      );

      this.tableData = data.flowInstanceNodeRecordsResponses || [];
    },
  },
};
</script>
<style lang="scss" scoped>
.offline-detail {
  padding: 20px;

  .container-title {
    margin-bottom: 10px;
    border-bottom: none;

    &::before {
      display: inline-block;
      content: "";
      width: 4px;
      height: 12px;
      background-color: #0147eb;
      margin-right: 5px;
    }
  }

  .node-wrap {
    margin-top: 30px;
  }

  .footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
