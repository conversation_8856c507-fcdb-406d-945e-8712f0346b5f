import { requestPlatForm } from "@/utils/requestBase";



export function getWorkShopList(query) {
    return requestPlatForm({
      url: '/workshop/manage/factory/tree',
      method: 'get',
      params: query
    })
  } 
  export function deleteWorkShop(data) {
    return requestPlatForm({
      url: '/workshop/manage/unify',
      method: 'delete',
      data
    })
  }

  export function addWorkShop(data) {
    return requestPlatForm({
      url: '/workshop/manage/unify',
      method: 'post',
      data: data
    })
}
export function editWorkShop(data) {
  return requestPlatForm({
    url: '/workshop/manage/unify',
    method: 'put',
    data: data
  })
}



