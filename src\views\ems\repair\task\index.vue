<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
    <div v-if="listFlag" class="execution">
        <el-card class="box-card btn-search page-search">
            <div slot="header" class="clearfix">
                <div class="btn-box">
                    <el-button
                            type="info"
                            icon="el-icon-refresh-left"
                            @click="refreshChange()"
                    ></el-button
                    >
                    <el-button id="gwButton"
                               type="primary"
                               style="backgroundColor:#E1b980"
                               icon="el-icon-circle-plus-outline"
                               v-if="permissions.ems_emsreprepairtask_add"
                               @click="toAdd()"
                    >立即生成任务
                    </el-button
                    >
                    <el-button
                            type="check"
                            icon="el-icon-download"
                            @click="exportExcel"
                    >导出
                    </el-button
                    >
                </div>
                <div class="icon-box">
                    <i class="el-icon-search" @click="searchShow"></i>
                    <i class="el-icon-refresh" @click="refreshChange"></i>
                    <i class="el-icon-goods"></i>
                    <i class="el-icon-setting" @click="columnShow"></i>
                    <i class="icon-zuixiaohua"/>
                </div>
            </div>

        </el-card>
        <basic-container>
            <avue-crud
                    ref="crud"
                    :page.sync="page"
                    :data="tableData"
                    :permission="permissionList"
                    :table-loading="tableLoading"
                    :option="tableOption"
                    @selection-change="selectionChange"
                    @on-load="getList"
                    @search-change="searchChange"
                    @refresh-change="refreshChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    @cell-click="cellClick"
                    :cell-style="cellStyle"

            >
<!--                @row-click="rowClick"-->
                <template slot="header">
                    <IconTitle class="selfTitle" title="检修任务" imgUrl="yunwei"/>
                </template>
                <template slot="status" slot-scope="scope">
                    <el-tag type="danger" v-if="scope.row.status==0">未开始</el-tag>
                    <el-tag type="danger" v-if="scope.row.status==1">执行中</el-tag>
                    <el-tag type="danger" v-if="scope.row.status==2">待核验</el-tag>
                    <el-tag type="danger" v-if="scope.row.status==3">已完成</el-tag>
                    <el-tag type="danger" v-if="scope.row.status==4">已过期</el-tag>
                </template>
            </avue-crud>
        </basic-container>
        <el-drawer
                class="drawerStyle"
                title="任务信息"
                :show-close="false"
                :visible.sync="detail"
                direction="rtl"
                size="50%"
                append-to-body
        >
            <div>
                <img style="float: right;margin-right: 180px" :src="require('@/assets/imagesAssets/xgfj.png')">
                <div>
                    <span class="labelS">设备编号：</span>
                    <span class="contentS">{{rowCheck.deviceNum}}</span>
                </div>
                <div>
                    <span class="labelS">设备名称：</span>
                    <span class="contentS">{{rowCheck.deviceName}}</span>
                </div>
                <div>
                    <span class="labelS">位置：</span>
                    <span class="contentS">{{rowCheck.deviceLocation}}</span>
                </div>
            </div>
            <div class="statusList">
                <span class="labelS">故障描述</span>
                <span class="statusContent">出问题了</span>
            </div>
            <div class="line"></div>
            <div>
                <div class="icon-style">
                    <div class="con-right">
                        <div class="my-steps-box">
                            <template v-for="(item, index) in menulist">

                                <div
                                        :class="['step-item', activeMenu == index ? 'step-active' : '']"

                                        :key="index"
                                >
                                    <div class="top-title">
                                        <span class="text" @click="boxClick(index)">{{ item.label }}</span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="con-left" id="scrollBox" v-if="rowCheck.boxIndexOne">
                        <el-card shadow="always" style="width: 90%" class="box-card">
                            <div class="tableTitle">检修时间轴</div>
                            <div class="vertical"    >
                                <el-card shadow="always"class="box-card" v-for="item in 1" :key="item">
                                    <div >
                                        <div class="headerStyle">
                                            <span class="dottedS"></span>
                                            <i class="el-icon-date" style="color: #0ccb82"></i>
                                            <span style="color: #0ccb82">任务节点：创建任务</span>
                                            <i class="el-icon-arrow-up status"></i>
                                        </div>
                                        <div class="contentStyle">
                                            <div class="boxContent">
                                                <span class="desc">检修任务已创建</span>
                                                <span class="status">
                                                  <i class="el-icon-success"></i>
                                                  正常
                                                </span>
                                            </div>
                                            <div>
                                                <span class="label">处理人：</span>
                                                <span class="content">系统自动操作</span>
                                            </div>
                                            <div>
                                                <span class="label">执行时间：</span>
                                                <span class="content"> 2022/03/12 00:00:00</span>
                                            </div>
                                            <div class="line"></div>
                                            <div>
                                                <span></span>
                                                <span class="label timeTotal">用时：</span>
                                                <span>1分钟</span>
                                            </div>
                                        </div>
                                    </div>

                                </el-card>
                            </div>
                        </el-card>
                    </div>
                    <div class="con-left" v-else-if="rowCheck.boxIndexTwo">
                        <el-card shadow="always" class="box-card">
                            <div><span class="tableTitle">关联计划</span></div>
                            <el-table v-loading="rowCheck.loading" :data="rowCheck.deviceList">
                                <el-table-column
                                        label="序号"
                                        width="50px">
                                    <template slot-scope="scope">
                                        {{scope.$index+1}}
                                    </template>
                                </el-table-column>
                                <el-table-column label="id" align="center" prop="id" v-if="false"/>
                                <el-table-column label="模块" align="center" prop="itemsName"/>
                                <el-table-column label="项目" align="center" prop="itemsName"/>
                                <el-table-column label="编号" align="center" prop="itemsName"/>
                                <el-table-column label="内容" align="center" prop="itemsName"/>
                                <el-table-column label="结果" align="center" prop="itemsName"/>
                                <el-table-column label="是否异常" align="center" prop="specification">
                                    <template slot-scope="scope">
                                          <span>{{
                                            scope.row.resultsIsAbnormal == 0
                                              ? "正常"
                                              : scope.row.resultsIsAbnormal == 1
                                              ? "异常": ""
                                          }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="异常是否处理" align="center" prop="resultsImageList"/>
                                <el-table-column label="处理措施" align="center" prop="resultsImageList"/>
                                <el-table-column label="图片" align="center" prop="resultsImageList"/>
                            </el-table>
                            <pagination
                                    v-show="rowCheck.queryParams.total>0"
                                    :total="rowCheck.queryParams.total"
                                    :page.sync="rowCheck.queryParams.pageNum"
                                    :limit.sync="rowCheck.queryParams.pageSize"
                                    @pagination="resultsGetList"
                            />
                        </el-card>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
    <div v-else-if="detaillistFlag">
        <IndexDetail :id='detailId'/>
    </div>
</template>
<script>
    import {taskFetchList, taskGetObj, taskAddObj, taskPutObj, taskDelObj}
        from "@/api/ems/repair/emsreprepairtask";
    import {tableOption} from '@/const/crud/ems/repair/emsreprepairtask'
    import {mapGetters} from "vuex";
    import jQuery from "jquery";
    import IconTitle from "@/components/ems/icon-title/index.vue";
    import IndexDetail from "./detail.vue";

    export default {
        name: 'task',
        components: {
            IconTitle,
            IndexDetail,
        },
        data() {
            return {
                tableData: [],
                searchForm: {}, // 查询参数
                single: true,  // 非单个禁用
                multiple: true, // 非多个禁用
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条
                },
                tableLoading: false,
                tableOption: tableOption,
                ids: [],
                from: {
                    id: null,
                    standardId: null,
                    itemsNum: null,
                    itemsName: null,
                    methodBenchmark: null,
                    type: null,
                    lowerLimit: null,
                    upperLimit: null,
                    optional: null,
                    normalOption: null,
                    referencePicture: null,
                },
                selectionList: [],
                detail: false,
                tableData1: [],

                listFlag: true,
                detailId: 0,
                detaillistFlag: false,

                rowCheck: {
                    taskNum: null,
                    liableUserId: null,
                    planName: null,
                    deviceLocation:null,

                    loading: false,
                    deviceList: [],
                    queryParams: {
                        pageNum: 1,
                        pageSize: 10,
                        total: 0,
                    },
                    taskObject: {
                        status: null,
                        statusName: null,
                        beginTime: null,
                        endTime: null,
                        abnormalNum: 0,
                        leakDetectionNum: 0,

                    },
                    rowId: null,
                    boxIndexOne: true,
                    boxIndexTwo: false,
                },
                tableNewTask: false,
                taskStatusStyle: "",
                menulist: [
                    {
                        label: '检修动态'
                    },
                    {
                        label: '计划详情'
                    },
                ],
                activeMenu: 0,
            };
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinsinspecttask_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinsinspecttask_del, false),
                    editBtn: this.vaildData(this.permissions.ems_emsinsinspecttask_edit, false),
                };
            },
        },
        mounted() {
            this.initElement();
            this.changeThme();
        },
        methods: {
            initElement() {
                var mediumb = document.createElement("b"); //思路一样引入中间元素
                jQuery(".avue-crud__tip").after(mediumb);
                jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
                jQuery(mediumb).after(jQuery(".selfTitle"));
                jQuery(mediumb).remove();
            },
            selectionChange(list) {
                this.selectionList = list
                this.single = list.length !== 1;
                this.multiple = !list.length;
                this.ids = list.map((item) => item.id);
            },

            columnShow() {
                this.$refs.crud.$refs.dialogColumn.columnBox = !0;
            },
            // 搜索框显示与否
            searchShow() {
                this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
            },
            toAdd() {
                taskAddObj(this.from).then(data => {
                    this.$message.success('已根据计划生成任务')
                    // this.visible = false
                    // this.checkGetList()
                }).catch(() => {
                    // this.canSubmit = true;
                });
            },

            cellStyle(data) {
                if (data.column.property == "taskNum") {
                    return "color:#02b980;cursor:pointer";
                }

            },
            cellClick(row, column) {
                if (column.property === "taskNum") {
                    this.toDetail(row);
                } else {
                    return;
                }
            },
            rowClick(row, column) {
                if (column.property === "taskNum") {
                    return;
                } else {
                    this.detail = true;
                    this.rowCheck.deviceLocation = row.deviceLocation
                    this.rowCheck.deviceNum = row.deviceNum
                    this.rowCheck.deviceName = row.deviceName
                    this.rowCheck.rowId = row.id;
                    this.rowCheck.deviceList = [];
                    deviceListTaskId(Object.assign(
                        {
                            current: this.rowCheck.queryParams.pageNum,
                            size: this.rowCheck.queryParams.pageSize,
                        },
                        {id: row.id}
                        )
                    ).then(response => {
                        this.rowCheck.deviceList = response.data.data.records;
                        this.rowCheck.queryParams.total = response.data.data.total;
                        this.rowCheck.loading = false;
                    });
                }
            },
            toDetail(row) {
                this.detailId = row.id
                this.listFlag = false;
                this.detaillistFlag = true;
            },

            deviceGetList() {
                this.rowCheck.loading = true;
                deviceListTaskId(Object.assign(
                    {
                        current: this.rowCheck.queryParams.pageNum,
                        size: this.rowCheck.queryParams.pageSize,
                    },
                    {id: this.rowCheck.rowId}
                    )
                ).then(response => {
                    this.rowCheck.deviceList = response.data.data.records;
                    this.rowCheck.queryParams.total = response.data.data.total;
                    this.rowCheck.loading = false;
                });
            },
            boxClick(key) {
                // alert(key)
                this.activeMenu = key;
                if (key == 0) {
                    this.rowCheck.boxIndexOne = true;
                    this.rowCheck.boxIndexTwo = false;
                } else {

                    this.rowCheck.boxIndexOne = false;
                    this.rowCheck.boxIndexTwo = true;
                    this.getboxIndexTwo(this.rowCheck.rowId);
                }

            },
            getboxIndexTwo(id) {
                // this.rowCheck.loading = true;
                // taskObjectPlanId(id).then(response => {
                //     if (response.data.data != null) {
                //         this.tableNewTask = true;
                //         var taskStatus = ["未开始", "执行中", "待核验", "已完成", "已过期"]
                //         var taskStatusColor = [
                //             "#002aff",
                //             "#00f5e2",
                //             "#F7CA60",
                //             "#40f500",
                //             "#ff0000"]
                //         this.rowCheck.taskObject.statusName = taskStatus[Number(response.data.data.status)]
                //         this.taskStatusStyle = "background-color:" + taskStatusColor[Number(response.data.data.status)]
                //         this.rowCheck.taskObject.beginTime = response.data.data.planBeginTime;
                //         this.rowCheck.taskObject.endTime = response.data.data.planEndTime;
                //         this.rowCheck.taskObject.abnormalNum = response.data.data.abnormalNum == null ? 0 : response.data.data.abnormalNum;
                //         this.rowCheck.taskObject.leakDetectionNum = response.data.data.leakDetectionNum == null ? 0 : response.data.data.leakDetectionNum;
                //     } else {
                //         this.tableNewTask = false;
                //         this.rowCheck.taskObject.statusName = "暂未生成任务"
                //     }
                //
                //     this.rowCheck.loading = false;
                // });
            },
            resultsGetList() {
                this.rowCheck.loading = true;
                getEmsInsCheckResultsRecordIdPage(Object.assign(
                    {
                        current: this.rowCheck.queryParams.pageNum,
                        size: this.rowCheck.queryParams.pageSize,
                    },
                    {id: this.rowCheck.rowId}
                    )
                ).then(response => {
                    this.rowCheck.deviceList = response.data.data.records;
                    this.rowCheck.queryParams.total = response.data.data.total;
                    this.rowCheck.loading = false;
                });
            },
            // 列表查询
            getList(page, params) {
                this.tableLoading = true;
                taskFetchList(
                    Object.assign({
                        current: page.currentPage,
                        size: page.pageSize,
                    }, params, this.searchForm)).then((response) => {
                    this.tableData = response.data.data.records;
                    this.page.total = response.data.data.total;
                    this.tableLoading = false;
                })
                    .catch(() => {
                        this.tableLoading = false;
                    });
            },
            //编辑
            handleEdit() {
                var refsDate = this.$refs
                refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
            },
            // 删除
            handleDel: function (row, index) {
                this.$confirm("是否确认删除所选数据项", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        let id = "";
                        if (row) {
                            id = row.id;
                        } else {
                            id = this.ids;
                        }
                        return taskDelObj(id);
                    })
                    .then((data) => {
                        this.$message.success("删除成功");
                        this.getList(this.page);
                    });
            },
            // 更新
            handleUpdate: function (row, index, done, loading) {
                taskPutObj(row)
                    .then((data) => {
                        this.$message.success("修改成功");
                        done();
                        this.getList(this.page);
                    })
                    .catch(() => {
                        loading();
                    });
            },
            // // 保存
            // handleSave: function (row, done, loading) {
            //     taskAddObj(row)
            //             .then((data) => {
            //                 this.$message.success("添加成功");
            //                 done();
            //                 this.getList(this.page);
            //             })
            //             .catch(() => {
            //                 loading();
            //             });
            // },
            // 每页条数改变事件
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            // 当前页发生改变事件
            currentChange(current) {
                this.page.currentPage = current;
            },
            // 查询事件
            searchChange(form, done) {
                this.searchForm = form
                this.page.currentPage = 1
                this.getList(this.page, form)
                done()
            },
            // 刷新事件
            refreshChange() {
                this.getList(this.page);
            },
            // 导出excel
            exportExcel() {
                this.downBlobFile(
                    "/ems/emsinsinspecttask/export",
                    this.searchForm,
                    "emsinsinspecttask.xlsx"
                );
            },
            // 改变主题颜色
            changeThme() {
                //"#02b980"
                document.getElementById("gwButton").style.backgroundColor = this.theme;
            },
        },
    };
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style lang="scss" scoped>
    @import "@/styles/color.scss";

    .labelS {
        display: inline-block;
        width: 100px;
        margin-right: 10px;
        text-align: right;
        color: #888888;
        font-size: 14px;
    }

    .contentS {
        font-weight: bold;
        color: #101010;
        margin: 10px 0;
        display: inline-block;
        font-size: 14px;
    }

    .line {
        border: 2px solid rgba(236, 240, 244, 100);
        margin: 30px 0;
    }

    ::v-deep.drawerStyle {
        .el-drawer__header {
            background-color: #F2F2F5;
            padding: 20px 0 20px 20px;
            color: #101010;
            margin-bottom: 10px;
        }

        .el-card__body {
            padding: 0 20px 10px;
        }

        .box-card {
            box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, .12);
            margin: 0 10px;
        }

        .tableTitle {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: inline-block;
        }
    }

    .statusList {
        margin-top: 20px;

        .labelS {
            color: #C1C8D2;
        }

        .dottedStyle {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background-color: #F7CA60;
        }

        .detailInfo {
            color: #5D5D5D;
            font-size: 14px;
            margin-right: 10px;
        }

        .statusContent {
            font-size: 14px;
        }
    }

    .icon-style {
        display: flex;

    }

    .con-left {
        width: 80%;
        height: 400px;
        overflow-y: scroll;

        .contentS {
            font-weight: normal;
        }

        .small-title {
            font-size: 12px;
            font-weight: 600;
            margin: 10px 15px;
            display: block;
        }
    }

    .con-right {
        width: 20%;
        border-right: 1px solid #ECF0F4;
        padding-left: 23px;
        text-align: center;
        box-sizing: border-box;

        .my-steps-box {
            font-size: 12px;
            margin-top: 50px;

            .step-item {
                margin-bottom: 15px;
                padding: 6px 0;
                position: relative;

                .text {
                    cursor: pointer;
                }
            }

            .step-item.step-active {
                border-right: 2px solid #26AE61;

                .text {
                    color: $theme;
                }
            }

            .step-item:nth-last-child(1) .line {
                display: none;
            }
        }
    }

    .icon-style {
        display: flex;
    }

    ::v-deep.con-left {
        width: 80%;
        height: 400px;
        overflow-y: scroll;
        font-size: 12px;
        position: relative;

        .headerStyle {
            .dottedS {
                width: 10px;
                height: 10px;
                display: inline-block;
                border-radius: 50%;
                background-color: #666;
                position: absolute;
                left: -6px;
            }

            color: #0ccb82;

            i {
                margin-right: 25px;
            }
        }

        .vertical {
            border-left: 2px dashed #EDEEF2;
            position: relative;
            padding-left: 10px;

            .el-card__body {
                padding: 10px 0 10px 10px;
            }
        }

        .contentStyle {
            padding: 10px;

            .line {
                border: 1px solid rgba(236, 240, 244, 100);
                margin: 15px 0;
            }
        }

        .status {
            float: right;
            margin-right: 10px;

            i {
                color: #0ccb82;
                margin-right: 5px;
            }
        }

        .boxContent {
            background-color: #F6F6FC;
            font-size: 14px;
            height: 50px;
            line-height: 50px;
            margin: 10px 0 15px;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                height: 50px;
                width: 5px;
                background-color: #DEDEE4;

            }

            .desc {
                margin-left: 10px;
            }

        }

        .label {
            color: #999;
            margin-right: 10px;
            display: inline-block;
            margin-bottom: 10px;
            width: 70px;
        }

        .content {
            color: #101010;
        }

        .timeTotal {
            margin-right: 20px;
        }
    }

    .con-right {
        width: 20%;
        border-right: 1px solid #ecf0f4;
        padding-left: 23px;
        text-align: center;
        box-sizing: border-box;

        .my-steps-box {
            font-size: 12px;
            margin-top: 50px;

            .step-item {
                margin-bottom: 15px;
                padding: 6px 0;
                position: relative;

                .text {
                    cursor: pointer;
                }
            }

            .step-item.step-active {
                border-right: 2px solid #26ae61;

                .text {
                    color: $theme;
                }
            }

            .step-item:nth-last-child(1) .line {
                display: none;
            }
        }
    }
</style>
