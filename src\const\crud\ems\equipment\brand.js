export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
  "column": [
	 	  {
      "type": "input",
      "label": "品牌名称",
      "prop": "brandName",
      "span": 12,
        search:true,
      rules: [{
        required: true,
        message: '请输入品牌名称',
        trigger: 'blur'
      },
      {
          min: 0,
          max: 20,
          message: '长度在 0 到 20 个字符',
          trigger: 'blur'
      }]
    },	  {
      row: true,
      minRows: 2,
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12,
      rules: [
          {
              min: 0,
              max: 255,
              message: '长度在 0 到 255 个字符',
              trigger: 'blur'
          }]
    },	 ]
}
