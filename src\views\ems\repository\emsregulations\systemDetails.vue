<template>
  <div class="systemDetails">
    <div class="body" v-if="newsContent != null" v-html="newsContent" />
    <div class="body" style="text-align: center;font-size: 20px; color: #bdbdbd" v-else v-html="'暂无数据'" />
  </div>
</template>

<script>
import { getObj } from "@/api/ems/repository/emsregulations"
export default {
  name: "systemDetails",
  data(){
    return{
      newsContent: ""
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList(){
      let id = this.$route.query.id;
      getObj(id).then(res => {
        this.newsContent = res.data.data.newsContent;
        console.log("111>>>" , this.newsContent)
      })
    }
  }
}
</script>

<style scoped lang="less">
.systemDetails{
  background-color: #fff;
  width: 100%;
  height: 100%;
  .body{
    margin: 0px 0px 10px 10px;
    padding-top: 10px;
  }
}


</style>
