<template>
    <div class="app-container facilityList">

        <div class="dataLevelLeft">
            <el-button type="primary" icon="el-icon-plus" :disabled="JSON.stringify(rightRowData) == '{}'"
                @click="detailHandle()">添加设备</el-button>
            <div class="dataLevelLeftImg">
                <span style="margin-left: 12px;margin-right: 52px;"><img src="@/assets/img/onLine.png" />在线</span>
                <span> <img src="@/assets/img/offLine.png" />离线</span>
            </div>
            <el-input placeholder="搜索" v-model="filterCatalogueText" />
            <div class="dataLevelTree">
                <el-tree v-loading="treeLoading" :data="dataTrees" :props="defaultProps" @node-click="fileClick"
                    :filter-node-method="filterNode" node-key="uniqueFlag" :highlight-current="current"
                    :default-expanded-keys="defaultKeys" :current-node-key="currentKey" ref="treeRef">
                    <template slot-scope="{ node, data }">
                        <span class="custom-tree-node">
                            <span v-if="!data.workshopId" :class="lookNode(node, data)">{{ node.label }}</span>
                            <span v-else :class="lookNode(node, data)" class="endlabel">
                                <img v-if="data.connectStatus === 0" src="@/assets/img/offLine.png" />
                                <img v-else src="@/assets/img/onLine.png">
                                <span>{{ node.label }}</span>
                            </span>
                        </span>
                    </template>
                </el-tree>
            </div>
        </div>

        <div class="dataLevelRight" v-if="JSON.stringify(rightRowData) != '{}'">
            <el-form :inline="true" :model="queryParams" @submit.native.prevent>
                <el-form-item style="width: 60%;" prop="deviceName">
                    <el-input style="width:100%" @keyup.enter.native="getDataList" v-model="queryParams.deviceName"
                        placeholder="请输入设备名称…"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="getDataList()" type="primary">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button @click="reset()">重置</el-button>
                </el-form-item>
            </el-form>

            <div v-loading="dataListLoading" style="min-height: 160px;">
                <el-row :gutter="20" class="cardDemo" v-if="dataList.length > 0">
                    <el-col :xs="24" :sm="24" :md="12" :lg="8" style="margin-bottom: 24px;"
                        v-for="(item, index) in dataList" :key="item.id">
                        <div class="cardDemoCols">
                            <div class="cardDemoCentre">
                                <div class="cardDemoText">
                                    <div style="display: flex;justify-content: space-between;">
                                        <el-tooltip effect="dark" :content="item.deviceName" placement="top-start">
                                            <div style="color: #409EFF;cursor:pointer" class="cardDemoTitle"
                                                @click="openBig(item)">
                                                {{ item.deviceName }}
                                            </div>
                                        </el-tooltip>
                                        <i v-if="item.deviceName" class="el-icon-message-solid"
                                            style="color: #F56C6C; cursor: pointer;" @click="reportFuc(item)">
                                        </i>
                                    </div>
                                    <el-tooltip effect="dark" :content="item.deviceNum" placement="bottom-start">
                                        <div class="cardDemoContent">{{ item.deviceNum }}</div>
                                    </el-tooltip>
                                </div>
                            </div>
                            <div class="cardDemoFooter">
                                <div class="dotDemo">
                                    <span style="width:6px;height:6px;display: inline-block;margin-right:6px"
                                        :class="item.deviceStatus === 0 ? 'dotred' : 'dotgreen'"></span>
                                    <span :class="item.deviceStatus === 0 ? 'textRed' : 'textGreen'">
                                        {{ item.deviceStatus === 0 ? '已关机' : '运行中' }}</span>
                                </div>
                                <div class="editDemo">
                                    <span @click="detailHandle(item.id)" class="edit"><i class="el-icon-edit"></i>编辑</span>
                                    <el-dropdown placement="bottom" style="margin-left: 12px;"
                                        @command="(command) => handleCommand(command, item)">
                                        <i class="el-icon-more"></i>
                                        <el-dropdown-menu slot="dropdown">
                                            <template v-if="item.deviceStatus !== 0">
                                                <el-dropdown-item v-if="item.collectStatus === 0"
                                                    command="setCollectFuc">启动采集</el-dropdown-item>
                                                <el-dropdown-item v-else command="setStepFuc">停止采集</el-dropdown-item>
                                            </template>
                                            <el-dropdown-item style="color: rgb(245, 108, 108);"
                                                command="deleteBatchHandle">删除设备</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-empty v-else class="emptyDemo" description="暂无数据" />
            </div>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
                @pagination="getDataList" />
        </div>
        <el-empty class="dataLevelRight" v-else description="请选择左侧目录网关查看数据" />


        <div class="drawerClass" style="height:100%">
            <el-drawer :with-header="false" ref="detailDrawer" :modal="false" :visible.sync="detailDrawerVisible"
                size="100%" :show-close="false" :destroy-on-close="true">
                <div class="title_header">
                    <el-button @click="closeDrawer" type="text">
                        <svg-icon icon-class="back" style="margin-right: 5px" />
                        返回
                    </el-button>
                    <h3 class="title">安装驱动</h3>
                </div>
                <!-- <Detail/> -->
            </el-drawer>
        </div>

    </div>
</template>
  
<script>
import { getFlagTree, getDeviceList, setCollect, setStep, deleteProduce } from "@/api/gatewayManage/facility/index.js";
import { SessionStorage } from '@/utils/storage'
import Detail from './compile/index.vue'
export default {
    name: "facilityList",
    components: { Detail },
    data() {
        return {
            // 遮罩层
            loading: true,
            showSearch: true,
            total: 0,
            dataList: [],
            total: 0,
            title: "",
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                deviceName: '',
                gatewayId: '',
            },
            filterCatalogueText: '',
            dataTrees: [],
            defaultProps: {
                children: 'childrenList',
                label: 'tname',
            },
            treeLoading: false,
            current: true,
            currentKey: "",
            rightRowData: {},
            dataListLoading: false,
            detailDrawerVisible: false,
            defaultKeys: [],
            dataTreesFlat: []
        };
    },
    created() {
        this.getFlagTreeFtn()
    },
    watch: {
        filterCatalogueText(val) {
            this.$refs.treeRef.filter(val);
        }
    },
    methods: {

        handleCommand(val, row) {
            switch (val) {
                case 'setCollectFuc':
                    this['setCollectFuc'](row)
                    return
                case 'setStepFuc':
                    this['setStepFuc'](row)
                    return
                case 'deleteBatchHandle':
                    this['deleteBatchHandle'](row.id)
                    return
                default:
                    return
            }
        },
        //停
        setStepFuc(row) {
            setStep({ id: row.id }).then(res => {
                this.$modal.msgSuccess("停止设备成功！");
                this.getDataList();
            })
        },
        //启
        setCollectFuc(row) {
            setCollect({ id: row.id }).then(res => {
                this.$modal.msgSuccess("启动设备成功！");
                this.getDataList();
            })
        },
        //删
        deleteBatchHandle(id) {
            this.$modal.confirm('是否确认数据项？').then(function () {
                return deleteProduce([id]);
            }).then(() => {
                this.getDataList();
                this.$modal.msgSuccess("删除成功！");
            }).catch(() => { });
        },
        closeDrawer() {
            this.detailDrawerVisible = false;
            //   detailDrawer.value.close();
            this.realitySetList()
        },
        reportFuc(row) {
            // router.push({ path: '/equipment-rule/report-record/index', query: { deviceName: row.deviceName } })
        },
        openBig(item) {
            window.open(item.screenAddress, item.deviceNum)
        },
        fileClick(e, data, n, t) {
            if (data.data.workshopId) {
                const row = data.data;
                this.queryParams.gatewayId = row.id;
                SessionStorage.setItem('facilityPams', { gatewayId: row.id, uniqueFlag: row.uniqueFlag });
                this.rightRowData = row;
                this.current = true
                this.getDataList();
            }
            else {
                this.rightRowData = {};
                this.current = false
                SessionStorage.removeItem('facilityPams');
            }
        },
        filterNode(value, data) {
            if (!value) return true
            return data.tname.includes(value) || data.tname.includes(value.toUpperCase()) || data.tname.includes(value.toLowerCase())
        },
        lookNode(n, d) {
            if (d.companyName) {
                return 'stepOne'
            }
            else if (d.factoryName) {
                return 'stepTwo'
            }
            else {
                return 'stepThree'
            }
        },
        getFlagTreeFtn() {
            // this.treeLoading=true;
            getFlagTree().then(res => {
                this.dataTrees = res.data;
                this.dataTreesFlat = this.flattenTree(res.data)
                this.$nextTick(() => {
                    this.realitySetList();

                });
            }).finally(() => { this.treeLoading = false })
        },
        flattenTree(data) {
            const flattened = []
            for (const item of data) {
                flattened.push(item)
                if (item.childrenList && item.childrenList.length > 0) {
                    flattened.push(...this.flattenTree(item.childrenList))
                }
            }
            return flattened
        },
        realitySetList() {
            const { query } = this.$route;
            if (query.gatewayId) {
                this.queryParams = { ...this.queryParams, gatewayId: query.gatewayId };
                this.rightRowData = { id: query.gatewayId, uniqueFlag: query.uniqueFlag }
                this.current = true;
                this.currentKey = query.uniqueFlag;
                this.defaultKeys =[this.dataTreesFlat.filter(v=>v.parentUniqueFlag&&v.uniqueFlag==query.uniqueFlag)[0].parentUniqueFlag]
                this.$refs.treeRef.setCurrentKey(query.uniqueFlag);
                this.getDataList();
            }
        },

        detailHandle(id = undefined) {
            const { rightRowData } = this;
            const routeQuery = {
                gatewayId: rightRowData.id,
                uniqueFlag: rightRowData.uniqueFlag,
                id
            }
            // SessionStorage.setItem('facilityPams', routeQuery);
            // this.detailDrawerVisible=true
            this.$router.push({ path: `facility/redact`, query: { ...routeQuery } })
        },
        getDataList() {
            this.dataListLoading = true;
            const params = { ...this.queryParams };
            getDeviceList(params).then(response => {
                const { list, total } = response.data
                this.dataList = list || [];
                this.total = total;
            }).finally(() => this.dataListLoading = false)
        },
        reset() {
            this.queryParams = {
                ...this.queryParams,
                deviceName: '',
            }
            this.resetForm("queryParams");
            this.getDataList();
        },
        setDriveFuc(id) {
            // this.detailDrawerVisible = true;
            this.$router.push({ path: `list/drawer`, query: { id } })
        }
    }
};
</script>



<style lang="scss" scoped>
.facilityList {
    /* min-height: calc(100vh - 170px); */
    height: calc(100vh - 170px);
    position: relative;
    overflow: hidden;
    display: flex;

    .dataLevelLeft {
        width: 28%;
        height: 100%;
        overflow: auto;
        border-right: 1px solid #EAEDF3;
        padding-right: 20px;

        .dataLevelLeftImg {
            margin-top: 10px;
            margin-bottom: 10px;
            display: flex;

            span {
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #515A6E;
            }

            img {
                width: 20px;
                height: 20px;
                margin-right: 6px;
            }
        }

        .dataLevelTree {
            margin-top: 10px;

            ::v-deep .el-tree-node__content {
                height: 35px;
            }

            .stepOne {
                font-family: PingFang SC;
                font-weight: bold;
                font-size: 14px;
                color: #181F2D;
            }

            .stepTwo {
                font-family: PingFang SC;
                font-size: 14px;
                color: #181F2D;
            }

            .stepThree {
                font-family: PingFang SC;
                font-size: 14px;
                color: #515A6E;
            }

            .endlabel {
                display: flex;
                align-items: center;

                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 6px;
                }
            }
        }
    }

    .dataLevelRight {
        flex: 1;
        padding-left: 20px;

        ::v-deep .el-form-item__content {
            width: 100% !important;
        }

        .cardDemo {
            max-height: calc(100vh - 280px);
            width: 100%;
            overflow-y: auto;

            .cardDemoCols {
                height: 160px;
                display: flex;
                flex-direction: column;
                /* background: red; */
                border: 1px solid #DCDFE8;
                box-shadow: 0px 2px 8px #F3F5F8;
                border-radius: 8px;

                .cardDemoCentre {
                    padding: 24px 24px 0px;
                    display: flex;
                    flex: 1;
                    /* background-image: url('@/assets/img/gateway-dealbagrd.png'); */
                    background-image: url('../../../assets/img/gateway-dealbagrd.png');
                    /* background-size: cover; */
                    background-size: 100% 100%;
                    background-repeat: no-repeat;

                    img {
                        width: 50px;
                        height: 50px;
                    }

                    .cardDemoText {
                        margin-left: 16px;
                        flex: 1;
                        white-space: nowrap;
                        overflow-x: hidden;
                        text-overflow: ellipsis;

                        .cardDemoTitle {
                            font-family: PingFang SC;
                            font-weight: bold;
                            font-size: 16px;
                            color: #515A6E;
                            margin-bottom: 8px;
                            white-space: nowrap;
                            overflow-x: hidden;
                            text-overflow: ellipsis;
                        }

                        .cardDemoContent {
                            font-family: PingFangSC-Regular;
                            font-size: 13px;
                            color: #515A6E;
                            white-space: nowrap;
                            overflow-x: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }

            .cardDemoFooter {
                height: 40px;
                background: #F3F5F8;
                display: flex;
                align-items: center;
                justify-content: space-around;
                box-shadow: 0px 2px 8px #F3F5F8;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top: 1px solid #DCDFE8;

                .dotDemo {
                    display: flex;
                    align-items: center;
                    font-size: 14px;

                    .dotred {
                        background: #F50000;
                        border-radius: 50%;
                        font-size: 14px;
                    }

                    .dotgreen {
                        background: #34B576;
                        border-radius: 50%;
                    }

                    .textRed {
                        font-family: PingFang SC;
                        font-weight: 400;
                        color: #F50000;
                    }

                    .textGreen {
                        font-family: PingFang SC;
                        font-weight: 400;
                        color: #34B576;
                    }
                }

                .editDemo {
                    font-size: 14px;
                    color: #536387;
                    display: flex;
                    align-items: center;

                    .editPen {
                        margin-right: 4px;
                    }

                    .filled {
                        margin-left: 12px;
                    }

                    .edit {
                        display: flex;
                        align-items: center;
                        cursor: pointer
                    }
                }

                .install {
                    margin-right: 24px;
                    height: 28px;
                    background: rgba(1, 71, 235, 0.05);
                    border-radius: 0px;
                    border: 1px solid #0147EB;
                    font-size: 12px;
                }
            }

        }
    }

    .drawerClass {
        background-color: red;

        ::v-deep .el-drawer__wrapper {
            height: 100%;
            position: absolute !important;
            overflow: hidden;

            .el-drawer__header {
                height: 40px;
            }

            .title_header {
                padding: 0 24px 12px;
                border-bottom: 1px solid #dbdfe9;

                .el-button {
                    padding: 0;
                    margin-right: 16px;
                }

                .title {
                    display: inline-block;
                    font-size: 18px;
                    margin: 0;
                    vertical-align: middle;
                    padding-left: 20px;
                    color: #181f2d;
                    font-weight: bold;
                    border-left: 1px solid #dbdfe9;
                }
            }

            .el-drawer__body {
                padding: 20px;
            }
        }
    }

}
</style>