<template>
    <div id="main"
    v-loading="lookFlag"
    element-loading-background="rgba(255, 255, 255, 0.5)"
    >
      <div id="diagramContainer"></div>
    </div>
  </template>

  <script>
    import jsplumb from 'jsplumb'
    import 'jquery-ui/ui/widgets/draggable'
    import 'jquery-ui/ui/widgets/droppable'
    import 'jquery-ui/ui/widgets/resizable'
    import $ from 'jquery'
    const anchors = [0, 1]
  export default {
      props: ['nodesList', 'relationList', 'isDraggable','lookFlag'],
      data () {
          return {
              canDraggable: this.isDraggable,
              anchors,
              jsPlumb: {},
              UUID: require('uuid'),
              common: {
              isSource: true,
              isTarget: true,
              connector: 'Flowchart', // 连接线线性
              hoverPaintStyle: {
                  outlineStroke: 'green',
                  fill: 'green'
              }, // 鼠标悬停在端点样式
              endpointHoverStyle: {
                  fill: 'green'
              },
              paintStyle: { fill: '#456', stroke: '#456', radius: 3 }, // 端点样式
              maxConnections: -1, // 端点最大连接数 -1为无限制
              connectorOverlays: [['Arrow', { width: 12, length: 12, location: 1 }]] // 端点箭头样式
              },
              activeNode: ''
          }
      },
      mounted () {
        this.initJsplumb()
      },
      watch: {
      nodesList: function (newVal, oldVal) {
        if (newVal) {
          $('#diagramContainer').empty()
            newVal.forEach((item) => {
            item.classList = ['node', 'jtk-endpoint-anchor', 'jtk-managed', 'jtk-draggable', 'jtk-connected', 'ui-draggable-handle', 'ui-draggable']
            this.addNode(item)
        })
        }
      },
      relationList: function (newVal, oldVal) {
        if (newVal) {
            newVal.forEach((item) => {
                this.jsPlumb.connect({ uuids: [item.source, item.target] })
          })
        }
      }
    },
      methods: {
        initJsplumb () {
          const jsPlumb = jsplumb.jsPlumb
          this.jsPlumb = jsPlumb
           const _this = this
          jsPlumb.ready(function () {
            jsPlumb.setContainer('diagramContainer') // 节点只能在固定该区域内移动。
            // 一般来说拖动创建的链接，可以再次拖动，让链接断开。如果不想触发这种行为，可以设置。
            jsPlumb.importDefaults({
              ConnectionsDetachable: false
            })
            // $('#toolPlan').children().draggable({
            //   helper: 'clone',
            //   scope: 'plan' // 值随意命名
            // })
            // $('#diagramContainer').droppable({
            //   scope: 'plan',
            //   drop: function (event, ui) {
            //     _this.addNode({
            //       id: _this.UUID.v1(),
            //       left: parseInt(ui.offset.left - $(this).offset().left),
            //       top: parseInt(ui.offset.top - $(this).offset().top),
            //       text: $(ui.helper).html(),
            //       classList: ui.draggable[0].className.split(' ')
            //     })
            //   }
            // })
            jsPlumb.bind('click', function (conn) {
                jsPlumb.deleteConnection(conn)
            })

            // 当链接建立前判定,防止同一节点自环
            jsPlumb.bind('beforeDrop', function (conn) {
              const relations = jsplumb.jsPlumb.getAllConnections()
              if (conn.sourceId !== conn.targetId && !_this.checkRelation(relations, conn)) {
                return true
              } else {
                _this.$message.warning('连接失败，流程设计有闭环/当前节点已连接关系')
                return false
              }
            })
          })
          // this.loadData()
        },
        checkRelation (relations, conn) {
          if (!relations) {
            return false
          }
         const map = new Map()
          map.set(conn.sourceId, [conn.targetId])
          const thisA = this
          for (let i = 0; i < relations.length; i++) {
            const relation = relations[i]
            const sourceId = relation.sourceId
            const targetId = relation.targetId
            if (conn.sourceId === sourceId && conn.targetId === targetId) {
              return true
            }
            let list = map.get(sourceId)
            if (!list) {
              list = []
            }
            list.push(targetId)
            map.set(sourceId, list)
          }
          let flag = false
          map.forEach(function (value, key) {
            const arr = []
              if (thisA.recursionRelation(map, key, key, arr)) {
                   flag = true
                   return true
              }
          })
         return flag
        },
        // 递归
        recursionRelation (map, key, keyT, arr) {
          const valueList = map.get(keyT)
          if (!valueList) {
            return false
          }
          if (valueList.indexOf(key) !== -1) {
            return true
          }
         for (let i = 0; i < valueList.length; i++) {
           const nodeId = valueList[i]
             if (arr.indexOf(nodeId) !== -1) {
               continue
             }
             arr.push(nodeId)
            if (this.recursionRelation(map, key, nodeId, arr)) {
              return true
            }
         }
          return false
        },

        /**
       * 保存图形数据
       */
      clearNode () {
          this.jsPlumb.deleteEveryEndpoint()
          this.jsPlumb.deleteEveryConnection()
            $('#diagramContainer').empty()
          // this.jsPlumb.empty('#diagramContainer')
      },
        // 修改节点名称
        changeNodeName (nodeId, nodeName) {
          $(`#${nodeId} span`).html(nodeName)
        },
      saveData () {
        // 节点信息
         const nodesInfo = this.jsPlumb.getManagedElements()
        // 关系信息
         const ledgeInfo = this.jsPlumb.getAllConnections()
         const nodes = []
         const ledges = []
        let temp
        for (const item in nodesInfo) {
          // if (Object.prototype.hasOwnProperty.call(nodesInfo, "item")) {
            temp = nodesInfo[item]
          const id = $(nodesInfo[item].el).attr('id')
          const left = $('#' + id).css('left').split('p')[0]
          const top = $('#' + id).css('top').split('p')[0]
            nodes.push({
              id: id,
              left: left,
              top: top,
              text: temp.el.innerText
              // anchors: this.jsPlumb.getEndpoints(temp.el).map((i) => {
              //   return i.getUuid()
              // }),
              // classList: temp.el.className.split(' ')
            })
          // }
        }
        // 边信息
        for (const item in ledgeInfo) {
          // if (Object.prototype.hasOwnProperty.call(ledgeInfo, "item")) {
            temp = ledgeInfo[item]
            ledges.push({
              id: temp.sourceId,
              nextId: temp.targetId
            })
          // }
        }
        const datas = {
          nodes: nodes,
          ledges: ledges
        }
        // datas为图形数据，在http请求中将datas直接作为传输数据对
        this.$emit('saveData', datas)
      },
      /**
       * 添加节点，并且为节点添加四周端点、可拖拽、双击事件、悬停显示删除按钮
       * @param node json对象，包含了id、left、top、classList、text
       */
      addNode (node) {
        node.id = '_' + node.id
        $('#diagramContainer').append(
          '<div class="' +
            node.classList.join(' ') +
            '" id="' +
            node.id +
            '" >' +
            '</div>'
        )
        $('#' + node.id).append(
            '<span style="padding:0 10px;width:100%;height:100%;display:inline-block;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;">' + node.text + '</span>'
        )
        $('#' + node.id).append(
          '<img id="' +
            node.id +
            '-img" style="position:absolute;z-index:1;display:none;width: 50px"  src="data:image/png;base64,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"  />'
        )
        $('#' + node.id + '-img')
          .css('left', 100)
          .css('top', -25)
        $('#' + node.id)
          .css('left', node.left)
          .css('top', node.top)
        this.addEndpoint(node, this.common)
        this.draggable(node.id)
        if (this.canDraggable) {
          this.deleteNode(node)
        }
        this.doubleclick(node.id, node)
      },
      /**
       * 为节点添加四周端点
       * @param id 节点ID
       * @param common 绘制节点的默认参数
       * @param anchor 端点UUID数组
       */
      addEndpoint (node, common = {}) {
            this.jsPlumb.addEndpoint(
              node.id,
            {
              anchors: ['Right'],
              uuid: 'right' + node.id
            },
            common
          )
           this.jsPlumb.addEndpoint(
              node.id,
            {
              anchors: ['Left'],
              uuid: 'left' + node.id
            },
            common
          )
      },
      /**
       * 允许节点拖拽且网格对齐
       * @param id 节点ID
       */

      draggable (id) {
        if (this.canDraggable) {
          this.jsPlumb.draggable(id, {
            containment: 'parent',
            grid: [10, 10]
          })
        }
      },
      /**
       * 双击节点触发事件
       * @param id
       */
      doubleclick (id) {
        const _this = this
        $('#' + id).dblclick(function () {
          _this.activeNode = id
          // var text = $(`#${id} span`).text()
          // $(this).append("<input type='text'   value='" + text + "' />")
          // $(this).mouseleave(function () {
          //   $(`#${id} span`).html($("input[type='text']").val())
          //   $("input[type='text']").remove()
          // })
        })
      },
      /**
       * 鼠标悬停 显示可删除按钮
       * @param node
       */
      deleteNode (node) {
          const _this = this
        $('#' + node.id).mouseenter(function () {
          $('#' + node.id + '-img').show()
        })
        $('#' + node.id + '-img').click(function () {
          _this.jsPlumb.remove(node.id)
          _this.$emit('deleteNode', node)
        })
        $('#' + node.id).mouseleave(function () {
          $('#' + node.id + '-img').hide()
        })
      }
      }
  }
  </script>

  <style >
  /* #HelloWorld {
    position: relative;
    height: 250px;
  } */
  #toolPlan {
    width: 19%;
    height: 250px;
    border: 1px solid gray;
    left: 0;
    position: absolute;
  }
  #main {
    /* left: 20%; */
    margin-top: 10px;
    width: 100%;
    height: 250px;
    position: relative;
  }
  #operPlan {
    right: 0;
    width: 19%;
    height: 250px;
    border: 1px solid gray;
    position: absolute;
  }
  #diagramContainer {
    position: relative;
    /* float: left; */
    width: 100%;
    height: 100%;
    border: 1px solid gray;
    background-repeat: repeat;
  }
  .node {
    -moz-border-radius: 0.5em;
    border-radius: 0.5em;
    opacity: 0.8;
    filter: alpha(opacity=80);
    border: 1px solid #346789;
    text-align: center;
    z-index: 20;
    position: absolute;
    color: black;
    font-size: 9pt;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    background-color: #f0f7ff;
    border: 1px solid #1879ff;
    width: 120px;
    height: 35px;
  }
  .radius {
    border-radius: 25em;
  }
  .node:hover {
    box-shadow: 2px 2px 19px #444;
    -o-box-shadow: 2px 2px 19px #444;
    -webkit-box-shadow: 2px 2px 19px #444;
    -moz-box-shadow: 2px 2px 19px #444;
    opacity: 0.8;
    filter: alpha(opacity=80);
  }
  .jtk-endpoint {
    z-index: 21;
  }
  .jtk-endpoint:hover {
    cursor: pointer;
  }



.el-loading-parent--relative  .el-loading-spinner{
    display: none !important;
  }



  </style>
