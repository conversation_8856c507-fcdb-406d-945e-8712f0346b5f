<template>
  <div class="drawer-box">
    <div style="margin-left: 20px;margin-bottom: 20px">
      <span class="contentS">{{ deviceData.name }}</span>
    </div>
    <div class="info-box">
      <div class="info-item">
        <span class="labelS">登录账户：</span>
        <span class="contentS">{{ deviceData.username }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">联系电话：</span>
        <span class="contentS">{{ deviceData.phone }}</span>
      </div>
      <div class="info-item">
        <img class="sbtzxq" :src="require('@/assets/imagesAssets/sbtzxq.png')">
      </div>
      <!--      <div class="info-item">-->
      <!--        <span class="labelSMS">描述信息</span>-->
      <!--        <span class="content-status"-->
      <!--        >设备状态:-->
      <!--            <div v-if="deviceData.status == 0" class="sbzt">-->
      <!--              <span class="circle-red"></span>-->
      <!--              <p>故障</p>-->
      <!--            </div>-->
      <!--            <div v-if="deviceData.status == 1" class="sbzt">-->
      <!--              <span class="circle-green"></span>-->
      <!--              <p>正常</p>-->
      <!--            </div>-->
      <!--            <div v-if="deviceData.status == 2" class="sbzt">-->
      <!--              <span class="circle-yellow"></span>-->
      <!--              <p>带病运行</p>-->
      <!--            </div>-->
      <!--        </span>-->
      <!--        <span class="content-status"-->
      <!--        >使用状态:-->
      <!--          <div v-if="deviceData.useStatus == 0" class="sbzt">-->
      <!--            <span class="circle-red"></span>-->
      <!--            <p>禁用</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.useStatus == 1" class="sbzt">-->
      <!--            <span class="circle-green"></span>-->
      <!--            <p>闲置</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.useStatus == 2" class="sbzt">-->
      <!--            <span class="circle-blue"></span>-->
      <!--            <p>在用</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.useStatus == 3" class="sbzt">-->
      <!--            <span class="circle-purple"></span>-->
      <!--            <p>出租</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.useStatus == 4" class="sbzt">-->
      <!--            <span class="circle-yellow"></span>-->
      <!--            <p>报废</p>-->
      <!--          </div>-->
      <!--        </span>-->
      <!--        <span class="content-status"-->
      <!--        >设备等级:-->
      <!--          <div v-if="deviceData.deviceLevel == 'A'" class="sbzt">-->
      <!--            <span class="circle-blue"></span>-->
      <!--            <p>A(关键)</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.deviceLevel == 'B'" class="sbzt">-->
      <!--            <span class="circle-green"></span>-->
      <!--            <p>B(重点)</p>-->
      <!--          </div>-->
      <!--          <div v-if="deviceData.deviceLevel == 'C'" class="sbzt">-->
      <!--            <span class="circle-purple"></span>-->
      <!--            <p>C(一般)</p>-->
      <!--          </div>-->
      <!--        </span>-->
      <!--      </div>-->
    </div>
    <div class="tab-box">
      <el-tabs tab-position="left" style="font-size: 12px">
        <el-tab-pane label="基本信息" style="font-size: 12px">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="基本信息" imgUrl="yunwei">
                  <span class="slot">人员管理</span>
                </IconTitle>
                <table class="table_details" border="1">
                  <tr>
                    <th>用户名</th>
                    <td colspan="3">{{ deviceData.username }}</td>
                  </tr>
                  <tr>
                    <th>出生日期</th>
                    <td>{{ deviceData.birthday }}</td>
                    <th>性别</th>
                    <td v-if="deviceData.sex == 0">未知</td>
                    <td v-if="deviceData.sex == 1">男</td>
                    <td v-if="deviceData.sex == 2">女</td>
                  </tr>
                  <tr>
                    <th>资质证书</th>
                    <td colspan="3">{{ deviceData.certificate }}</td>
                  </tr>
                  <tr>
                    <th>所属部门</th>
                    <td>{{ deviceData.deptName }}</td>
                    <th>联系电话</th>
                    <td>{{ deviceData.phone }}</td>
                  </tr>
                  <tr>
                    <th>说明</th>
                    <td colspan="3">{{ deviceData.remark }}</td>
                  </tr>
                </table>

              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="维修工单">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="报修工单" imgUrl="yunwei">
                  <span class="slot">报修任务列表</span>
                </IconTitle>
                <template>
                  <el-table
                      ref="singleTable"
                      :data="maintenanceOrderData.records"
                      highlight-current-row
                      style="width: 100%">
                    <el-table-column
                        type="index"
                        label="序号"
                        align="center"
                        width="60">
                    </el-table-column>
                    <el-table-column
                        property="repairNum"
                        align="center"
                        label="维修编号"
                        width="140">
                    </el-table-column>
                    <el-table-column
                        property="deviceNum"
                        label="设备编号"
                        align="center"
                        width="150">
                    </el-table-column>
                    <el-table-column
                        property="deviceName"
                        label="设备名称"
                        align="center"
                        width="140">
                    </el-table-column>
                    <el-table-column
                        property="reportTime"
                        label="报修时间"
                        align="center"
                        width="140">
                    </el-table-column>
                  </el-table>
                  <pagination
                      style="float: right"
                      v-show="maintenanceOrderData.total > 0"
                      :total="maintenanceOrderData.total"
                      :page.sync="queryParams.pageNum"
                      :limit.sync="queryParams.pageSize"
                      @pagination="getMaintenanceOrder"
                  />
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="保养">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="保养工单" imgUrl="yunwei">
                    <span class="slot">保养任务列表</span>
                </IconTitle>
                <template>
                  <el-table
                      ref="singleTable"
                      :data="maintainByUserData.records"
                      highlight-current-row
                      style="width: 100%">
                    <el-table-column
                        type="index"
                        label="序号"
                        align="center"
                        width="60">
                    </el-table-column>
                    <el-table-column
                        property="planNum"
                        align="center"
                        label="计划编号"
                        width="110">
                    </el-table-column>
                    <el-table-column
                        property="planBeginTime"
                        label="开始时间"
                        align="center"
                        width="110">
                    </el-table-column>
                    <el-table-column
                        property="planEndTime"
                        label="结束时间"
                        align="center"
                        width="110">
                    </el-table-column>
                    <el-table-column
                        property="deviceNum"
                        label="设备编号"
                        align="center"
                        width="110">
                    </el-table-column>
                    <el-table-column
                        property="deviceName"
                        align="center"
                        label="设备名称"
                        width="110">
                    </el-table-column>
                  </el-table>
                  <pagination
                      style="float: right"
                      v-show="maintainByUserData.total > 0"
                      :total="maintainByUserData.total"
                      :page.sync="queryParams.pageNum"
                      :limit.sync="queryParams.pageSize"
                  />
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="点巡检">
          <IconTitle title="点巡检" imgUrl="yunwei">
            <span class="slot">巡检点检工单</span>
          </IconTitle>

          <template>
            <el-table
                ref="singleTable"
                :data="tallyTourData"
                highlight-current-row
                style="width: 100%">
              <el-table-column
                  type="index"
                  label="序号"
                  align="center"
                  width="50">
              </el-table-column>
              <el-table-column
                  property="taskNum"
                  align="center"
                  label="任务编号"
                  width="100">
              </el-table-column>
              <el-table-column
                  property="planName"
                  label="任务名称"
                  align="center"
                  width="100">
              </el-table-column>
              <el-table-column
                  property="status"
                  align="center"
                  label="状态"
                  width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 0"><el-tag type="info" effect="dark">未开始</el-tag></span>
                  <span v-if="scope.row.status == 1"><el-tag effect="dark">执行中</el-tag></span>
                  <span v-if="scope.row.status == 2"><el-tag type="warning" effect="dark">待核验</el-tag></span>
                  <span v-if="scope.row.status == 3"><el-tag type="success" effect="dark">已完成</el-tag></span>
                  <span v-if="scope.row.status == 4"><el-tag type="danger" effect="dark">已过期</el-tag></span>
                </template>
              </el-table-column>
              <el-table-column
                  property="shouldCheckNum"
                  label="应检设备数"
                  align="center"
                  width="100">
              </el-table-column>
              <el-table-column
                  property="ofCheckedNum"
                  label="已检设备数"
                  align="center"
                  width="100">
              </el-table-column>
              <el-table-column
                  property="abnormalNum"
                  align="center"
                  label="异常设备数"
                  width="100">
              </el-table-column>
            </el-table>
            <pagination
                style="float: right"
                v-show="tallyTourData.total > 0"
                :total="tallyTourData.total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getTallyTourByUserList"
            />
          </template>

        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getTallyTourByUserId} from "@/api/ems/inspection/task"
import {getMaintenanceOrder} from "@/api/ems/repair/order"
export default {
  name: "drawerCon",
  components: {
    IconTitle,
  },
  data() {
    return {
      cData: [],
      tallyTourData: [],
      maintenanceOrderData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      FDeviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "浙大中空",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "浙大中空",
        },
      ],
    };
  },
  props: {
    deviceData: {
      type: Object
    },
    maintainByUserData:{
      type: Object
    }
  },
  watch: {
    deviceData: function(newVal,oldVal){
      this.cData = newVal;  //newVal即是chartData
      let id = this.cData.userId;
      this.getTallyTourByUserList(id);
      this.getMaintenanceOrder(id);
    }
  },
  mounted() {
    // this.getTallyTourByUserList();

  },

  methods: {
    deviceSelectionChange() {
    },

    getMaintenanceOrder(id) {
      getMaintenanceOrder(id).then(res => {
        this.maintenanceOrderData = res.data.data;

      });
    },

    getTallyTourByUserList(id) {
      getTallyTourByUserId(id).then(res => {
        this.tallyTourData = res.data.data.records;
      })
    }


  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/mixin.scss";
@import "@/styles/color.scss";

::v-deep .el-drawer__body {
  overflow: auto;
}

.drawer-box {
  .info-box {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #f2f2f5;
    margin-bottom: 20px;
    margin-left: 50px;

  }

  .info-item {
    display: flex;
    margin-bottom: 10px;

    .sbtzxq {
      float: left;
      margin-top: -143px;
      margin-left: 250px;
    }

    .labelSMS {
      width: 15%;
      color: rgba(193, 200, 210, 100);
      font-size: 12px;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
    }

    .labelS {
      width: 15%;
      color: #888;
      font-size: 12px;
      margin-left: -50px;
    }

    .contentS {
      font-weight: bold;
      color: #101010;
      display: inline-block;
      font-size: 12px;
      margin-top: 0px;
    }

    .content-status {
      margin-right: 85px;
      font-size: 12px;
      display: flex;

      .sbzt {
        display: flex;
      }

      .circle-lan {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-lv {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, $theme);
      }

      .circle-zi {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-blue {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-green {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #5ec28e);
      }

      .circle-purple {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-red {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ff0000);
      }

      .circle-yellow {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ffff00);
      }
    }
  }

  .tab-box {
    .basic-box-con {
      overflow: auto;
      height: 500px;

      .basic-big {
        .box-card {
          padding: 10px;
          box-shadow: 0px 0px 7px 0px #eff2f5;
          margin: 5px;
          border-radius: 10px;

          .table_details {
            border: 1px solid #e8eef4;
            font-size: 12px;
            width: 644px;
            height: 181px;
            margin-top: 10px;

            td {
              padding-left: 10px;
            }
          }

          .card-image {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            margin-left: 10px;

            .image {
              left: 1435px;
              top: 776px;
              width: 124px;
              height: 113px;
              margin-top: 10px;
              margin-left: 5px;
              border-radius: 5px;
            }
          }

          .card-item {
            display: flex;
            align-items: center;
            margin-top: 10px;

            .icon-gongsi {
              color: #128bed;
            }

            .icon-map1 {
              color: #128bed;
            }

            span {
              display: inline-block;
              width: 15%;
              text-align: right;
              color: #888;
              margin-right: 20px;
            }

            .card-con {
              color: #101010;
            }
          }

          .image {
            left: 1435px;
            top: 776px;
            width: 116px;
            height: 83px;
          }

          .el-table {
            margin-top: 20px;
          }
        }
      }
    }
  }
}

</style>
