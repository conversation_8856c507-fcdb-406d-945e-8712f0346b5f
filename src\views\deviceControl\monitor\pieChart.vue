<template>
    <div :id="chartId" :style="{ height: height, width: width }" />
</template>
<script>
import * as echarts from "echarts";
require("echarts/theme/macarons");
import resize from "./resize";

export default {
    mixins: [resize],
    props: {
        chartId: {
            type: String,
            default: new Date().getTime(),
        },
        chartData: {
            type: Array,
            default: [],
        },
        width: {
            type: String,
            default: "100%",
        },
        height: {
            type: String,
            default: "100%",
        },
    },
    data() {
        return {};
    },
    watch: {
        chartData: {
            handler(val, oldVal) {
                this.initChart();
            },
            deep: true,
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
        });
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        initChart() {
            const { chartData } = this;
            const total = chartData.reduce((prev, next) => prev + next.value, 0)
            this.chart && this.chart.dispose();
            this.chart = echarts.init(document.getElementById(this.chartId));
            this.chart.setOption({
                title: {
                    textStyle: {
                        fontSize: 18,
                        color: "#181F2D",
                    },
                },
                tooltip: {
                    type: "axias",
                    formatter: (params) => {
                        return params.marker + params.name + '：' + params.percent + "%"
                    },
                },
                legend: {
                    type: "plain",
                    right: "10%",
                    top: "middle",
                    width: 110,
                    fontSize: 14,
                    formatter: (name) => {
                        let targetVal = 0
                        for (const item of chartData) {
                            if (item.name == name) {
                                targetVal = item.value
                            }
                        }
                        const percent = (targetVal / total * 100).toFixed(2);
                        return name + "（" + percent + "%）"
                    },
                },
                series: [
                    {
                        name: "30%",
                        type: "pie",
                        radius: ["30%", "50%"],
                        center: ["30%", "50%"],
                        labelLine: {
                            length: 15,
                            length2: 0,
                            maxSurfaceAngle: 80,
                        },
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderWidth: 10,
                            borderColor: "#fff",
                            color: function (params) {
                                return params.data.color
                            },
                        },
                        label: {
                            show: true,
                            position: "center",
                            color: "#515A6E",
                            formatter: '{num|总数量}' + '\n' + '{total|' + total + '}',
                            rich: {
                                num: {
                                    fontSize: 14,
                                    color: "#515A6E",
                                    lineHeight: 30,
                                },
                                total: {
                                    fontSize: 20,
                                    color: "#181F2D",
                                },
                            },
                        },
                        data: chartData
                    },
                ],
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.pieChart {
    flex: 1;
}
</style>
