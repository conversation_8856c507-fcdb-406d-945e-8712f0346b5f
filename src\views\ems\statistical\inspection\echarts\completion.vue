<template>
  <div id="completion" :style="{width: '600px', height: '300px'}"></div>
</template>

<script>
import {taskCompletionList} from "@/api/ems/statistical/insperction";

let numberEchartsOptions = {

  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '任务数',
      type: 'bar',
      color: '#63b2ee',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#63b2ee',
              fontSize: 12,
            },
          },
        },
      },
    },
    {
      name: '完成数',
      type: 'bar',
      color: '#76da91',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#76da91',
              fontSize: 12,
            },
          },
        },
      },
    },
    {
      name: '未完成数',
      type: 'bar',
      color: '#f89588',
      data: [],
      //显示数值
      itemStyle: {
        normal: {
          label: {
            show: true, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#f89588',
              fontSize: 12,
            },
          },
        },
      },
    }
  ]
};

export default {
  data() {
    return {
      allData: [],
      numberEchartsOptions,
      taskNumData: [],
      finishNumData: [],
      unfinishedNumData: []
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {


    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let completion = this.$echarts.init(document.getElementById('completion'));
      taskCompletionList().then(res => {
        this.allData = res.data.data;
        this.allData.forEach(function (item, index) {
          numberEchartsOptions.series[index].name = item.taskName;
          numberEchartsOptions.series[index].data = item.numCharts;
          // console.log("111>>>",numberEchartsOptions.series[index].data)
        });
        completion.setOption(this.numberEchartsOptions);
      });
    }
  }
}

</script>
