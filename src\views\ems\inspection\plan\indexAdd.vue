<template>
    <div class="add-box">
        <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="140px"
                size="small"
                class="demo-ruleForm"
        >
            <div class="info-box">
                <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
                <div class="info-from">
                    <el-form-item label="计划编号" prop="planNum">
                        <el-input v-model="form.planNum" :disabled="true" placeholder="无需填写自动生成"/>
                    </el-form-item>
                    <el-form-item label="计划名称" prop="planName">
                        <el-input v-model="form.planName" placeholder="请输入计划名称"/>
                    </el-form-item>
                    <el-form-item label="所属部门" prop="deptId">
                        <treeselect
                                v-model="form.deptId"
                                :options="treeDeptData"
                                :normalizer="normalizer"
                                placeholder="所属部门"
                        />
                    </el-form-item>
                    <el-form-item label="负责人" prop="liableUserId">
                        <el-input v-model="form.liableUserName" :disabled="true" placeholder="请选择负责人">
                            <el-button @click="liableUser()" resource="false" style="padding-right:10px" slot="suffix"
                                       type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="开始执行时间" prop="beginTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.beginTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择开始执行时间"
                                style="width: 100%;"
                                @blur="blurBeginTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束执行时间" prop="endTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.endTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择结束执行时间"
                                style="width: 100%;"
                                @blur="blurEndTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="巡检周期" prop="inspectCycle">
                                <el-input v-model="form.inspectCycle"
                                          placeholder="请输入巡检周期"
                                          style="width: 150px"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          @blur="blurInspectCycle"/>

                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="巡检周期单位" prop="cycleUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.cycleUnit"-->
                                <!--                                placeholder="请选择巡检周期单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <el-input v-model="form.cycleUnitName"
                                          style="width: 50px;" :disabled="true" placeholder="日"/>
                                <!--                        </el-select>-->
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                           <span>*巡检周期：开始执行时间-到—结束执行时间中间循环的周期，如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，巡检周期为1，代表在5天中，每天走一个循环</span>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="任务有效期" prop="effectiveTime">
                                <el-input v-model="form.effectiveTime"
                                          placeholder="请输入任务有效期"
                                          style="width: 150px"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          @blur="blurEffectiveTime"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="任务有效期单位" prop="effectiveUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.effectiveUnit"-->
                                <!--                                placeholder="请选择任务有效期单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName"
                                          style="width: 50px;" :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*任务有效期：开始执行时间-到—结束执行时间中间循环的周期中有多长时间为有效的，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，巡检周期为2，任务有效期为1，
                                代表在5天中，每两天走一个循环，在这两天中，只有前面一天才是任务执行期</span>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="提前提醒时间" prop="noticeTime">
                                <el-input v-model="form.noticeTime"
                                          placeholder="请输入提前提醒时间"
                                          style="width: 150px"
                                          oninput="value=value.replace(/[^\d]/g,'')"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="提前提醒时间单位" prop="noticeUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.noticeUnit"-->
                                <!--                                placeholder="请选择提前提醒时间单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName" style="width: 50px;"
                                          :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*提前提醒时间：根据巡检周期生成任务后，在移动端提前提醒执行人的时间，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，
                                巡检周期为1,提前提醒时间为1，那么会在2000/2/1号提醒执行人注意巡检待办任务</span>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="提前生成时间" prop="generateTime">
                                <el-input v-model="form.generateTime"
                                          placeholder="请输入提前生成时间"
                                          style="width: 150px"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          @blur="blurGenerateTime"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="提前生成时间单位" prop="generateUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.generateUnit"-->
                                <!--                                placeholder="请选择提前生成时间单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName" style="width: 50px;"
                                          :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*提前生成时间：开始执行时间-到—结束执行时间中间循环的周期中提前生成的时间，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，巡检周期为1，
                                提前生成时间为1，那么会在2000/2/1号生成任务</span>
                        </el-col>
                    </el-row>

                    <el-form-item label="审核人" prop="auditUserId">
                        <el-input v-model="form.auditUserName" :disabled="true" placeholder="请选择审核人">
                            <el-button @click="auditUser()" style="padding-right:10px" slot="suffix" type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="是否启用" prop="enable">
                        <el-radio-group v-model="form.enable" @change="handleEnable">
                            <el-radio :label="0">不启用</el-radio>
                            <el-radio :label="1">启用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="巡检设置" prop="inspectSettings" style="width: 100%"
                    >
                        <el-radio-group v-model="form.inspectSettings" >

<!--                            <el-radio :label="0" @mouseenter="inspectSettings_0()" @mouseleave="inspectSettings_0_1()">无需扫码</el-radio>-->
<!--                            <el-radio :label="1" @mouseenter="inspectSettings_1()" @mouseleave="inspectSettings_1_1()">扫一个二维码即可提交</el-radio>-->
<!--                            <el-radio :label="3" @mouseenter="inspectSettings_3()" @mouseleave="inspectSettings_3_1()">只需填写异常项</el-radio>-->
                            <el-tooltip effect="light"  content="不用扫码，巡检任务中点击设备即可填写" placement="top" @click.stop.prevent>
                                <el-radio :label="0"  >无需扫码</el-radio>
                            </el-tooltip>
                            <el-tooltip effect="light"  content="需要至少扫一个设备的二维码才能提交巡检记录，表示已经到达现场" placement="top" @click.stop.prevent>
                            <el-radio :label="1"  >扫一个二维码即可提交</el-radio>
                            </el-tooltip>
<!--                            <el-tooltip  effect="light"  popper-class="tooltip_3" content="需要填写异常项：只有检查有异常的项目才需要填写，其他不填写的项目系统自动默认为正常" placement="top" @click.stop.prevent>-->
<!--                                <el-radio :label="3"  >只需填写异常项</el-radio>-->
<!--                            </el-tooltip>-->
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="说明" prop="remark" style="width: 100%;">
                        <el-input type="textarea" v-model="form.remark" placeholder="请输入说明"/>
                    </el-form-item>
                    <el-form-item label="点检策略" prop="strategyId">
                        <el-input v-model="form.strategyName" :disabled="true" placeholder="请关联点检策略">
                            <el-button @click="strategy()" resource="false" style="padding-right:10px" slot="suffix"
                                       type="text">关联
                            </el-button>
                        </el-input>
                    </el-form-item>
                </div>
                <!--用户弹框-->
                <el-dialog :title="user.title" :visible.sync="user.open" width="1000px" append-to-body>
                    <el-row :gutter="20">
                        <!--部门数据-->
                        <el-col :span="4" :xs="24">
                            <div class="head-container">
                                <div class="tree">
                                    <el-tree
                                            :data="treeDeptData"
                                            :props="defaultProps"
                                            :expand-on-click-node="false"
                                            :filter-node-method="filterNode"
                                            ref="tree"
                                            default-expand-all
                                            @node-click="handleNodeClick"
                                    />
                                </div>
                            </div>

                        </el-col>
                        <!--用户数据-->
                        <el-col :span="20" :xs="24">
                            <el-table v-loading="user.loading" :data="userList"
                                      @row-click="userRowClick">
                                <el-table-column label="用户编号" align="center" key="userId" prop="userId"/>
                                <el-table-column label="用户名称" align="center" key="userName" prop="userName"/>
                                <el-table-column label="部门" align="center" key="dept.deptName" prop="dept.deptName"/>
                                <el-table-column label="角色" align="center" key="roleName" prop="roleName"/>
                                <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber"/>
                                <el-table-column label="状态" align="center" key="status">
                                    <template slot-scope="scope">
                                          <span>{{
                                            scope.row.status === '0'
                                              ? "正常"
                                              : scope.row.status == '1'
                                              ? "停用": ""
                                          }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="创建时间" align="center" prop="createTime"
                                                 width="160">
                                </el-table-column>
                            </el-table>

                            <!-- <pagination
                                    v-show="userQueryParams.total>0"
                                    :total="userQueryParams.total"
                                    :page.sync="userQueryParams.pageNum"
                                    :limit.sync="userQueryParams.pageSize"
                                    @pagination="getUserList"
                            /> -->
                        </el-col>
                    </el-row>
                </el-dialog>
                <!--路线策略弹框-->
                <el-dialog :title="strateg.title" :visible.sync="strateg.open" width="700px" append-to-body>
                    <el-table v-loading="strateg.loading" :data="strategyList"
                              @row-click="strategyRowClick">
                        <el-table-column prop="id" v-if="false"/>
<!--                        <el-table-column label="策略编号" align="center" key="userId" prop="strategyNum"/>-->
                        <el-table-column label="策略名称" align="center" key="username" prop="strategyName"/>
                        <el-table-column label="状态" align="center" key="enable" prop="enable">
                            <template slot-scope="scope">
                                          <span>{{
                                            scope.row.enable == 0
                                              ? "不启用"
                                              : scope.row.enable == 1
                                              ? "启用": ""
                                          }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="部门" align="center" key="deptName" prop="deptName"/>
                        <el-table-column label="创建时间" align="center" prop="createTime"
                                         width="160">
                        </el-table-column>
                    </el-table>

                    <pagination
                            v-show="strategyQueryParams.total>0"
                            :total="strategyQueryParams.total"
                            :page.sync="strategyQueryParams.pageNum"
                            :limit.sync="strategyQueryParams.pageSize"
                            @pagination="getStrategyList"
                    />
                </el-dialog>
            </div>
            <!--            <div class="info-box">-->
            <!--                <IconTitle title="负责人" imgUrl="yunwei"></IconTitle>-->
            <!--                <el-table v-loading="liableUser.loading" :data="liableUserList">-->
            <!--                    <el-table-column label="id" align="center" prop="id" v-if="false"/>-->
            <!--                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>-->
            <!--                    <el-table-column label="设备名称" align="center" prop="deviceName"/>-->
            <!--                    <el-table-column label="品牌" align="center" prop="brandNewName"/>-->
            <!--                    <el-table-column label="规格型号" align="center" prop="specification"/>-->
            <!--                </el-table>-->
            <!--            </div>-->
            <div class="info-box">
                <IconTitle title="巡检设备" imgUrl="yunwei"></IconTitle>
                <!--                @selection-change="handleSelectionChange"-->
                <el-table v-loading="device.loading" :data="deviceList">
                    <el-table-column
                            label="序号"
                            width="70px">
                        <template slot-scope="scope">
                            {{scope.$index+1}}
                        </template>
                    </el-table-column>
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                    <el-table-column label="名称" align="center" prop="deviceName"/>
                    <el-table-column label="所属部门" align="center" prop="deptName"/>
                    <el-table-column label="类别" align="center" prop="categoryName">
                    </el-table-column>
                    <el-table-column label="规则型号" align="center" prop="specification"/>
                    <el-table-column label="位置" align="center" prop="locationName"/>
                </el-table>

                <pagination
                        v-show="deviceQueryParams.total>0"
                        :total="deviceQueryParams.total"
                        :page.sync="deviceQueryParams.pageNum"
                        :limit.sync="deviceQueryParams.pageSize"
                        @pagination="getDeviceList"
                />
            </div>
            <div class="info-btn-box">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
        </el-form>

    </div>

</template>
<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import ImageUpload from "@/components/ImageUpload/index.vue";
    import {fetchListTree} from "@/api/ems/equipment/category";
    import {getBrandList} from "@/api/ems/equipment/brand";
    import {fetchTree} from "@/api/admin/dept";
    import Treeselect from "@riophae/vue-treeselect";
    import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    import {mapGetters} from "vuex";
    import {getUser} from "@/api/system/user";
    import {strategyFetchList, deviceList_strategyId, strategyGetObj} from '@/api/ems/inspection/emsinsinspectstrategy'
    import {planGetObj, planAddObj, planPutObj, planFetchList, planDelObj} from '@/api/ems/inspection/plan'
    import { listUser } from '@/api/system/user'

    import user from "../../../../store/modules/user";

    export default {
        name: "AddIndex",
        components: {
            IconTitle,
            Treeselect,
            ImageUpload,
        },
        props: {
            id: {
                type: Number,
            },
        },
        data() {
            return {
                defaultProps: {
                    children: "children",
                    label: "label",
                },
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条,
                },
                // 用户数组
                userList: [],
                //用户数据分页
                userQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                    deptId: null,
                },
                user: {
                    title: "",
                    open: false,
                    loading: false,
                    type: null,
                },
                strategyList: [],
                strategyQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                strateg: {
                    title: "",
                    open: false,
                    loading: false,
                },
                deviceList: [],
                device: {
                    loading: false,
                },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                list: [],
                treeData: [],
                loading: false,
                categoryList: [], //设备类别
                brandList: [], //设备品牌
                treeDeptData: [], //部门
                // 一天的毫秒数
                dayTime: 86400000,
                cycleUnitName: "日",
                form: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,


                    cycleUnit: 1,
                    effectiveTime: null,
                    effectiveUnit: 1,
                    generateTime: null,
                    generateUnit: 1,
                    noticeTime: null,
                    noticeUnit: 1,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                },
                oldForm: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: 1,
                    effectiveTime: null,
                    effectiveUnit: 1,
                    generateTime: null,
                    generateUnit: 1,
                    noticeTime: null,
                    noticeUnit: 1,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                },
                rules: {
                    planName: [
                        {required: true, message: '请输入计划名称', trigger: 'blur'}
                    ],
                    deptId: [
                        {required: true, message: '请选择部门', trigger: 'blur'}
                    ],
                    liableUserId:[
                        {required: true, message: '请选择负责人', trigger: 'blur'}
                    ],
                    beginTime:[
                        {required: true, message: '请选择开始执行时间', trigger: 'blur'}
                    ],
                    endTime:[
                        {required: true, message: '请选择结束执行时间', trigger: 'blur'}
                    ],
                    // inspectCycle:[
                    //     {required: true, message: '请输入巡检周期', trigger: 'blur'}
                    // ],
                    // effectiveTime:[
                    //     {required: true, message: '请输入任务有效期', trigger: 'blur'}
                    // ],
                    auditUserId:[
                        {required: true, message: '请选择审核人', trigger: 'blur'}
                    ],

                    strategyId:[
                        {required: true, message: '请选择点检策略', trigger: 'blur'}
                    ],
                },
                //所有的设备数据
                deviceNewList: [],
                coverImgTem: [],
                imgArrayTem: [],
                dialogVisible: false,
            };
        },
        created() {
            this.getSelect();
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
                };
            },
        },
        mounted() {
            this.reset();
            if (this.id > 0) {
                planGetObj(this.id).then((res) => {
                    this.oldForm = res.data
                    this.oldForm.enable = parseInt(this.oldForm.enable);
                    this.oldForm.inspectSettings = parseInt(this.oldForm.inspectSettings);
                    strategyGetObj(this.oldForm.strategyId).then((res) => {
                        this.oldForm.strategyName = res.data.strategyName;
                    });
                    deviceList_strategyId(Object.assign(
                        {
                            current: this.deviceQueryParams.pageNum,
                            size: this.deviceQueryParams.pageSize,
                        },
                        {id: this.oldForm.strategyId}
                        )
                    ).then(response => {
                        this.deviceList = response.data.records;
                        this.deviceQueryParams.total = response.data.total;
                        this.device.loading = false;
                    });
                    if (this.oldForm.liableUserId!=null) {
                        getUser(this.oldForm.liableUserId).then(res => {
                            this.oldForm.liableUserName = res.data.userName
                            this.userList = [];
                            var o = {};
                            o.name = this.oldForm.liableUserName;
                            o.sex = "未知"
                            o.zs = "未知"
                            this.userList.push(o);
                            this.user.loading = false;
                        })
                    }
                    if (this.oldForm.auditUserId!=null){
                        getUser(this.oldForm.auditUserId).then(res =>{
                            this.oldForm.auditUserName=res.data.userName
                        })
                    }
                    this.form = this.oldForm;
                });
            }
        },
        methods: {
            liableUser() {
                this.user.title = "选择负责人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 1;
                this.getUserList()
            },
            auditUser() {
                this.user.title = "选择审核人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 0;
                this.getUserList()
            },
            deviceGetList() {
                this.device.loading = true;
            },
            userRowClick(row, event, column) {
                if (this.user.type == 1) {
                    this.form.liableUserId = row.userId;
                    this.form.liableUserName = row.userName;
                } else if (this.user.type == 0) {
                    this.form.auditUserId = row.userId;
                    this.form.auditUserName = row.userName;
                }
                this.user.open = false;
            },
            // 筛选节点
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            // 节点单击事件
            handleNodeClick(data) {
                this.userQueryParams.deptId = data.id;
                this.getUserList();
            },
            getUserList() {
                listUser(Object.assign(
                    {
                        current: this.userQueryParams.pageNum,
                        size: this.userQueryParams.pageSize,
                    },
                    {deptId: this.userQueryParams.deptId}
                    )
                ).then(response => {
                    this.userList = response.rows;
                    this.userQueryParams.total = response.total;
                    this.user.loading = false;
                });
            },
            blurBeginTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('开始日期必须小于结束日期，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
                if (this.form.beginTime != null) {
                    const beginTime = this.form.beginTime;
                    const startTime = new Date(beginTime).getTime();
                    const time = new Date(new Date(new Date().toLocaleDateString()).getTime())
                    if (startTime < time) {
                        this.$message.error('开始日期必须大于当前时间，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurEndTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('结束日期必须大于开始日期，请重新选择！')
                        this.form.endTime = ''
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurInspectCycle() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    const time = endTime - startTime;
                    const timeSum = this.dayTime * this.form.inspectCycle
                    if (time < timeSum) {
                        this.$message.error('巡检周期不能大于总时间，请重新选择！')
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                } else {
                    if (this.form.inspectCycle != null) {
                        this.$message.error('请先填写开始时间和结束时间！');
                        this.form.inspectCycle = null;
                    }

                }
            },
            blurGenerateTime() {
                if (this.form.generateTime != null && this.form.inspectCycle) {
                    if (this.form.inspectCycle < this.form.generateTime) {
                        this.$message.error('提前生成时间不能大于巡检周期，请重新选择！')
                        this.form.generateTime = null;
                    }
                } else {
                    this.$message.error('请先填写巡检周期！')
                    this.form.generateTime = null;
                }
            },
            blurEffectiveTime() {
                if (this.form.effectiveTime != null && this.form.inspectCycle) {
                    if (this.form.inspectCycle < this.form.effectiveTime) {
                        this.$message.error('任务有效期不能大于巡检周期，请重新选择！')
                        this.form.effectiveTime = null;
                    }
                } else {
                    this.$message.error('请先填写巡检周期！')
                    this.form.effectiveTime = null;
                }
            },
            handleEnable() {
                if (this.form.enable == 1) {
                    this.$confirm('是否确认选择启用！一旦启用提交后无法修改', '警告', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(res => {
                        this.form.enable = 1;
                        return;
                    }).catch(err => {
                        this.form.enable = 0;
                        return;
                    })
                }
            },
            strategy() {
                this.strateg.title = "关联点检策略";
                this.strateg.loading = true;
                this.strateg.open = true;
                this.strateg.type = 0;
                this.getStrategyList()
            },
            strategyRowClick(row, event, column) {
                this.form.strategyId = row.id;
                this.form.strategyName = row.strategyName;
                this.strateg.open = false;
                this.getDeviceList();
            },

            getStrategyList() {
                strategyFetchList(Object.assign(
                    {
                        current: this.strategyQueryParams.pageNum,
                        size: this.strategyQueryParams.pageSize,
                    },
                    {enable: "1"}
                    )
                ).then(response => {
                    this.strategyList = response.data.records;
                    // this.strategyQueryParams.total = response.data.total;
                    this.strateg.loading = false;
                });
            },
            getDeviceList() {
                this.device.loading = true;
                deviceList_strategyId(Object.assign(
                    {
                        current: this.deviceQueryParams.pageNum,
                        size: this.deviceQueryParams.pageSize,
                    },
                    {id: this.form.strategyId}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    this.deviceQueryParams.total = response.data.total;
                    this.device.loading = false;
                });
            },
            submitForm(formName) {
                const startDate = this.form.beginTime
                const endDate = this.form.endTime
                const startTime = new Date(startDate).getTime()
                const endTime = new Date(endDate).getTime()
                const time = new Date(new Date(new Date().toLocaleDateString()).getTime())
                if (startTime < time) {
                    this.$message.error('开始日期必须大于当前时间，请重新选择！')
                    this.form.beginTime = null
                    this.form.inspectCycle = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return
                }
                if (startTime > endTime) {
                    this.$message.error('结束日期必须大于开始日期，请重新选择！')
                    this.form.endTime = ''
                    this.form.inspectCycle = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return
                }

                if(this.form.inspectCycle!=null){
                    const timeSum = this.dayTime * this.form.inspectCycle
                    const time1 = endTime - startTime;
                    if (time1 < timeSum) {
                        this.$message.error('巡检周期不能大于总时间，请重新选择！')
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                        return
                    }
                }else {
                    this.form.inspectCycle=0;
                }
                if(this.form.noticeTime==null){
                    this.form.noticeTime=0;
                }
                if (this.form.generateTime != null && this.form.inspectCycle>0) {
                    if (this.form.inspectCycle < this.form.generateTime) {
                        this.$message.error('提前生成时间不能大于巡检周期，请重新选择！')
                        this.form.generateTime = null;
                        return
                    }
                }else {
                    this.form.generateTime=0;
                }
                if (this.form.effectiveTime != null && this.form.inspectCycle>0) {
                    if (this.form.inspectCycle < this.form.effectiveTime) {
                        this.$message.error('任务有效期不能大于巡检周期，请重新选择！')
                        this.form.effectiveTime = null;
                        return
                    }
                }
                let data = JSON.parse(JSON.stringify(this.form));
                this.$refs[formName].validate((valid) => {
                    if (valid){
                        if (data.id) {
                            planPutObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("修改成功");
                            });
                        } else {
                            planAddObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("新增成功");
                            });
                        }
                    }
                });
            },

            reset() {
                this.form = {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    inspectCycle: null,
                    cycleUnit: 1,
                    effectiveTime: null,
                    effectiveUnit: 1,
                    generateTime: null,
                    generateUnit: 1,
                    noticeTime: null,
                    noticeUnit: 1,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                };
                //this.resetForm("form");
            },
            getSelect() {
                // remote("ins_inspect_items_type").then(response => {
                //     this.typeList = response.data;
                // });
                // fetchListTree("").then((res) => {
                //     this.categoryList = res.data ? res.data : [];
                // });
                // getBrandList().then((res) => {
                //     this.brandList = res.data;
                // });
                //部门
                fetchTree().then((response) => {
                    this.treeDeptData = response.data;
                });
            },
            normalizer(node) {
                if (node.children && !node.children.length) {
                    delete node.children;
                }
                return {
                    id: node.id,
                    label: node.label,
                    children: node.children,
                };
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss">
    .add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;

            }

        }
    }
</style>

<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }


    .el-popup-parent--hidden >.tooltip_3{
        width:	24px;
        height: auto;
    }
</style>
