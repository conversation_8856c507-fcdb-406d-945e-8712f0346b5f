<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="任务编号" prop="taskNum">
      <el-input v-model="dataForm.taskNum" placeholder="任务编号"></el-input>
    </el-form-item>
    <el-form-item label="计划id" prop="planId">
      <el-input v-model="dataForm.planId" placeholder="计划id"></el-input>
    </el-form-item>
    <el-form-item label="计划开始时间" prop="planBeginTime">
      <el-input v-model="dataForm.planBeginTime" placeholder="计划开始时间"></el-input>
    </el-form-item>
    <el-form-item label="计划结束时间" prop="planEndTime">
      <el-input v-model="dataForm.planEndTime" placeholder="计划结束时间"></el-input>
    </el-form-item>
    <el-form-item label="任务状态(0未开始 1执行中 2待核验 3已完成 4已过期)" prop="status">
      <el-input v-model="dataForm.status" placeholder="任务状态(0未开始 1执行中 2待核验 3已完成 4已过期)"></el-input>
    </el-form-item>
    <el-form-item label="开始执行时间" prop="executeTime">
      <el-input v-model="dataForm.executeTime" placeholder="开始执行时间"></el-input>
    </el-form-item>
    <el-form-item label="执行结束时间" prop="executeEndTime">
      <el-input v-model="dataForm.executeEndTime" placeholder="执行结束时间"></el-input>
    </el-form-item>
    <el-form-item label="延误天数" prop="delayDay">
      <el-input v-model="dataForm.delayDay" placeholder="延误天数"></el-input>
    </el-form-item>
    <el-form-item label="是否启用(0不启用 1启用)" prop="enable">
      <el-input v-model="dataForm.enable" placeholder="是否启用(0不启用 1启用)"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createBy">
      <el-input v-model="dataForm.createBy" placeholder="创建者"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updateBy">
      <el-input v-model="dataForm.updateBy" placeholder="更新者"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    <el-form-item label="删除标志(0正常 1删除)" prop="delFlag">
      <el-input v-model="dataForm.delFlag" placeholder="删除标志(0正常 1删除)"></el-input>
    </el-form-item>
    <el-form-item label="租户Id" prop="tenantId">
      <el-input v-model="dataForm.tenantId" placeholder="租户Id"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
    import {getObj, addObj, putObj} from '@/api/ems/inspection/emsinsinspecttask'

    export default {
    data () {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          id: 0,
          taskNum: '',
          planId: '',
          planBeginTime: '',
          planEndTime: '',
          status: '',
          executeTime: '',
          executeEndTime: '',
          delayDay: '',
          enable: '',
          createBy: '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          remark: '',
          delFlag: '',
          tenantId: ''
        },
        dataRule: {
          taskNum: [
            { required: true, message: '任务编号不能为空', trigger: 'blur' }
          ],
          planId: [
            { required: true, message: '计划id不能为空', trigger: 'blur' }
          ],
          planBeginTime: [
            { required: true, message: '计划开始时间不能为空', trigger: 'blur' }
          ],
          planEndTime: [
            { required: true, message: '计划结束时间不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '任务状态(0未开始 1执行中 2待核验 3已完成 4已过期)不能为空', trigger: 'blur' }
          ],
          executeTime: [
            { required: true, message: '开始执行时间不能为空', trigger: 'blur' }
          ],
          executeEndTime: [
            { required: true, message: '执行结束时间不能为空', trigger: 'blur' }
          ],
          delayDay: [
            { required: true, message: '延误天数不能为空', trigger: 'blur' }
          ],
          enable: [
            { required: true, message: '是否启用(0不启用 1启用)不能为空', trigger: 'blur' }
          ],
          createBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          delFlag: [
            { required: true, message: '删除标志(0正常 1删除)不能为空', trigger: 'blur' }
          ],
          tenantId: [
            { required: true, message: '租户Id不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0;
        this.visible = true;
        this.canSubmit = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
                this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false;
            if (this.dataForm.id) {
                putObj(this.dataForm).then(data => {
                    this.$notify.success('修改成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            } else {
                addObj(this.dataForm).then(data => {
                    this.$notify.success('添加成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            }
          }
        })
      }
    }
  }
</script>
