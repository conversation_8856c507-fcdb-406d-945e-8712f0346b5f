<template>
  <div class="app-container">
    <el-form
      ref="dateFormRefs"
      :model="dataForm"
      :disabled="formDisabled"
      :rules="dataRules"
      v-loading="formLoading"
      label-width="120px"
    >
      <el-row :gutter="24">
        <el-col>
          <span class="form_title">基本信息</span>
        </el-col>
        <el-col :span="24">
          <el-form-item label="商品名称" prop="deviceName">
            <el-input
              v-model="dataForm.deviceName"
              maxlength="60"
              show-word-limit
              placeholder="请输入商品名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="销项税率" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入销项税率"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="产地" prop="practicalRadius">
            <el-input
              v-model="dataForm.practicalRadius"
              placeholder="请输入产地"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品类" prop="grade">
            <el-checkbox-group v-model="dataForm.grade">
              <el-checkbox
                v-for="(item, index) in dict.type.goods_grade"
                :label="item.value"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="种类" prop="category">
            <el-checkbox-group v-model="dataForm.category">
              <el-checkbox
                v-for="(item, index) in dict.type.goods_category"
                :label="item.value"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <span class="form_title">销售信息</span>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规格明细" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入规格明细"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品价格" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入商品价格"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品库存" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入商品库存"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品重量（g）" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入商品重量（g）"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品货号" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入商品货号"
            ></el-input>
          </el-form-item>
        </el-col>
         <el-col :span="24">
          <span class="form_title">图文描述</span>
        </el-col>
         <el-col :span="24">
          <el-form-item label="商品货号" prop="specification">
                        <ImageUpload  :isShowTip="false" :showTipText="showTipText" :value="dataForm.picture" :limit="1" @input="handleUpload" />

          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  dicts: ["goods_grade", "goods_category"],
  data() {
    return {
      dataForm: {
        grade: [],
        category: [],
      },
      showTipText:"商品",
      formDisabled: false,
      formLoading: false,
      btnLoading: false,
      dataRules: {
        deviceName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        specification: [{ required: true, message: "请输入规格", trigger: "blur" }],
        practicalRadius: [{ required: true, message: "请输入实际半径", trigger: "blur" }],
        abrasionRadius: [{ required: true, message: "请输入磨损半径", trigger: "blur" }],
        manufacturer: [{ required: true, message: "请输入生产厂家", trigger: "blur" }],
        manufactureTime: [{ required: true, message: "请选择生产日期", trigger: "blur" }],
        lifeSpan: [{ required: true, message: "请输入生命周期", trigger: "blur" }],
      },
    };
  },
};
</script>

<style lang="scss" scoped>
.form_title {
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 8px;
}
</style>
