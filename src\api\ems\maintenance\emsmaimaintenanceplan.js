/*
 *    Copyright (c) 2018-2025, gewu All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: gewu
 */

import request from '@/utils/request'

export function  planFetchList(query) {
  return request({
    url: '/platform/emsmaimaintenanceplan/page',
    method: 'get',
    params: query
  })
}

export function  planAddObj(obj) {
  return request({
    url: '/platform/emsmaimaintenanceplan',
    method: 'post',
    data: obj
  })
}

export function  planGetObj(id) {
  return request({
    url: '/platform/emsmaimaintenanceplan/' + id,
    method: 'get'
  })
}

export function  planDelObj(id) {
  return request({
    url: '/platform/emsmaimaintenanceplan/' + id,
    method: 'delete'
  })
}

export function  planPutObj(obj) {
  return request({
    url: '/platform/emsmaimaintenanceplan',
    method: 'put',
    data: obj
  })
}

// export function taskObjectPlanId(id) {
//   return request({
//     url: '/platform/emsmaimaintenanceplan/taskObjectPlanId/' + id,
//     method: 'get'
//   })
// }
export function devicePlanIdList(query) {
  return request({
    url: '/platform/emsmaimaintenanceplan/deviceList/page',
    method: 'get',
    params: query
  })
}
export function deviceListAll(query) {
  return request({
    url: '/platform/emsmaimaintenanceplan/getDeviceListAllPage/page',
    method: 'get',
    params: query
  })
}
// 审核保养计划
export function updateCheckById(obj) {
  return request({
    url: '/platform/emsmaimaintenanceplan/updateCheckById',
    method: 'put',
    data: obj
  })
}



