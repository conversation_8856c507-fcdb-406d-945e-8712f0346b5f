<template>

  <div class="add-box">
    <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        size="small"
        class="demo-ruleForm"
    >
      <div class="info-box">
        <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>

        <div class="info-from">

          <el-form-item label="制度名称" prop="regulationName">
            <el-input v-model="form.regulationName" maxlength="20" show-word-limit placeholder="请输入制度名称" style="width:100%"></el-input>
          </el-form-item>

          <el-form-item label="是否置顶" prop="isNoTop">
            <el-select v-model="form.isNoTop" placeholder="请选择文档密级" style="width: 100%">
              <el-option
                  v-for="item in isNoTopData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="分类" prop="regulationCategory">
            <el-cascader :options="treeData"
                         v-model="form.regulationCategory"
                         :props="optionProps"
                         :show-all-levels="false"
                         style="width: 100%"
                         placeholder="请选择分类"></el-cascader>
          </el-form-item>

          <!--<el-form-item label="发布范围" prop="releaseScope">-->
          <!--  <treeselect-->
          <!--      v-model="form.releaseScope"-->
          <!--      :options="treeDeptData"-->
          <!--      style="width: 100%"-->
          <!--      :normalizer="normalizer"-->
          <!--      placeholder="请选择发布范围"-->
          <!--  />-->
          <!--</el-form-item>-->

          <el-form-item label="文件" prop="fileIdArray">
            <device-upload ref="fileupload"
                           style="width: 100%"
                           :fileListTem="imgArrayTem"
            ></device-upload>
          </el-form-item>

          <el-form-item label="说明" prop="remark">
            <el-input v-model="form.remark" maxlength="255" show-word-limit placeholder="请输入说明" style="width: 100%"></el-input>
          </el-form-item>

        </div>
      </div>

      <!-- 富文本编辑器 -->
      <div class="info-box" style="height: 100%">
        <IconTitle title="规章制度内容" imgUrl="yunwei"></IconTitle>
        <div style="padding-top: 20px; height: 100%" >
          <ueditor v-model="form.newsContent"
                   :options="options"></ueditor>
        </div>
      </div>

      <div class="info-btn-box" style="height: 100%;">
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import {getObj, putObj, getTree} from "@/api/ems/repository/emsregulations";
import deviceUpload from "../emsdevicedata/deviceUpload";
import {tableOption} from "@/const/crud/ems/repository/emsregulations";
import IconTitle from "@/components/ems/icon-title/index.vue";
import {mapGetters} from "vuex";
import {fetchTree} from "@/api/admin/dept";
import Treeselect from "@riophae/vue-treeselect";
import AvueUeditor from "avue-plugin-ueditor/packages/ueditor/src/index.vue";
export default {
  name: "editRegulations",
  components: {
    IconTitle,
    Treeselect,
    deviceUpload,
    AvueUeditor
  },
  data() {
    return {

      // 富文本编辑器
      options: {
        //普通图片上传
        action: "/admin/sys-file/upload",
        customConfig: {},//wangEditor编辑的配置
        props: {
          res: "data",
          url: "url"
        },
      },

      title: "",
      tableData: [],
      searchForm: {
        //制度名称
        regulationName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      // 树状结构
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        props: {
          label: "name",
          value: "id",
        },
      },
      treeData: [],
      // 新增弹出框
      dialogFormVisible: false,
      form: {
        id: "",
        releaseScope: undefined,
        regulationName: "",
        isNoTop: "",
        regulationCategory: "",
        fileIdArray: "",
        remark: "",
        newsContent: '', // 富文本内容
      },
      rules: {
        releaseScope: [
          {required: true, message: '请选择发布范围', trigger: 'change'}
        ],
        regulationName: [
          {required: true, message: '请输入制度名称', trigger: 'change'}
        ],
        isNoTop: [
          {required: true, message: '请选择是否置顶', trigger: 'change'}
        ],
        regulationCategory: [
          {required: true, message: '请选择分类', trigger: 'change'}
        ],
      },
      imgArrayTem: [],
      treeDeptData: [], //部门
      isNoTopData: [
        {
          value: 0,
          label: "是"
        },
        {
          value: 1,
          label: "否"
        }
      ],
      optionProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      editId: 0
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsregulations_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsregulations_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsregulations_edit, false),
      };
    },
  },
  mounted() {
    this.getTreeData();
    // this.getSelect();
    this.getUpdateList();
  },
  methods: {

    // 回显数据
    getUpdateList() {
      getObj(this.$route.query.editId).then(res => {

        this.imgArrayTem = [];
        if (res.data.data.fileArray != null) {
          res.data.data.fileArray.forEach((element) => {
            this.imgArrayTem.push({
              name: element.original,
              id: element.id,
              url: element.url,
              temUrl: element.url,
            });
          });
        }
        // 修改回显数据
        Object.keys(this.form).forEach((item, index) => {
          if (item !== "fileArray") {
            this.form[item] = res.data.data[item];
          }
        });

        this.form.id = res.data.data.id;
        this.form.releaseScope = res.data.data.releaseScope;
        this.form.regulationName = res.data.data.regulationName;
        this.form.isNoTop = Number(res.data.data.isNoTop);
        this.form.regulationCategory = res.data.data.regulationCategory;
        this.form.fileIdArray = res.data.data.fileIdArray;
        this.form.remark = res.data.data.remark;
        this.form.newsContent = res.data.data.newsContent;
      })
    },

    // 返回按钮
    goBack() {
      this.$router.push({
        path: '/ems/repository/emsregulations/index'
      })
    },

    // 提交表单
    submitForm(formName) {
      let data = JSON.parse(JSON.stringify(this.form));
      if (this.$refs.fileupload.fileList != null) {
        data.fileIdArray = this.$refs.fileupload.fileList.map((item) =>
            item.id
        );
      } else {
        data.fileIdArray = [];
      }


      this.$refs[formName].validate((valid) => {
        if (valid) {
          // console.log(">>>>>111>>>>>>>>>>>" + JSON.stringify(data));
          if (data.regulationCategory instanceof Array) {
            data.regulationCategory = data.regulationCategory.pop();
          }
          putObj(data).then((res) => {
            this.$parent.$message.success("修改成功！")
            this.$parent.listFlag = true;
            this.$router.push({
              path: '/ems/repository/emsregulations/index'
            })
          });
        }

      });
    },

    // 规章制度树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "规章制度类型") {
              common_table_info.push(treeDataList[index])
            }
          })
          this.treeData = common_table_info;
        }
      })
    },

    // 部门树
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },

    // getSelect() {
    //   //部门
    //   fetchTree().then((response) => {
    //     this.treeDeptData = response.data.data;
    //   });
    // },
  }
}
</script>

<style scoped lang="scss">
@import "@/styles/color.scss";
@import "@/styles/ems/mixin.scss";

.add-box {
  height: 800px;
}

.info-box {
  background: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  margin-bottom: 10px;
  padding: 10px 15px;
  overflow: hidden;

  .info-from {
    display: flex;
    flex-wrap: wrap;
    padding-top: 20px;
    position: relative;

    .el-form-item {
      width: 50%;
      padding-right: 10px;
    }


    .info-from::before {
      position: absolute;
      top: 10px;
      height: 1px;
      content: "";
      left: -15px;
      right: -15px;
      display: block;
      background: #eff2f5;
    }

    .runTime {

      ::v-deep .el-form-item__content {
        display: flex;

        span {
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
  }
}

.info-btn-box {
  width: 100%;
  text-align: center;
}


</style>
