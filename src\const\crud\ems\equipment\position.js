export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": false,
  "column": [
	  {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
      hide:true
    },	  {
      "type": "input",
      "label": "位置名称",
      "prop": "location",
      "span": 12
    },	  {
      "type": "input",
      "label": "父id",
      "prop": "parentId",
      "span": 12
    },	  {
      "type": "input",
      "label": "所在地",
      "prop": "address",
      "span": 12
    },	  {
      "type": "input",
      "label": "坐标",
      "prop": "coordinate",
      "span": 12
    },	  {
      "type": "input",
      "label": "排序值",
      "prop": "sort",
      "span": 12
    },	  {
      "type": "input",
      "label": "状态(0启用，1禁用)",
      "prop": "status",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建者",
      "prop": "createBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "更新者",
      "prop": "updateBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "更新时间",
      "prop": "updateTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12
    },	  {
      "type": "input",
      "label": "删除标志(0正常 1删除)",
      "prop": "delFlag",
      "span": 12
    },	  {
      "type": "input",
      "label": "租户Id",
      "prop": "tenantId",
      "span": 12
    }  ]
}
