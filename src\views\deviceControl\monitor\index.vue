<template>
  <div class="app-container monitorPage">

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="工站" prop="workstationId">
        <el-select v-model="queryParams.workstationId" placeholder="请选择工站" clearable>
          <el-option v-for="dict in worStation" :key="dict.id" :label="dict.workstation" :value="dict.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" prop="deviceStatus">
        <el-select v-model="queryParams.deviceStatus" placeholder="请选择设备状态" clearable>
          <el-option v-for="dict in dict.type.device_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row style="margin-top: 20px;">
      <el-col :span="9">
        <div class="hintTitle titleTags">设备信息</div>
        <div style="height:300px" v-loading="pieLoading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
          <pie-chart v-if="countData.length > 0"  chartId="regiongdp_chart" :chartData="countData" />
        </div>
      </el-col>
      <el-col :span="15">
        <div class="hintTitle titleTags">设备OEE</div>
        <div style="height:300px"  v-loading="lineLoading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading">
          <line-chart v-if="oeeData && oeeData.oeeYData.length > 0" chartId="oeelinChart" :chartData="oeeData" />
        </div>
      </el-col>
    </el-row>

    <div class="hintTitle titleTags">设备监控</div>
    <el-table v-loading="loading" :data="postList">
      <el-table-column label="设备编号" prop="deviceNum" />
      <el-table-column label="设备名称" prop="deviceName" />
      <el-table-column label="所属工站" prop="workstation" />
      <el-table-column label="设备类型" prop="deviceType" />
      <el-table-column label="设备状态" prop="deviceStatus">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.deviceStatus==0">运行中</el-tag>
          <el-tag type="danger" v-else-if="scope.row.deviceStatus==1">故障</el-tag>
          <el-tag type="warning" v-else>带病运行</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="OEE(%)" prop="oee" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
      @pagination="getList" />

  </div>
</template>
    
<script>
import { getCountData, getOeeData, monitorList } from "@/api/deviceControl/monitor";
import { getWorStation } from "@/api/deviceControl/information";


import PieChart from './pieChart.vue'
import LineChart from './lineChart.vue'

export default {
  name: "MonitorPage",
  components: {
    LineChart,
    PieChart,
  },
  dicts: ['device_status'],
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      postList: [],
      queryParams: {
        page: 1, //page
        limit: 10,  //limit
        workstationId:undefined,
        deviceStatus:undefined
      },
      countData: [],
      oeeData: {
        xData: [],
        oeeYData: []
      },
      worStation: [],
      pieLoading:true,
      lineLoading:true
    };
  },
  created() {
    this.getList();
    getWorStation().then(res => this.worStation = res.data);
  },
  methods: {

    getOeeDataFtn() {
      const {workstationId,deviceStatus} = this.queryParams
      const parame= {
        workstationId,
        deviceStatus
      }
      getOeeData(parame).then(res => {
        this.oeeData = {
          oeeYData: res.data.map(v => v.oee),
          xData: res.data.map(v => v.oeeName)
        };
      }).finally(() => { this.lineLoading = false })
    },

    getCountDataFtn() {
      const {workstationId,deviceStatus} = this.queryParams
      const statusColors = {
        2: '#E6A23C',
        1: '#F56C6C',
        0: '#67C23A',
      }
      const parame= {
        workstationId,
        deviceStatus
      }
      getCountData(parame).then(res => {
        this.countData = res.data.map(v => {
          return {
            name: v.deviceStatusCH,
            value: v.count,
            color: statusColors[v.deviceStatus]
          }
        })
      }).finally(() => { this.pieLoading = false })
    },

    getList() {
      this.getCountDataFtn()
      this.getOeeDataFtn();
      this.loading = true;
      monitorList(this.queryParams).then(response => {
        const { list, total } = response.data;
        this.postList = list;
        this.total = total;
      }).finally(() => { this.loading = false });
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.queryParams = {
        page: 1,
        limit: 10,
        workstationId:undefined,
        deviceStatus:undefined
      }
      this.resetForm("queryForm");
      this.handleQuery();
    },
  }
};
</script>
  
<style lang="scss" scoped>
.monitorPage {

}
</style>
    
    