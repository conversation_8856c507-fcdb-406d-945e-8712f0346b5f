import { requestPlatForm } from "@/utils/requestBase";




export function tagManageList(query) {
  return requestPlatForm({
    url: '/label/manage/list',
    method: 'get',
    params: query
  })
}


export function addTag(data) {
    return requestPlatForm({
      url: '/label/manage',
      method: 'post',
      data: data
    })
  }
  export function editTage(data) {
    return requestPlatForm({
      url: '/label/manage',
      method: 'put',
      data: data
    })
  }
  export function delTag(data) {
    return requestPlatForm({
      url: '/label/manage',
      method: 'delete',
      data
    })
  }

