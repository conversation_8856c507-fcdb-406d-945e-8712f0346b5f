<template>
    <div class="financialCommn">
        <el-form :disabled="detailFlag" ref="form" :model="form" :rules="rules" label-width="120px" style="display:flex">
            <el-row style="width:80%" :gutter="24">
                <el-col :span="10">
                    <el-form-item label="产品名称" prop="serviceName">
                        <el-input v-model="form.serviceName" placeholder="请输入产品名称" style="width: 100%;" />
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="标签" prop="tags">
                        <el-select v-model="form.tags" placeholder="请选择标签" multiple style="width:100%" clearable>
                            <el-option v-for="dict in dict.type.financial_tags" :key="dict.value" :label="dict.label"
                                :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="最低年化利率" prop="minInterestRate">
                        <el-input  @input="numValid(form.minInterestRate,'minInterestRate')" class="inputNum" :min="0" :max="100" type="number" v-model="form.minInterestRate"
                            placeholder="请输入最低年化利率" style="width: 100%;">
                            <template slot="append">%</template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="10">
                    <el-form-item label="最高年化利率" prop="maxInterestRate">
                        <el-input class="inputNum" :min="0" :max="100" 
                        @input="numValid(form.maxInterestRate,'maxInterestRate')"
                        type="number" v-model="form.maxInterestRate"
                            placeholder="请输入最高年化利率" style="width: 100%;">
                            <template slot="append">%</template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="担保方式" prop="collateralMethods">
                        <el-select v-model="form.collateralMethods" placeholder="请选择担保方式" multiple style="width:100%"
                            clearable>
                            <el-option v-for="dict in dict.type.collateral_methods" :key="dict.value" :label="dict.label"
                                :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="适用地区" prop="applicableRegions">
                        <el-cascader v-model="form.applicableRegions" :options="cityData" :props="{
                            multiple: true,
                            checkStrictly: true,
                            children: 'children',
                            label: 'name',
                            value: 'code',
                        }" clearable placeholder="请选择适用地区" style="width:100%">
                        </el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="贷款额度小" prop="minLoanAmount">
                        <el-input class="inputNum" :min="1" type="number" v-model="form.minLoanAmount"
                            placeholder="请输入贷款额度小" style="width: 100%;">
                            <template slot="append">万元</template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="10">
                    <el-form-item label="机构名称" prop="financialInstitutionName">
                        <el-input v-model="form.financialInstitutionName" placeholder="请输入机构名称" style="width: 100%;" />
                    </el-form-item>
                </el-col> -->
                <el-col :span="10">
                    <el-form-item label="贷款额度大" prop="maxLoanAmount">
                        <el-input class="inputNum" :min="1" type="number" v-model="form.maxLoanAmount"
                            placeholder="请输入贷款额度大" style="width: 100%;">
                            <template slot="append">万元</template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="服务企业类型" prop="serviceType">
                        <el-select v-model="form.serviceType" placeholder="请选择服务企业类型" style="width:100%" clearable>
                            <el-option v-for="dict in dict.type.service_type" :key="dict.value" :label="dict.label"
                                :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="单款期限小" prop="minLoanYears">
                        <el-input class="inputNum" :min="1" type="number" v-model="form.minLoanYears" placeholder="请输入单款期限小"
                            style="width: 100%;">
                            <template slot="append">月</template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="单款期限大" prop="maxLoanYears">
                        <el-input class="inputNum" :min="1" type="number" v-model="form.maxLoanYears" placeholder="请输入单款期限大"
                            style="width: 100%;">
                            <template slot="append">月</template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="20">
                    <el-form-item label="适用客群" prop="applyCustomers">
                        <el-input v-model="form.applyCustomers" placeholder="请输入适用客群" style="width: 100%;" type="textarea"
                            :rows="3" />
                    </el-form-item>
                </el-col>
                <el-col :span="20">
                    <el-form-item label="适用客群及其准入条件" prop="permitCondition">
                        <el-input v-model="form.permitCondition" placeholder="请输入适用客群及其准入条件" style="width: 100%;"
                            type="textarea" :rows="3" />
                    </el-form-item>
                </el-col>
                <el-col :span="20">
                    <el-form-item label="产品优势" prop="proMerit">
                        <el-input v-model="form.proMerit" placeholder="请输入产品优势" style="width: 100%;" type="textarea"
                            :rows="3" />
                    </el-form-item>
                </el-col>
                <el-col :span="20">
                    <el-form-item label="产品介绍" prop="proIntroduce">
                        <el-input v-model="form.proIntroduce" placeholder="请输入产品介绍" style="width: 100%;" type="textarea"
                            :rows="3" />
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="20">
                    <el-form-item class="uploadItem" label='图片' prop="financialInstitutionIconUrl">
                        <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                            :http-request="uploadApiFtn" action="#" :show-file-list="false"
                            :on-success="handleAvatarSuccess">
                            <img v-if="form.financialInstitutionIconUrl" :src="form.financialInstitutionIconUrl"
                                class="appImage">
                            <i v-else class="el-icon-plus avatar-uploader-icon appIcon"></i>
                            <div slot="tip" style="font-size: 12px; color: #9EA5B6;">支持扩展名：.jpg .img .png</div>
                        </el-upload>
                    </el-form-item>
                </el-col> -->
            </el-row>
            <el-row >
                <el-col >
                    <el-form-item label="图标" prop="logo" label-width="60px" style="text-align:left">
                        <NewImageUpload 
                        :disabled="detailFlag"
                        v-model="form.logo" 
                        :limit="1"/>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>


    </div>
</template>
  
<script>
import { getToken } from "@/utils/auth";
import {
    getCityData
} from "@/api/release/financial";
import { getInfo, uploadApi } from "@/api/release/index.js";
import NewImageUpload from '@/components/NewImageUpload'

export default {
    name: "indAppCommn",
    dicts: ['financial_tags', 'collateral_methods', 'service_type'],
    props: {

    },
    components:{
        NewImageUpload
    },
    data() {
        return {
            form: {
                tags: [],
                collateralMethods: [],
            },
            rules: {
                serviceName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                tags: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                minInterestRate: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                collateralMethods: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                maxInterestRate: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                applicableRegions: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                minLoanAmount: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                // financialInstitutionName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                maxLoanAmount: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                serviceType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                minLoanYears: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                permitCondition: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                maxLoanYears: [{ required: true, message: "必填项不能为空", trigger: ['blur', 'change'] }],
                proMerit: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                proIntroduce: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                applyCustomers: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                logo: [{ required: true, message: "请上传logo", trigger: "blur" }],
                // financialInstitutionIconUrl: [{ required: true, message: "必填项不能为空", trigger: "change" }],
            },
            cityData: [],
            submitDing: false,
            detailFlag: (this.$route.query.pageType == 'detail' || this.$route.query.pageType == 'check') || false
        };
    },

    created() {
        this.getCityDataFtn();
        const { flowInstanceId } = this.$route.query;
        flowInstanceId && this.getFormDataFtn(flowInstanceId)
    },

    destroyed() {
    },

    methods: {

        getFormDataFtn(flowInstanceId) {
            getInfo({ flowInstanceId }).then(res => {
                const { params } = res.data;
                this.form = {
                    ...params,
                    // appCases: params.appCases && params.appCases.split(','),
                    tags:params.tags.map(v=>v+''),
                    collateralMethods:params.collateralMethods.map(v=>v+'')
                }
            })
        },

        getCityDataFtn() {
            getCityData().then((res => { this.cityData = res.data }))
        },

        handleAvatarSuccess() { },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (['jpg', 'img', 'png'].indexOf(fileType) == -1) {
                this.$message.error('请上传后缀为.jpg .img .png格式的图片文件');
                return false;
            }
        },
        numValid(value,type){
     this.form[type] = value>100 ? 100 : value.replace(/[^0-9]/g, "") ;
  },
        uploadApiFtn(event) {
            let fileData = new FormData();
            fileData.append('file', event.file);
            uploadApi(fileData).then(res => {
                this.form = { ...this.form, financialInstitutionIconUrl: res.data.url };
                this.$refs.form.clearValidate('financialInstitutionIconUrl')
            })
        },

        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.$emit('submitFtn', this.form, (res) => {
                        // 相应结束后的其他逻辑
                    });
                }
            });
        },

    },

};
</script>



<style lang="scss" >
.financialCommn {
    width: 100% !important;
    .el-row{
        display: flex;
        flex-wrap: wrap;
    }

    .uploadItem {
        .el-form-item__content {
            line-height: normal;
        }
    }

    .dynaCard {
        /* width: 100%; */
        background: #F6F8FC;
        border-radius: 5px;
        padding: 12px 24px;
        margin-bottom: 20px;

        .dynaCardHead {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            margin-top: 12px;
            margin-left: 22px;

            .hintTitleSamil {
                font-weight: bold;
                font-size: 15px;
                color: #0D162A;

                &:before {
                    content: '';
                    display: inline-block;
                    width: 4px;
                    height: 10px;
                    background: #6FC342;
                    border-radius: 0px;
                    margin-right: 6px;
                }
            }
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        text-align: center;
    }

    .avatar-uploader {
        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
    }

    .appIcon {
        width: 160px;
        height: 160px;
        line-height: 160px;
    }

    .appImage {
        width: 160px;
        height: 160px;
    }
}
</style>