<template>
  <div class="form-title">
    <div class="bg"></div>
    <div class="titleLabel">{{ title }} </div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: "FormTitle",
  props: {
    title: {
      type: String,
      required: true,
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";
@import "@/styles/edge/common-btn.scss";
.form-title {
  margin-bottom: 15px;
  .bg{
    background: #26AE61;
    width: 2px;
    height: 18px;
  }
  .titleLabel {
    margin-left: 10px;
    font-size: 14px;
  }
  display: flex;
}
</style>