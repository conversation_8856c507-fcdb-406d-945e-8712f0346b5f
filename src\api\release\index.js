import { requestPlatForm } from "@/utils/requestBase";
import request from "@/utils/request";

export function getTableList(query) {
  return requestPlatForm({
    url: "/product/list",
    method: "get",
    params: query,
  });
}

//类型
export function getProductList(whetherExaminePage) {
  return requestPlatForm({
    url: "/product/getProductList/" + whetherExaminePage,
    method: "get",
  });
}

//上下架
export function groundUpIng(data) {
  return requestPlatForm({
    url: "/product/groundUpIng",
    method: "post",
    data: data,
  });
}
//热门
export function groundHot(data) {
  return requestPlatForm({
    url: "/product/hot",
    method: "post",
    data: data,
  });
}

//提交
export function submitProduct(data) {
  const { flowInstanceId } = data;
  if (!flowInstanceId) {
    return requestPlatForm({
      url: "/product",
      method: "post",
      data: data,
    });
  } else {
    return requestPlatForm({
      url: "/product",
      method: "put",
      data: data,
    });
  }
}

//获取详情数据
export function getInfo(query) {
  return requestPlatForm({
    url: "/product/getInfo",
    method: "get",
    params: query,
  });
}

// 删除
export function delProduct(noticeIds) {
  return requestPlatForm({
    url: "/product/" + noticeIds,
    method: "delete",
  });
}

export function uploadApi(params) {
  return request({
    url: "/system/file/upload",
    method: "POST",
    data: params,
  });
}

export function setExamine(data) {
  return requestPlatForm({
    url: "/product/examine",
    method: "POST",
    data,
  });
}

export function getConditionByFlowInstanceId(id) {
  return requestPlatForm({
    url: "/product/getConditionByFlowInstanceId/" + id,
    method: "get",
  });
}

//服务商
export function getFacilitatorAdminList(query) {
  return requestPlatForm({
    url: "/product/getFacilitatorAdminList",
    method: "get",
    params: query,
  });
}

//用户分配
export function getFacilitatorUserList() {
  return requestPlatForm({
    url: "/product/getFacilitatorUserList",
    method: "get",
  });
}

//所有用户
export function getSelectLikeUserByUserName(query) {
  return requestPlatForm({
    url: "/product/selectLikeUserByUserName",
    method: "get",
    params: query,
  });
}

//产品列表
export function getTree(query) {
  return requestPlatForm({
    url: "/tree/getTree",
    method: "get",
    params: query,
  });
}

//产品标签
export function getListByProductType(query) {
  return requestPlatForm({
    url: "/label/getListByProductType",
    method: "get",
    params: query,
  });
}

//获取统一表单数据
export function getExInfo(query) {
  return requestPlatForm({
    url: "/product/getExInfo",
    method: "get",
    params: query,
  });
}
//线下诊断添加文件图片
export function updateNodeRecord(data) {
  return requestPlatForm({
    url: "/product/updateNodeRecord",
    method: "POST",
    data,
  });
}
//线下诊断移除文件图片
export function removeFileByRecordId(id) {
  return requestPlatForm({
    url: "/base/flow/removeFileByRecordId?id=" + id,
    method: "get",
  });
}
