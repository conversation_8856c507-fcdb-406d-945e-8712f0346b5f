export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": false,
  "column": [
	  {
      "type": "input",
      "label": "id",
      "prop": "id",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建人",
      "prop": "createBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "更新时间",
      "prop": "updateTime",
      "span": 12
    },	  {
      "type": "input",
      "label": "更新人",
      "prop": "updateBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "标题",
      "prop": "title",
      "span": 12
    },	  {
      "type": "input",
      "label": "所属分类",
      "prop": "category",
      "span": 12
    },	  {
      "type": "input",
      "label": "简介",
      "prop": "briefIntroduction",
      "span": 12
    },	  {
      "type": "input",
      "label": "知识内容",
      "prop": "knowledgeContent",
      "span": 12
    },	  {
      "type": "input",
      "label": "图片路径",
      "prop": "imageUrl",
      "span": 12
    },	  {
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12
    },	  {
      "type": "input",
      "label": "删除标志",
      "prop": "delFlag",
      "span": 12
    },	  {
      "type": "input",
      "label": "租户id",
      "prop": "tenantId",
      "span": 12
    }  ]
}
