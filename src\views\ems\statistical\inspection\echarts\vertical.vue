<template>
  <div id="vertical" :style="{width: '400px', height: '150px'}"></div>
</template>

<script>
let numberEchartsOptions = {
  backgroundColor: '#FEFAF9',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    top: '8%',
    right: '5%',
    left: '8%',
    bottom: '20%'
  },
  xAxis: [{
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    axisLine: {
      lineStyle: {
        color: '#D1D9EB'
      }
    },
    axisLabel: {
      margin: 10,
      color: '#A1A7B3',
      textStyle: {
        fontSize: 12
      },
    },
    axisTick: {
      show: false
    }
  }],
  yAxis: {show: false},
  // [
  //
  //   {
  //     type: "value",
  //     nameTextStyle: {
  //       color: '#C1C6CF',
  //       fontSize: 12,
  //       align: "right",
  //       padding:5
  //     },
  //     axisLabel: {
  //       formatter:'{value}',
  //       color: '#A1A7B3',
  //       fontSize:14
  //     },
  //     axisTick: {
  //       show: false
  //     },
  //     axisLine: {
  //       show: false,
  //     },
  //     splitLine: {
  //       lineStyle: {
  //         color: '#D1D9EB',
  //         type:'dashed'
  //       }
  //     }
  //   },
  // ],
  series: [{
    type: 'bar',
    data: [80, 80, 0, 0, 95, 26, 72],
    barWidth: '24px',
    itemStyle: {
      color:'#63b2ee',
      // barBorderRadius: [2 ,2 ,0 ,0 ]
    },
  }]
};
export default {
  data() {
    return {
      numberEchartsOptions,
      resultData: []
    };
  },
  props: {
    weekCountData: {
      type: Object
    }

  },
  watch: {
    weekCountData: function(newVal,oldVal){
      this.resultData = newVal.weekList;  //newVal即是chartData
      let weekData = this.resultData
      this.drawLine(weekData);
    }
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
  },

  methods: {

    drawLine(weekData) {
      // 基于准备好的dom，初始化echarts实例
      let vertical = this.$echarts.init(document.getElementById('vertical'))
      let result = [];
      weekData.forEach((item,i) => {
        result.push(item.num)
      })
      numberEchartsOptions.series[0].data = result;
      // 绘制图表
      vertical.setOption(this.numberEchartsOptions);
    }
  }
}

</script>
