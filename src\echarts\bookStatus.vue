<template>
  <div id="bookStatus" :style="{width: '975px', height: '250px'}"></div>
</template>

<script>
export default {
  data() {
    return {}
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let bookStatus = this.$echarts.init(document.getElementById('bookStatus'))

      // 绘制图表
      bookStatus.setOption({
        color: '#5894ff',
        tooltip: {
          trigger: 'item',
          formatter: '{b} : <br/> 开机个数: {c}'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [11, 12, 13, 20, 30, 40, 35, 37, 33, 22, 35, 15, 17, 19, 39, 14, 23, 38, 3, 6, 9, 8, 12, 34, 27],
            type: 'line',
            areaStyle: {
              color: '#deeaff'
            }
          }
        ]

      });
    }
  }
}

</script>
