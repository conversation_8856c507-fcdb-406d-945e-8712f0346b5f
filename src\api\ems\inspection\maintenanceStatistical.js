import request from '@/utils/request'

//保养统计-历史未完成
export function getUnfinishedHistory() {
    return request({
        url: '/platform/maintenanceStatistical/unfinishedHistory',
        method: 'get',
    })
}
//保养统计-今日完成情況
export function getTodayCompletion() {
    return request({
        url: '/platform/maintenanceStatistical/todayCompletion',
        method: 'get',
    })
}
//保养统计-周统计
export function getWeekStatistics() {
    return request({
        url: '/platform/maintenanceStatistical/weekStatistics',
        method: 'get',
    })
}

//保养统计-季度统计
export function getQuarterCount() {
    return request({
        url: '/platform/maintenanceStatistical/quarterCount',
        method: 'get',
    })
}

//保养统计-保养时间折线图
export function getServiceTime() {
    return request({
        url: '/platform/maintenanceStatistical/serviceTime',
        method: 'get',
    })
}