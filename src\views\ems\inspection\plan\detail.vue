<template>
  <div>
    <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>基本信息</span></div>
      <div class="devTitle"><span>{{form.planName}}</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">计划编号</div>
          <div class="contentS">{{form.planNum}}</div>
          <div class="labelS">计划名称</div>
          <div class="contentS">{{form.planName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">所属部门</div>
          <div class="contentS">{{form.deptName}}</div>
          <div class="labelS">负责人</div>
          <div class="contentS">{{form.liableUserName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">创建人</div>
          <div class="contentS">{{form.createBy}}</div>
           <div class="labelS">创建时间</div>
          <div class="contentS">{{form.createTime}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">开始执行时间</div>
          <div class="contentS">{{form.beginTime}}</div>
           <div class="labelS">结束执行时间</div>
            <div class="contentS">{{form.endTime}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">巡检类型</div>
          <div class="contentS">日</div>
           <div class="labelS">巡检周期</div>
            <div class="contentS">{{form.inspectCycle}}日</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">任务有效期</div>
          <div class="contentS">{{form.effectiveTime}}日</div>
           <div class="labelS">提前提醒时间</div>
            <div class="contentS">{{form.noticeTime}}日</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">提前生成时间</div>
          <div class="contentS">{{form.generateTime}}日</div>
           <div class="labelS">审核人</div>
            <div class="contentS">{{form.auditUserName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">是否启用</div>
          <div class="contentS">
              {{form.enableName}}
          </div>
          <div class="labelS">下次生成任务</div>
          <div class="contentS">
              {{form.nextGenerateTime}}
          </div>
        </div>
        <div class="tableStyle">
          <div class="labelS">巡检设置</div>
          <div class="contentS">
              <el-tooltip effect="light"  content="无需扫码：不用扫码，巡检任务中点击设备即可填写" placement="top" @click.stop.prevent>
                  <el-radio :label="0"  v-model="form.inspectSettings">无需扫码</el-radio>
              </el-tooltip>
              <el-tooltip effect="light"  content="扫一个二维码即可提交：需要至少扫一个设备的二维码才能提交巡检记录，表示已经到达现场" placement="top" @click.stop.prevent>
                  <el-radio :label="1"  v-model="form.inspectSettings">扫一个二维码即可提交</el-radio>
              </el-tooltip>
<!--              <el-tooltip  effect="light"  popper-class="tooltip_3" content="需要填写异常项：只有检查有异常的项目才需要填写，其他不填写的项目系统自动默认为正常" placement="top" @click.stop.prevent>-->
<!--                  <el-radio :label="3"  v-model="form.inspectSettings">只需填写异常项</el-radio>-->
<!--              </el-tooltip>-->
<!--             <el-radio v-model="form.inspectSettings" :label="0">无需扫码</el-radio>-->
<!--             <el-radio v-model="form.inspectSettings" :label="1">扫一个二维码即可提交</el-radio>-->
<!--              <el-radio v-model="form.inspectSettings" :label="3">只需填写异常项</el-radio>-->
          </div>
        </div>
        <div class="tableStyle">
          <div class="labelS">说明</div>
          <div class="contentS">
              {{form.remark}}
          </div>
        </div>
      </div>
    </el-card>
<!--    <el-card shadow="always" class="box-card">-->
<!--      <div class="tableTitle"><span>执行信息</span></div>-->
<!--       <el-card shadow="always" class="box-card" style="box-shadow:4px 10px 5px 0px rgba(168, 168, 168, .2);">-->
<!--           <el-row>-->
<!--               <el-col :span="6">-->
<!--                    <div>已过周期</div>-->
<!--                    <div class="number">0</div>-->
<!--               </el-col>-->
<!--                <el-col :span="6">-->
<!--                    <div>实际执行周期</div>-->
<!--                    <div class="number" style="color:#54B03A">0</div>-->
<!--               </el-col>-->
<!--                <el-col :span="6">-->
<!--                    <div>故障次数</div>-->
<!--                    <div class="number" style="color:#52B7F5">0</div>-->
<!--               </el-col>-->
<!--                <el-col :span="6">-->
<!--                    <div>设备完好率</div>-->
<!--                    <div class="number"  style="color:#FF5722">0</div>-->
<!--               </el-col>-->
<!--           </el-row>-->
<!--       </el-card>-->
<!--    </el-card>-->
<!--    <el-card shadow="always" class="box-card">-->
<!--        <IconTitle title="负责人" imgUrl="yunwei"></IconTitle>-->
<!--        <el-table v-loading="user.loading" :data="userList">-->
<!--            <el-table-column-->
<!--                    label="序号"-->
<!--                    width="70px">-->
<!--                <template slot-scope="scope">-->
<!--                    {{scope.$index+1}}-->
<!--                </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column label="id" align="center" prop="id" v-if="false"/>-->
<!--            <el-table-column label="名称" align="center" prop="name"/>-->
<!--            <el-table-column label="性别"  align="center" prop="sex"/>-->
<!--            <el-table-column label="证书"  align="center" prop="zs"/>-->
<!--        </el-table>-->
<!--    </el-card>-->
    <el-card shadow="always" class="box-card">
        <IconTitle title="巡检设备" imgUrl="yunwei"></IconTitle>
        <el-table v-loading="device.loading" :data="deviceList">
            <el-table-column
                    label="序号"
                    width="70px">
                <template slot-scope="scope">
                    {{scope.$index+1}}
                </template>
            </el-table-column>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备编号" align="center" prop="deviceNum"/>
            <el-table-column label="名称" align="center" prop="deviceName"/>
            <el-table-column label="所属部门" align="center" prop="deptName"/>
            <el-table-column label="类别" align="center" prop="categoryName">
            </el-table-column>
            <el-table-column label="规则型号" align="center" prop="specification"/>
            <el-table-column label="位置" align="center" prop="locationName"/>
        </el-table>

        <pagination
                v-show="deviceQueryParams.total>0"
                :total="deviceQueryParams.total"
                :page.sync="deviceQueryParams.pageNum"
                :limit.sync="deviceQueryParams.pageSize"
                @pagination="getDeviceList"
        />
    </el-card>
      <el-card shadow="always" class="info-btn-box">
          <el-button @click="goBack">返回</el-button>
      </el-card>

<!--      <div class="info-btn-box">-->
<!--         -->
<!--      </div>-->
  </div>

</template>

<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import {planGetObj} from '@/api/ems/inspection/plan'
    import {deviceList_strategyId,strategyGetObj} from '@/api/ems/inspection/emsinsinspectstrategy'
    import { getUser } from "@/api/system/user"
    import {getDept} from '@/api/system/dept'
    export default {
        name: "detailPlanIndex",
        components: {
            IconTitle,
        },
        props: {
            id: {
                type: Number,
            },
        },
        data() {
            return {
                // 用户证书
                userList: [],
                user: {
                    loading: false,
                },
                // 设备数据
                deviceList: [],
                device: {
                    loading: false,
                },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                loading: false,
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                //点检项目
                checkList: [],
                check: {
                    title: "",
                    open: false,
                    loading: false,

                },
                queryParamsCheckList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                form: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    deptName:null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    enable: 0,
                    enableName:null,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    nextGenerateTime:null,
                },
                oldForm: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    deptName:null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    nextGenerateTime:null,
                },
                userForm:{
                    name:null,
                    sex:null,
                }
            };
        },
        mounted() {
            if (this.id > 0) {
                planGetObj(this.id).then((res) => {
                  // debugger;
                    this.oldForm = res.data
                    this.oldForm.enable=parseInt(this.oldForm.enable);
                    this.oldForm.inspectSettings=parseInt(this.oldForm.inspectSettings);
                    if (this.oldForm.strategyId!=null) {
                        strategyGetObj(this.oldForm.strategyId).then((res) => {
                            this.oldForm.strategyName = res.data.strategyName;
                        });
                    }
                    this.user.loading=true;
                    if (this.oldForm.liableUserId!=null) {
                        getUser(this.oldForm.liableUserId).then(res => {
                            this.oldForm.liableUserName = res.data.userName
                            this.userList = [];
                            var o = {};
                            o.name = this.oldForm.liableUserName;
                            o.sex = "未知"
                            o.zs = "未知"
                            this.userList.push(o);
                            this.user.loading = false;
                        })
                    }
                    if (this.oldForm.auditUserId!=null){
                        getUser(this.oldForm.auditUserId).then(res =>{
                            this.oldForm.auditUserName=res.data.userName
                        })
                    }

                    this.device.loading=true;
                    deviceList_strategyId(Object.assign(
                        {
                            current: this.deviceQueryParams.pageNum,
                            size: this.deviceQueryParams.pageSize,
                        },
                        {id: this.oldForm.strategyId}
                        )
                    ).then(response => {
                        this.deviceList = response.data.records;
                        this.deviceQueryParams.total = response.data.total;
                        this.device.loading = false;
                    });

                    if (this.oldForm.deptId!=null){
                        getDept(this.oldForm.deptId).then((res) => {
                            this.oldForm.deptName=res.data.deptName;
                        });
                    }
                    this.form=this.oldForm;
                  console.log(">>>>>>>>>>", JSON.stringify(this.form))
                    this.form.enableName=this.oldForm.enable=="0"?"不启用":"启用"
                });

            }
            // fetchTree().then((response) => {
            //     this.treeData = response.data;
            // });
        },
        methods: {
            getDeviceList() {
                this.device.loading = true;
                deviceList_strategyId(Object.assign(
                    {
                        current: this.deviceQueryParams.pageNum,
                        size: this.deviceQueryParams.pageSize,
                    },
                    {id: this.form.strategyId}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    this.deviceQueryParams.total = response.data.total;
                    this.device.loading = false;
                });
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";
    .info-btn-box {
        width: 100%;
        text-align: center;
    }
.tableTitle {
  color: #333;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}
.devTitle {
  color: #262626;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
  .el-card__body {
    padding-top: 10px;
  }
  .labelS {
    //display: inline-block;
    flex:0 0 150px;
    //height: 40px;
    // margin-right: 10px;
    text-align: left;
    color: #606266;
    padding: 10px;
    border: 1px solid rgba(236, 240, 244, 100);
    margin-bottom: -1px;
  }
  .contentS {
    border: 1px solid rgba(236, 240, 244, 100);
    // height: 40px;
    color: #606266;
    width: 100%;
    margin-left: -1px;
    margin-bottom: -1px;
    padding: 10px;
    // margin: 10px 0;
    // width: calc(100% - 120px);
    // display: inline-block;
  }
  .tableStyle {
    display: flex;
  }
  .number{
      font-weight: bold;
      margin-top: 5px;
      font-size: 16px;
  }
}
</style>
