<template>
  <div id="rankEcharts" :style="{width: '100%', height: '562px'}"></div>
</template>

<script>
import echarts from "echarts";
import {
  getRankingList
} from "@/api/ems/statistical/maintain"
export default {
  data() {
    return {
      resultData: []
    };
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let rankEcharts = this.$echarts.init(document.getElementById('rankEcharts'))
      getRankingList().then(res => {
        this.resultData = res.data.data.monthNums;

        let data = []
        this.resultData.forEach(function (item) {
          data.push(item.num);
        });


      // 绘制图表
      rankEcharts.setOption({
        grid: {
          top: '30',
          left: '1%',
          right: '1%',
          bottom: '8%',
          containLabel: true,
        },
        xAxis: [{
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#959FA9',
              fontSize: 14
            },
          },
          axisTick: {
            show: false,
          },
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月','11月','12月',]
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(226, 226, 226, 0.3)',
              width: 1
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgb(2,185,208)',
            },
          },
          axisLabel: {
            textStyle: {
              color: '#959FA9',
              fontSize: 14
            },
          },
        }],
        series: [{
          type: 'line',
          smooth: true,
          symbol: 'circle',
          showAllSymbol: true,
          symbolSize: 12,
          label: {
            show: true,
            position: 'top',

          },
          itemStyle: {
            normal: {
              color: "#3e87f7",
            }
          },
          areaStyle: {
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(42,191,250,.4)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(242,191,250, 0)',
                    },
                  ],
                  false
              ),
            },
          },

          data: data
        }

        ]
      });
      });
    }
  }
}

</script>
