<template>
  <div class="talEntsPage">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="人员名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入人员名称"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item class="uploadItem" label="头像" prop="avatarAddress">
        <UploadImage
          :disabled="detailFlag"
          :fileList="form.avatarAddress"
          @addUpload="addUpload"
          @removeUpload="removeUpload"
        />
      </el-form-item>
      <el-form-item label="个人简介" prop="biography">
        <el-input
          v-model="form.biography"
          placeholder="请输入个人简介"
          style="width: 100%"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="领域" prop="areasOfExpertise">
        <el-select
          v-model="form.areasOfExpertise"
          multiple
          placeholder="请选择领域"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in dict.type.areas_of_expertise"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="行业" prop="industryList">
                <el-cascader v-model="form.industryList" :options="industryTypes" :props="{
                    children: 'childrenList',
                    label: 'name',
                    value: 'industryCode',
                }" placeholder="请选择行业" style="width:100%" clearable>
                </el-cascader>
            </el-form-item> -->
      <el-form-item label="产业" prop="kind">
        <el-select
          v-model="form.kind"
          multiple
          placeholder="请选择产业"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in industryKinds"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <div class="tagsTilte" style="margin-top: 30px; margin-bottom: 15px">
        课题申报
      </div>
      <div
        class="dynaCard"
        v-for="(item, index) in form.projectDeclaration"
        :key="item.key"
      >
        <div class="dynaCardHead">
          <div class="hintTitleSamil">申报：NO.{{ index + 1 }}</div>
          <el-popconfirm
            title="确认是否删除"
            @confirm="removeFormItem(item, 'projectDeclaration')"
            v-if="
              !detailFlag &&
              form.projectDeclaration &&
              form.projectDeclaration.length > 1
            "
          >
            <template #reference>
              <i
                style="color: red; font-size: 18px; cursor: pointer"
                class="el-icon-delete"
              ></i>
            </template>
          </el-popconfirm>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="申报名称"
              :prop="'projectDeclaration.' + index + '.name'"
              :rules="{
                required: true,
                message: '必填项不能为空',
                actions: 'blur',
              }"
            >
              <el-input placeholder="请输入" v-model="item.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="地址链接"
              :prop="'projectDeclaration.' + index + '.url'"
              :rules="{
                required: false,
                actions: 'blur',
                validator: validateURL,
              }"
            >
              <el-input placeholder="请输入" v-model="item.url"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-button
        icon="el-icon-circle-plus-outline"
        type="text"
        @click="addFormItem('projectDeclaration')"
        >新增课题申报</el-button
      >

      <div class="tagsTilte" style="margin-top: 30px; margin-bottom: 15px">
        相关培训
      </div>
      <div
        class="dynaCard"
        v-for="(item, index) in form.relevantTraining"
        :key="item.key"
      >
        <div class="dynaCardHead">
          <div class="hintTitleSamil">培训：NO.{{ index + 1 }}</div>
          <el-popconfirm
            title="确认是否删除"
            @confirm="removeFormItem(item, 'relevantTraining')"
            v-if="
              !detailFlag &&
              form.relevantTraining &&
              form.relevantTraining.length > 1
            "
          >
            <template #reference>
              <i
                style="color: red; font-size: 18px; cursor: pointer"
                class="el-icon-delete"
              ></i>
            </template>
          </el-popconfirm>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="培训名称"
              :prop="'relevantTraining.' + index + '.name'"
              :rules="{
                required: true,
                message: '必填项不能为空',
                actions: 'blur',
              }"
            >
              <el-input placeholder="请输入" v-model="item.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="地址链接"
              :prop="'relevantTraining.' + index + '.url'"
              :rules="{
                required: false,
                actions: 'blur',
                validator: validateURL,
              }"
            >
              <el-input
                placeholder="请输入"
                type="url"
                v-model="item.url"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-button
        icon="el-icon-circle-plus-outline"
        type="text"
        @click="addFormItem('relevantTraining')"
        >新增相关培训</el-button
      >

      <div class="tagsTilte" style="margin-top: 30px; margin-bottom: 15px">
        相关资讯
      </div>
      <div
        class="dynaCard"
        v-for="(item, index) in form.inquiries"
        :key="item.key"
      >
        <div class="dynaCardHead">
          <div class="hintTitleSamil">资讯：NO.{{ index + 1 }}</div>
          <el-popconfirm
            title="确认是否删除"
            @confirm="removeFormItem(item, 'inquiries')"
            v-if="!detailFlag && form.inquiries && form.inquiries.length > 1"
          >
            <template #reference>
              <i
                style="color: red; font-size: 18px; cursor: pointer"
                class="el-icon-delete"
              ></i>
            </template>
          </el-popconfirm>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="资讯名称"
              :prop="'inquiries.' + index + '.name'"
              :rules="{
                required: true,
                message: '必填项不能为空',
                actions: 'blur',
              }"
            >
              <el-input placeholder="请输入" v-model="item.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="地址链接"
              :prop="'inquiries.' + index + '.url'"
              :rules="{
                required: false,
                actions: 'blur',
                validator: validateURL,
              }"
            >
              <el-input placeholder="请输入" v-model="item.url"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-button
        icon="el-icon-circle-plus-outline"
        type="text"
        @click="addFormItem('inquiries')"
        >新增相关培训</el-button
      >

      <el-row :gutter="20" style="margin-top: 24px">
        <el-col :span="12">
          <el-form-item
            class="uploadItem"
            label="相关证书"
            prop="relevantCertificates"
          >
            <el-upload
              :before-upload="
                (file) => beforeFileUpload(file, ['doc', 'docx', 'pdf'])
              "
              :on-preview="previewLoad"
              :on-remove="(file) => removeFile(file, 'relevantCertificates')"
              :http-request="
                (event) => uploadFileApiFtn(event, 'relevantCertificates')
              "
              accept=".doc,.docx,.pdf"
              action="#"
              :file-list="form.relevantCertificates"
            >
              <el-button slot="trigger" type="primary" size="small"
                ><i class="el-icon-upload el-icon--right"></i
                >选择文件</el-button
              >
              <div
                slot="tip"
                class="el-upload__tip"
                style="color: silver; font-size: 12px"
              >
                支持扩展名：.docx .doc .pdf
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="uploadItem" label="相关附件" prop="files">
            <el-upload
              :before-upload="
                (file) => beforeFileUpload(file, ['doc', 'docx', 'pdf', 'xlsx'])
              "
              :on-preview="previewLoad"
              :on-remove="(file) => removeFile(file, 'files')"
              :http-request="(event) => uploadFileApiFtn(event, 'files')"
              accept=".doc,.docx,.pdf,.xlsx"
              action="#"
              :file-list="form.files"
            >
              <el-button slot="trigger" type="primary" size="small"
                ><i class="el-icon-upload el-icon--right"></i
                >选择文件</el-button
              >
              <div
                slot="tip"
                class="el-upload__tip"
                style="color: silver; font-size: 12px"
              >
                支持扩展名：.docx .doc .pdf .xlsx
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getInfo, uploadApi } from "@/api/release/index.js";
import { getSysIndustryType, getSysIndustryKind } from "@/api/release/indApp";
import UploadImage from "@/components/UploadImage";

export default {
  name: "talEntsPage",
  dicts: ["areas_of_expertise"],
  props: {},
  components: {
    UploadImage,
  },
  data() {
    return {
      form: {
        areasOfExpertise: [],
        projectDeclaration: [{ name: "", url: "" }],
        relevantTraining: [{ name: "", url: "" }],
        inquiries: [{ name: "", url: "" }],
        avatarAddress:[],
        // relevantCertificates:[]
        kind: [],
      },
      rules: {
        name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
        avatarAddress: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        biography: [
          { required: true, message: "必填项不能为空", trigger: "blur" },
        ],
        // industryList: [{ required: true, message: "必填项不能为空", trigger: "change" }],
        kind: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
        areasOfExpertise: [
          { required: true, message: "必填项不能为空", trigger: "change" },
        ],
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
      appList: [],
      industryTypes: [],
      industryKinds: [],
      relevantCertificates: [],
      files: [],
    };
  },

  created() {
    // this.getSysIndustryTypeFtn();  //行业
    this.getSysIndustryKindFtn(); //产业
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },

  methods: {
    validateURL(rule, value, callback) {
      // const urlRegex = /^(http(s)?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
      const urlRegex = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
      if (value == "") {
        callback();
        return;
      }
      if (!urlRegex.test(value)) {
        callback(new Error("请输入有效的URL"));
      } else {
        callback();
      }
    },

    getFormDataFtn(flowInstanceId) {
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.form = {
          ...params,
          avatarAddress:params.avatarAddress?params.avatarAddress.split(','):[],
          // kind:params.kind.map(v=>v-0)
          areasOfExpertise: params.areasOfExpertise.map((v) => v + ""),
        };
      });
    },

    removeFile(file, key) {
      const findex = this[key].map((f) => f.uid).indexOf(file.uid);
      if (findex > -1) {
        this[key].splice(findex, 1);
      }
    },

    previewLoad(file) {
      const { status, url } = file;
      if (status === "success") {
        window.open(url, "_blank");
      }
    },

    beforeFileUpload(file, typeArr) {
      var FileExt = file.name.replace(/.+\./, "");
      if (typeArr.indexOf(FileExt.toLowerCase()) === -1) {
        this.$message({
          type: "warning",
          message: "请上传正确的文件！",
        });
        return false;
      }
    },

    uploadFileApiFtn(event, key) {
      let fileData = new FormData();
      fileData.append("file", event.file);
      uploadApi(fileData).then((res) => {
        const { data } = res;
        this[key].push({
          name: data.name,
          url: data.url,
          uid: event.file.uid,
        });
      });
    },

    removeFormItem(item, key) {
      var index = this.form[key].indexOf(item);
      if (index !== -1) {
        this.form[key].splice(index, 1);
      }
    },

    addFormItem(key) {
      let dataOp = {
        name: "",
        url: "",
        key: Date.now(),
      };
      this.form[key].push(dataOp);
    },

    getSysIndustryKindFtn() {
      getSysIndustryKind().then((res) => {
        this.industryKinds = res.data;
      });
    },

    // getSysIndustryTypeFtn() {
    //     getSysIndustryType().then(res => {
    //         this.industryTypes = res.data;
    //     });
    // },

    addUpload(res) {
      this.form.avatarAddress = [...this.form.avatarAddress, res.url];
    },
    removeUpload(file) {
      const index = this.form.avatarAddress.indexOf(file);
      if (index > -1) {
        this.form.avatarAddress.splice(index, 1);
      }
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            relevantCertificates: this.relevantCertificates,
            files: this.files,
            avatarAddress: this.form.avatarAddress.join(),
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.talEntsPage {
  width: 55%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }

  .miniIcon {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }

  .miniImage {
    width: 80px;
    height: 80px;
  }
}
</style>
