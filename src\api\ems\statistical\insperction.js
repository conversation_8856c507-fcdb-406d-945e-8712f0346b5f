import request from '@/utils/request'

export function taskCompletionList() {
  return request({
    url: '/ems/checkingStatistics/taskCompletionList',
    method: 'get',
  })
}
//负责人完成情况
export function personCompletionList() {
  return request({
    url: '/ems/checkingStatistics/personCompletionList',
    method: 'get',
  })
}
//周统计
export function getWeekCountList() {
  return request({
    url: '/ems/checkingStatistics/weekCountList',
    method: 'get',
  })
}
//今日完成情況
export function getCheckingTodayCompletion() {
  return request({
    url: '/ems/checkingStatistics/checkingTodayCompletion',
    method: 'get',
  })
}
//历史未完成
export function getCheckingHistoryUnfinished() {
  return request({
    url: '/ems/checkingStatistics/checkingHistoryUnfinished',
    method: 'get',
  })
}

//季度统计
export function getCheckingQuarterCount() {
  return request({
    url: '/ems/checkingStatistics/checkingQuarterCount',
    method: 'get',
  })
}


