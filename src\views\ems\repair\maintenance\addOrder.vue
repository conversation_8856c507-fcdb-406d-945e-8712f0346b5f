<template>
    <div class="add-box">
        <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="100px"
                size="small"
                class="demo-ruleForm"
        >
            <div class="info-box">
                <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
                <div class="info-from">
                    <!--<el-form-item label="报修编号" prop="repairNum">
                        <el-input v-model="form.repairNum"  :disabled="true" placeholder="无需填写自动生成"/>
                    </el-form-item>-->
                    <el-form-item label="设备名称" prop="deviceId">
                        <el-select v-model="form.deviceId" @change="devChange" placeholder="请选择" clearable style="width: 100%">
                            <el-option
                                    v-for="item in accountList"
                                    :key="item.id"
                                    :label="item.deviceName"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="故障等级" prop="faultGrade">
                        <el-select v-model="form.faultGrade" placeholder="请选择" clearable style="width: 100%">
                            <el-option
                                    v-for="item in faultGradeList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                  <!--  <el-form-item label="故障描述" prop="faultRemark">
                        <el-input type="textarea" v-model="form.faultRemark" placeholder="请输入备注"/>
                    </el-form-item>-->
                    <el-form-item label="处理人" prop="handler">
                        <el-input v-model="form.handler" readonly placeholder="请选择处理人">
                            <el-button @click="handleUser()" style="padding-right:10px" slot="suffix" type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="验收人" prop="checkName">
                        <el-input v-model="form.checkName" readonly placeholder="请选择验收人">
                            <el-button @click="checkUser()" style="padding-right:10px" slot="suffix" type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                   <!-- <el-form-item label="维修方式" prop="repairMethod">
                        <el-select v-model="form.repairMethod" placeholder="请选择" clearable style="width: 100%">
                            <el-option
                                    v-for="item in repairMethodList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="form.repairMethod == 1" label="外委单位" prop="outName">
                        <el-input v-model="form.outName"  placeholder="请输入外委单位"></el-input>
                    </el-form-item>
                    <el-form-item v-if="form.repairMethod == 1" label="预计金额" prop="expectMoney">
                        <el-input v-model="form.expectMoney"  placeholder="请输入预计金额"></el-input>
                    </el-form-item>
                    <el-form-item v-if="form.repairMethod == 1" label="工期要求" prop="duration">
                        <el-input v-model="form.duration"  placeholder="请输入工期要求"></el-input>
                    </el-form-item>
                    <el-form-item v-if="form.repairMethod == 1" label="委外理由" prop="outReason">
                        <el-input v-model="form.outReason"  placeholder="请输入委外理由"></el-input>
                    </el-form-item>-->
                </div>
            </div>
            <div class="info-box">
                <IconTitle title="关联异常项" imgUrl="yunwei"></IconTitle>
                <el-button style="float: right"
                           type="primary"
                           icon="el-icon-plus"
                           size="mini"
                           @click="selectExcep"
                >新增
                </el-button>
                <el-table v-loading="loading" :data="excepData">
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="编号" align="center" key="exceptionNum" prop="exceptionNum"/>
                    <el-table-column label="设备名称" align="center" key="username" prop="deviceName"/>
                    <el-table-column label="异常项目" align="center" key="enable" prop="falutName">
                    </el-table-column>
                    <el-table-column label="创建人" align="center" key="deptId" prop="createBy"/>
                    <el-table-column label="创建时间" align="center" prop="createTime"
                                     width="160">
                    </el-table-column>
                    <el-table-column label="故障描述" align="center" prop="remark"
                                     width="160">
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="excepDataDele(scope.row)"
                            >删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-show="queryParams.total>0"
                        :total="queryParams.total"
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="excepGetList"
                />

            </div>
            <div class="info-btn-box">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
        </el-form>
        <!--用户弹框-->
        <el-dialog :title="user.title" :visible.sync="user.open" width="1000px" append-to-body>
            <el-row :gutter="20">
                <!--部门数据-->
                <el-col :span="4" :xs="24">
                    <div class="head-container">
                        <div class="tree">
                            <el-tree
                                    :data="treeDeptData"
                                    :props="defaultProps"
                                    :expand-on-click-node="false"
                                    :filter-node-method="filterNode"
                                    ref="tree"
                                    default-expand-all
                                    @node-click="handleNodeClick"
                            />
                        </div>
                    </div>

                </el-col>
                <!--用户数据-->
                <el-col :span="20" :xs="24">
                    <el-table v-loading="user.loading" :data="userList"
                              @row-click="userRowClick">
                        <el-table-column label="用户编号" align="center" key="userId" prop="userId"/>
                        <el-table-column label="用户名称" align="center" key="username" prop="username"/>
                        <el-table-column label="部门" align="center" key="deptName" prop="deptName"/>
                        <el-table-column label="角色" align="center" key="role" prop="roleList[0].roleName"/>
                        <el-table-column label="手机号码" align="center" key="phone" prop="phone"/>
                        <el-table-column label="状态" align="center" key="lockFlag">
                            <template slot-scope="scope">
                                          <span>{{
                                            scope.row.lockFlag == 0
                                              ? "有效"
                                              : scope.row.lockFlag == 2
                                              ? "锁定": ""
                                          }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" align="center" prop="createTime"
                                         width="160">
                        </el-table-column>
                    </el-table>

                    <pagination
                            v-show="userQueryParams.total>0"
                            :total="userQueryParams.total"
                            :page.sync="userQueryParams.pageNum"
                            :limit.sync="userQueryParams.pageSize"
                            @pagination="getUserList"
                    />
                </el-col>
            </el-row>
        </el-dialog>
        <!--异常项弹框-->
        <el-dialog :title="excep.title" :visible.sync="excep.open" width="800px" append-to-body>
            <el-table v-loading="excep.loading" :data="excepList"
                       @selection-change="handleSelectionChange">
                <el-table-column type="selection" align="center"/>
                <el-table-column prop="id" v-if="false"/>
                <el-table-column label="编号" align="center" key="exceptionNum" prop="exceptionNum"/>
                <el-table-column label="设备名称" align="center" key="username" prop="deviceName"/>
                <el-table-column label="异常项目" align="center" key="enable" prop="falutName">
                </el-table-column>
                <el-table-column label="创建人" align="center" key="deptId" prop="createBy"/>
                <el-table-column label="创建时间" align="center" prop="createTime"
                                 width="160">
                </el-table-column>
                <el-table-column label="故障描述" align="center" prop="remark"
                                 width="160">
                </el-table-column>
            </el-table>
            <pagination
                    v-show="excepQueryParams.total>0"
                    :total="excepQueryParams.total"
                    :page.sync="excepQueryParams.pageNum"
                    :limit.sync="excepQueryParams.pageSize"
                    @pagination="getExcepList"
            />
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" :disabled="this.ids.length<=0"  @click="submitList">确 定</el-button>
                <el-button @click="cancelList">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import IconTitle from "@/components/ems/icon-title/index.vue";
    import Pagination from "@/components/Pagination/index.vue"
    import {fetchList} from "@/api/admin/user";
    import {fetchTree} from "@/api/admin/dept";
    import {excepFetchList, getAccountListByExcep} from "@/api/ems/repair/exceptions";
    import {getTree} from "@/api/ems/repository/emsregulations";
    import {addObj} from "@/api/ems/repair/order";
    export default {
        name: "addOrder",
        components: {
            IconTitle,
            Pagination
        },
        data() {
            return {
                defaultProps: {
                    children: "children",
                    label: "name",
                },
                loading: false,
                form: {
                    "exceptionId":[],
                    "repairNum":null,
                    "deviceId":null,
                    "faultGrade":null,
                    "faultRemark":null,
                    "handlerId":null,
                    "handler":null,
                    "repairMethod":"0",
                    "checkName":null,
                    "checkId":null,
                    "outName":null,
                    "expectMoney":null,
                    "duration":null,
                    "outReason":null
                },
                rules: {
                    falutCategoriesId:[
                        { required: true, message: '请选择故障类型', trigger: 'blur' },
                    ],
                    faultGrade:[
                        { required: true, message: '请选择故障等级', trigger: 'blur' },
                    ],
                    faultRemark:[
                        { required: true, message: '请输入故障描述', trigger: 'blur' },
                    ],
                    handler:[
                        { required: true, message: '请选择处理人', trigger: 'blur' },
                    ],
                    repairMethod:[
                        { required: true, message: '请选择维修方式', trigger: 'blur' },
                    ],
                    checkName:[
                        { required: true, message: '请选择验收人', trigger: 'blur' },
                    ],
                    outReason:[
                        { required: true, message: '请输入委外理由', trigger: 'blur' },
                    ],
                },
                //故障等级列表
                faultGradeList:[
                    {"value":0,"label":"一般"},
                    {"value":1,"label":"严重"},
                    {"value":2,"label":"紧急"},
                    {"value":3,"label":"其他"}
                ],
                //维修方式列表
                repairMethodList:[
                    {"value":0,"label":"自修"},
                    {"value":1,"label":"外协"},
                ],
                //user弹窗配置
                user: {
                    title: "",
                    open: false,
                    loading: false,
                    type: null,
                },
                // 用户数组
                userList: [],
                //部门数据
                treeDeptData: [],
                //用户数据分页
                userQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                    deptId: null,
                },
                //异常项数据
                excepList: [],
                //异常数据表格
                excepData:[],
                //异常项数据分页
                excepQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                excep: {
                    title: "",
                    open: false,
                    loading: false,
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                //故障类型列表
                falutCategoriesList: [],
                //设备列表
                accountList:[],
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
            }
        },
        created() {
            this.getSelect()
            this.getAccount()
        },
        methods: {
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addFlag = false;
            },
            // 筛选节点
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            // 节点单击事件
            handleNodeClick(data) {
                this.userQueryParams.deptId = data.id;
                this.getUserList();
            },
            excepGetList(){
                excepFetchList(Object.assign(
                    {
                        current: this.queryParams.pageNum,
                        size: this.queryParams.pageSize,
                    },
                    {status:"0",ids:this.form.exceptionId.toString()}
                    )
                ).then(response => {
                    this.excepData = response.data.data.records;
                    this.queryParams.total = response.data.data.total;
                    this.excep.open = false;
                });
            },
            //获取用户数据
            getUserList() {
                fetchList(Object.assign(
                    {
                        current: this.userQueryParams.pageNum,
                        size: this.userQueryParams.pageSize,
                    },
                    {deptId: this.userQueryParams.deptId}
                    )
                ).then(response => {
                    this.userList = response.data.data.records;
                    this.userQueryParams.total = response.data.data.total;
                    this.user.loading = false;
                });
            },
            //用户行点击事件
            userRowClick(row, event, column) {
                if (this.user.type == 1) {
                    this.form.handlerId = row.userId;
                    this.form.handler = row.username;
                } else if (this.user.type == 0) {
                    this.form.checkId = row.userId;
                    this.form.checkName = row.username;
                }
                this.user.open = false;
            },
            handleUser(){
                this.user.title = "选择处理人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 1;
                this.getUserList()
            },
            checkUser(){
                this.user.title = "选择验收人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 0;
                this.getUserList()
            },
            getSelect() {
                //部门
                fetchTree().then((response) => {
                    this.treeDeptData = response.data.data;
                });
            },
            submitForm(formName) {
                this.form.exceptionId = this.form.exceptionId.toString()
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        console.log(this.form)
                        let data = JSON.parse(JSON.stringify(this.form));
                        addObj(data).then((res) => {
                            this.$parent.listFlag = true;
                            this.$parent.addFlag = false;
                            this.$message.success("新增成功");
                        });
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            //获取异常项数据
            getExcepList(){
                excepFetchList(Object.assign(
                    {
                        current: this.excepQueryParams.pageNum,
                        size: this.excepQueryParams.pageSize,
                    },
                    {status:"0",deviceId:this.form.deviceId,idss:this.form.exceptionId.toString()}
                    )
                ).then(response => {
                    console.log(response)
                    this.excepList = response.data.data.records;
                    this.excepQueryParams.total = response.data.data.total;
                    this.excep.loading = false;
                });
            },
            //设备改变事件
            devChange(){
                this.excepData = []
                this.form.exceptionId = []
                this.queryParams.total = 0
                excepFetchList(Object.assign(
                    {
                        current: 1,
                        size: 100,
                    },
                    {status:"0",deviceId:this.form.deviceId}
                    )
                ).then(response => {
                    if( response.data.data.records.length>0){
                        for (var i = 0; i < response.data.data.records.length; i++) {
                            this.form.exceptionId.push(response.data.data.records[i].id);
                        }
                        this.excepGetList()
                    }
                });

            },
            //添加异常项
            selectExcep(){
                if(this.form.deviceId == null || this.form.deviceId <= 0 ){
                    this.$message({
                        message: '请先选择设备',
                        type: 'warning'
                    });
                    return false;
                }
                this.excep.title = "异常项";
                this.excep.loading = true;
                this.excep.open = true;
                this.getExcepList()
            },
            //关闭
            cancelList(){
                this.excep.title = "";
                this.excep.open = false;
            },
            getFalutList(){
                getTree().then(res => {
                    if (res.data.code == 0) {
                        let common_table_info = [];
                        let treeDataList = [];
                        treeDataList = res.data.data;
                        treeDataList.forEach(function (item, index) {
                            if (item.name == "故障库类型") {
                                common_table_info.push(treeDataList[index])
                            }
                        })
                        this.falutCategoriesList = common_table_info[0].children
                    }
                })
            },
            getAccount(){
                getAccountListByExcep().then(res => {
                    if(res.data.code == 0){
                        this.accountList = res.data.data
                    }
                })
            },
            //删除行数据
            excepDataDele(row){
                for (var i = 0; i < this.excepData.length; i++) {
                    if (this.excepData[i].id == row.id) {
                        this.excepData.splice(i, 1)
                        this.queryParams.total = this.queryParams.total - 1;
                    }
                }
                for (var i = 0; i < this.form.exceptionId.length; i++) {
                    if (this.form.exceptionId[i] == row.id) {
                        this.form.exceptionId.splice(i, 1)
                    }
                }
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            //确认添加异常项
            submitList(){
                this.loading = true;
                for (var i = 0; i < this.ids.length; i++) {
                    this.form.exceptionId.push(this.ids[i]);
                }
                excepFetchList(Object.assign(
                    {
                        current: this.excepQueryParams.pageNum,
                        size: this.excepQueryParams.pageSize,
                    },
                    {status:"0",ids:this.form.exceptionId.toString()}
                    )
                ).then(response => {
                    console.log(response)
                    this.excepData = response.data.data.records;
                    this.queryParams.total = response.data.data.total;
                    this.excep.open = false;
                });
                this.loading = false
            }
        }
    }
</script>

<style lang="scss" scoped>
    .add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;
            }
        }
    }
</style>

<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
</style>
