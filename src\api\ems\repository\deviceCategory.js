/*
 *    Copyright (c) 2018-2025, gewu All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: gewu
 */

import request from '@/utils/request'

export function fetchList(query) {
    return request({
        url: '/ems/emsknocategory/page',
        method: 'get',
        params: query
    })
}

export function fetchMenuBtn(lazy, parentId) {
    return request({
        url: '/ems/emsknocategory/tree',
        method: 'get',
        params: {lazy: lazy, parentId: parentId}
    })
}

export function addObj(obj) {
    return request({
        url: '/ems/emsknocategory',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/ems/emsknocategory/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/ems/emsknocategory/' + id,
        method: 'delete'
    })
}

export function fetchMenuTreeMenu() {
    return request({
        url: '/ems/emsknocategory/tree',
        method: 'get',
        params: {lazy: true, type: 'nobutton'}
    })
}

export function getEmsKnoCategoryIsNotNull() {
    return request({
        url: '/ems/emsknocategory/isNotNull',
        method: 'get',
    })
}

export function fetchMenuTree(lazy, parentId) {
    return request({
        url: '/ems/emsknocategory/tree',
        method: 'get',
        params: {lazy: lazy, parentId: parentId}
    })
}

export function putObj(obj) {
    return request({
        url: '/ems/emsknocategory',
        method: 'put',
        data: obj
    })
}
