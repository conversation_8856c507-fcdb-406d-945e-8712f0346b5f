<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
            <el-form-item label="文档类型名称" prop="docName">
                <el-input style="width: 240px" v-model="queryParams.docName" placeholder="请输入文档类型名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="文档后缀" prop="docSuffix">
                <el-input style="width: 240px" v-model="queryParams.docSuffix" placeholder="请输入文档后缀" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>
            <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
        </el-row>

        <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
            <!-- <template slot="empty"><EmptyModel/></template> -->
          <el-table-column label="文档类型主名称"  prop="docMainName" />
            <el-table-column label="文档类型名称"  prop="docName">
                <template slot-scope="scope">
                    {{ scope.row.docName }}
                </template>
            </el-table-column>
            <el-table-column label="文档后缀"  prop="docSuffix" />
          <el-table-column label="是否必填"  prop="status" >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.required==='YES'" >是</el-tag>
              <el-tag v-else type="info">否</el-tag>
            </template>
          </el-table-column>
            <el-table-column label="创建时间"  prop="createTime" />
            <el-table-column label="更新时间"  prop="updateTime" />

            <el-table-column label="文件名后缀"  prop="remark" />
            <el-table-column label="操作"  class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        >修改</el-button>
                    <el-button class="button--danger" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                        >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <!-- 添加或修改文档类型管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" class="smallDialog" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
              <el-form-item label="文档类型主名称" prop="docMainName">
                <el-input v-model="form.docMainName" placeholder="请输入文档类型主名称" style="width: 400px" />
              </el-form-item>
                <el-form-item label="文档类型名称" prop="docName">
                    <el-input v-model="form.docName" placeholder="请输入文档类型名称" style="width: 400px" />
                </el-form-item>

                <el-form-item label="名称后缀" prop="docSuffix">
                    <el-input v-model="form.docSuffix" placeholder="请输入名称后缀" style="width: 400px" />
                </el-form-item>
              <el-form-item label="文档后缀名" prop="remark">
                <el-select v-model="form.remark" placeholder="文档后缀名" multiple>
                  <el-option v-for="dict in dict.type.file_suffix" :key="dict.value"
                             :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
                <el-form-item label="是否必填" prop="required">
                  <el-select v-model="form.required"  placeholder="选择">
                    <el-option label="是" value="YES"></el-option>
                    <el-option label="否" value="NO"></el-option>
                  </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listDocument, getDocument, delDocument, addDocument, updateDocument } from "@/api/base/flow";

export default {
    name: "Document",
    dicts: ['file_suffix'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 文档类型管理表格数据
            documentList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                docName: null,
                docSuffix: null,
                docMainName: null,
            },
            // 表单参数
            form: {
                remark:[]
            },
            // 表单校验
            rules: {
                delFlag: [
                    { required: true, message: "删除标志;不能为空", trigger: "blur" },
                ],
                docName: [
                    { required: true, message: "请输入文档类型名称", trigger: "blur" },
                ],
                docSuffix: [
                    { required: true, message: "请选择后缀类型", trigger: "change" },
                ],
                remark: [
                    { required: true, message: "请选择文件后缀名", trigger: "change" },
                ],
                required: [
                    { required: true, message: "请选择是否必填", trigger: "change" },
                ],

            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询文档类型管理列表 */
        getList() {
            this.loading = true;
            listDocument(this.queryParams).then(response => {
                this.documentList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                docName: null,
                docSuffix: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: []
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "新增";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getDocument(id).then(response => {
                this.form = {
                    ...response.data,
                    remark:response.data.remark?response.data.remark.split(','):[]
                };
                this.open = true;
                this.title = "编辑";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    const prames ={
                        ...this.form,
                        remark:this.form.remark.join(',')
                    }
                    if (prames.id != null) {
                        updateDocument(prames).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addDocument(prames).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal.confirm('是否确认删除文档类型名称为"' + row.docName + '"的数据项？').then(function () {
                return delDocument(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },

    }
};
</script>


<style lang="scss" scoped>

</style>
