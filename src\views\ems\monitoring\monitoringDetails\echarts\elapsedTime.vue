<template>
  <div id="elapsedTime" :style="{width: '230px', height: '100px'}"></div>
</template>

<script>
export default {
  data() {
    return {};
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let elapsedTime = this.$echarts.init(document.getElementById('elapsedTime'))

      let dataList = [
        { value: 5, name: '运行时间' },
        { value: 2, name: '关机时间' },
        { value: 3, name: '待机时间' },
      ];
      const colorList = ['#26ae61', '#606266', '#e29836'];


      // 绘制图表
      elapsedTime.setOption({
        tooltip: {},
        series: [
          {
            type: 'pie',
            radius: ['30%', '50%'],
            center: ['50%', '50%'],
            label: {
              fontWeight:'bold',
              rich: {
                rich_blue: {
                  color: '#4D88FE',
                },
                rich_orange: {
                  color: '#FFBF3C',
                },
                rich_green: {
                  color: '#50CCCB',
                },
              },
              formatter: function (params) {
                if (params.name === '油路故障') {
                  return `{rich_blue|${params.name}: }` + `{rich_blue|${params.value}} `
                }else if(params.name === '管道故障'){
                  return `{rich_green|${params.name}: }` + `{rich_green|${params.value}} `
                }else if(params.name === '电器故障'){
                  return `{rich_orange|${params.name}: }` + `{rich_orange|${params.value}} `
                }
              },
            },
            itemStyle: {
              normal: {
                borderColor: '#fff',
                borderWidth: 2,
                color: function (params) {
                  return colorList[params.dataIndex];
                },
              },
            },
            data: dataList,
          },
        ]
      });
    }
  }
}

</script>
