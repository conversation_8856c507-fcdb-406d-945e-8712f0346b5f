<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="50%"
    append-to-body
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form
      ref="dateFormRefs"
      :model="dataForm"
      :disabled="formDisabled"
      :rules="dataRules"
      v-loading="formLoading"
      label-width="120px"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="名称" prop="deviceName">
            <el-input v-model="dataForm.deviceName" placeholder="请输入名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格" prop="specification">
            <el-input
              v-model="dataForm.specification"
              placeholder="请输入规格"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际半径" prop="practicalRadius">
            <el-input
              v-model="dataForm.practicalRadius"
              type="number"
              min="0"
              placeholder="请输入实际半径"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产厂家" prop="manufacturer">
            <el-input
              v-model="dataForm.manufacturer"
              placeholder="请输入生产厂家"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产日期" prop="manufactureTime">
            <el-date-picker
              v-model="dataForm.manufactureTime"
              style="width: 100%"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择生产日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生命周期" prop="lifeSpan">
            <el-input v-model="dataForm.lifeSpan" placeholder="请输入生命周期"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="磨损半径" prop="abrasionRadius">
            <el-input
              v-model="dataForm.abrasionRadius"
              type="number"
              min="0"
              placeholder="请输入磨损半径"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        :loading="bthLoading"
        :disabled="updateType === 3"
        @click="handleConfirm"
        >保 存</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { addGoodsManage, updateGoodsManage, getGoodsManageDetail } from "@/api/goods/list";
export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: "",
      dataForm: {},
      dataRules: {
        deviceName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        specification: [{ required: true, message: "请输入规格", trigger: "blur" }],
        practicalRadius: [{ required: true, message: "请输入实际半径", trigger: "blur" }],
        abrasionRadius: [{ required: true, message: "请输入磨损半径", trigger: "blur" }],
        manufacturer: [{ required: true, message: "请输入生产厂家", trigger: "blur" }],
        manufactureTime: [{ required: true, message: "请选择生产日期", trigger: "blur" }],
        lifeSpan: [{ required: true, message: "请输入生命周期", trigger: "blur" }],
      },
      formDisabled: false,
      updateType: null,
      bthLoading: false,
      formLoading: false,
    };
  },
  methods: {
    // 初始化
    init(type, data) {
      this.updateType = type;
      switch (type) {
        case 1: // 新增
          this.dialogTitle = "新增";
          this.dataForm = {
            deviceName: null,
            specification: null,
            practicalRadius: null,
            manufacturer: null,
            manufactureTime: null,
            lifeSpan: null,
            abrasionRadius: null,
          };
          break;
        case 2: // 编辑
          this.dialogTitle = "编辑";
          this.dataForm.id = data.id;
          this.getDetails();
          break;
        case 3: // 查看
          this.dialogTitle = "查看";
          this.formDisabled = true;
          this.dataForm.id = data.id;
          this.getDetails();
          break;
        default:
          break;
      }
      this.dialogVisible = true;
    },
    // 获取详情
    getDetails() {
      this.formLoading = true;
      getGoodsManageDetail(this.dataForm.id).then((res) => {
        this.dataForm = { ...res.data };
        this.formLoading = false;
      });
    },
    // 关闭弹窗取消验证
    handleClose() {
      this.dialogVisible = false;
      this.formDisabled = false;
      this.formLoading = false;
      this.$refs.dateFormRefs.resetFields();
    },
    // 提交表单
    handleConfirm() {
      this.$refs.dateFormRefs.validate((valid) => {
        if (valid) {
          const submitWheel = this.updateType === 1 ? addGoodsManage : updateGoodsManage;
          this.bthLoading = true;
          submitWheel({ ...this.dataForm })
            .then((res) => {
              this.dialogVisible = false;
              this.bthLoading = false;
              this.$message.success("操作成功");
              this.$emit("update:success");
            })
            .catch(() => {
              this.bthLoading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
