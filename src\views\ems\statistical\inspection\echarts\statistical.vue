<template>
  <div id="myChart" :style="{width: '90px', height: '90px'}"></div>
</template>

<script>
import {
  statusList
} from "@/api/ems/statistical/device";

import echarts from 'echarts'
import 'echarts-liquidfill'

export default {
  data() {
    return {
      status: [],
      array: [],
      sum: "1",
      num: "",
      value: ""
    };
  },
  props: {
    checkingTodayCompletionData:{
      type: Object
    }
  },
  watch: {
    checkingTodayCompletionData: function(newVal,oldVal){
      let a, b;
      this.cData = newVal;  //newVal即是chartData
      a = this.cData.num;
      b = this.cData.allNum;
      this.drawLine(a,b);
    }
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    // this.getStatusList();
  },
  methods: {

    // // 状态统计
    // getStatusList() {
    //   statusList().then(res => {
    //     this.status = res.data.data;
    //
    //     // 转换this.status[i].statusNum的值为int类型
    //     for (let i = 0; i < this.status.length; i++) {
    //       this.status[i].statusNum = parseInt(this.status[i].statusNum);
    //     }
    //     // 把数组中每个对象的statusNum值取到一个新的数组中
    //     this.array = this.status.map(item => {
    //       return item.statusNum;
    //     })
    //
    //     // 把新数组中的每个元素相加
    //     this.sum = this.array.reduce(function (a, b) {
    //       return a + b;
    //     }, 0)
    //     // 得到完好设备的完好率
    //     this.num = this.status[0].statusNum;
    //     this.value = this.num / this.sum;
    //     this.drawLine();
    //   })
    //
    // },

    drawLine(a, b) {
      let value = this.value;

      // 基于准备好的dom，初始化echarts实例
      let myChart = this.$echarts.init(document.getElementById('myChart'))

      // 绘制图表
      myChart.setOption({
        backgroundColor: '#fff',
        title: [
          {
            text: '完成率',
            left: '48%',
            top: "58%",
            textAlign: 'center',
            textStyle: {
              fontSize: '12',
              fontWeight: '400',
              color: '#fff',
              textAlign: 'center',
            },
          },
          {
            text: (a === 0 ? 0 : ((a / b).toFixed(2) * 100)) + '%',
            left: '48%',
            top: '25%',
            textAlign: 'center',
            textStyle: {
              fontSize: 18,
              color: '#fff',
            },
          },
        ],
        series: [{
          type: 'liquidFill',
          radius: '90%',
          z: 6,
          center: ['50%', '50%'],
          amplitude: 5,
          backgroundStyle: {
            borderWidth: 1,
          },
          color: [
            new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0.6,
              color: "#72D1FB",
            }, {
              offset: 0.8,
              color: "#43ABF7",
            }
            ])],
          data: [value + 0.02,
            {
              value: value - 0.01,
              direction: 'left',
            },
            value - 0.01,
          ],
          label: {
            normal: {
              formatter: '',
            }
          },
          outline: {
            show: true,
            itemStyle: {
              borderWidth: 0,
            },
            borderDistance: 0,
          }
        }
        ]
      });
    }
  }
}

</script>
