import { requestPlatForm } from "@/utils/requestBase";

import request from "@/utils/request";
// 查询列表
export function getConsultList(query) {
  return requestPlatForm({
    url: "/consult/list",
    method: "get",
    params: query,
  });
}
// 查询咨询类型
export function getProductList() {
  return requestPlatForm({
    url: "/product/getProductList/1",
    method: "get",
  });
}

export function getMyList(query) {
  return requestPlatForm({
    url: "/consult/myList",
    method: "get",
    params: query,
  });
}