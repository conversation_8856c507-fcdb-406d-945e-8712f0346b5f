<template>
  <div class="drawer-box">
    <div class="info-box">
      <div class="info-item">
        <span class="labelS">设备名称：</span>
        <span class="contentS">{{ device.deviceName }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">设备编号：</span>
        <span class="contentS">1{{ device.deviceNum }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">位置：</span>
        <span class="contentS">{{ device.location }}</span>
      </div>
      <div class="info-item">
        <img class="sbtzxq" :src="require('@/assets/imagesAssets/sbtzxq.png')">
      </div>
      <!--<div class="info-item">
          <span class="labelSMS">描述信息</span>
          <span class="content-status">{{order.faultRemark}}</span>
      </div>-->
    </div>
    <div class="tab-box">
      <el-tabs tab-position="left" style="font-size: 12px;">
        <!--<el-tab-pane label="审批信息" style="font-size: 12px">-->
        <!--  <div class="basic-box-con">-->
        <!--    <div class="basic-big">-->
        <!--      <div class="box-card">-->
        <!--        <IconTitle title="基本信息" imgUrl="yunwei">-->
        <!--        </IconTitle>-->
        <!--        &lt;!&ndash; 背景图片&ndash;&gt;-->

        <!--      </div>-->
        <!--    </div>-->
        <!--  </div>-->
        <!--</el-tab-pane>-->
        <el-tab-pane label="计划详情">
          <div class="basic-box-con">
            <div class="basic-big">
              <div class="box-card">
                <IconTitle title="维修时间轴" imgUrl="yunwei">
                </IconTitle>
                <div class="block">
                  <el-timeline>
                    <el-timeline-item v-for="(item, index) in list" color="#666666" :key="index"
                                      :timestamp="getTitlt(item.actions)" placement="top">
                      <div class="contentStyle">
                        <div class="boxContent">
                          <span class="desc">{{ item.handleContent }}</span>
                          <span class="status">
                                                            <i class="el-icon-success"></i>正常
                                                        </span>
                        </div>
                        <div>
                          <span class="label">处理人：</span>
                          <span class="content">{{ item.handler }}</span>
                        </div>
                        <div>
                          <span class="label">执行时间：</span>
                          <span class="content"> {{ item.handleTime }}</span>
                        </div>
                        <div class="line"></div>
                        <div>
                          <span></span>
                          <span class="label timeTotal">用时：</span>
                          <span>6时32分</span>
                        </div>
                        <div v-if="item.actions == '验收' && order.score != null && index == list.length-1">
                          <span></span>
                          <span class="label timeTotal">评分：</span>
                          <span>{{ order.score }}</span>
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import {getOrder} from "@/api/ems/repair/order";
import IconTitle from "@/components/ems/icon-title/index.vue";

export default {
  name: "drawerCon",
  components: {
    IconTitle,
  },
  data() {
    return {
      /* device: {},
       order: {},
       list: [],*/
      getTitlt(name) {
        if (name == 0) {
          name = "报修"
        }
        if (name == 1) {
          name = "分配"
        }
        if (name == 2) {
          name = "接收"
        }
        if (name == 3) {
          name = "维修"
        }
        if (name == 4) {
          name = "验收"
        }
        if (name == 5) {
          name = "转外委"
        }
        return "任务节点：" + name;
      }
    };
  },
  props: {
    id: {
      type: Number,
    },
    device: {
      type: Object,
    },
    order: {
      type: Object,
    },
    list: {
      type: Array,
    }
  },
  mounted() {

  },
  created() {
    /*this.getOrderDetail();*/
  },

  methods: {
    getOrderDetail() {
      /*getOrder(this.id).then((res)=>{
          this.device = res.data.data.device
          this.order = res.data.data.order
          this.list = res.data.data.list
          console.log(res.data.data)
      })*/
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/mixin.scss";
@import "@/styles/color.scss";

::v-deep .el-drawer__body {
  overflow: auto;
}

.drawer-box {
  .info-box {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #f2f2f5;
    margin-bottom: 20px;
    margin-left: 50px;

  }

  .info-item {
    display: flex;
    margin-bottom: 10px;

    .sbtzxq {
      float: left;
      margin-top: -143px;
      margin-left: 250px;
    }

    .labelSMS {
      width: 15%;
      color: rgba(193, 200, 210, 100);
      font-size: 12px;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
    }

    .labelS {
      width: 15%;
      color: #888;
      font-size: 12px;
      margin-left: -50px;
    }

    .contentS {
      font-weight: bold;
      color: #101010;
      display: inline-block;
      font-size: 12px;
      margin-top: 0px;
    }

    .content-status {
      margin-right: 85px;
      font-size: 12px;
      display: flex;

      .sbzt {
        display: flex;
      }

      .circle-lan {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-lv {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, $theme);
      }

      .circle-zi {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-blue {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #498ae8);
      }

      .circle-green {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #5ec28e);
      }

      .circle-purple {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #7748d4);
      }

      .circle-red {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ff0000);
      }

      .circle-yellow {
        margin-top: 3px;
        margin-left: 10px;
        @include gradeCircle-v1(10px, #ffff00);
      }

    }
  }

  .tab-box {
    .basic-box-con {
      overflow: auto;
      height: 500px;

      .basic-big {
        .box-card {
          padding: 10px;
          box-shadow: 0px 0px 7px 0px #eff2f5;
          margin: 5px;

          .card-image {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            margin-left: 10px;

            .image {
              left: 1435px;
              top: 776px;
              width: 124px;
              height: 113px;
              margin-top: 10px;
              margin-left: 5px;
              border-radius: 5px;
            }

            //.image_tar{
            //  width: 50%;
            //  height: 50%;
            //}
          }

          .bjq {
            position: absolute;
            left: 310px;
            top: 100px;
          }

          .card-item {
            display: flex;
            align-items: center;
            margin-top: 10px;

            .icon-gongsi {
              color: #128bed;
            }

            .icon-map1 {
              color: #128bed;
            }

            span {
              display: inline-block;
              width: 11%;
              color: #888;
              text-align: right;
              margin-right: 10px;
            }

            .card-con {
              color: #101010;
            }
          }

          .image {
            left: 1435px;
            top: 776px;
            width: 116px;
            height: 83px;
          }

          .el-table {
            margin-top: 20px;
          }
        }
      }
    }
  }
}
.block {
  margin-top: 20px;
  width: 80%;
  margin-left: 10%;
}

::v-deep .el-timeline-item__timestamp {
  color: #0CCB82;
}

::v-deep .el-timeline-item__tail {
  border-left: 2px dashed #EDEEF2 !important;
}

::v-deep .contentStyle {
  padding: 10px;
  box-shadow: 0px 2px 6px 0px rgba(168, 168, 168, 10);

  .line {
    border: 1px solid rgba(239, 239, 244, 100);
    margin: 10px 0;
  }
}

::v-deep .status {
  float: right;
  margin-right: 10px;

  i {
    color: #0ccb82;
    margin-right: 5px;
  }
}

::v-deep .boxContent {
  background-color: #F6F6FC;
  font-size: 14px;
  height: 50px;
  line-height: 50px;
  margin: 10px 0 10px;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    height: 50px;
    width: 5px;
    background-color: #DEDEE4;

  }

  .desc {
    margin-left: 10px;
  }

}

::v-deep .label {
  color: #999;
  margin-right: 10px;
  display: inline-block;
  margin-bottom: 10px;
  width: 70px;
}

::v-deep .content {
  color: #101010;
}

::v-deep .timeTotal {
  margin-right: 20px;
}

::v-deep .el-tabs--left .el-tabs__header.is-left {
  width: 11%;
}

</style>
