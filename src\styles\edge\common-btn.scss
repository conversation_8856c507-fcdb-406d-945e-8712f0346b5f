.edge-common-btn-refresh,.edge-common-btn-refresh:hover,.edge-common-btn-refresh:focus,.edge-common-btn-refresh:active{ //刷新按钮
  background: #293B4D;
  border: 1px solid #293B4D;
}
.edge-common-btn-add,.edge-common-btn-add:hover,.edge-common-btn-add:focus,.edge-common-btn-add:active{ // 新增按钮
  background: #26AE61;
  border: 1px solid #26AE61;
}
.edge-common-btn-del,.edge-common-btn-del:hover,.edge-common-btn-del:focus,.edge-common-btn-del:active{ // 删除
  background: #E74C3D;
  border: 1px solid #E74C3D;
}
.edge-common-btn-start,.edge-common-btn-start:hover,.edge-common-btn-start:focus,.edge-common-btn-start:active{ // 开启按钮
  background: #52ACAE;
  border: 1px solid #52ACAE;
}
.edge-common-btn-stop,.edge-common-btn-stop:hover,.edge-common-btn-stop:focus,.edge-common-btn-stop:active{ // 停止按钮
  background: #F0A82B;
  border: 1px solid #F0A82B;
}
.edge-common-btn-more,.edge-common-btn-more:hover,.edge-common-btn-more:focus,.edge-common-btn-more:active{ // 更多操作按钮
  background: #293B4D;
  border: 1px solid #293B4D;
  margin-left: 15px;
}
.edge-common-btn-search,.edge-common-btn-search:hover,.edge-common-btn-search:focus,.edge-common-btn-search:active{ // 搜索按钮
  background: #52B7F5;
  border: 1px solid #52B7F5;
  margin-left: 10px;
}
.edge-common-btn-remove,.edge-common-btn-remove:hover,.edge-common-btn-remove:focus,.edge-common-btn-remove:active{ // 清空按钮
  background: #ECF0F4;
  border: 1px solid #ECF0F4;
  margin-left: 10px;
  color: #282828;
}
.tag1 {
  display: inline-block;
  width: 61px;
  height: 20px;
  line-height: 18px;
  border-radius: 2px;
  background-color: rgba(238, 248, 232, 100);
  color: rgba(122, 199, 86, 100);
  font-size: 12px;
  text-align: center;
  font-family: Roboto;
  border: 1px solid rgba(122, 199, 86, 100);
}
.tag2 {
  display: inline-block;
  width: 61px;
  height: 20px;
  line-height: 18px;
  border-radius: 2px;
  font-size: 12px;
  text-align: center;
  background-color: rgba(253, 245, 233, 100);
  color: rgba(226, 152, 54, 100);
  border: 1px solid rgba(226, 152, 54, 100);
}