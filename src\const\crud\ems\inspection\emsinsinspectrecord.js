export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "addBtn": false,
    "refreshBtn": false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "gridBtn": false,
     menu:false,

  "column": [
	  {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
          hide:true
    },	  {
      "type": "input",
      "label": "记录编号",
      "prop": "recordNum",
      "span": 12,
      "search":true,
    },	  {
          "type": "input",
          "label": "任务编号",
          "prop": "taskNum",
          "span": 12
      },  {
          "type": "input",
          "label": "设备编号",
          "prop": "deviceNum",
          "span": 12,
          "search":true,
      }, {
          "type": "input",
          "label": "设备名称",
          "prop": "deviceName",
          "span": 12
      },  {
          "type": "input",
          "label": "所属部门",
          "prop": "deptId",
          "span": 12
      }, {
          "type": "input",
          "label": "位置",
          "prop": "locationName",
          "span": 12
      },{
      "type": "input",
      "label": "点检人",
      "prop": "inspector",
      "span": 12
    },	  {
      "type": "input",
      "label": "点检时间",
      "prop": "inspectionTime",
      "span": 12
    },
    // {
    //   label: '日期',
    //   prop: 'inspectionTime',
    //   type:'datetime',
    //   dateDefault: true,
    //   format: 'yyyy-MM-dd HH:mm:ss',                  // 这是组件展示的日期格式
    //   valueFormat: 'yyyy-MM-dd',        // 这是组件value值的格式
    //   searchSpan: 9,
    //   searchRange:true,
    //   search:true,
    //   searchClearable: false,
    // }
  ]
}
