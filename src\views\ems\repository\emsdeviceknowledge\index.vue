<template>
  <div class="execution">
    <el-card class="sbzl">
      <div class="sbtp">
        <img :src="require('@/assets/imagesAssets/sbzl.png')"/>
      </div>
      <div class="tbzl">
        <div class="anzhuo">
          <img src="@/assets/svg/anzhuo.svg"/>
        </div>
        <div class="zl">
          <span>知识设备</span>
        </div>
      </div>
      <div class="sm">
        可以管理平台菜单和应用菜单，采用统一管理方式实现对所有系统菜单的统一增删改查，方便用户操作及统一管控
      </div>
    </el-card>
    <el-card class="box-card btn-search page-search crud">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     style="backgroundColor:#E1b980"
                     icon="el-icon-circle-plus-outline"
                     v-if="permissions.ems_emsdeviceknowledge_add"
                     @click="deviceAdd"
          >新增
          </el-button
          >
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="form">
        <el-form :inline="true">
          <el-form-item label="知识名称">
            <el-input placeholder="请输入知识名称" v-model="searchForm.title" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChangeU">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <basic-container class="container">

      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <IconTitle title="知识列表" imgUrl="yunwei"/>
          <div style="display: flex; flex-direction: column">
            <div class="zslb" v-for="item in tableData">
              <div class="row">
                <div class="image">
                  <img :src="headIMG(item.imgShow)">
                </div>
                <div class="lb">
                  <div class="wz">
                    <div class="zsmc">
                      <span style="font-weight: 700">{{ item.title }}</span>
                      <span style="color: blue">【{{ item.category }}】</span>
                      <el-button type="text" size="mini" style="float: right; margin-left: 15px"
                                 @click="handleDel(item.id)">删除
                      </el-button>
                      <el-button type="text" size="mini" style="float: right" @click="deviceEdit(item.id)">编辑</el-button>
                    </div>
                    <span style="color: #8c939d;margin-top: 5px">{{ item.description }}</span>
                  </div>
                  <div class="time">
                    <span style="color: #8c939d; margin-top: 5px; float: right">{{ item.createTime }}</span>
                  </div>

                </div>
              </div>
            </div>
            <pagination
                style="text-align: right;"
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
          </div>
        </el-col>
      </el-row>
    </basic-container>

  </div>
</template>
<script>
import {getTree} from "@/api/ems/repository/emsdevicedata";
import {fetchList, getObj, addObj, putObj, delObj} from "@/api/ems/repository/emsdeviceknowledge";

import {tableOption} from '@/const/crud/ems/repository/emsdevicedata'
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
import Tenant from "@/views/admin/tenant";
import DrawerCon from "../emsdevicedata/drawerCon"
import deviceUpload from "../emsdevicedata/deviceUpload";
import {fetchListTree} from "@/api/ems/equipment/category";
import Treeselect from "@riophae/vue-treeselect";

export default {
  name: 'emsdevicedata',
  components: {
    Tenant,
    IconTitle,
    DrawerCon,
    deviceUpload,
    Treeselect
  },
  data() {
    return {
      categoryList: [], //设备类别

      //分页
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },

      title: "",
      tableData: [],
      searchForm: {
        title: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      editId: 0,
      // 树状图
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        props: {
          label: "name",
          value: "id",
        },
        filterText: "搜索关键字自定义",
        defaultExpandAll: true
      },
      treeData: [],

      // 详情页
      drawer: false,
      drawerData: {},
      fileArray: [],

      // 新增和修改弹出框
      // dialogFormVisible: false,
      rowsData: {}
    };
  },

  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsdeviceknowledge_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsdeviceknowledge_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsdeviceknowledge_edit, false),
      };
    },
  },
  created() {
    this.getSelect();
  },
  mounted() {
    this.initElement();
    this.changeThme();
    this.getTreeData();
    this.getList();
  },
  methods: {

    // 新增
    deviceAdd() {
      this.$router.push({
        path: "/ems/repository/emsdeviceknowledge/addDeviceknowledge",
      });
    },

    // 修改
    deviceEdit(id) {
      console.log(id)
      this.$router.push({
        path: "/ems/repository/emsdeviceknowledge/addDeviceknowledge",
        query: {
          id: id
        }
      });
    },


    // 设备知识树状图数据
    getTreeData() {
      getTree().then(res => {
        if (res.data.code == 0) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data.data;
          treeDataList.forEach(function (item, index) {
            if (item.name == "设备知识类型") {
              common_table_info.push(treeDataList[index])
            }
          })
          this.treeData = common_table_info;
        }
      })

    },

    nodeClick(data) {
      this.page.page = 1;
      this.getList(this.page, {categoryId: data.id});
    },

    searchChangeU(param, done) {
      //console.log(this.$refs.crud)
      this.page.currentPage = 1
      this.getList(this.page, this.searchForm)
      //done()
    },
    resetBtn() {
      this.searchForm.title = '';
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      // debugger;
      this.tableLoading = true;
      fetchList(
          Object.assign({
            // current: page.currentPage,
            // size: page.pageSize,
          }, params, this.searchForm)).then((res) => {
        this.tableData = res.data.data.records;

        // console.log("qqq",JSON.stringify(this.tableData));
        this.total = res.data.data.total;
        this.tableLoading = false;
      }).catch(() => {
        this.tableLoading = false;
      });
    },

    //编辑
    handleEdit() {
      var refsDate = this.$refs
      refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
    },
    // 删除
    handleDel: function (id) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 更新
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      addObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    headIMG(img) {
      const imgData = require('@/assets/imagesAssets/kong.png')
      return img != null ? img.url : imgData
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.downBlobFile(
          "/ems/emsdevicedata/export",
          this.searchForm,
          "emsdevicedata.xlsx"
      );
    },
    // 改变主题颜色
    changeThme() {
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor = this.theme;
    },

    getSelect() {
      fetchListTree("").then((res) => {
        this.categoryList = res.data.data ? res.data.data : [];
      });
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="scss">

.drawerStyle {
  ::v-deep .el-drawer__header {
    background-color: #f2f2f5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 20px;
  }
}

.execution {
  font-size: 12px;

  .container {
    .user__main {
      .zslb {

        .row {

          border-bottom: 2px solid #f0f2f5;
          padding: 15px 0px 0px 15px;
          float: left;
          display: flex;
          width: 100%;

          .image {
            img {
              width: 60px;
              height: 60px;
            }
          }

          .lb {
            width: 100%;
            float: right;
            margin-left: 20px;

            .wz {
              display: flex;
              flex-direction: column;

              .zsmc {

              }
            }
          }
        }
      }
    }
  }

  .crud {
    .form {
      float: top;
    }
  }

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }
}

.sbzl {
  margin-bottom: 10px;
  height: 120px;

  .sbtp {
    width: 147px;
    height: 100px;
    float: right;
    margin-right: 24px;
    margin-top: -10px;
  }

  .tbzl {
    display: flex;
    flex-direction: row;

    .anzhuo {
      left: 262px;
      top: 100px;
      width: 15px;
      height: 15px;
      color: rgba(89, 89, 89, 100);
      margin-right: 13px;
    }

    .zl {
      left: 295px;
      top: 95px;
      width: 72px;
      height: 27px;
      color: rgba(89, 89, 89, 100);
      font-size: 18px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
    }
  }

  .sm {
    left: 262px;
    top: 152px;
    width: 700px;
    height: 20px;
    color: rgba(134, 129, 129, 100);
    font-size: 12px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
    margin-top: 40px;
  }

}
</style>
