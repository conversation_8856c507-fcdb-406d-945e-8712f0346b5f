export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  editBtn:false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": false,
  "column": [
    {
      "type": "input",
      "label": "编号",
      "prop": "faultNumber",
      "span": 12,
      width: 150
    },
    {
      "type": "input",
      "label": "故障名称",
      "prop": "faultName",
      "span": 12
    },
    {
      "type": "input",
      "label": "部位",
      "prop": "position",
      "span": 12
    },
    {
      "label": "故障描述",
      "prop": "faultDescription",
      "span": 12
    },
    {
      "type": "textarea",
      "label": "故障现象",
      "prop": "faultPhenomenon",
      "span": 12,
      overHidden: true
    },
    {
      "type": "input",
      "label": "解决办法",
      "prop": "solution",
      "span": 12
    },
    {
      "type": "input",
      "label": "预防对策",
      "prop": "prevention",
      "span": 12
    },
    {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12
    },

    // {
    //   "type": "input",
    //   "label": "适用设备类别",
    //   "prop": "applicableDeviceCategory",
    //   "span": 12,
    //   width: 120
    // },
    // {
    //   "type": "input",
    //   "label": "id",
    //   "prop": "id",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "创建人",
    //   "prop": "createBy",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "更新时间",
    //   "prop": "updateTime",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "更新人",
    //   "prop": "updateBy",
    //   "span": 12
    // },
    //
    //
    // {
    //   "type": "input",
    //   "label": "使用设备id",
    //   "prop": "deviceId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "备注",
    //   "prop": "remark",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "删除标志",
    //   "prop": "delFlag",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "租户id",
    //   "prop": "tenantId",
    //   "span": 12
    // }
  ]
}
