import request from '@/utils/request'
//维修统计-故障等级分布
export function getFailureLevelDistributeList() {
  return request({
    url: '/ems/repairStatistic/failureLevelDistribute',
    method: 'get',
  })
}

//维修统计-故障类型分布
export function getFailureTypeDistributeList() {
  return request({
    url: '/ems/repairStatistic/failureTypeDistribute',
    method: 'get',
  })
}

//维修统计-故障次数
export function getFailureNumberList() {
  return request({
    url: '/ems/repairStatistic/failureNumber',
    method: 'get',
  })
}

//维修统计-维修费用
export function getMaintenanceCostsList() {
  return request({
    url: '/ems/repairStatistic/maintenanceCosts',
    method: 'get',
  })
}

//维修统计-时间
export function getMaintenanceTimeList() {
  return request({
    url: '/ems/repairStatistic/maintenanceTime',
    method: 'get',
  })
}

//维修统计-月度统计
export function getMonthlyStatisticsList() {
  return request({
    url: '/ems/repairStatistic/monthlyStatistics',
    method: 'get',
  })
}

//维修统计-基本信息
export function getBasicInformationList() {
  return request({
    url: '/ems/repairStatistic/basicInformation',
    method: 'get',
  })
}

//维修统计-微信小程序排名
export function getRankingList() {
  return request({
    url: '/ems/repairStatistic/ranking',
    method: 'get',
  })
}

