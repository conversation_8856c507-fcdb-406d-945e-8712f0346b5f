<template>
    <div class="add-box" style="height: 100%">
        <div class="info-box">
            <el-card shadow="always" class="box-card">
                <div class="tableTitle"><span>基本信息</span></div>
                <div class="devTitle"><span>{{form.standardName}}</span></div>
                <div>
                    <div class="tableStyle">
                        <div class="labelS">标准编号</div>
                        <div class="contentS">{{form.standardNum}}</div>
                    </div>
                    <div class="tableStyle">
                        <div class="labelS">标准名称</div>
                        <div class="contentS">{{form.standardName}}</div>
                    </div>
                    <div class="tableStyle">
                        <div class="labelS">所属部门</div>
                        <div class="contentS">{{form.deptName}}</div>
                    </div>
                    <div class="tableStyle">
                        <div class="labelS">使用类别</div>
                        <div class="contentS">{{form.categoryName}}</div>
                    </div>
                    <div class="tableStyle">
                        <div class="labelS">要求</div>
                        <div class="contentS">
                            {{form.requirement}}
                        </div>
                    </div>
                    <div class="tableStyle">
                        <div class="labelS">备注</div>
                        <div class="contentS">
                            {{form.remark}}
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <div class="info-box">
            <el-card shadow="always" class="box-card">
                <IconTitle title="设备配置" imgUrl="yunwei"></IconTitle>
                <!--                @selection-change="handleSelectionChange"-->
                <el-table v-loading="loading" :data="deviceList">
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                    <el-table-column label="设备名称" align="center" prop="deviceName"/>
                    <el-table-column label="品牌" align="center" prop="brandNewName"/>
                    <el-table-column label="规格型号" align="center" prop="specification"/>
                </el-table>

                <pagination
                        v-show="queryParams.total>0"
                        :total="queryParams.total"
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="deviceGetList"
                />
            </el-card>
        </div>
        <div class="info-box">
            <el-card shadow="always" class="box-card">
                <IconTitle title="保养项目" imgUrl="yunwei"></IconTitle>
                <el-table v-loading="check.loading" :data="checkList">
                    <el-table-column
                            label="序号"
                            width="70px">
                        <template slot-scope="scope">
                            {{scope.$index+1}}
                        </template>
                    </el-table-column>
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="编号" align="center" prop="itemsNum"/>
                    <el-table-column label="项目" align="center" prop="itemsName"/>
                    <el-table-column label="方法及基准" align="center" prop="methodBenchmark"/>
                    <el-table-column label="类型" align="center" prop="type">
                        <template slot-scope="scope">
                      <span>{{
                        scope.row.type == 1
                          ? "数字"
                          : scope.row.type == 2
                          ? "选项"
                          : ""
                      }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="下限" align="center" prop="lowerLimit"/>
                    <el-table-column label="上线" align="center" prop="upperLimit"/>
                    <el-table-column label="可选项" align="center" prop="optional"/>
                    <el-table-column label="正常选项" align="center" prop="normalOption"/>
                    <el-table-column label="参考图片" align="center" prop="referencePicture"/>
                </el-table>
                <pagination
                        v-show="queryParamsCheckList.total>0"
                        :total="queryParamsCheckList.total"
                        :page.sync="queryParamsCheckList.pageNum"
                        :limit.sync="queryParamsCheckList.pageSize"
                        @pagination="checkGetList"
                />
            </el-card>
        </div>
        <div class="info-btn-box">
            <el-button @click="goBack">返回</el-button>
        </div>
    </div>
</template>
<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import {fetchList, emsmaimaintenancestandardGetObj, addObj, putObj, delObj, deviceList, deviceListAll} from "@/api/ems/maintenance/emsmaimaintenancestandard";
    import {
        checkFetchList,
        checkDelObj,
        checkGetObj,
        checkAddObj,
        checkPutObj
    } from '@/api/ems/maintenance/emsmaimaintenanceitems';
    import Treeselect from "@riophae/vue-treeselect";
    import {getDept} from '@/api/system/dept';
    export default {
        name: "detailIndex",
        components: {
            IconTitle,
        },
        props: {
            id: {
                type: String,
            },
        },
        data() {
            return {
                // 设备数据
                deviceList: [],
                loading: false,
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                //点检项目
                checkList: [],
                check: {
                    title: "",
                    open: false,
                    loading: false,

                },
                queryParamsCheckList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                form: {
                    id: null,
                    standardNum: null,
                    standardName: null,
                    deptId: null,
                    requirement: null,
                    remark: null,
                    categoryName:null,
                },
            };
        },
        mounted() {
            if (this.id > 0) {
                emsmaimaintenancestandardGetObj(this.id).then((res) => {
                    this.form = res.data
                    // console.log(JSON.stringify(res))
                    deviceList(Object.assign(
                        {
                            current: this.queryParams.pageNum,
                            size: this.queryParams.pageSize,
                        },
                        {id: this.id}
                        )
                    ).then(response => {
                        this.deviceList = response.data.records;
                        this.queryParams.total = response.data.total;
                        for (var i = 0; i < this.deviceList.length; i++) {
                            this.device.deviceForm.deviceId.push(this.deviceList[i].id);
                        }
                        this.loading = false;
                        this.device.open = false;
                    });
                    if (this.form.deptId!=null){
                        getDept(this.form.deptId).then((res) => {
                            this.form.deptName=res.data.deptName;
                        });
                    }
                });
                this.checkGetList();

            }
            // fetchTree().then((response) => {
            //     this.treeData = response.data;
            // });
        },
        methods: {
            deviceGetList() {
                this.device.loading = true;
                deviceList(Object.assign(
                    {
                        current: this.queryParamsDeviceList.pageNum,
                        size: this.queryParamsDeviceList.pageSize,
                    },
                    {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceNewList = response.data.records;
                    this.queryParamsDeviceList.total = response.data.total;
                    this.device.loading = false;
                });
            },
            checkGetList() {
                this.check.loading = true;
                checkFetchList(Object.assign(
                    {
                        current: this.queryParamsCheckList.pageNum,
                        size: this.queryParamsCheckList.pageSize,
                    },
                    {standardId: this.id}
                    )
                ).then(response => {
                    this.checkList = response.data.records;
                    this.queryParamsCheckList.total = response.data.total;
                    this.check.loading = false;
                });
            },

            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";
    add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;

            }

        }
    }
    .tableTitle {
        color: #333;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .devTitle {
        color: #262626;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .info-btn-box {
        width: 100%;
        text-align: center;
    }

    .box-card {
        margin-bottom: 20px;

        .el-card__body {
            padding-top: 10px;
        }

        .labelS {
            //display: inline-block;
            width: 150px;
            //height: 40px;
            // margin-right: 10px;
            text-align: left;
            color: #606266;
            padding: 10px;
            border: 1px solid rgba(236, 240, 244, 100);
            margin-bottom: -1px;
        }

        .contentS {
            border: 1px solid rgba(236, 240, 244, 100);
            // height: 40px;
            color: #606266;
            width: 100%;
            margin-left: -1px;
            margin-bottom: -1px;
            padding: 10px;
            // margin: 10px 0;
            // width: calc(100% - 120px);
            // display: inline-block;
        }

        .tableStyle {
            display: flex;
        }
    }

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
</style>
