<template>
  <div class="maintenance">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="6">
            <div class="situation">
            <!--      今日完成情况        -->
              <div class="echarts-item" style="height: 100px; margin-bottom: 10px">
                <div class="item-title">
                  <i class="icon-ziliao"></i>
                  <span style="font-weight: 600; color: #847f8c">今日完成情况</span>
                </div>
                <div style="display: flex; margin-left: 15px">
                  <span style="font-weight: 600; color: #3f3365;font-size: 18px; margin-top: 20px">{{ todayCompletionData.num }} / {{ todayCompletionData.allNum }}</span>
                  <span style="color: #b4aeb1;margin: 20px 0 0 5px">次</span>
                  <!--<ball style="margin: -25px 0 0 80px" :todayCompletionData="todayCompletionData"/>-->
                  <statistical style="margin: -25px 0 0 90px" :todayCompletionData="todayCompletionData"/>
                </div>
              </div>

              <!--      历史未完成        -->
              <div class="echarts-item" style="height: 100px">
                <div class="item-title">
                  <i class="icon-ziliao"></i>
                  <span style="font-weight: 600; color: #847f8c">历史未完成</span>
                </div>
                <div style="display: flex">
                  <div style="display: flex; flex-direction: column; margin: 10px 0 0 20px">
                    <span style="font-weight: 600; margin-bottom: 10px">今年</span>
                    <div>
                      <span style="font-weight: 600; font-size: 18px; color: #7c90c2">{{ unfinishedHistoryData.num }}</span>
                      <span style="color: #b4aeb1; margin-left: 5px">次</span>
                    </div>
                  </div>

                  <div style="display: flex; flex-direction: column; margin: 10px 0 0 130px">
                    <span style="font-weight: 600; margin-bottom: 10px">总数</span>
                    <div>
                      <span style="font-weight: 600; font-size: 18px; color: #676767">{{ unfinishedHistoryData.allNum }}</span>
                      <span style="color: #b4aeb1; margin-left: 5px">次</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="echarts-item" style="height: 210px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">季度统计</span>
              </div>
              <div>
                <transverse style="margin: -30px 0 0 -20px"/>
              </div>
            </div>
          </el-col>
          <el-col :span="10"
          >
            <div class="echarts-item" style="height: 210px; padding: 10px 0px;">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">周统计</span>
              </div>
              <div class="weeks">
                <div class="total">
                  <div class="complete">
                    <span style="margin: 10px;">完成数(次)</span>
                    <span style="color: #b47b81; font-size: 18px; font-weight: 600; margin: 0 15px;">{{ weekStatisticsData.accomplishNum }}</span>
                  </div>
                  <div class="unfinished">
                    <span style="margin: 10px;">未完成(次)</span>
                    <span style="color: #333333; font-size: 16px; font-weight: 600; margin: 0 15px;">{{ weekStatisticsData.unfinishedNum }}</span>
                  </div>
                </div>
                <div>
                  <vertical style="margin-left: 20px" :weekStatisticsData="weekStatisticsData"/>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="table-box" style="height: 350px">
      <IconTitle title="保养统计" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 300px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">保养次数</span>
              </div>
              <div>
                <maintenance-num style="margin-top: -30px"/>
              </div>
            </div>
          </el-col
          >
          <el-col :span="12"
          >
            <div class="echarts-item" style="height: 300px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">保养时间</span>
              </div>
              <div>
                <maintenance-date style="margin-top: -30px"/>
              </div>
            </div>
          </el-col
          >
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getUnfinishedHistory,getTodayCompletion,getWeekStatistics,getQuarterCount} from "@/api/ems/inspection/maintenanceStatistical"
import ball from "./echarts/ball";
import statistical from "./echarts/statistical";
import transverse from "./echarts/transverse";
import vertical from "./echarts/vertical";
import maintenanceNum from "./echarts/maintenanceNum";
import maintenanceDate from "./echarts/maintenanceDate";
export default {
  name: "maintenance",
  components: {
    IconTitle,
    ball,
    statistical,
    transverse,
    vertical,
    maintenanceNum,
    maintenanceDate
  },
  data(){
    return {
      unfinishedHistoryData: [],
      todayCompletionData: {},
      weekStatisticsData: {}
    };
  },
  created() {
    this.getUnfinishedHistory();
    this.getTodayCompletion();
    this.getWeekStatistics();
  },
  methods: {

    getWeekStatistics() {
      getWeekStatistics().then(res => {
        this.weekStatisticsData = res.data.data;
      });
    },

    getUnfinishedHistory() {
      getUnfinishedHistory().then(res => {
        this.unfinishedHistoryData = res.data.data;
      })
    },
    getTodayCompletion() {
      getTodayCompletion().then(res => {
        this.todayCompletionData = res.data.data;

      });
    }
  }
}
</script>

<style scoped lang="less">
.maintenance{
  font-size: 12px;
  .table-box{
    .echarts-box{
      .echarts-item{
        .weeks{
          width: 100%;
          height: 150px;
          background-color: #FEFAF9;
          margin-top: 10px;
          display: flex;

          .total{

            .complete{
              display: flex;
              flex-direction: column;

            }
            .unfinished{
              display: flex;
              flex-direction: column;
              margin-top: 20px;
            }

          }

        }
      }
    }
  }
}
</style>
