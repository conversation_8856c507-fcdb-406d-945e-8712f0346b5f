<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-form :disabled="detailFlag" ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="需求名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入需求名称" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用地区" prop="area">
            <el-cascader v-model="form.area" :options="cityData" :props="{
              checkStrictly: true,
              children: 'children',
              label: 'name',
              value: 'code',
            }" clearable placeholder="请选择适用地区" style="width: 100%">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择资源类型" style="width: 100%" clearable>
              <el-option v-for="dict in dict.type.sd_demand_type" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行业" prop="industrialSubstitutionIdList">
            <el-cascader @change="industryChange" v-model="form.industrialSubstitutionIdList" :options="industrys"
              :props="{
                children: 'children',
                label: 'vueName',
                value: 'id',
              }" placeholder="请选择" style="width: 100%" clearable>
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品" prop="productTypeId">
            <el-select v-model="form.productTypeId" placeholder="请先选择行业" style="width: 100%">
              <el-option v-for="item in specsData" :key="item.id" :label="item.productName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品数量" prop="num">
            <el-input-number style="width: 100%" v-model="form.num" :min="0" label="产品数量"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="包装说明" prop="packing">
            <el-input v-model="form.packing" placeholder="请输入包装说明" style="width: 100%" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="linkmen">
            <el-input v-model="form.linkmen" placeholder="请输入" maxlength="20" show-word-limit style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="telephoneCounseling">
            <el-input v-model="form.telephoneCounseling" placeholder="请输入联系电话" maxlength="20" show-word-limit
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="资源描述内容" prop="description">
            <el-input
              v-model="form.description"
              placeholder="请输入资源描述内容"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col> -->
        <!--        <el-col :span="12">
          <el-form-item label="行业" prop="industryTypeCodes">
            <el-cascader
              v-model="form.industryTypeCodes"
              :options="industryTypes"
              :props="{
                children: 'childrenList',
                label: 'name',
                value: 'industryCode',
              }"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>-->
        <el-col :span="12">
          <el-form-item label="需求类型" prop="detailType">
            <el-radio-group v-model="form.detailType">
              <el-radio :label="0">采购需求</el-radio>
              <el-radio :label="1">服务需求</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效日期类型" prop="validTimeType">
            <el-radio-group v-model="form.validTimeType">
              <el-radio :label="0">长期有效</el-radio>
              <el-radio :label="1">指定具体有效时间</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="form.validTimeType == 1" :span="12">
          <el-form-item label="有效日期" prop="validTime">
            <el-date-picker v-model="form.validTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最高预算类型" prop="maximumBudgetType">
            <el-radio-group v-model="form.maximumBudgetType">
              <el-radio :label="0">面议</el-radio>
              <el-radio :label="1">具体最高预算</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.maximumBudgetType == 1">
          <el-form-item label="最高预算（元）" prop="maximumBudget">
            <el-input-number type="number" v-model="form.maximumBudget" controls-position="right" :precision="2"
              :min="0" placeholder="请输入最高预算" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="其他详情说明" prop="otherDescription">
            <editor :readOnly="detailFlag" v-model="form.otherDescription" :min-height="192" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过期时间" prop="expireTime">
            <el-date-picker v-model="form.expireTime" style="width: 100%" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
              :picker-options="pickerOptions" placeholder="过期时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getCityData } from "@/api/release/financial";
import { getSysIndustry, getSysIndustryType } from "@/api/release/policy";
import { getInfo } from "@/api/release/index.js";
import { listByIndustrialSubstitutionId } from "@/api/release/resource.js";
export default {
  name: "declarationEnterprise",
  dicts: ["sd_demand_type"],
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  data() {
    return {
      form: {
        name: undefined,
        type: undefined,
        industryTypeCodes: undefined,
        industrialSubstitutionIdList: undefined,
        detailType: 0,
        validTimeType: 0,
        description: undefined,
        validTime: undefined,
        productTypeTreeNames: undefined, //所属产品
        productTypeId: undefined, //规格
        num: 0, //产品数量
        packing: undefined, //包装
        area: undefined, //交易地点
        expireTime: undefined, //过期时间
        otherDescription: undefined, //其他说明
        maximumBudgetType: 0, //交易类型
        maximumBudget: 0, //交易额
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 如果当前时间之前的时间都禁用，则减去一天的毫秒数
        },
      },
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        area: [{ required: true, message: "请选择交易地点", trigger: "blur" }],
        type: [{ required: true, message: "请选择需求类型", trigger: "blur" }],
        expireTime: [
          { required: true, message: "请选择过期时间", trigger: "blur" },
        ],
        industrialSubstitutionIdList: [
          { required: true, message: "请选择行业", trigger: "change" },
        ],
        // industryTypeCodes: [
        //   { required: true, message: "请选择行业", trigger: "change" },
        // ],
        description: [
          { required: true, message: "请输入描述", trigger: "blur" },
        ],
        productTypeTreeNames: [
          { required: true, message: "请选择产品", trigger: "change" },
        ],
        // productTypeId: [
        //   { required: true, message: "请选择规格", trigger: "change" },
        // ],
        maximumBudget: [
          { required: true, message: "请输入最高预算", trigger: "blur" },
        ],
        otherDescription: [
          { required: true, message: "请输入其他说明", trigger: "blur" },
        ],
        maximumBudgetType: [
          { required: true, message: "请选择交易类型", trigger: "change" },
        ],
        validTime: [
          { required: true, message: "请选择有效日期", trigger: "change" },
        ],
        validTimeType: [
          { required: true, message: "请选择有效日期类型", trigger: "change" },
        ],
        linkmen: [{ required: true, message: "请输入联系人", trigger: 'blur' }],
        telephoneCounseling: [{ required: true, message: "请输入联系电话", trigger: 'blur' }],
        detailType: [
          {
            required: true,
            message: "请选择需求详情类型",
            trigger: "change",
          },
        ],
        productTypeId: [
          { required: true, message: "请选择产品", trigger: "change" },
        ],
        specifications: [
          { required: true, message: "请输入规格", trigger: "blur" },
        ],
        num: [{ required: true, message: "请输入产品数量", trigger: "blur" }],
        packing: [{ required: true, message: "请输入包装", trigger: "blur" }],
      },
      industryTypes: [],
      industrys: [],
      cityData: [],
      productData: [],
      specsData: [],
      submitDing: false,
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },

  created() {
    this.getCityDataFtn();
    this.getSysIndustryTypeFtn(); //行业
    // this.getSpecsData();
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },
  methods: {
    //行业变化事件
    industryChange(res) {
      if (res[0] === undefined) {
        return;
      }
      let industrialSubstitutionId = res[1];
      if (industrialSubstitutionId === undefined) {
        industrialSubstitutionId = res[0];
      }
      this.getSpecsData(industrialSubstitutionId); //产品
    },
    //详情接口回显
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.form = {
          ...params,
          area: JSON.parse(params.area),
          type: params.type.toString(),
        };
        if (this.form.industrialSubstitutionIdList) {
          this.industryChange(this.form.industrialSubstitutionIdList);
        }
        this.submitDing = false;
      });
    },
    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },
    getSysIndustryTypeFtn() {
      // getSysIndustryType().then((res) => {
      //   this.industryTypes = res.data;
      // });
      getSysIndustry().then((res) => {
        this.industrys = res.data;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const foundItem = this.specsData.find(
            (item) => item.id === this.form.productTypeId
          );
          let specifications = foundItem ? foundItem.productIndex : undefined;
          const params = {
            ...this.form,
            specifications,
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
    getSpecsData(industrialSubstitutionId) {
      listByIndustrialSubstitutionId(industrialSubstitutionId).then((res) => {
        this.specsData = res.data;
      });
    },
  },
};
</script>

<style lang="scss">
.financialCommn {
  width: 80%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }
}
</style>
