<template>
  <div class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
<!--          <el-button id="gwButton"-->
<!--                     type="primary"-->
<!--                     style="backgroundColor:#E1b980"-->
<!--                     icon="el-icon-circle-plus-outline"-->
<!--                     v-if="permissions.ems_emsregulations_add"-->
<!--                     @click="userAdd"-->
<!--          >新增-->
<!--          </el-button-->
<!--          >-->
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="true"
              :disabled="single"
              @click="handleEdit"
          >编辑
          </el-button
          >
<!--          <el-button-->
<!--              type="check"-->
<!--              icon="el-icon-download"-->
<!--              @click="exportExcel"-->
<!--          >导出-->
<!--          </el-button-->
<!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>

    </el-card>
    <basic-container>
      <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          :cell-style="cellStyle"
          @cell-click="cellClick"
          @selection-change="selectionChange"
          @on-load="getList"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @size-change="sizeChange"
          @current-change="currentChange"
          @row-update="handleUpdate"
          @row-save="handleSave"
          @row-del="handleDel"
      >
        <template slot="header">
          <IconTitle class="selfTitle" title="实施人员列表" imgUrl="yunwei"/>
        </template>

        <template slot-scope="scope" slot="menu">
<!--          <el-button type="text" @click="handleDel(scope.row)">-->
<!--            <i class="el-icon-delete" style="font-size: 13px"></i>删除-->
<!--          </el-button>-->
          <el-button type="text" @click="handleEdit(scope.row)">
            <i class="icon-bianji" style="font-size: 13px"></i>编辑
          </el-button>
          <el-button type="text" @click="handleView(scope.row)">
            <i class="el-icon-view" style="font-size: 13px"></i>查看
          </el-button>
        </template>

      </avue-crud>
    </basic-container>

    <!--  新增和修改  -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="60%">
      <div>
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="form.username" disabled placeholder="请输入用户名"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="性别" prop="sex">
                <el-select v-model="form.sex" style="width: 100%" placeholder="请选择性别">
                  <el-option
                      :label="item.label"
                      :value="item.value"
                      :key="item.id"
                      v-for="item in sexList"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="部门" prop="deptId">
                <treeselect
                    v-model="form.deptId"
                    :options="treeDeptData"
                    :normalizer="normalizer"
                    placeholder="请选择部门"
                />
              </el-form-item>
            </el-col>
          </el-row>
<!--            <el-col :span="12">-->
<!--              <el-form-item label="密码" prop="password">-->
<!--                <el-input v-model="form.password" placeholder="请输入密码"></el-input>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
          <el-row>
            <el-col :span="12">
              <el-form-item label="出生日期" prop="phone">
                <el-date-picker
                    v-model="form.birthday"
                    type="date"
                    style="width: 100%"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择出生日期">
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="状态" prop="lockFlag">
                <el-radio-group v-model="form.lockFlag" size="mini">
                  <el-radio label="0" border>有效</el-radio>
                  <el-radio label="1" border>锁定</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
<!--            <el-col :span="12">-->
<!--              <el-form-item label="角色" prop="role">-->
<!--                <el-select v-model="form.role" disabled  style="width: 100%" placeholder="请选择角色" :multiple-limit="3" multiple>-->
<!--                  <el-option-->
<!--                      v-for="item in roleList"-->
<!--                      :key="item.roleId"-->
<!--                      :label="item.roleName"-->
<!--                      :value="item.roleId">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->

            <el-col :span="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="说明" prop="remark">
                <el-input v-model="form.remark" placeholder="请输入说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="资质证书" prop="fileIdArray">
                <device-upload ref="fileupload"
                               :fileListTem="imgArrayTem"
                               :limit="3"
                ></device-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item style="float: right">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="dialogFormVisible = false">返回</el-button>
              </el-form-item>

            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>

    <!-- 查看详情 -->
    <el-drawer
        class="drawerStyle"
        title="人员履历"
        :visible.sync="drawer"
        direction="rtl"
        size="50%"
        append-to-body
    >
      <drawer-con :deviceData="deviceData" :maintainByUserData="maintainByUserData"/>
    </el-drawer>
  </div>
</template>
<script>
import {fetchList,getSex, getObj, addObj, putObj, delObj} from "@/api/ems/generator/sysuser";
import {getMaintainByUserList} from "@/api/ems/inspection/emsinsinspectplan";
import {tableOption} from '@/const/crud/ems/generator/sysuser'
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/ems/icon-title/index.vue";
import deviceUpload from "../../repository/emsdeviceknowledge/deviceUpload";
import Treeselect from "@riophae/vue-treeselect";
import {fetchTree} from "@/api/admin/dept";
import {deptRoleList} from "@/api/admin/role";
import drawerCon from "./drawerCon";

export default {
  name: 'sysuser',
  components: {
    IconTitle,
    deviceUpload,
    Treeselect,
    drawerCon
  },
  data() {
    return {
      title: '',
      tableData: [],
      treeDeptData: [], //部门
      searchForm: {}, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      sexList: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      maintainByUserData: [],
      dialogFormVisible: false,
      form: {
        id: '',
        userId: '',
        username: '',
        sex: null,
        birthday: '',
        phone: '',
        deptId: undefined,
        password: '',
        lockFlag: '',
        role: [],
        remark: '',
        fileIdArray: {
          id:'',
          url: ''
        },
        urlArray: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/, message: '请输入合法手机号码', trigger: 'blur' }
        ],
        deptId: [
          { required: true, message: '请选择所属部门', trigger: 'blur' }
        ],
        lockFlag: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'blur' }
        ],
      },
      imgArrayTem: [],
      roleList: [],
      drawer: false,
      deviceData: {}
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.generator_sysuser_add, false),
        delBtn: this.vaildData(this.permissions.generator_sysuser_del, false),
        editBtn: this.vaildData(this.permissions.generator_sysuser_edit, false),
      };
    },
  },
  mounted() {
    this.getSelect();
    this.initElement();
    this.changeThme();
  },
  methods: {

    // 查看功能
    handleView(row){
      // 获取基本功能
      getObj(row.userId).then(res => {
        this.deviceData = res.data.data;
      })
      getMaintainByUserList(row.userId).then(res => {
        this.maintainByUserData = res.data.data;
      })
      this.drawer = true;

    },

    // // 新增人员
    // userAdd() {
    //   this.reset();
    //   this.dialogFormVisible = true;
    //   this.title = "新增"
    //   this.getRoleList();
    //   console.log("111>>>>>>>" , JSON.stringify(this.roleList))
    //   // this.form.role = array;
    //
    // },

    // 编辑人员
    handleEdit(row) {

      let userId = row.userId || this.selectionList[0].userId;
      this.dialogFormVisible = true;
      this.title = "编辑"
      this.getRoleList();

      // 回显
      getObj(userId).then(res => {
        // console.log("111>>>>>>>" , JSON.stringify(res.data.data))
        this.imgArrayTem = [];
        if (res.data.data.fileArray != null) {
          this.imgArrayTem = res.data.data.fileArray;
        }
        // 修改回显数据
        Object.keys(this.form).forEach((item, index) => {
          if (item !== "fileIdArray" ) {
            this.form[item] = res.data.data[item];
          }
        });

        this.form = res.data.data;
        this.form.password = "";

        let array = res.data.data.roleList.map(item => {
          return item.roleId;
        })
        this.form.role = array;

        this.form.remark = res.data.data.remark;

      })
    },

    // 删除人员
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = '';
            if (row) {
              id = row.userId;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },

    // 提交表单
    submitForm(formName) {
      let data = JSON.parse(JSON.stringify(this.form));
      if (this.$refs.fileupload.fileList != null) {
        // console.log("111>>>>>>>", JSON.stringify(this.$refs.fileupload.fileList));
        data.fileIdArray = this.$refs.fileupload.fileList.map((item) =>
            item.id
        );
        data.urlArray = this.$refs.fileupload.fileList.map((item) =>
            item.temUrl
        );
      } else {
        data.fileIdArray = [];
      }


      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (data.userId) {
            putObj(data).then((res) => {
              this.$parent.$message.success("修改成功！")
              this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          } else {
            addObj(data).then((res) => {
              this.$parent.$message.success("新增成功!")
              // this.$parent.listFlag = true;
              this.dialogFormVisible = false;
              this.getList(this.page);
            });
          }
        }
      });
    },

    // 清空表单
    reset() {
      this.form = {
        id: null,
        username: null,
        sex: null,
        birthday: null,
        phone: null,
        deptId: undefined,
        password: null,
        lockFlag: null,
        role: null,
        remark: null,
        fileIdArray: null
      };
      this.imgArrayTem = [];
    },

    // 获取角色列表
    getRoleList() {
      deptRoleList().then(res => {
        this.roleList = res.data.data;
      })
    },

    // 改变资料编号的颜色
    cellStyle(data) {
      if (data.columnIndex === 2) {
        return "color:#02b980;cursor:pointer";
      }
    },

    // 点击名称跳转页面
    cellClick(row, column) {
      if (column.property === "username") {
        this.$router.push({
          path: "/ems/generator/sysuser/userDetails",
          query: {
            userId: row.userId
          }
        });
      } else {
        return;
      }
    },


    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.tableData = response.data.data.records;
        this.page.total = response.data.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
    },

    // 更新
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      addObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    // exportExcel() {
    //   this.downBlobFile(
    //       "/generator/sysuser/export",
    //       this.searchForm,
    //       "sysuser.xlsx"
    //   );
    // },
    // 改变主题颜色
    changeThme() {
      //"#02b980"
      document.getElementById("gwButton").style.backgroundColor = this.theme;
    },

    getSelect() {
      //部门
      fetchTree().then((response) => {
        this.treeDeptData = response.data.data;
      });
      getSex().then(res => {
        this.sexList = res.data.data;
        // console.log("111<>>>>" , JSON.stringify( this.sexList))
      });
    },

    // 部门
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
.drawerStyle {
  ::v-deep .el-drawer__header {
    background-color: #f2f2f5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 20px;
  }
}
</style>
