
import request from '@/utils/request'

export function strategyDeviceFetchList(query) {
  return request({
    url: '/platform/emsinsstrategydevice/page',
    method: 'get',
    params: query
  })
}

export function strategyDeviceAddObj(obj) {
  return request({
    url: '/platform/emsinsstrategydevice',
    method: 'post',
    data: obj
  })
}

export function strategyDeviceGetObj(id) {
  return request({
    url: '/platform/emsinsstrategydevice/' + id,
    method: 'get'
  })
}

export function strategyDeviceDelObj(id) {
  return request({
    url: '/platform/emsinsstrategydevice/' + id,
    method: 'delete'
  })
}

export function strategyDevicePutObj(obj) {
  return request({
    url: '/platform/emsinsstrategydevice',
    method: 'put',
    data: obj
  })
}
