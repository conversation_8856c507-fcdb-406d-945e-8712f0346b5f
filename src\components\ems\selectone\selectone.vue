<template>
  <div class="mainAnalysis">
    <div class="left_card">
      <el-card class="box-card">
        <!--<IconTitle class="selfTitle" title="数据采集/设备列表" imgUrl="yunwei"/>-->
        <div class="tabs">
          <el-tabs v-model="activeName" stretch>
            <el-tab-pane label="类别" name="first">
              <div class="tree">
                <el-tree
                    style="width: 100%;"
                    accordion
                    :data="cat.data"
                    :props="cat.defaultProps"
                    :normalizer="normalizer"
                    @node-click="catHandleNodeClick">
                </el-tree>
              </div>

            </el-tab-pane>
            <el-tab-pane label="位置" name="second">
              <!--位置-->
              <div class="tree">
                <el-tree
                    style="width: 100%;"
                    accordion
                    :data="posi.data"
                    :props="posi.defaultProps"
                    :normalizer="normalizer"
                    @node-click="handleNodeClick">
                </el-tree>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
    <div class="right_card">
      <div id="box-card">
        <div class="tab">
          <div class="table_data">
            <div>
              <el-table
                  v-loading="loading"
                  @row-click="rowClick"
                  highlight-current-row
                  :data="deviceList"
                  :header-cell-style="{background: '#f4f7fc'}"
                  fit
              >
                <el-table-column
                    label="设备编号"
                    align="center"
                    prop="deviceNum"
                    :width="flexColumnWidth('deviceNum',deviceList)"
                />
                <el-table-column
                    label="设备名称"
                    align="center"
                    prop="deviceName"
                    :width="flexColumnWidth('deviceName',deviceList)"
                />
                <el-table-column
                    label="资产编号"
                    align="center"
                    prop="assetNum"
                    :width="flexColumnWidth('assetNum',deviceList)"
                />
                <el-table-column
                    label="类别"
                    align="center"
                    prop="category"
                    :width="flexColumnWidth('category',deviceList)"
                />
                <el-table-column
                    label="负责人"
                    align="center"
                    prop="liableUser"
                    :width="flexColumnWidth('liableUser',deviceList)"
                />
                <el-table-column
                    label="规格型号"
                    align="center"
                    prop="specification"
                    :width="flexColumnWidth('specification',deviceList)"
                />
                <el-table-column
                    label="所属部门"
                    align="center"
                    prop="deptName"
                    :width="flexColumnWidth('deptName',deviceList)"
                />
                <el-table-column
                    label="位置"
                    align="center"
                    prop="location"
                    :width="flexColumnWidth('location',deviceList)"
                />
                <el-table-column
                    label="单位"
                    align="center"
                    prop="unit"
                    :width="flexColumnWidth('unit',deviceList)"
                />
              </el-table>
              <pagination
                  v-show="page.total > 0"
                  :total="page.total"
                  :page.sync="page.currentPage"
                  :limit.sync="page.pageSize"
                  :page-sizes="['10']"
                  @pagination="pageChange"
              />
            </div>
            <!--            <div style="text-align: center; margin-top: 80px;">
                          <el-button round @click="cancel">取消</el-button>
                          <el-button type="primary" round @click="determine">确定</el-button>
                        </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {fetchPositionListTree} from "@/api/ems/equipment/position";
import {fetchList as catTree} from "@/api/ems/equipment/category";
import IconTitle from "@/components/icon-title/index.vue"
import Pagination from "@/components/Pagination/index.vue"
import {fetchList} from "@/api/ems/equipment/account";

export default {
  name: "selectone",
  components: {
    IconTitle,
    Pagination
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      deviceList: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      activeName: 'first',
      //位置的树形数据
      posi: {
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
      //分类的树形数据
      cat: {
        data: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      },
      //rows: 存放点击行的信息
      rows: {}
    };
  },
  mounted() {
    this.getPostionTree()
    this.getCatTree()
    this.getList(this.page)
  },
  methods: {
    // 点击取消，把选中的数据清空,并返回
    cancel(){
      this.rows = {};
      this.$router.back();  // 返回上一个路由
    },
    pageChange(page){
      this.page.currentPage = page.page
      this.page.pageSize = page.limit
      this.getList(this.page)
    },

    // 点击确定，把rows传递给父组件,并返回
    determine(){
      this.$emit("goData", this.rows);
      this.$router.back();
    },

    //位置节点点击事件
    handleNodeClick(data) {
      //storageLocationId
      console.log(data.id)
      this.getList(this.page, {"storageLocationId": data.id});
    },
    //分类节点点击事件
    catHandleNodeClick(data) {
      //categoryId
      console.log(data.id)
      this.getList(this.page, {"categoryId": data.id});
    },
    //获取位置的tree数据
    getPostionTree() {
      fetchPositionListTree()
          .then((response) => {
            this.posi.data = response.data.data ? response.data.data : [];
          })
    },
    //获取分类的tree数据
    getCatTree() {
      catTree()
          .then((response) => {
            this.cat.data = response.data.data;
          })
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    // 列表查询
    getList(page, params) {
      this.loading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.deviceList = response.data.data.records;
        this.page.total = response.data.data.total;
        this.loading = false;
      })
          .catch(() => {
            this.loading = false;
          });
    },
    rowClick(row, column, event) {
      this.rows = row;
    },
    flexColumnWidth(str, tableData, flag = 'max') {
      // str为该列的字段名(传字符串);tableData为该表格的数据源(传变量);
      // flag为可选值，可不传该参数,传参时可选'max'或'equal',默认为'max'
      // flag为'max'则设置列宽适配该列中最长的内容,flag为'equal'则设置列宽适配该列中第一行内容的长度。
      str = str + ''
      let columnContent = ''
      if (!tableData || !tableData.length || tableData.length === 0 || tableData === undefined) {
        return
      }
      if (!str || !str.length || str.length === 0 || str === undefined) {
        return
      }
      if (flag === 'equal') {
        // 获取该列中第一个不为空的数据(内容)
        for (let i = 0; i < tableData.length; i++) {
          if (tableData[i][str].length > 0) {
            // console.log('该列数据[0]:', tableData[0][str])
            columnContent = tableData[i][str]
            break
          }
        }
      } else {
        // 获取该列中最长的数据(内容)
        let index = 0
        for (let i = 0; i < tableData.length; i++) {
          if (tableData[i][str] === null) {
            return
          }
          const now_temp = tableData[i][str] + ''
          const max_temp = tableData[index][str] + ''
          if (now_temp.length > max_temp.length) {
            index = i
          }
        }
        columnContent = tableData[index][str]
      }
      // console.log('该列数据[i]:', columnContent)
      // 以下分配的单位长度可根据实际需求进行调整
      let flexWidth = 0
      for (const char of columnContent) {
        if ((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z')) {
          // 如果是英文字符，为字符分配8个单位宽度
          flexWidth += 8
        } else if (char >= '\u4e00' && char <= '\u9fa5') {
          // 如果是中文字符，为字符分配15个单位宽度
          flexWidth += 20
        } else {
          // 其他种类字符，为字符分配8个单位宽度
          flexWidth += 9
        }
      }
      if (flexWidth < 80) {
        // 设置最小宽度
        flexWidth = 80
      }
      // if (flexWidth > 250) {
      //   // 设置最大宽度
      //   flexWidth = 250
      // }
      return flexWidth + 'px'
    },
  }
}
</script>

<style scoped lang="less">

/* 用来设置当前页面element全局table 选中某行时的背景色*/
/deep/ .el-table__body tr.current-row > td {
  background-color: #7AC756 !important;
  color: #FFFFFF; /* 设置文字颜色，可以选择不设置 */
}

/deep/ .el-tree-node.is-current > .el-tree-node__content {
  color: #7AC756;
}

/deep/ .pagination-container {
  background: #fff;
  padding: 32px 16px;
  text-align: right;
}

.first {
  color: #26AE61;
}

.mainAnalysis {
  width: 100%;
  /*height: 80%;*/
  background-color: #FFFFFF;
  font-size: 12px;
  display: flex;
  /*height: 100%;*/

  .right_card {
    width: 80%;
    margin-left: 15px;

    .box-card {
      border-radius: 10px;
      background-color: #ffffff;

      .tab {
        margin: 0 10px;

        .table_data {
          margin-top: 20px;

          //.button_inner{
          //  width: 100%;
          //  text-align: center;
          //}

          .btn-box {
            margin-bottom: 20px;
          }
        }

        .tableData {
          .table {

            height: 200px;

            th {
              color: #B6B6B6;
            }

            td {
              color: #101010;

              div {
                width: 80px;
                height: 20px;
                background-color: #00C0DE;
                color: #fff;
                text-align: center;
              }
            }
          }
        }


      }

    }
  }

  .left_card {
    margin-left: 15px;
    width: 20%;

    .box-card {
      height: auto;
      border-radius: 10px;
      background-color: #ffffff;
    }
  }
}


.el-tabs__nav-wrap::after {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #E4E7ED;
  z-index: 1;
}

.el-tabs__item {
  font-size: 12px;
}

.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 71em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
</style>
