<template>
  <div class="execution">
    <el-card class="box-card btn-search page-search crud">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="primary"
                     icon="el-icon-circle-plus-outline"
                     v-if="true"
                     @click="addRegulations"
          >新增
          </el-button
          >
          <!--<el-button-->
          <!--    type="success"-->
          <!--    icon="el-icon-edit"-->
          <!--    v-if="permissions.ems_emsregulations_edit"-->
          <!--    :disabled="single"-->
          <!--    @click="deviceEdit"-->
          <!--&gt;编辑-->
          <!--</el-button-->
          <!--&gt;-->
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="true"
              @click="delTreeMenu"
          >删除
          </el-button
          >
          <!--          <el-button-->
          <!--              type="check"-->
          <!--              icon="el-icon-download"-->
          <!--              @click="exportExcel"-->
          <!--          >导出-->
          <!--          </el-button-->
          <!--          >-->
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>
    </el-card>
    <!--<el-card>-->
    <!--  <div class="form">-->
    <!--    <el-form :inline="true">-->
    <!--      <el-form-item label="位置名称">-->
    <!--        <el-input placeholder="请输入位置名称" v-model="searchForm.locationName" clearable></el-input>-->
    <!--      </el-form-item>-->
    <!--      <el-form-item>-->
    <!--        <el-button type="primary" icon="el-icon-search" @click="searchChangeU">查询</el-button>-->
    <!--        <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>-->
    <!--      </el-form-item>-->
    <!--    </el-form>-->
    <!--  </div>-->
    <!--</el-card>-->
    <basic-container>
      <el-row :span="24">
        <el-col :xs="24" :sm="24" :md="5" class="user__tree">
          <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
          >
            <span class="el-tree-node__label" slot-scope="{ node, data }">
              <el-tooltip
                  class="item"
                  effect="dark"
                  content="无数据权限"
                  placement="right-start"
                  v-if="data.isLock"
              >
                <span>{{ node.label }} <i class="el-icon-lock"></i></span>
              </el-tooltip>
              <span v-if="!data.isLock">{{ node.label }}</span>
            </span>
          </avue-tree>
        </el-col>
        <el-col :xs="24" :sm="24" :md="19" class="user__main">
          <IconTitle title="存放位置信息" imgUrl="yunwei"/>
          <el-card style="width: 700px; padding: 25px 85px 10px 0px; margin: 25px 30px 0 20px; box-shadow: 1px 1px 5px #888888;">
            <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm" style="text-align: center">
              <el-form-item label="上级位置:" prop="parentId">
                <Treeselect
                    v-model="form.parentId"
                    :options="tableData"
                    :normalizer="normalizer"
                    placeholder="请选择上级"
                />
              </el-form-item>
              <el-form-item label="名称:" prop="location">
                <el-input v-model="form.location"  maxlength="20"
                          show-word-limit placeholder="请输入名称"></el-input>
              </el-form-item>
              <el-form-item label="所在地:" prop="address">
                <el-input v-model="form.address" maxlength="35"
                          show-word-limit placeholder="请输入所在地"></el-input>
              </el-form-item>
              <el-form-item label="排序:" prop="sort">
                <el-input-number style="width:100%"
                    v-model="form.sort"
                    controls-position="right"
                    :min="0"
                />
              </el-form-item>
              <el-form-item label="坐标:" prop="coordinate">
                <el-input v-model="form.coordinate" maxlength="25"
                          show-word-limit placeholder="请输入坐标"></el-input>
              </el-form-item>
              <el-form-item label="状态:" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                  <el-option
                      label="启用"
                      value="0">
                  </el-option>
                  <el-option
                      label="禁用"
                      value="1">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="说明:" prop="remark">
                <el-input type="textarea" maxlength="255"
                          show-word-limit v-model="form.remark"></el-input>
              </el-form-item>
              <el-button type="primary" v-if="form.id != null" @click="submitForm('ruleForm')" style="width: 100px;">更新</el-button>
              <el-button type="primary" v-if="form.id == null" @click="submitForm('ruleForm')" style="width: 100px;">新增</el-button>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </basic-container>

<!--    <el-dialog title="新增设备位置" :visible.sync="dialogFormVisible" width="40%">-->

<!--      <el-form ref="form" :model="form" label-width="70px">-->
<!--        <el-form-item label="上级" prop="">-->
<!--          <treeselect-->
<!--              v-model="form.parentId"-->
<!--              :options="tableData"-->
<!--              :normalizer="normalizer"-->
<!--              placeholder="请选择上级"-->
<!--          />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="名称:" prop="location">-->
<!--          <el-input v-model="form.location" placeholder="请输入名称"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="所在地:" prop="address">-->
<!--          <el-input v-model="form.address" placeholder="请输入所在地"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="排序:" prop="sort">-->
<!--          <el-input type="number" v-model="form.sort" placeholder="请输入排序"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="坐标:" prop="coordinate">-->
<!--          <el-input v-model="form.coordinate" placeholder="请输入坐标"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="状态:" prop="status">-->
<!--          <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">-->
<!--            <el-option-->
<!--                label="启用"-->
<!--                value="0">-->
<!--            </el-option>-->
<!--            <el-option-->
<!--                label="禁用"-->
<!--                value="1">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="说明:" prop="remark">-->
<!--          <el-input type="textarea" v-model="form.remark"></el-input>-->
<!--        </el-form-item>-->

<!--        <el-form-item style="text-align: right">-->
<!--          <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>-->
<!--          <el-button @click="dialogFormVisible = false">取 消</el-button>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </el-dialog>-->

  </div>
</template>
<script>
import {
  fetchList,
  getObj,
  addObj,
  putObj,
  delObj,
  fetchListTree,
} from "@/api/ems/equipment/position";
import {tableOption} from "@/const/crud/ems/repository/emsregulations";
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/icon-title/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css"


export default {
  name: 'emsregulations',
  components: {
    IconTitle,
    Treeselect
  },
  data() {
    return {
      title: "",
      tableData: [],
      searchForm: {
        //位置名称
        locationName: ''
      }, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {

      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      // 树状结构
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        defaultExpandAll:false,
        props: {
          label: "name",
          value: "id",
        },
      },
      treeData: [],
      // 新增弹出框
      dialogFormVisible: false,
      form: {
        id: null,
        parentName: '',
        location: '',
        address: '',
        sort: 0,
        coordinate: '',
        status: '',
        remark: ''
      },
      rules: {
        parentId: [
          { required: true, message: "上级位置不能为空", trigger: "blur" },
        ],
        location: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "blur" },
        ],
      },
      imgArrayTem: [],
      treeDeptData: [], //部门
      isNoTopData: [
        {
          value: 0,
          label: "是"
        },
        {
          value: 1,
          label: "否"
        }
      ],
      optionProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      editId: 0,
    };
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsregulations_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsregulations_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsregulations_edit, false),
      };
    },
  },
  mounted() {
    this.initElement();
    this.getTreeData();
    this.addRegulations();
    // this.getSelect();
  },
  methods: {

    // 新增
    addRegulations(){
      this.resetData();
      this.getList();
    },

    resetData() {
      this.form = {
        id: null,
        parentName: '',
        location: '',
        address: '',
        sort: 0,
        coordinate: '',
        status: '',
        remark: ''
      }
    },

    // 部门树
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },

    // 提交表单
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if(!this.form.parentId) {
            this.form.parentId = 0;
          }

          if (this.form.id > 0) {
            putObj(this.form).then((data) => {
              this.$message.success("更新成功");
              this.getList();
              this.getTreeData();
              this.resetData();
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.getList();
              this.getTreeData();
              this.resetData();
              // this.open = false;
            });

          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 设备位置树状图数据
    getTreeData() {
      fetchList().then(res => {
        if (res.code === 200) {
          let common_table_info = [];
          let treeDataList = [];
          treeDataList = res.data;
          treeDataList.forEach(function (item, index) {
            // if (item.name == "设备位置") {
            //   common_table_info.push(treeDataList[index])
            // }
            common_table_info.push(treeDataList[index])
          })
          this.treeData = common_table_info;
        }
      })
    },

    // 树状图每次点击重新搜索
    nodeClick(data) {
      // console.log("aa",data.id);
      this.page.page = 1;
      this.getList(this.page, {id: data.id});

      // 回显数据
      getObj(data.id).then((res) => {
        this.form = res.data;
        this.menuItem = res.data;
      });
    },

    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList() {
      this.tableLoading = true;
      fetchList(Object.assign({}, this.searchForm))
          .then((response) => {
            this.tableData = [];
            const menu = { id: 0, name: "主目录", children: [] };
            menu.children = response.data;
            this.tableData.push(menu);
            console.log(this.tableData);
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableLoading = false;
          });
    },
    //编辑
    // handleEdit() {
    //   var refsDate = this.$refs
    //   refsDate.crud.rowEdit(this.selectionList[0], this.selectionList[0].$index);
    // },
    // 删除
    delTreeMenu(){
      let row = this.menuItem;
      // return;
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(function () {
            return delObj(row.id);
          })
          .then(() => {
            this.$message.success("删除成功");
            this.getList();
            this.getTreeData();
            this.resetData();
          });
    },
    // 更新
    // handleUpdate: function (row, index, done, loading) {
    //   putObj(row)
    //       .then((data) => {
    //         this.$message.success("修改成功");
    //         done();
    //         this.getList(this.page);
    //       })
    //       .catch(() => {
    //         loading();
    //       });
    // },
    // 保存
    // handleSave: function (row, done, loading) {
    //   addObj(row)
    //       .then((data) => {
    //         this.$message.success("添加成功");
    //         done();
    //         this.getList(this.page);
    //       })
    //       .catch(() => {
    //         loading();
    //       });
    // },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsregulations/export",
          this.searchForm,
          "设备位置.xlsx"
      );
    },

    // getSelect() {
    //   //部门
    //   fetchTree().then((response) => {
    //     this.treeDeptData = response.data.data;
    //   });
    // },

  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
</style>
<style scoped lang="less">
.execution {

  .crud {
    .form {
      float: top;
    }
  }

  &__tree {
    padding-top: 3px;
    padding-right: 20px;
  }

  &__main {
    .el-card__body {
      padding-top: 0;
    }
  }

  .gzzd {
    margin-bottom: 10px;
    height: 120px;

    .sbtp {
      width: 147px;
      height: 100px;
      float: right;
      margin-right: 24px;
      margin-top: -10px;
    }

    .tbzl {
      display: flex;
      flex-direction: row;

      .anzhuo {
        left: 262px;
        top: 100px;
        width: 15px;
        height: 15px;
        color: rgba(89, 89, 89, 100);
        margin-right: 13px;
      }

      .zl {
        left: 295px;
        top: 95px;
        width: 72px;
        height: 27px;
        color: rgba(89, 89, 89, 100);
        font-size: 18px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: bold;
      }
    }

    .sm {
      left: 262px;
      top: 152px;
      width: 700px;
      height: 20px;
      color: rgba(134, 129, 129, 100);
      font-size: 12px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
      margin-top: 40px;
    }
  }
  .clearfix {
    height: 40px;
    position: relative;
    .btn-box {
        position: absolute;
        top: 0;
        left: 0;
    }
    .icon-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 40px;
    }
  }
}
</style>
