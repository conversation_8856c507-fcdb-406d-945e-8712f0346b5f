<template>
    <div class="app-container driveManageList">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"
            @submit.native.prevent>
            <el-form-item label="协议名称" prop="agreementName">
                <el-input v-model="queryParams.agreementName" placeholder="请输入" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:post:add']">新增</el-button>
            </el-col>
        </el-row>

        <el-table v-loading="loading" :data="postList">
            <el-table-column label="设备类型" prop="driveName" />
            <el-table-column label="协议名称" prop="agreementName" />
            <el-table-column label="当前版本" prop="agreementVersion" />
            <el-table-column label="最近修改" prop="updateTime" />
            <el-table-column label="创建时间" prop="createTime" />
            <el-table-column label="操作" class-name="small-padding fixed-width" width="120">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getList" />

        <!-- 添加或修改岗位对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">

                <el-form-item label="设备类型" prop="driveId">
                    <el-select style="width: 90%;" v-model="form.driveId" placeholder="请选择" clearable>
                        <el-option v-for="dict in driveIdArr" :key="dict.id" :label="dict.driveName" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="协议名称" prop="agreementName">
                    <el-input v-model="form.agreementName" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>

                <el-form-item class="uploadItem" label="上传文件1" prop="file">
                    <el-upload class="upload-demo" style="position: relative;width: 100%;" :headers="headers" ref="upload"
                        :action="actionurl" :auto-upload="true" :on-remove="fileRemove" :file-list="fileList"
                        :on-error="handleUploadError" :before-upload="beforeUpload" :on-success="handleSuccess" :limit="1"
                        accept=".zip">
                        <el-button slot="trigger" type="primary" size="small">选择文件</el-button>
                        <div style="margin-top:0px;color: silver;font-size: 12px;line-height:20px" class="el-upload__tip"
                            slot="tip">支持扩展名:.zip</div>
                    </el-upload>
                </el-form-item>


                <el-form-item label="协议版本" prop="agreementVersion">
                    <el-input v-model="form.agreementVersion" placeholder="协议版本" style="width: 90%"></el-input>
                </el-form-item>




            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>
import { gatDriveList, deleteDrive, addDrive, editDrive } from "@/api/gatewayManage/drive/index.js";
import { getDrivetypes } from "@/api/gatewayManage/list/index.js";
import { getToken } from '@/utils/auth'



export default {
    name: "driveManageList",
    dicts: ['sys_normal_disable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            showSearch: true,
            total: 0,
            postList: [],
            title: "",
            open: false,
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                agreementName: undefined,
            },
            form: {
                driveId: undefined,
                file: null,
                agreementName: '',
                agreementVersion: ''
            },
            rules: {
                driveId: [
                    { required: true, message: "请选择设备类型", trigger: "change" }
                ],
                agreementName: [
                    { required: true, message: "请输入协议名称", trigger: "blur" }
                ],
                agreementVersion: [
                    { required: true, message: "请输入协议版本", trigger: "blur" }
                ],
                file: [
                    { required: false, message: "请选择设备类型", trigger: "change" }
                ],
            },
            driveIdArr: [],
            headers: {
                Authorization: "Bearer " + getToken(),
            },
            actionurl: process.env.VUE_APP_BASE_API + "/system/file/upload",
            fileList: []
        };
    },
    created() {
        this.getList();
        getDrivetypes().then(res => this.driveIdArr = res.data);
    },
    methods: {

        handleUploadError() {
            this.$message.error("上传文件失败！");
        },

        beforeUpload(file) {
            if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
                this.$modal.msgError('文件大小不能超过100M')
                return false
            }
            if (!['.zip'].includes(file.name.substring(file.name.lastIndexOf('.')))) {
                this.$modal.msgError('请上传正确的文件格式');
                return false;
            }
            return true
        },

        handleSuccess(uploadFile, fileList) {
            this.form.file = {
                fileurl: uploadFile.data.url,
                fileName: fileList.name
            }
            if (fileList.length !== 0) {
                this.$refs.form.validateField('file')
            }
        },

        fileRemove(uploadFile, fileList) {
            if (fileList.length === 0) {
                this.form.file = null
                this.$refs.form.validateField('file')
            }
        },

        getList() {
            this.loading = true;
            gatDriveList(this.queryParams).then(response => {
                const { list, total } = response.data
                this.postList = list;
                this.total = total;
            }).finally(() => this.loading = false)
        },

        cancel() {
            this.open = false;
            this.reset();
        },

        reset() {
            this.form = {
                driveId: undefined,
                file: null,
                agreementName: '',
                agreementVersion: ''
            };
            this.resetForm("form");
        },

        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },

        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },

        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加岗位";
        },

        handleUpdate(row) {
            this.reset();
            this.form = {
                id: row.id,
                agreementName: row.agreementName,
                agreementVersion: row.agreementVersion,
                driveId: row.driveId,
                file: {
                    fileName: row.agreementUrl,
                    fileurl: row.agreementUrl
                },
            };
            // this.fileList=[{
            // 	name:row.agreementUrl,
            // 	url:row.agreementUrl
            // }]
            this.open = true;
            this.title = "修改岗位";
        },

        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    const { file } = this.form;
                    const pamres = {
                        ...this.form,
                        agreementUrl: file ? file.fileurl : undefined,
                    }
                    delete pamres.file
                    if (pamres.id != undefined) {
                        editDrive(pamres).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addDrive(pamres).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },

        handleDelete(row) {
            const id = [row.id]
            this.$modal.confirm('是否确认删除？').then(function () {
                return deleteDrive(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },

    }
};
</script>



<style lang="scss" scoped>
.driveManageList {}
</style>