<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="政策类型" prop="policyOrActivity">
            <el-radio-group v-model="form.policyOrActivity" @input="radioInput">
              <el-radio
                v-for="(item, index) in OrActivityList"
                :key="item.type"
                :label="item.value"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="标题" prop="policyTitle">
            <el-input
              v-model="form.policyTitle"
              placeholder="请输入标题"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="介绍" prop="policyContent">
            <el-input
              v-model="form.policyContent"
              placeholder="请输入介绍"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="发文部门" prop="lssuingDepartment">
            <el-select
              v-model="form.lssuingDepartment"
              placeholder="请选择发文部门"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.lssuing_department"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="!shouldShowLssuingDepartment" :span="10">
          <el-form-item label="标签" prop="label">
            <el-select
              v-model="form.label"
              placeholder="请选择标签"
              multiple
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.industrial_ore_label"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="政策等级" prop="policyLevel">
            <el-select
              v-model="form.policyLevel"
              placeholder="请选择政策等级"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.policy_grade"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="信息来源" prop="informationSources">
            <el-select
              v-model="form.informationSources"
              placeholder="请选择信息来源"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.information_sources"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item
            :label="ActivityObj.label + '类型'"
            prop="policyCategory"
          >
            <el-select
              v-model="form.policyCategory"
              :placeholder="'请选择' + ActivityObj.label + '类型'"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type[ActivityObj.dict]"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="适用地区" prop="regionCode">
            <el-cascader
              v-model="form.regionCode"
              :options="cityData"
              :props="{
                checkStrictly: true,
                children: 'children',
                label: 'name',
                value: 'code',
              }"
              clearable
              placeholder="请选择适用地区"
              style="width: 100%"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col v-if="!shouldShowLssuingDepartment" :span="10">
          <el-form-item label="领域" prop="domainCode">
            <el-select
              v-model="form.domainCode"
              :placeholder="'请选择领域'"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.wx_field"
                :key="dict.label"
                :label="dict.label"
                :value="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item class="uploadItem" label="图片" prop="imgUrl">
            <UploadImage
              :disabled="detailFlag"
              :fileList="form.imgUrl"
              @addUpload="addUpload"
              @removeUpload="removeUpload"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item class="uploadItem" label="附件" prop="annexUrl">
            <el-upload
              ref="upload"
              :limit="1"
              accept="*"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :before-remove="beforeRemove"
              :file-list="form.annexUrl"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="政策内容" prop="richText">
            <editor
              :readOnly="detailFlag"
              v-model="form.richText"
              :min-height="192"
            />
          </el-form-item> </el-col
        >·
        <el-col :span="20">
          <el-form-item label="链接" prop="policyUrl">
            <el-input
              v-model="form.policyUrl"
              placeholder="请输入完整的链接地址(http://www.baidu.com)"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="发布时间" prop="releaseTime">
            <el-date-picker
              v-model="form.releaseTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              :picker-options="pickerOptions"
              placeholder="选择发布时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="是否热门" prop="hotFlag">
            <el-switch
              v-model="form.hotFlag"
              active-value="'1'"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="是否置顶" prop="topFlag">
            <el-switch
              v-model="form.topFlag"
              active-value="1"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col v-if="shouldShowLssuingDepartment" :span="10">
          <el-form-item label="政策状态" prop="status">
            <el-switch
              v-model="form.status"
              active-value="0"
              inactive-value="1"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import UploadImage from "@/components/UploadImage";
import { getCityData } from "@/api/release/financial";
import { getToken } from "@/utils/auth";
import { getInfo } from "@/api/release/index.js";
export default {
  name: "policyGuide",
  dicts: [
    "lssuing_department",
    "policy_grade",
    "information_sources",
    "policytypes",
    "diagnosis_policy",
    "activity",
    "industrial_ore_label",
    "wx_field",
  ],
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  components: {
    UploadImage,
  },
  computed: {
    shouldShowLssuingDepartment() {
      return (
        this.form.policyOrActivity !== "news" &&
        this.form.policyOrActivity !== "newsIndustryReport"
      );
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 如果当前时间之前的时间都禁用，则减去一天的毫秒数
        },
      },
      ActivityObj: {}, //根据policyOrActivity动态更换字典
      OrActivityList: [
        {
          label: "政策",
          value: "policy",
          type: 1,
          dict: "policytypes",
        },
        {
          label: "活动",
          value: "activity",
          type: 3,
          dict: "activity",
        },
        {
          label: "诊断",
          value: "diagnosis",
          type: 4,
          dict: "diagnosis_policy",
        },
        {
          label: "行业新闻",
          value: "news",
          type: 5,
        },
        {
          label: "产业报告",
          value: "newsIndustryReport",
          type: 6,
        },
      ],
      form: {
        policyOrActivity: "policy",
        policyTitle: undefined,
        policyContent: undefined,
        policyCategory: undefined,
        regionCode: undefined,
        imgUrl: [],
        policyUrl: undefined,
        lssuingDepartment: undefined,
        policyLevel: undefined,
        domainCode: undefined,
        label: [],
        informationSources: undefined,
        hotFlag: "0",
        topFlag: "0",
        status: "0",
        richText: "",
        annexUrl: [],
      },
      rules: {
        policyOrActivity: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
        policyTitle: [
          { required: true, message: "请输入标题", trigger: "blur" },
        ],
        policyContent: [
          { required: true, message: "请输入介绍", trigger: "blur" },
        ],
        policyCategory: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
        lssuingDepartment: [
          { required: true, message: "请选择发布单位", trigger: "change" },
        ],
        policyLevel: [
          { required: true, message: "请选择政策等级", trigger: "change" },
        ],
        regionCode: [
          { required: true, message: "请选择区域", trigger: "change" },
        ],
        policyUrl: [{ required: true, message: "请输入链接", trigger: "blur" }],
        releaseTime: [
          { required: true, message: "请选择时间", trigger: "change" },
        ],
        richText: [{ required: true, message: "请输入内容", trigger: "blur" }],
        hotFlag: [{ required: true, message: "请选择", trigger: "change" }],
        topFlag: [{ required: true, message: "请选择", trigger: "change" }],
        status: [{ required: true, message: "请选择", trigger: "change" }],
        // imgUrl: [{ required: true, message: "请上传", trigger: "change" }],
      },
      cityData: [],
      submitDing: false,
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/file/upload",
      },
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },
  created() {
    //默认的第一个选项，根据policyOrActivity动态更换字典下拉
    this.ActivityObj = this.OrActivityList[0];
    this.getCityDataFtn();
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },
  methods: {
    addUpload(res) {
      this.form.imgUrl = [...this.form.imgUrl, res.url];
    },
    removeUpload(file) {
      const index = this.form.imgUrl.indexOf(file);
      if (index > -1) {
        this.form.imgUrl.splice(index, 1);
      }
    },
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        //查看详情，根据返回的政策类型动态更换字典下拉
        this.radioInput(params.policyOrActivity);
        this.form = {
          ...params,
          imgUrl: params.imgUrl ? params.imgUrl.split(",") : [],
          regionCode: params.regionCode ? params.regionCode.split(",") : [], // 确保regionCode为数组
          label: params.label ? params.label.split(",") : [],
          annexUrl: params.enclosureName
            ? [
                {
                  name: params.enclosureName,
                  url: params.enclosureUrls,
                },
              ]
            : [],
        };
        this.submitDing = false;
      });
    },
    //单选框切换的事件
    radioInput(e) {
      const foundItem = this.OrActivityList.find((item) => item.value === e);
      if (foundItem) {
        this.ActivityObj = foundItem;
      }
      this.$emit("roll");
      this.form.policyCategory = undefined;
    },
    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const matchedItem = this.OrActivityList.find(
            (item) => item.value === this.form.policyOrActivity
          );
          const policyType = matchedItem ? matchedItem.type : null; // 使用条件运算符来确保访问的是有效对象的属性
          const params = {
            ...this.form,
            policyType,
            label: this.form.label ? this.form.label.join(",") : undefined,
            imgUrl: this.form.imgUrl.join(","),
            regionCode: this.form.regionCode
              ? this.form.regionCode.join()
              : undefined,
            // 处理多个附件的情况
            enclosureName: this.form.annexUrl
              .map((item) => item.name)
              .join(","),
            enclosureUrl: this.form.annexUrl.map((item) => item.url).join(","),
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
    // 文件上传进度处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      const data = {
        ...response.data,
        uid: this.generateUniqueId(),
      };
      this.form.annexUrl.push(data);
    },
    // 生成唯一ID的函数
    generateUniqueId() {
      // 这里可以使用例如UUID库或其他方式生成唯一ID
      return Math.random().toString(36).substring(7);
    },
    // 文件移除事件
    beforeRemove(file, fileList) {
      // 手动在fileList中删除文件
      const index = this.form.annexUrl.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        this.form.annexUrl.splice(index, 1);
      }
      return true;
    },
  },
};
</script>

<style lang="scss">
.financialCommn {
  width: 80%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }
}
</style>
