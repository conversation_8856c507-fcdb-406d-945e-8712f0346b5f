<template>
  <div class="offline-detail app-container">
    <h3 class="container-title">基本信息</h3>
    <el-descriptions
      :labelStyle="labelStyle"
      class="margin-top"
      :column="2"
      border
    >
      <el-descriptions-item v-for="item in labeColumn" :label="item.label">
        <dict-tag
          v-if="item.dict"
          :options="dict.type[item.dict]"
          :value="baseInfo[item.value]"
        />
        <span v-else>{{ baseInfo[item.value] }}</span>
      </el-descriptions-item>
    </el-descriptions>
    <h3 v-if="isShow" class="container-title node-wrap">节点列表</h3>
    <el-table v-if="isShow" height="100%" :data="tableData">
      <el-table-column
        align="center"
        prop="nodeName"
        label="节点名"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="handler"
        label="处理人"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="handleTime"
        label="处理时间"
      ></el-table-column>
      <el-table-column align="center" prop="nodeStatus" label="节点状态">
        <template slot-scope="scope">
          <span>{{
            scope.row.nodeStatus == "UNHANDLE"
              ? "未处理"
              : scope.row.nodeStatus == "FINISH"
              ? "已处理"
              : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="costTime"
        label="花费时长"
      ></el-table-column>
      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.whetherInfo == 'YES'"
            type="text"
            @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
      <el-empty slot="empty" :image-size="100" />
    </el-table>
    <el-dialog @close="visible = false" :visible="visible" title="节点详情">
      <offline
        :tableData="nodeTableData"
        offlineType="details"
      />
      <!-- <el-table :data="nodeTableData">
        <el-table-column
          align="center"
          prop="wordName"
          label="类型"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="fileName"
          label="名称"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="handler"
          label="处理人"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="handleTime"
          label="上传时间"
        ></el-table-column>
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="fileDownload(scope.row)"
              >文档查看</el-button
            >
          </template>
        </el-table-column>
      </el-table> -->
      <div slot="footer">
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- <div v-if="isShow" class="footer">
      <el-button type="primary" @click="$router.back()">返回</el-button>
    </div> -->
  </div>
</template>
<script>
import { getOfflineDetail, getDiagnosisType } from "@/api/diagnosis";
import offline from "./offline.vue"; //线下诊断的表格弹窗

export default {
  // dicts: ['diagnostic_level_status', 'diagnosis_type'],
  components: { offline },
  data() {
    return {
      isShow: true,
      labeColumn: [
        { label: "企业名称", value: "deptName" },
        // { label: '诊断等级', value: 'leval', dict: 'diagnostic_level_status' },
        { label: "诊断类型", value: "diagnosisTypeName" },
        { label: "诊断开始时间", value: "startDate" },
        { label: "诊断结束时间", value: "endDate" },
        { label: "附加条件", value: "additionalCondition" },
        { label: "第一联系人", value: "relationUser" },
        { label: "联系电话", value: "relationPhone" },
        { label: "第二联系人", value: "twoRelationUser" },
        { label: "联系电话", value: "twoRelationPhone" },
      ],
      baseInfo: {},
      tableData: [],
      visible: false,
      nodeTableData: [],
      diagnosisTypeList: [],
    };
  },
  created() {
    const { type, flowInstanceId } = this.$route.query;
    // this.isShow = type ? false : true;
    getDiagnosisType().then((res) => {
      const data = res.data || [];
      this.diagnosisTypeList = data;
    });
    this.getEnterpriseInfo(flowInstanceId);
  },
  computed: {
    labelStyle() {
      return { width: "200px" };
    },
  },
  methods: {
    async getEnterpriseInfo(flowInstanceId) {
      const { data } = await getOfflineDetail({ flowInstanceId });
      data.params.ddiagnosticOfflineByIdVO.diagnosisTypeName =
        this.diagnosisTypeList.find(
          (item) =>
            item.id == data.params.ddiagnosticOfflineByIdVO.diagnosisType
        )?.labelName || "";
      this.$set(
        this,
        "baseInfo",
        data.params ? data.params.ddiagnosticOfflineByIdVO : {}
      );
      console.log(data);

      this.tableData = data.params
        ? data.params.flowInstanceNodeResponseVOS
        : [];
    },
    handleDetail(row) {
      this.nodeTableData = row.flowNodeInfos || [];
      this.visible = true;
    },
    fileDownload(row) {
      const win = window.open(this.ensureFullUrl(row.file), "_blank");
      win.opener = null;
    },
  },
};
</script>
<style lang="scss" scoped>
.offline-detail {
  padding: 20px;

  .container-title {
    margin-bottom: 10px;
    border-bottom: none;

    &::before {
      display: inline-block;
      content: "";
      width: 4px;
      height: 12px;
      background-color: #0147eb;
      margin-right: 5px;
    }
  }

  .node-wrap {
    margin-top: 30px;
  }

  .footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
