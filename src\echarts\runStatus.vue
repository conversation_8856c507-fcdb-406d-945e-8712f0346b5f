<template>
  <div id="runStatus" :style="{width: '880px', height: '220px'}"></div>
</template>

<script>
export default {
  data() {
    return {}
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {
    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let runStatus = this.$echarts.init(document.getElementById('runStatus'))

      // 绘制图表
      runStatus.setOption({
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: '5%',
          data: ['运行', '待机', '故障']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            // saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '运行',
            type: 'line',
            stack: 'Total',
            data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 11, 21, 31, 41, 51, 61, 71, 81, 91, 12, 22, 32, 42, 52, 62]
          },
          {
            name: '待机',
            type: 'line',
            stack: 'Total',
            data: [62, 52, 42, 32, 22, 12, 91, 81, 71, 61, 51, 41, 31, 21, 11, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
          },
          {
            name: '故障',
            type: 'line',
            stack: 'Total',
            data: [15, 25, 35, 45, 55, 65, 75, 85, 95, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
          }
        ]
      });
    }
  }
}

</script>
