export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "delBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon":false,
  "searchShow": true,
  "column": [
    {
      "type": "input",
      "label": "名称",
      "prop": "username",
      "span": 12,
      "search":true,
    },
    {
      "type": "input",
      "label": "性别",    //0-男，1-女
      "prop": "sex",
      "span": 12,
      dicUrl:'/system/dict/type/gender'
    },
    {
      "type": "input",
      "label": "联系电话",
      "prop": "phone",
      "span": 12,
      "search":true,
    },
    {
      "type": "input",
      "label": "资质证书",
      "prop": "certificate",
      "span": 12
    },

    {
      "type": "input",
      "label": "所属部门",
      "prop": "deptName",
      "span": 12
    },
    // {
    //   label: '角色',
    //   prop: 'roleList',
    //   formslot: true,
    //   slot: true,
    //   overHidden: true,
    //   span: 24,
    // },
    // {
    //   label: '状态',
    //   prop: 'lockFlag',
    //   type: 'radio',
    //   slot: true,
    //   border:true,
    //   span: 24,
    // },
    // {
    //   "type": "input",
    //   "label": "主键ID",
    //   "prop": "userId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "password",
    //   "prop": "password",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "salt",
    //   "prop": "salt",
    //   "span": 12
    // },
    //
    // {
    //   "type": "input",
    //   "label": "avatar",
    //   "prop": "avatar",
    //   "span": 12
    // },
    //
    // {
    //   "type": "input",
    //   "label": "创建时间",
    //   "prop": "createTime",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "修改时间",
    //   "prop": "updateTime",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "lockFlag",
    //   "prop": "lockFlag",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "delFlag",
    //   "prop": "delFlag",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "微信登录openId",
    //   "prop": "wxOpenid",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "小程序openId",
    //   "prop": "miniOpenid",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "QQ openId",
    //   "prop": "qqOpenid",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "码云 标识",
    //   "prop": "giteeLogin",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "开源中国 标识",
    //   "prop": "oscId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "所属租户",
    //   "prop": "tenantId",
    //   "span": 12
    // },
    // {
    //   "type": "input",
    //   "label": "1-实施人员，0-正常",
    //   "prop": "isImplementer",
    //   "span": 12
    // },
    //
    // {
    //   "type": "input",
    //   "label": "出生日期",
    //   "prop": "birthday",
    //   "span": 12
    // }
  ]
}
