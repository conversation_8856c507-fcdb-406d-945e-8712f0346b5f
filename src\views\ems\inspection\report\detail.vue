<template>
  <div>
    <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>基本信息</span></div>
      <div class="devTitle"><span>{{form.deviceName}}</span></div>
      <div>
        <div class="tableStyle">
          <div class="labelS">记录编号</div>
          <div class="contentS">{{form.recordNum}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">设备编号</div>
          <div class="contentS">{{form.deviceNum}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">位置</div>
          <div class="contentS">{{form.locationName}}</div>
        </div>
        <div class="tableStyle">
          <div class="labelS">点检人</div>
          <div class="contentS">{{form.inspector}}</div>
           <div class="labelS">点检时间</div>
            <div class="contentS">{{form.inspectionTime}}</div>
        </div>
<!--        <div class="tableStyle">-->
<!--          <div class="labelS">带你就要求</div>-->
<!--          <div class="contentS">-->
<!--              {{form.id}}-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="tableStyle">-->
<!--          <div class="labelS">点检图片</div>-->
<!--          <div class="contentS">-->
<!--            检查是否完好，是否有防静电措施，相关设备是否完整-->
<!--          </div>-->
<!--        </div>-->
      </div>
    </el-card>
    <el-card shadow="always" class="box-card">
      <div class="tableTitle"><span>点检信息</span></div>
       <el-card shadow="always" class="box-card" style="box-shadow:4px 10px 5px 0px rgba(168, 168, 168, .2);">
           <el-row>
               <el-col :span="6">
                    <div>点检项目个数</div>
                    <div class="number">{{form.allNum}}</div>
               </el-col>
                <el-col :span="6">
                    <div>已检数</div>
                    <div class="number" style="color:#54B03A">{{form.yiNum}}</div>
               </el-col>
                <el-col :span="6">
                    <div>异常项目</div>
                    <div class="number" style="color:#FF5722">{{form.ycNum}}</div>
               </el-col>
                <el-col :span="6">
                    <div>执行说明</div>
                    <div class="number">未知</div>
               </el-col>
           </el-row>
       </el-card>
    </el-card>
    <el-card shadow="always" class="box-card">
        <IconTitle title="巡检情况" imgUrl="yunwei"></IconTitle>
        <el-table v-loading="device.loading" :data="deviceList"
        >
            <el-table-column
                    label="序号"
                    width="70px">
                <template slot-scope="scope">
                    {{scope.$index+1}}
                </template>
            </el-table-column>
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="编号" align="center" prop="itemsNum"/>
            <el-table-column label="项目" align="center" prop="itemsName"/>
            <el-table-column label="点检值" align="center" prop="resultsInspectValue"/>
            <el-table-column label="点检时间" align="center" prop="resultsCreateTime"/>
            <el-table-column label="是否异常" align="center">
                <template slot-scope="scope">
                                          <span>{{
                                            scope.row.resultsIsAbnormal == 0
                                              ? "正常"
                                              : scope.row.resultsIsAbnormal == 1
                                              ? "异常": ""
                                          }}</span>
                </template>
            </el-table-column>
            <el-table-column label="点检照片" align="center" prop="resultsImageList"/>
            <el-table-column label="备注" align="center" prop="resultsRemark"/>
        </el-table>

        <pagination
                v-show="deviceQueryParams.total>0"
                :total="deviceQueryParams.total"
                :page.sync="deviceQueryParams.pageNum"
                :limit.sync="deviceQueryParams.pageSize"
                @pagination="getDeviceList"
        />
    </el-card>
      <el-card shadow="always" class="info-btn-box">
          <el-button @click="goBack">返回</el-button>
      </el-card>
  </div>
</template>
<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import {inspectrecordGetObj,inspectrecordGetObjTaskId,getEmsInsCheckResultsRecordIdPage} from "@/api/ems/inspection/emsinsinspectrecord";
    export default {
        name: "detailReprotIndex",
        components: {
            IconTitle,
        },
        props: {
            id: {
                type: Number,
            },
        },
        data() {
            return {
                deviceList: [],
                device: {
                    loading: false,
                },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                form: {
                    id: null,
                    deviceNum:null,
                    recordNum:null,
                    deviceName:null,
                    locationName:null,
                    inspector:null,
                    inspectorName:null,
                    inspectionTime:null,
                    allNum:0,
                    yiNum:0,
                    ycNum:Number(0),
                },
            };
        },
        mounted() {
            if (this.id > 0) {
                inspectrecordGetObj(this.id).then((res) => {
                    this.form = res.data
                    this.getDeviceList();
                });
            }
        },
        methods: {
            getDeviceList() {
                this.device.loading = true;
                getEmsInsCheckResultsRecordIdPage(Object.assign(
                    {
                        current: this.deviceQueryParams.pageNum,
                        size: this.deviceQueryParams.pageSize,
                    },
                    {id:this.id}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    this.form.allNum=this.deviceList.length;
                    this.form.yiNum=this.deviceList.length;
                    var a = Number(0)
                    for (var i =0;i< this.deviceList.length;i++){
                        if (this.deviceList[i].resultsIsAbnormal=='1'){
                            a++
                        }
                    }
                    this.form.ycNum=a
                    this.deviceQueryParams.total = response.data.total;
                    this.device.loading = false;
                });
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss" scoped>
    .info-btn-box {
        width: 100%;
        text-align: center;
    }
.tableTitle {
  color: #333;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}
.devTitle {
  color: #262626;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
  .el-card__body {
    padding-top: 10px;
  }
  .labelS {
    //display: inline-block;
    flex:0 0 150px;
    //height: 40px;
    // margin-right: 10px;
    text-align: left;
    color: #606266;
    padding: 10px;
    border: 1px solid rgba(236, 240, 244, 100);
    margin-bottom: -1px;
  }
  .contentS {
    border: 1px solid rgba(236, 240, 244, 100);
    // height: 40px;
    color: #606266;
    width: 100%;
    margin-left: -1px;
    margin-bottom: -1px;
    padding: 10px;
    // margin: 10px 0;
    // width: calc(100% - 120px);
    // display: inline-block;
  }
  .tableStyle {
    display: flex;
  }
  .number{
      font-weight: bold;
      margin-top: 5px;
      font-size: 16px;
  }
}
</style>
