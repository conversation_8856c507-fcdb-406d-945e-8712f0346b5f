
const DIC = {
  vaild: [{
    label: '启用',
    value: '0'
  }, {
    label: '禁用',
    value: '1'
  }]
}

export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": false,
  tree: true,
  "column": [
    {
      "type": "input",
      "label": "设备类别",
      "prop": "category",
      "span": 12,
      rules: [{
        required: true,
        message: '请输入设备类别',
        trigger: 'blur'
      }]
    }, {
      "type": "input",
      "label": "父id",
      "prop": "parentId",
      "span": 12
    },{
      label: '状态',
      prop: 'status',
      width: 80,
      type: 'select',
      dicData: DIC.vaild,
      rules: [{
        required: true,
        message: '请选择类型',
        trigger: 'blur'
      }]
    },
    {
      row: true,
      minRows: 2,
      "type": "textarea",
      "label": "备注",
      "prop": "remark",
      "span": 12
    }]
}
