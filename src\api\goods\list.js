
import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

export function getGoodsManageList(query) {
  return request({
    url: '/platform/goods/manage',
    method: 'get',
    params: query
  })
}


export function getCityData(query) {
  return request({
    url: '/system/cityData/getCityData',
    method: 'get',
    params: query
  })
}

// 获取商品详细信息
export function getGoodsManageDetail(id) {
  return request({
    url: "/platform/goods/manage/" + parseStrEmpty(id),
    method: "get",
  });
}
// 新增商品
export function addGoodsManage(data) {
  return request({
    url: "/platform/goods/manage",
    method: "post",
    data: data,
  });
}

// 修改商品
export function updateGoodsManage(data) {
  return request({
    url: "/platform/goods/manage",
    method: "put",
    data: data,
  });
}

// 删除商品
export function deleteGoodsManage(id) {
  return request({
    url: "/platform/goods/manage/" + id,
    method: "delete",
  });
}