<template>
  <div class="cost">
    <el-row>
      <el-col :span="15">
        <div class="table-box" style="height: 100%">
          <IconTitle title="设备总价值" imgUrl="yunwei">
          </IconTitle>
          <div class="echarts-box" style="height:450px;overflow-y:auto">
            <el-table
                :data="equipmentAccountData"
                border
                @row-click="getNum"
                show-summary
                :summary-method="getSummaries"
                style="width: 100%">
              <el-table-column
                  type="index"
                  label="序号">
              </el-table-column>
              <el-table-column
                  prop="deviceName"
                  label="设备名称"
                  width="180">
              </el-table-column>
              <el-table-column
                  prop="deviceNum"
                  label="设备编号"
                  width="180">
              </el-table-column>
              <el-table-column
                  prop="purchaseAmount"
                  label="原值">
              </el-table-column>
              <el-table-column
                  prop="currentNetWorth"
                  label="净值">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
      <el-col :span="8" style="margin-left: 20px; height: 500px">
        <div class="table-box" style="height: 100%">
          <IconTitle title="价值波动" imgUrl="yunwei"></IconTitle>
          <div style="height:450px;overflow-y:auto">
            <div class="block">
              <el-timeline v-for="(item,index) in numData" :key="index">
                <el-timeline-item :timestamp=item.mon placement="top">
                  <el-card class="el-card">
                    <h4>{{ item.num }}</h4>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!--    <div class="table-box" style="height: 100%">-->
    <!--      <IconTitle title="价值波动" imgUrl="yunwei">-->
    <!--      </IconTitle>-->
    <!--      <div class="echarts-box" style="height: 100%">-->
    <!--        <fluctuations style="margin-top: -30px"/>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script>
import {
  getEquipmentAccount,
  getFluctuationsValue
} from "@/api/ems/statistical/device";
import IconTitle from "@/components/ems/icon-title/index.vue";
import fluctuations from "./echarts/fluctuations";
import timeLine from "./timeLine";

export default {
  name: "cost",
  components: {
    IconTitle,
    fluctuations,
    timeLine
  },
  data() {
    return {
      equipmentAccountData: [],
      numData: [],
      currentNetWorth: '',
      purchaseAmount: '',
      list: [
        {
          title: '完成高优先级需求及问题的研发、测试及上线',
          description: '2020-04-20',
          status: 0 // 判断是否有按钮及盒子凸显，例子中0代表不凸显无按钮。 具体情况可自行判断
        },
        {
          title: '完成中优先级需求及问题的研发、测试及上线',
          description: '2020-05-31',
          status: 1
        },
        {
          title: '完成中低先级需求及问题的研发、测试及上线',
          description: '2020-05-31',
          status: 0
        }
      ],
      current: 1


    }
  },
  created() {
    this.getEquipmentAccountList();
  },
  methods: {

    //鼠标移入移出事件
    mouseOver(val) {
      this.current = val
      this.hoverData[val] = true
      console.log(this.hoverData)
      console.log(this.hoverData[val] === true);
    },
    mouseLeave(val) {
      this.current = null
      this.hoverData[val] = false
      console.log(this.hoverData[val] === true);
    },
    getNum(val) {
      let thisRowData = this;
      thisRowData = val;
      console.log("111>>>", thisRowData.id)
      getFluctuationsValue(thisRowData.id).then(res => {
        this.numData = res.data.data;
        console.log("222>>>", res.data.data)
      })
    },
    getEquipmentAccountList() {
      getEquipmentAccount().then(res => {
        this.equipmentAccountData = res.data.data.equipmentAccountList;
        this.currentNetWorth = res.data.data.currentNetWorth;
        this.purchaseAmount = res.data.data.purchaseAmount;
      });
    },
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        // const values = data.map(item => Number(item[column.property]));
        // if (!values.every(value => isNaN(value))) {
        //   sums[index] = values.reduce((prev, curr) => {
        //     const value = Number(curr);
        //     if (!isNaN(value)) {
        //       return prev + curr;
        //     } else {
        //       return prev;
        //     }
        //   }, 0);
        //   sums[index] += ' 元';
        // } else {
        //   sums[index] = '';
        // }
        //根据当前列绑定的字段名进行判断，根据字段名决定展示什么内容
        switch (column.property) {
            //金额;
          case "purchaseAmount":
            //在这里你就可以根据需要对数据进行一些处理（保留小数位数，加上单位等）
            sums[index] = this.purchaseAmount + '元';
            break;
            //人数
          case "currentNetWorth":
            sums[index] = this.currentNetWorth + '元'
            break;
          default:
            sums[index] = "--";
            break;
        }
      });

      return sums;
    }
  }
}


</script>

<style scoped lang="less">

.hoverSteps {
  /deep/ .ai-step__description {
    padding-right: 0 !important;
  }
}

.stepNoBtn {
  padding: 12px 12px;
  box-sizing: border-box;
  margin-bottom: 10px;

  .step-title-font {
    font-size: 12px;
    font-weight: bold;;
  }
}

.el-card {
  box-shadow: 0px 10px 10px #efefef;
}

.block {
  margin-top: 20px;
  width: 90%;
}

.stepBtn {
  box-sizing: border-box;
  background: #fff;
  /*width: 90%;*/
  border-radius: 4px;
  border: 1px solid #ebeef5;
  line-height: 1.4;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  word-break: break-all;

  .btnPosition {
    text-align: right;
  }
}

</style>
