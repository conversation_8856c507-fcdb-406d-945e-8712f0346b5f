<template>
  <div class="inspection">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>巡检统计</span>
              </div>
              <div class="total-tj-gz">
                <div class="tj">
                  <span class="top">任务总数</span>
                  <b>{{ inspection.taskSum }}</b>
                </div>
                <div class="tj">
                  <span class="top">已完成</span>
                  <b class="wancheng">{{ inspection.completed }}</b>
                </div>
                <div class="tj">
                  <span class="top">待核验</span>
                  <b class="zhixing">{{ inspection.toCheck }}</b>
                </div>
                <div class="tj">
                  <span class="top">已过期</span>
                  <b class="guoqi">{{ inspection.outOfDate }}</b>
                </div>
              </div>
            </div>
          </el-col
          >
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>巡检情况</span>
              </div>
              <div class="main-item">
                <span>最近巡检</span>
                <div class="flex">
                  <span>{{ inspectionSituation1 != null ? inspectionSituation1.recentInspection : "暂无数据" }}</span>
                  <span>日</span>
                  <span>{{ inspectionSituation1 != null ? inspectionSituation1.userName : "暂无数据" }}</span>
                </div>
              </div>
              <div class="main-item">
                <span>下次巡检</span>
                <div class="flex green">
                  <span>{{ inspectionSituation2 != null ? inspectionSituation2.nextInspection : "暂无数据" }}</span>
                  <span>日</span>
                  <span>{{ inspectionSituation2 != null ? inspectionSituation2.userName : "暂无数据" }}</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span>巡检结果</span>
              </div>
              <div id="typeEcharts" style="height: 140px"></div>
            </div>
          </el-col>
        </el-row>
      </div>

    </div>
    <div class="table-box">
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="16"
          >
            <div class="echarts-item">
              <IconTitle title="关联计划" imgUrl="yunwei">
                <span class="slot">关联的标准计划</span>
              </IconTitle>
              <el-table
                  :data="planData"
                  height="140"
                  border
                  style="width: 100%"
                  @selection-change="deviceSelectionChange"
              >
                <el-table-column prop="planNum" label="计划编号" align="center">
                </el-table-column>
                <el-table-column prop="planName" label="计划名称" align="center">
                </el-table-column>
                  <el-table-column prop="liableUserName" label="负责人" align="center">
                </el-table-column>
                <el-table-column prop="inspectCycle" label="巡检周期" align="center">
                  <template slot-scope="scope">
                    <span>{{
                        scope.row.inspectCycle == 1
                            ? "日"
                            : ""
                      }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveTime" label="任务有效期(天)" align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="8"
          >
            <div class="echarts-item">
              <IconTitle title="关联标准" imgUrl="yunwei">
                <span class="slot">关联标准项目</span>
              </IconTitle>
              <el-table
                  :data="standardData"
                  border
                  height="140"
                  style="width: 100%"
                  @selection-change="deviceSelectionChange"
              >
                <el-table-column prop="standardNum" label="标准编号" align="center">
                </el-table-column>
                <el-table-column prop="standardName" label="标准名称" align="center">
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <el-card class="box-card btn-search page-search">
      <div slot="header">
        <div class="btn-box">
          <el-button type="info" icon="el-icon-refresh-left"></el-button>
          <el-button type="check" icon="el-icon-download" @click="exportExcel">导出</el-button>
        </div>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="任务编号">
            <el-input
                placeholder="请输入任务编号"
                v-model="searchForm.taskNum"
                clearable
            ></el-input>
          </el-form-item>

          <el-form-item label="任务状态">
            <el-select
                placeholder="请选择任务状态"
                v-model="searchForm.status"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.label"
                  :value="item.value"
                  :key="index"
                  v-for="(item, index) of dict.type.task_status"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="负责人">
            <el-select
                placeholder="请选择负责人"
                v-model="searchForm.liableUserId"
                clearable
                style="width: 200px"
            >
              <el-option
                  :label="item.userName"
                  :value="item.userId"
                  :key="item.userId"
                  v-for="item in userAll"
              ></el-option>
            </el-select>
          </el-form-item>

          <!--<el-form-item label="负责人">-->
          <!--  <el-input-->
          <!--      placeholder="请输入负责人"-->
          <!--      v-model="searchForm.liableUserId"-->
          <!--  ></el-input>-->
          <!--</el-form-item>-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChange">查询</el-button>
            <el-button icon="el-icon-refresh-right" @click="resetBtn">重置</el-button>
          </el-form-item>
        </el-form>
        <div></div>
      </div>
    </el-card>
    <div class="table-box">
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="24"
          >
      <IconTitle title="巡检记录" imgUrl="yunwei">
        <span class="slot">巡检记录表</span>
      </IconTitle>
      <!-- 设备文档 -->
      <el-table
          :data="inspectionRecordData"
          border
          style="width: 100%"
          @selection-change="deviceSelectionChange"
      >
        <el-table-column prop="taskNum" label="任务编号" align="center">
        </el-table-column>
        <el-table-column prop="inspectCycle" label="巡检类型" align="center">
          <template slot-scope="scope">
                    <span>{{
                        scope.row.inspectCycle == 1
                            ? "日"
                            : ""
                      }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="任务状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0"><el-tag type="info" effect="dark">未开始</el-tag></span>
            <span v-if="scope.row.status == 1"><el-tag effect="dark">执行中</el-tag></span>
            <span v-if="scope.row.status == 2"><el-tag type="warning" effect="dark">待核验</el-tag></span>
            <span v-if="scope.row.status == 3"><el-tag type="success" effect="dark">已完成</el-tag></span>
            <span v-if="scope.row.status == 4"><el-tag type="danger" effect="dark">已过期</el-tag></span>
          </template>

        </el-table-column>
        <el-table-column prop="planNum" label="计划编号" align="center">
        </el-table-column>
        <el-table-column prop="planName" label="计划名称" align="center">
        </el-table-column>
        <el-table-column prop="beginTime" label="计划开始时间" width="120" align="center">
        </el-table-column>
        <el-table-column prop="endTime" label="计划结束时间" width="120" align="center">
        </el-table-column>
        <el-table-column prop="executeTime" label="开始执行时间" width="120" align="center">
        </el-table-column>
        <el-table-column prop="executeEndTime" label="结束执行时间" width="120" align="center">
        </el-table-column>
        <el-table-column prop="liableUser" label="责任人" align="center">
        </el-table-column>
        <el-table-column prop="ofCheckedNum" label="已检" align="center">
        </el-table-column>
        <el-table-column prop="leakDetectionNum" label="未检" align="center">
        </el-table-column>
        <el-table-column prop="abnormalNum" label="异常" align="center">
        </el-table-column>
      </el-table>
      <pagination
          style="float: right"
          v-show="inspectionRecordData.total > 0"
          :total="inspectionRecordData.total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getInspectionRecordById"
      />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import echarts from "echarts";
import {getEquipmentInspectionById,getInspectionSituation} from "@/api/ems/equipment/account";
import {listUser} from '@/api/system/user'
import {getInspectionRecord,getSelectTaskStatus} from "@/api/ems/inspection/task"
import IconTitle from "@/components/icon-title/index.vue";


let typeOption = {
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  legend: {
    orient: 'vertical',
    right: 'left',
    y:'center',
    formatter: function(name) {
      let data = typeOption.series[0].data;
      let total = 0;
      for (let i = 0; i<data.length; i++) {
        total = total + Number(data[i].value);
      }
      let p ;
      let tarValue;
      for (let i = 0; i<data.length; i++) {
        if (data[i].name == name) {
          tarValue = data[i].value;
          p = (tarValue / total * 100).toFixed(1);
        }
      }


      return name + ' ' + tarValue + ' ' + '(' + p + '%)';
    },
  },

  color: ['#63b2ee', '#76da91', '#f8cb7f' ,'#f89588', '#7cd6cf','#9192ab', '#7898e1' , '#efa666','#eddd86', '#9987ce' ,'#63b2ee', '#76da91'],
  series: [
    {
      type: "pie",
      radius: ["35%", "55%"],
      center: ["35%", "50%"],
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      data: [],
    },
  ],
};
export default {
  name: "inspection",
  components: {
    IconTitle,
  },
  dicts: ['task_status'],
  data() {
    return {
      typeOption,
      inspection: {},
      searchForm: {
        taskNum: '',
        status: '',
        liableUserId: '',
        deviceId: ''
      },
      planData: [],
      standardData: [],
      inspectionRecordData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      deviceData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1517 弄",
        },
      ],
      inspectionSituation1: [],
      taskStatusSelect: [],
      inspectionSituation2: [],
      situation: [],
      userListL: [],
      page: {
        currentPage: 1,
        pageSize: 10
      },
      userAll: [],

    };
  },
  created() {
    this.getSelect();
  },
  mounted() {
    this.getInspectionSituationList();
    this.getEquipmentInspectionById();
    this.getInspectionRecordById();
    this.getListUser();
  },
  methods: {

    // 获取负责人
    getListUser() {
      listUser().then(res => {
        this.userAll = res.rows
      })
    },

    resetBtn(){
      this.searchForm.taskNum='';
      this.searchForm.status='';
      this.searchForm.liableUserId='';
    },
    deviceSelectionChange() {
    },
    getEquipmentInspectionById() {
      getEquipmentInspectionById(this.$route.query.id).then(res => {
        if (res.data.code == 0) {
          this.inspection = res.data;
          if (this.inspection != null) {
            this.planData = res.data.insInspectPlans;
            this.standardData = res.data.insInspectStandards;
            this.inspectionSituation1 = res.data.userNameAndRecentInspection;
            this.inspectionSituation2 = res.data.userNameAndNextInspection;
          }

        }
      })
    },

    searchChange() {
      this.getInspectionRecordById(this.searchForm);
    },

    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsinsinspecttask/inspectionRecord/export",
          this.searchForm,
          "巡检记录.xlsx"
      );
    },

    getSelect() {
      // getSelectTaskStatus().then((res) => {
      //   this.taskStatusSelect = res.data;
      // });
    },

    getInspectionSituationList() {
      getInspectionSituation(this.$route.query.id).then(res => {
        this.situation = res.data;
        let chartDom = document.getElementById("typeEcharts");
        let myChart = echarts.init(chartDom);
        if (this.situation.length > 0) {
          typeOption.series[0].data = this.situation;
          myChart.setOption(this.typeOption);
        } else {
          myChart.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }

      });
    },

    getInspectionRecordById(params) {
      let id = this.$route.query.id;
      this.searchForm.deviceId = id;
      getInspectionRecord(this.searchForm).then(res => {
        if (res.data.code == 0) {
          this.inspectionRecordData = res.data.records;
          // this.total = res.data.total;
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/color.scss";

.inspection {
  .total-tj-gz {
    padding: 25px 0 20px 0;
    display: flex;

    .tj {
      width: 50%;

      .top {
        display: block;
        margin-bottom: 10px;
      }

      b {
        font-size: 20px;
      }

      b.wancheng {
        color: $theme;
      }

      b.zhixing {
        color: $colorEdit;
      }

      b.guoqi {
        color: $colorDe;
      }
    }
  }

  .main-item {
    margin-top: 15px;

    .flex {
      padding: 5px;
      background: #f3f5fb;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .flex.green {
      background: #effcf4;
    }
  }

}
</style>
