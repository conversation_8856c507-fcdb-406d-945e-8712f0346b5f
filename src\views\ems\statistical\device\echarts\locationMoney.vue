<template>
  <div id="locationMoney" :style="{width: '500px', height: '300px'}"></div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    locationPurchaseAmountDate: {
      type: Array
    },
  },

  watch: {
    locationPurchaseAmountDate: function(newVal,oldVal){
      this.resultData = newVal;
      let resultArray = [];
      this.resultData.forEach((self,index) => {
        let obj = {};
        obj.value = this.resultData[index].purchaseAmount;
        obj.name = this.resultData[index].deviceLocation;
        resultArray.push(obj)
      })
      this.drawLine(resultArray);
    }
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {

  },
  methods: {

    drawLine(resultArray) {

      // 基于准备好的dom，初始化echarts实例
      let locationMoney = this.$echarts.init(document.getElementById('locationMoney'))

      let dataList = resultArray;
      if (dataList.length > 0) {
        const colorList = ['#63b2ee', '#76da91', '#f8cb7f' ,'#f89588', '#7cd6cf','#9192ab', '#7898e1' , '#efa666','#eddd86', '#9987ce' ,'#63b2ee', '#76da91'];

        // 绘制图表
        locationMoney.setOption({
          tooltip: {},
          series: [
            {
              type: 'pie',
              minAngle: [8],
              radius: ['30%', '50%'],
              center: ['50%', '50%'],
              label: {
                fontWeight: 'bold',
                fileSize: 12,
                show:true,
                formatter: '{b} : {c} ',
                rich: {
                  rich_blue: {
                    color: '#4D88FE',
                  },
                  rich_orange: {
                    color: '#FFBF3C',
                  },
                  rich_green: {
                    color: '#50CCCB',
                  },
                },
              },
              labelLine: {
                normal:{
                  // 统一设置指示线长度
                  length:15,
                  length2:15,
                }
              },
              itemStyle: {
                normal: {
                  borderColor: '#fff',
                  borderWidth: 2,
                  color: function (params) {
                    return colorList[params.dataIndex];
                  },
                },
              },
              data: dataList,
            },
          ],
        });
      } else {
        locationMoney.showLoading({
          text: '暂无数据',
          showSpinner: false,    // 隐藏加载中的转圈动图
          textColor: '#9d9d9d',
          maskColor: 'rgba(255, 255, 255, 0.8)',
          fontSize: '14px',
          fontFamily: 'Microsoft YaHei'
        });
      }

    }
  }
}

</script>
