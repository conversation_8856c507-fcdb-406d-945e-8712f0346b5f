<template>
    <div class="add-box">
        <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="140px"
                size="small"
                class="demo-ruleForm"
        >
            <div class="info-box">
                <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
                <div class="info-from">
                    <el-form-item label="计划编号" prop="planNum">
                        <el-input v-model="form.planNum" :disabled="true" placeholder="无需填写自动生成"/>
                    </el-form-item>
                    <el-form-item label="计划名称" prop="planName">
                        <el-input v-model="form.planName" placeholder="请输入计划名称"/>
                    </el-form-item>
                    <el-form-item label="所属部门" prop="deptId">
                        <treeselect
                                v-model="form.deptId"
                                :options="treeDeptData"
                                :normalizer="normalizer"
                                placeholder="所属部门"
                        />
                    </el-form-item>
                    <el-form-item label="负责人" prop="liableUserId">
                        <el-input v-model="form.liableUserName" :disabled="true" placeholder="请选择负责人">
                            <el-button @click="liableUser()" resource="false" style="padding-right:10px" slot="suffix"
                                       type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="开始执行时间" prop="beginTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.beginTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择开始执行时间"
                                style="width: 100%;"
                                @blur="blurBeginTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束执行时间" prop="endTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.endTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择结束执行时间"
                                style="width: 100%;"
                                @blur="blurEndTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="检修周期" prop="inspectCycle">
<!--                        <el-input v-model="form.inspectCycle"-->
<!--                                  @blur="blurInspectCycle"/>-->
                        <el-input v-model="form.inspectCycle" :disabled="true" @input="yzInput" placeholder="请选择检修标准">
                            <el-button @click="liableStandard()" resource="false" style="padding-right:10px" slot="suffix"
                                       type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="验收人" prop="auditUserId">
                        <el-input v-model="form.auditUserName" :disabled="true" placeholder="请选择验收人">
                            <el-button @click="auditUser()" style="padding-right:10px" slot="suffix" type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="是否启用" prop="enable">
                        <el-radio-group v-model="form.enable" @change="handleEnable">
                            <el-radio :label="0">不启用</el-radio>
                            <el-radio :label="1">启用</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="说明" prop="remark" style="width: 100%;">
                        <el-input type="textarea" v-model="form.remark" placeholder="请输入说明"/>
                    </el-form-item>
                </div>
                <!--用户弹框-->
                <el-dialog :title="user.title" :visible.sync="user.open" width="1000px" append-to-body>
                    <el-row :gutter="20">
                        <!--部门数据-->
                        <el-col :span="4" :xs="24">
                            <div class="head-container">
                                <div class="tree">
                                    <el-tree
                                            :data="treeDeptData"
                                            :props="defaultProps"
                                            :expand-on-click-node="false"
                                            :filter-node-method="filterNode"
                                            ref="tree"
                                            default-expand-all
                                            @node-click="handleNodeClick"
                                    />
                                </div>
                            </div>

                        </el-col>
                        <!--用户数据-->
                        <el-col :span="20" :xs="24">
                            <el-table v-loading="user.loading" :data="userList"
                                      @row-click="userRowClick">
                                <el-table-column label="用户编号" align="center" key="userId" prop="userId"/>
                                <el-table-column label="用户名称" align="center" key="username" prop="username"/>
                                <el-table-column label="部门" align="center" key="deptName" prop="deptName"/>
                                <el-table-column label="角色" align="center" key="role" prop="roleList[0].roleName"/>
                                <el-table-column label="手机号码" align="center" key="phone" prop="phone"/>
                                <el-table-column label="状态" align="center" key="lockFlag">
                                    <template slot-scope="scope">
                                          <span>{{
                                            scope.row.lockFlag == 0
                                              ? "有效"
                                              : scope.row.lockFlag == 2
                                              ? "锁定": ""
                                          }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="创建时间" align="center" prop="createTime"
                                                 width="160">
                                </el-table-column>
                            </el-table>

                            <pagination
                                    v-show="userQueryParams.total>0"
                                    :total="userQueryParams.total"
                                    :page.sync="userQueryParams.pageNum"
                                    :limit.sync="userQueryParams.pageSize"
                                    @pagination="getUserList"
                            />
                        </el-col>
                    </el-row>
                </el-dialog>
                <!--检修标准-->
                <el-dialog :title="standard.title" :visible.sync="standard.open" width="1300px" append-to-body>
                    <el-table v-loading="standard.loading" :data="standardList"
                              @row-click="standardRowClick">
                        <el-table-column prop="id" v-if="false"/>
                        <el-table-column label="检修标准编号" align="center" prop="standardNum"/>
                        <el-table-column label="检修标准名称" align="center" prop="standardName"/>
                        <el-table-column label="设备类别" align="center" prop="categoryId"/>
                        <el-table-column label="检修周期" align="center" prop="inspectCycle"/>
                        <el-table-column label="作业内容" align="center" prop="jobContent"/>
                        <el-table-column label="技术要求" align="center" prop="technicalRequirement"/>
                        <el-table-column label="安全要点" align="center"  prop="safety"/>
                        <el-table-column label="质量要求" align="center"  prop="qualityRequirements"/>
                    </el-table>
                    <pagination
                            v-show="standardQueryParams.total>0"
                            :total="standardQueryParams.total"
                            :page.sync="standardQueryParams.pageNum"
                            :limit.sync="standardQueryParams.pageSize"
                            @pagination="getStandardyList"
                    />
                </el-dialog>
            </div>
            <div class="info-box">
                <IconTitle title="设备列表" imgUrl="yunwei"></IconTitle>
                <!--                @selection-change="handleSelectionChange"-->
                <el-table v-loading="device.loading" :data="deviceList">
                        <el-table-column
                                label="序号"
                                width="70px">
                            <template slot-scope="scope">
                                {{scope.$index+1}}
                            </template>
                        </el-table-column>
                        <el-table-column label="id" align="center" prop="id" v-if="false"/>
                        <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                        <el-table-column label="名称" align="center" prop="deviceName"/>
                        <el-table-column label="所属部门" align="center" prop="deptId"/>
                        <el-table-column label="类别" align="center" prop="categoryName">
                        </el-table-column>
                        <el-table-column label="规则型号" align="center" prop="specification"/>
                        <el-table-column label="位置" align="center" prop="locationName"/>
                    </el-table>

                    <pagination
                            v-show="deviceQueryParams.total>0"
                            :total="deviceQueryParams.total"
                            :page.sync="deviceQueryParams.pageNum"
                            :limit.sync="deviceQueryParams.pageSize"
                            @pagination="getDeviceList"
                    />
            </div>
            <div class="info-btn-box">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
        </el-form>

    </div>

</template>
<script>
    import IconTitle from "@/components/ems/icon-title/index.vue";
    import ImageUpload from "@/components/ImageUpload/index.vue";
    import {fetchTree} from "@/api/admin/dept";
    import Treeselect from "@riophae/vue-treeselect";
    import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    import {mapGetters} from "vuex";
    import {fetchList, getObj} from "@/api/admin/user";
    import {
        planGetObj,
        planAddObj,
        planPutObj,
        planFetchList,
        planDelObj,
    } from '@/api/ems/repair/emsreprepairplan';
    import {standardFetchList } from "@/api/ems/repair/emsreprepairstandard";
    import {
        standardGetObj,
        deviceList
    } from "@/api/ems/repair/emsreprepairstandard";
    import {deviceListAll,devicePlanIdList} from '@/api/ems/maintenance/emsmaimaintenanceplan';

    import user from "../../../../store/modules/user";

    export default {
        name: "AddIndex",
        components: {
            IconTitle,
            Treeselect,
            ImageUpload,
        },
        props: {
            id: {
                type: Number,
            },
        },
        data() {
            return {
                rowIndex :null,
                defaultProps: {
                    children: "children",
                    label: "name",
                },
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条,
                },
                // 用户数组
                userList: [],
                //用户数据分页
                userQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                    deptId: null,
                },
                user: {
                    title: "",
                    open: false,
                    loading: false,
                    type: null,
                },
                standardList: [],
                standardQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                standard: {
                    title: "",
                    open: false,
                    loading: false,
                },
                deviceList: [],
                // device: {
                //     loading: false,
                // },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                list: [],
                treeData: [],
                loading: false,
                categoryList: [], //设备类别
                brandList: [], //设备品牌
                treeDeptData: [], //部门
                // 一天的毫秒数
                dayTime: 86400000,
                form: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    standardId: null,
                    standardName: null,
                    deviceId:[],
                    standardInspectCycle:null,
                },
                oldForm: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    standardId: null,
                    standardName: null,
                    deviceId:[],
                    standardInspectCycle:null,
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                queryParamsDeviceList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },


                device: {
                    title: "",
                    open: false,
                    loading: false,
                    deviceForm: {
                        id: null,
                        standardNum: null,
                        standardName: null,
                        deptId: null,
                        requirement: null,
                        remark: null,
                        deviceId: [],
                    }
                },

                dataRule: {
                    // inspectionType: [
                    //     {required: true, message: '保养类型不能为空', trigger: 'blur'}
                    // ],
                },
                //所有的设备数据
                deviceNewList: [],
                coverImgTem: [],
                imgArrayTem: [],
                rules: {
                    planName: [
                        {required: true, message: '请输入计划名称', trigger: 'blur'}
                    ],
                    deptId: [
                        {required: true, message: '请选择部门', trigger: 'blur'}
                    ],
                    liableUserId:[
                        {required: true, message: '请选择负责人', trigger: 'input'}
                    ],
                    beginTime:[
                        {required: true, message: '请选择开始执行时间', trigger: 'blur'}
                    ],
                    endTime:[
                        {required: true, message: '请选择结束执行时间', trigger: 'blur'}
                    ],
                    // inspectCycle:[
                    //     {required: true, message: '请选择检修周期', trigger: 'input'}
                    // ],
                    // effectiveTime:[
                    //     {required: true, message: '请输入任务有效期', trigger: 'blur'}
                    // ],
                    auditUserId:[
                        {required: true, message: '请选择验收人', trigger: 'input'}
                    ],
                },
                dialogVisible: false,

                deviceIdNewList:[],
            };
        },
        created() {
            this.getSelect();
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
                };
            },
        },
        mounted() {



            // this.reset();
            if (this.id > 0) {
                planGetObj(this.id).then((res) => {
                    this.oldForm = res.data.data
                    this.oldForm.enable = parseInt(this.oldForm.enable);
                    this.oldForm.inspectSettings = parseInt(this.oldForm.inspectSettings);
                    this.oldForm.inspectCycle=this.oldForm.standardInspectCycle;
                    getObj(this.oldForm.liableUserId).then(res =>{
                        this.oldForm.liableUserName=res.data.data.username
                        this.userList=[];
                        var o = {};
                        o.name=this.oldForm.liableUserName;
                        o.sex="未知"
                        o.zs="未知"
                        this.userList.push(o);
                        this.user.loading=false;
                    })
                    getObj(this.oldForm.auditUserId).then(res =>{
                        this.oldForm.auditUserName=res.data.data.username
                    })
                    if(this.oldForm.standardId!=null){
                        this.device.loading = true;
                        deviceList(Object.assign(
                            {
                                current: this.deviceQueryParams.pageNum,
                                size: this.deviceQueryParams.pageSize,
                            },
                            {id: this.oldForm.standardId}
                            )
                        ).then(response => {
                            this.deviceList = response.data.data.records;
                            this.deviceQueryParams.total = response.data.data.total;
                            this.device.loading = false;
                        });
                    }
                    this.form = this.oldForm;
                });
            }
        },
        methods: {
          yzInput(val) {
            // this.$nextTick(() => {
            //   // this.$refs[formName].resetFields();
            //   this.$refs['ruleForm'].validateOnRuleChange('inspectCycle')
            // });
          },



            getStandardyList(){
                standardFetchList(Object.assign(
                    {
                        current: this.standardQueryParams.pageNum,
                        size: this.standardQueryParams.pageSize,
                    },
                    )
                ).then(response => {
                    this.standardList = response.data.data.records;
                    this.standardQueryParams.total = response.data.data.total;
                    this.standard.loading = false;
                });
            },
            cancelDeviceList() {
                this.device.title = "";
                this.device.open = false;
            },
            deviceListDele(row) {
                for (var i = 0; i < this.deviceList.length; i++) {
                    if (this.deviceList[i].id == row.id) {
                        this.deviceList.splice(i, 1)
                        this.queryParams.total = this.queryParams.total - 1;
                    }
                }
                for (var i = 0; i < this.device.deviceForm.deviceId.length; i++) {
                    if (this.device.deviceForm.deviceId[i] == row.id) {
                        this.device.deviceForm.deviceId.splice(i, 1)
                    }
                }
            },
            packUp(row) {
                row.maiItemsListOnOFF= 1;
                row.css="display:none"
            },
            spread(row) {
                row.maiItemsListOnOFF= 2;
                row.css=""
            },

            liableUser() {
                this.user.title = "选择负责人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 1;
                this.getUserList()
            },
            liableStandard() {
                this.standard.title = "关联检修标准";
                this.standard.loading = true;
                this.standard.open = true;
                this.getStandardyList()
            },
            auditUser() {
                this.user.title = "选择审核人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 0;
                this.getUserList()
            },
            userRowClick(row, event, column) {
                // alert(row.userId)
                if (this.user.type == 1) {
                    this.form.liableUserId = row.userId;
                    this.form.liableUserName = row.username;
                } else if (this.user.type == 0) {
                    this.form.auditUserId = row.userId;
                    this.form.auditUserName = row.username;
                }
                this.user.open = false;
            },
            // 筛选节点
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            // 节点单击事件
            handleNodeClick(data) {
                this.userQueryParams.deptId = data.id;
                this.getUserList();
            },
            getUserList() {
                fetchList(Object.assign(
                    {
                        current: this.userQueryParams.pageNum,
                        size: this.userQueryParams.pageSize,
                    },
                    {deptId: this.userQueryParams.deptId}
                    )
                ).then(response => {
                    this.userList = response.data.data.records;
                    this.userQueryParams.total = response.data.data.total;
                    this.user.loading = false;
                });
            },
            getDeviceList() {
                this.device.loading = true;
                deviceList(Object.assign(
                    {
                        current: this.deviceQueryParams.pageNum,
                        size: this.deviceQueryParams.pageSize,
                    },
                    {id: this.form.standardId}
                    )
                ).then(response => {
                    this.deviceList = response.data.data.records;
                    this.deviceQueryParams.total = response.data.data.total;
                    this.device.loading = false;
                });
            },
            blurBeginTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('开始日期必须小于结束日期，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
                if (this.form.beginTime != null) {
                    const beginTime = this.form.beginTime;
                    const startTime = new Date(beginTime).getTime();
                    const time = new Date(new Date(new Date().toLocaleDateString()).getTime())
                    if (startTime < time) {
                        this.$message.error('开始日期必须大于当前时间，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurEndTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('结束日期必须大于开始日期，请重新选择！')
                        this.form.endTime = ''
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurInspectCycle() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    const time = endTime - startTime;
                    const timeSum = this.dayTime * this.form.inspectCycle
                    if (time < timeSum) {
                        this.$message.error('检修周期不能大于总时间，请重新选择！')
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                } else {
                    if (this.form.inspectCycle != null) {
                        this.$message.error('请先填写开始时间和结束时间！');
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                    }

                }
            },


            handleEnable() {
                if (this.form.enable == 1) {
                    this.$confirm('是否确认选择启用！一旦启用提交后无法修改', '警告', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(res => {
                        this.form.enable = 1;
                        return;
                    }).catch(err => {
                        this.form.enable = 0;
                        return;
                    })
                }
            },

            standardRowClick(row, event, column) {
                this.form.standardId = row.id;
                this.$set(this.form,"inspectCycle",row.inspectCycle)

                // this.form.inspectCycle=row.inspectCycle
                this.standard.open = false;
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    const time = endTime - startTime;
                    const timeSum = this.dayTime * this.form.inspectCycle
                    if (time < timeSum) {
                        this.$message.error('检修周期不能大于总时间，请重新选择！')
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                        this.form.standardId=null;
                        return
                    }
                    this.getDeviceList();
                } else {
                    if (this.form.inspectCycle != null) {
                        this.$message.error('请先填写开始时间和结束时间！');
                        this.form.inspectCycle = null;
                        this.form.standardId=null;
                        return
                    }
                }

            },

            submitForm(formName) {
                const startDate = this.form.beginTime
                const endDate = this.form.endTime
                const startTime = new Date(startDate).getTime()
                const endTime = new Date(endDate).getTime()
                const time = new Date(new Date(new Date().toLocaleDateString()).getTime())
                if (startTime < time) {
                    this.$message.error('开始日期必须大于当前时间，请重新选择！')
                    this.form.beginTime = null
                    this.form.inspectCycle = null;
                    this.form.standardId = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return;
                }
                if (startTime > endTime) {
                    this.$message.error('结束日期必须大于开始日期，请重新选择！')
                    this.form.endTime = ''
                    this.form.inspectCycle = null;
                    this.form.standardId = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return;
                }
                const timeSum = this.dayTime * this.form.inspectCycle
                const time1 = endTime - startTime;
                if (time1 < timeSum) {
                    this.$message.error('检修周期不能大于总时间，请重新选择！')
                    this.form.inspectCycle = null;
                    this.form.standardId = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return;
                }
                let data = JSON.parse(JSON.stringify(this.form));
                this.$refs[formName].validate((valid) => {
                    if(valid){
                        if (data.id) {
                            planPutObj(data).then((res) => {
                                // this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("修改成功");
                            });
                        } else {
                            planAddObj(data).then((res) => {
                                // this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("新增成功");
                            });
                        }
                    }
                });
            },

            reset() {
                this.form = {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    inspectCycle: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    standardId: null,
                    deviceId:[],
                };
                // //this.resetForm("form");
            },
            getSelect() {
                // remote("ins_inspect_items_type").then(response => {
                //     this.typeList = response.data.data;
                // });
                // fetchListTree("").then((res) => {
                //     this.categoryList = res.data.data ? res.data.data : [];
                // });
                // getBrandList().then((res) => {
                //     this.brandList = res.data.data;
                // });
                //部门
                fetchTree().then((response) => {
                    this.treeDeptData = response.data.data;
                });
            },
            normalizer(node) {
                if (node.children && !node.children.length) {
                    delete node.children;
                }
                return {
                    id: node.id,
                    label: node.name,
                    children: node.children,
                };
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss">
        .itemCss::-webkit-scrollbar {
            width : 8px;
            height: 7px;
            background-color: transparent;
        }
        .itemCss::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: hsla(220, 4%, 58%, .3);
        }
        .itemCss::-webkit-scrollbar-track {
            background-color: transparent;
        }
    /*#onAnd {*/
    /*    height: 90px;*/
    /*    overflow: auto;*/
    /*}*/

    .add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;

            }

        }
    }
</style>

<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
</style>
