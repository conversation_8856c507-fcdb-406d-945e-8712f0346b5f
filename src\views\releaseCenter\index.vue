<template>
  <div class="fixed-container releaseCenterPage ">
    <el-row>
      <el-col :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="产品类型" prop="productType">
            <el-select filterable v-model="queryParams.productType" placeholder="请选择产品类型" clearable>
              <el-option v-for="dict in productList" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd" >新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button class="el-button--danger" type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['system:user:remove']">删除</el-button>
          </el-col> -->
        </el-row>

        <el-table v-loading="loading" :data="tableList" height="calc(100vh - 300px)" @selection-change="handleSelectionChange" style="width: 100%;">
          <!-- <el-table-column type="selection" width="50" /> -->
          <el-table-column label="产品名称" prop="productName" :show-overflow-tooltip="true" />
          <el-table-column label="产品类型" prop="productType" :show-overflow-tooltip="true" >
            <template slot-scope="scope">
              {{
                productList.length > 0
                ? productList.filter(
                  (v) => v.dictValue === scope.row.productType
                )[0].dictLabel
                : ""
              }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime"  width="250" >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发布人" prop="createBy"  />
          <el-table-column label="是否上架" prop="groundingStatus" >
            <template slot-scope="scope">
              <el-tag :type="scope.row.groundingStatus === 'NO' ? 'danger' : 'success'
                ">
                {{ scope.row.groundingStatus === "NO" ? "未上架" : "已上架" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否热门" prop="hotStatus" >
            <template slot-scope="scope">
              <el-tag :type="scope.row.hotStatus !== 'NO' ? 'danger' : 'info'">
                {{ scope.row.hotStatus !== "NO" ? "是" : "否" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="examineStatus" >
            <template slot-scope="scope">
              <el-tag type="success" effect="dark" v-if="scope.row.examineStatus == 'PASS'">通过</el-tag>
              <el-tag type="danger" effect="dark" v-else-if="scope.row.examineStatus == 'REFUSE'">拒绝</el-tag>
              <el-tag effect="dark" v-else>待审核</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" class-name="small-padding fixed-width"  fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleLook(scope.row)">详情</el-button>
              <template v-if="scope.row.examineStatus=='REFUSE'||(scope.row.groundingStatus=='NO' && scope.row.examineStatus=='PASS')">
                 <el-button  size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
                <el-button  size="mini" type="text" class="button--danger"  @click="handleDelete(scope.row)">删除</el-button>
              </template>
              <el-dropdown v-if="scope.row.examineStatus=='PASS'" size="mini" @command="(command) => handleCommand(command, scope.row)">
                <span class="el-dropdown-link">
                  <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-if="scope.row.groundingStatus === 'NO'" command="grounding">上架</el-dropdown-item>
                  <el-dropdown-item v-else command="noGrounding">下架</el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.hotStatus === 'NO'" command="hot">设置热门</el-dropdown-item>
                  <el-dropdown-item v-else command="noHot">取消热门</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
      </el-col>
    </el-row>

    <el-dialog title="新增产品" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :rules="rules" :model="form" label-width="100px" size="mini">
        <el-form-item label="产品类型" prop="productType">
          <el-select filterable style="width: 90%" v-model="form.productType" placeholder="请选择产品类型" clearable>
            <el-option v-for="dict in productList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getProductList,
  getTableList,
  groundUpIng,
  groundHot,
  delProduct
} from "@/api/release/index.js";

export default {
  name: "releaseCenterPage",
  dicts: ["sys_normal_disable", "sys_user_sex"],
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      tableList: [],
      open: false,
      detailsOpen: false,
      form: {},
      rules: {
        productType: [
          { required: true, message: "产品类型不能为空", trigger: "change" },
        ],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      productList: [],
    };
  },

  created() {
    this.getList();
    this.getProductListFtn();
  },
  methods: {
    handleCommand(key, row) {
      switch (key) {
        case "grounding":
          this.handleGrounding(row, "YES");
          break;
        case "noGrounding":
          this.handleGrounding(row, "NO");
          break;
        case "hot":
          this.handleHotStatus(row, "YES");
          break;
        case "noHot":
          this.handleHotStatus(row, "NO");
          break;
        default:
          break;
      }
    },

    handleGrounding(row, groundingStatus) {
      const parmes = { id: row.id, groundingStatus };
      groundUpIng(parmes).then((res) => {
        this.$modal.msgSuccess(
          groundingStatus == "YES" ? "上架成功" : "下架成功"
        );
        this.getList();
      });
    },

    handleHotStatus(row, hotStatus) {
      const parmes = { id: row.id, hotStatus };
      groundHot(parmes).then((res) => {
        this.$modal.msgSuccess(
          hotStatus == "YES" ? "设置热门成功" : "取消热门成功"
        );
        this.getList();
      });
    },

    getProductListFtn() {
      getProductList(0).then((res) => {
        this.productList = res.data;
      });
    },

    getList() {
      this.loading = true;
      getTableList(this.queryParams)
        .then((response) => {
          this.tableList = response.rows;
          this.total = response.total;
        })
        .finally(() => (this.loading = false));
    },

    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        productType: undefined,
      };
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.reset();
      this.open = true;
    },
    handleUpdate(row) {
      const {productType,flowInstanceId} = row;
      this.$router.push({
            path: "/examine/releaseCenter/release",
            query: { type: productType,flowInstanceId ,pageType:'edit'},
          });
    },
    handleLook(row) {
      const {productType,flowInstanceId} = row;
      this.$router.push({
            path: "/examine/releaseCenter/release",
            query: { type: productType,flowInstanceId,pageType:'detail' },
          });
    },
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const { productType } = this.form;
          this.$router.push({
            path: "/examine/releaseCenter/release",
            query: { type: productType ,pageType:'add'},
          });
        }
      });
    },
    handleDelete(row) {
      const userIds = row.id || this.ids;
      this.$modal
        .confirm('是否确认？')
        .then(function () {
          return delProduct(userIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  },
};
</script>
