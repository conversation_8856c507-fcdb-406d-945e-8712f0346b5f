/*
 *    Copyright (c) 2018-2025, gewu All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: gewu
 */

import request from '@/utils/request'
import id from "element-ui/src/locale/lang/id";

export function taskFetchList(query) {
  return request({
    url: '/platform/emsinsinspecttask/page',
    method: 'get',
    params: query
  })
}

export function taskAddObj(obj) {
  return request({
    url: '/platform/emsinsinspecttask',
    method: 'post',
    data: obj
  })
}

export function taskGetObj(id) {
  return request({
    url: '/platform/emsinsinspecttask/' + id,
    method: 'get'
  })
}

export function taskDelObj(id) {
  return request({
    url: '/platform/emsinsinspecttask/' + id,
    method: 'delete'
  })
}

export function taskPutObj(obj) {
  return request({
    url: '/platform/emsinsinspecttask',
    method: 'put',
    data: obj
  })
}
export function deviceListTaskId(query) {
  return request({
    url: '/platform/emsinsinspecttask/deviceListTaskId/page',
    method: 'get',
    params: query
  })
}

export function taskGetCheckResults_TaskId(query) {
  return request({
    url: '/platform/emsinsinspecttask/taskGetCheckResults_TaskId/page',
    method: 'get',
    params: query
  })

}

//设备台账详情巡检记录
export function getInspectionRecord(query){
  return request({
    url: '/platform/emsinsinspecttask/inspectionRecordPage',
    method: 'get',
    params: query
  })
}

//任务状态
export function getSelectTaskStatus(){
  return request({
    url: '/admin/dict/data/type/task_status',
    method: 'get',
  })
}

export function getTallyTourByUserId(id) {
  return request({
    url: '/platform/emsinsinspecttask/getTallyTourByUserId/' + id,
    method: 'get'
  })

}
export function todayTasklist() {
  return request({
    url: '/platform/emsinsinspecttask/todayTasklist',
    method: 'get'
  })

}




