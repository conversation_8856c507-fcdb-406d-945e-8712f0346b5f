const DIC = {
    status: [{
        label: '未处理',
        value: '0'
    }, {
        label: '已报修',
        value: '1'
    }]
}

export const tableOption = {
    "border": false,
    "index": true,
    "indexLabel": "序号",
    "stripe": true,
    "menuAlign": "center",
    "align": "center",
    'selection': true,
    "searchMenuSpan": 6,
    "searchBtn": true,
    "addBtn": false,
    "editBtn": false,
    "refreshBtn": false,
    "crudMenu":false,
    "columnBtn": false,
    "searchShowBtn": false,
    "searchIcon":false,
    "searchShow": true,
    "column": [
        {
            "type": "input",
            "label": "主键",
            "prop": "id",
            "span": 12,
            "hide": true,
            addDisplay:false,
            editDisplay:false
        },	  {
            "type": "input",
            "label": "编号",
            "prop": "exceptionNum",
            "span": 12,
            addDisplay:false,
            editDisabled:true
        },	  {
            "type": "input",
            "label": "设备编号",
            "prop": "deviceNum",
            "span": 12,
            "search":true,
            addDisplay:false,
            editDisabled:true
        },	  {
            "type": "input",
            "label": "设备名称",
            "prop": "deviceName",
            "span": 12,
            "search":true,
            addDisplay:false,
            editDisabled:true
        },	  {
            "type": "select",
            "label": "设备",
            "prop": "deviceId",
            "span": 12,
            "hide": true,
            editDisabled:true,
            dicUrl: '/ems/emsdeviceaccount/list',
            props: {
                label: "deviceName",
                value: "id"
            },
            rules: [{
                required: true,
                message: '请选择设备',
                trigger: 'blur'
            }],
        },	  {
            "type": "input",
            "label": "异常项目",
            "prop": "falutName",
            "span": 12
        },	  {
            "type": "input",
            "label": "创建人",
            "prop": "createBy",
            "span": 12,
            addDisplay:false,
            editDisabled:true
        },	  {
            "type": "input",
            "label": "创建时间",
            "prop": "createTime",
            "span": 12,

            addDisplay:false,
            editDisabled:true
        },	  {
            row: true,
            minRows: 2,
            "type": "textarea",
            "label": "故障描述",
            "prop": "remark",
            "span": 12,
        },{
            "type": "select",
            "label": "状态",
            "prop": "status",
            "span": 12,
            "slot":true,
            dicData: DIC.status,
            rules: [{
                required: true,
                message: '请选择状态',
                trigger: 'blur'
            }]
        },	  {
            "type": "input",
            "label": "关联计划",
            "prop": "repairNum",
            "span": 12
        },	  	  /*{
            "type": "input",
            "label": "更新者",
            "prop": "updateBy",
            "span": 12
        },	  {
            "type": "input",
            "label": "更新时间",
            "prop": "updateTime",
            "span": 12
        },	  {
            "type": "input",
            "label": "删除标志(0正常 1删除)",
            "prop": "delFlag",
            "span": 12
        },	  {
            "type": "input",
            "label": "租户id",
            "prop": "tenantId",
            "span": 12
        }*/  ]
}
