<template>
  <div class="details_view">
    <el-card style="border-radius: 10px">
      <div class="details_data">
        <IconTitle class="selfTitle" title="经验详情" imgUrl="yunwei"/>
        <h3>{{ information.faultName }}</h3>
        <table class="table_details" border="1">
          <tr>
            <th>故障名称</th>
            <td colspan="4">{{ information.faultName }}</td>

          </tr>
          <tr>
            <th>编号</th>
            <td>{{ information.faultNumber }}</td>
            <th>创建时间</th>
            <td>{{ information.createTime }}</td>
          </tr>
          <tr>
            <th>故障类型</th>
            <td colspan="3">{{ information.falutCategoriesName }}</td>
          </tr>
          <tr>
            <th>故障现象</th>
            <td colspan="3">{{ information.faultPhenomenon }}</td>
          </tr>
          <tr>
            <th>故障部位</th>
            <td colspan="3">{{ information.position }}</td>
          </tr>
          <tr>
            <th>解决方法</th>
            <td colspan="3">{{ information.solution }}</td>
          </tr>
          <tr>
            <th>预防对策</th>
            <td colspan="3">{{ information.prevention }}</td>
          </tr>
        </table>
      </div>
    </el-card>

    <el-card style="border-radius: 10px;margin-top: 15px">
      <div class="details_attachment">
        <IconTitle class="selfTitle" title="相关附件" imgUrl="yunwei"/>
        <el-table
            :data="fileArray"
            border
            :header-row-style="{color: '#343141'}"
            style="width: 100%; margin-top: 10px">
          <el-table-column
              prop="original"
              label="文件名称"
              width="240">
          </el-table-column>
          <el-table-column
              prop="type"
              label="文件类型"
              width="180">
          </el-table-column>
          <el-table-column
              prop="fileSize"
              label="文件大小">
          </el-table-column>
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="查看次数">-->
          <!--          </el-table-column>-->
          <!--          <el-table-column-->
          <!--              prop="address"-->
          <!--              label="下载次数">-->
          <!--          </el-table-column>-->
          <el-table-column
              prop="createTime"
              label="上传时间">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-search"
                  @click="selectFile(scope.row)"
              >查看
              </el-button>
              <el-button
                  type="text"
                  size="small"
                  icon="el-icon-download"
                  @click="download(scope.row, scope.index)"
              >下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="预览" :visible.sync="viewVisible" width="50%" height="30%">
          <pdf
              :src="url"
              :page="pdfPage"
              @num-pages="pdfPageCount = $event"
              @page-loaded="pdfPage = $event"
          ></pdf>
          <!-- 上下翻页 -->
          <button @click="previousPage" style="float: left;">上一页</button>
          <button @click="nextPage" style="float: right">下一页</button>
        </el-dialog>

        <el-dialog title="预览" :visible.sync="viewVisibleImg" width="50%" height="30%">
          <img :src="url" style="width: 100%; height: 100%">
        </el-dialog>

      </div>
    </el-card>

    <el-card style="border-radius: 10px;margin-top: 15px">
      <div class="details_attachment">
        <IconTitle class="selfTitle" title="适用设备" imgUrl="yunwei"/>
        <el-table
            :data="deviceAccountArray"
            border
            :header-row-style="{color: '#343141'}"
            style="width: 100%; margin-top: 10px">
          <el-table-column
              type="index"
              label="序号"
              style="text-align: center"
              width="50">
          </el-table-column>
          <el-table-column
              prop="deviceNum"
              label="设备编号"
              width="180">
          </el-table-column>
          <el-table-column
              prop="deviceName"
              label="设备名称"
              width="180">
          </el-table-column>
          <el-table-column
              prop="brandName"
              label="品牌">
          </el-table-column>
          <el-table-column
              prop="category"
              label="类别">
          </el-table-column>
          <el-table-column
              prop="specification"
              label="规格型号">
          </el-table-column>
          <el-table-column
              prop="location"
              label="位置">
          </el-table-column>
          <el-table-column
              prop="remark"
              label="备注">
          </el-table-column>
        </el-table>
        <pagination
            style="float: right"
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getListData"
        />
      </div>
    </el-card>

  </div>
</template>

<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import {getObj} from "@/api/ems/repository/emsfaultlibrary"
import {changeByte} from "@/util/changeByte"
import pdf from 'vue-pdf'

export default {
  name: "faultlibraryDetails",
  data() {
    return {
      // 下载文件
      searchForm: {
        fileName: ''
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 资料信息数据
      information: [],
      // 相关附件
      fileArray: [],
      // 适用设备
      deviceAccountArray: [],
      // 条数
      total: '',

      viewVisible: false,
      viewVisibleImg: false,
      pdfPage: 1,
      pdfPageCount: 1,
      url: ''
    }
  },
  components: {
    IconTitle,
    pdf
  },
  mounted() {
    this.getListData();
  },
  methods: {
    // 获取基本数据
    getListData() {
      let id = this.$route.query.id;
      getObj(id).then(res => {
        this.information = res.data.data;
        this.fileArray = res.data.data.fileArray;
        this.deviceAccountArray = res.data.data.deviceAccountArray.records;
        this.total = res.data.data.deviceAccountArray.total;
        console.log(this.deviceAccountArray);
        for (let i = 0; i <= this.fileArray.length; i++) {
          this.fileArray[i].fileSize = changeByte(this.fileArray[i].fileSize);
        }
      })
    },
    // 查看功能
    selectFile(row) {
      if (row.type == 'pdf') {
        this.viewVisible = true;
        this.url = row.url;
      }
      if (row.type == 'jpg' || row.type == 'jpeg' || row.type == 'png') {
        this.viewVisibleImg = true;
        this.url = row.url;
      }
    },
    // 上一页
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pdfPageCount;
      this.pdfPage = p;
    },
    // 下一页
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pdfPageCount ? p + 1 : 1;
      this.pdfPage = p;
    },

    // 下载功能
    download: function (row, index) {
      this.downBlobFile(
          "/admin/sys-file/" + row.bucketName + "/" + row.fileName,
          this.searchForm,
          row.fileName
      );
    },
  }
}
</script>

<style scoped lang="less">
.details_view {
  height: 1000px;
  .details_data {
    .table_details {
      border: 1px solid #e8eef4;
      font-size: 12px;
      width: 1241px;
      height: 270px;

      td {
        padding-left: 10px;
      }
    }
  }

  .details_attachment {
    .selfTitle {
      margin-bottom: 10px;
    }
  }
}
</style>
