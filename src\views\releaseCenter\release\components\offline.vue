<template>
  <div class="diagnosis-offline-container">
    <el-table
      border
      :data="tableData"
      :span-method="objectSpanMethod"
      style="width: 100%"
    >
      <el-table-column prop="wordMainName" align="center" label="类型"/>
      <el-table-column prop="wordName" align="center" label="节点"/>
      <el-table-column prop="diagnosticLevel" align="center" label="诊断等级"/>
      <!-- <el-table-column prop="fileName" align="center" label="文件名称">
      </el-table-column> -->
      <el-table-column prop="examineStatus" align="center" label="审核状态">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.examineStatus"
            :type="statusList[scope.row.examineStatus].type"
            >{{ statusList[scope.row.examineStatus].label }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column prop="file" align="center" label="文件查看">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.file"
            @click="handlePreview(scope.row)"
            icon="el-icon-view"
            type="text"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="picturePath" align="center" label="图片预览">
        <template slot-scope="scope">
          <ImagePreview
            v-show="scope.row.picturePath"
            :src="ensureFullUrl(scope.row.picturePath)"
            width="50px"
            height="50px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" align="center" label="备注"/>
      <el-table-column
        v-if="offlineType !== 'details'"
        label="操作"
        align="center"
        width="180"
        fixed="right"
      >
        <template slot-scope="scope">
          <div v-if="offlineType == 'examine'">
            <el-button
              v-if="scope.row.examineStatus === 'EXAMINING'"
              @click="fileUpload(scope.row, '审核')"
              type="text"
              >审核</el-button
            >
          </div>
          <div v-else>
            <el-button
              v-if="!scope.row.file && !scope.row.diagnosisLevelList"
              @click="fileUpload(scope.row)"
              type="text"
              >上传</el-button
            >
            <el-button
              v-if="scope.row.file && !scope.row.diagnosisLevelList"
              @click="fileUpload(scope.row)"
              type="text"
              >覆盖上传</el-button
            >
            <el-button
              style="color: red"
              v-if="scope.row.file"
              @click="removeFileBtn(scope.row)"
              type="text"
              >移除</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.diagnosisLevelList"
              @click="fileUpload(scope.row, '评定')"
              >等级评定</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.file"
              @click="fileDetails(scope.row, '详情')"
              >查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="openTitle"
      :visible.sync="open"
      :width="openWidth"
      append-to-body
    >
      <el-form
        :disabled="detailFlag"
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        v-loading="loadingForm"
      >
        <template v-if="openTitle === '上传' || openTitle === '详情'">
          <el-form-item label="文件" prop="file">
            <el-upload
              :before-upload="(file) => beforeFileUpload(file, '')"
              :on-remove="(file) => removeFile(file, 'file')"
              :http-request="(event) => uploadFileApiFtn(event, 'file')"
              :file-list="form.file"
              :limit="1"
              :accept="typeArr"
              action="#"
            >
              <el-button slot="trigger" type="primary" size="small"
                ><i
                  class="el-icon-upload el-icon--right"
                  style="margin-right: 5px"
                ></i
                >选择文件</el-button
              >
              <div
                slot="tip"
                class="el-upload__tip"
                style="color: silver; font-size: 12px"
              >
                支持扩展名：{{ this.typeArr }}
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="图片" prop="imgUrl">
            <UploadImage
              :disabled="detailFlag"
              :fileList="form.imgUrl"
              @addUpload="addUpload"
              @removeUpload="removeUpload"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
        </template>
        <template v-if="openTitle === '审核' || openTitle === '详情'">
          <el-form-item label="审核状态" prop="examineStatus">
            <el-radio-group v-model="form.examineStatus">
              <el-radio label="PASS">通过</el-radio>
              <el-radio label="REFUSE">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="form.examineStatus === 'REFUSE'"
            label="拒绝原因"
            prop="refuseReason"
          >
            <el-input
              v-model="form.refuseReason"
              type="textarea"
              placeholder="请输入拒绝原因"
            ></el-input>
          </el-form-item>
        </template>
        <template v-if="openTitle === '评定'">
          <el-form-item label="诊断等级" prop="diagnosticLevel">
            <el-select
              v-model="form.diagnosticLevel"
              placeholder="请选择诊断等级"
            >
              <el-option
                v-for="item in diagnosticLevelList"
                :key="item"
                :label="item.label"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" v-if="openTitle !== '详情'" class="dialog-footer">
        <el-button @click="open = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="loadingForm"
          @click="submitForm('form')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import UploadImage from "@/components/UploadImage";
import {
  uploadApi,
  updateNodeRecord,
  removeFileByRecordId,
} from "@/api/release/index.js";

export default {
  name: "offline",
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    offlineType: {
      type: String,
    },
  },
  watch: {
    tableData(val) {
      this.tableArr({ keys: ["wordMainName"] });
    },
  },
  components: {
    UploadImage,
  },
  created() {
    this.tableArr({ keys: ["wordMainName"] });
  },
  data() {
    return {
      detailFlag: false,
      open: false,
      openTitle: "上传",
      openWidth: "500px",
      typeArr: [],
      loadingForm: false,
      form: {},
      rules: {
        file: [{ required: true, message: "请上传文件", trigger: "blur" }],
        diagnosticLevel: [
          { required: true, message: "请选择诊断等级", trigger: "blur" },
        ],
        examineStatus: [
          { required: true, message: "请选择审核状态", trigger: "blur" },
        ],
      },
      statusList: {
        PASS: {
          label: "已通过",
          type: "success",
        },
        REFUSE: {
          label: "已拒绝",
          type: "danger",
        },
        EXAMINING: {
          label: "审核中",
          type: "warning",
        },
      },
      spanArr: [],
      diagnosticLevelList: [],
    };
  },
  methods: {
    fileUpload(row, type) {
      this.openTitle = type ? type : "上传";
      this.detailFlag = false;
      this.form = {
        file: [],
        imgUrl: [],
        remark: "",
        diagnosticLevel: null,
        examineStatus: null,
        refuseReason: null,
      };
      if (type === "评定") {
        this.diagnosticLevelList = row.diagnosisLevelList;
      }
      this.typeArr = row.fileSuffix;
      this.form.id = row.id;
      this.open = true;
    },
    fileDetails(row, type) {
      this.openTitle = type;
      this.detailFlag = true;
      this.typeArr = row.fileSuffix;
      this.form = {
        ...row,
        file: [
          {
            name: row.fileName,
            url: this.ensureFullUrl(row.file),
            uid: this.generateUniqueId(),
          },
        ],
        imgUrl: row.picturePath ? [row.picturePath] : [],
      };
      this.open = true;
    },

    generateUniqueId() {
      // 这里可以使用例如UUID库或其他方式生成唯一ID
      return Math.random().toString(36).substring(7);
    },
    beforeFileUpload(file) {
      var FileExt = file.name.replace(/.+\./, "");
      if (this.isValidFileType(FileExt)) {
        this.$message({
          type: "warning",
          message: "请上传正确的文件！",
        });
        return false;
      }
    },
    isValidFileType(filename) {
      const validExtensions = this.typeArr.split(",");
      const fileExtension = filename
        .slice(filename.lastIndexOf("."))
        .toLowerCase();
      return validExtensions.includes(fileExtension);
    },
    uploadFileApiFtn(event, key) {
      let fileData = new FormData();
      fileData.append("file", event.file);
      uploadApi(fileData).then((res) => {
        const { data } = res;
        this.form[key].push({
          name: data.name,
          url: data.url,
          uid: event.file.uid,
        });
      });
    },
    removeFile(file, key, index) {
      if (key == "responses") {
        const { responses } = this.form;
        this.form = {
          ...this.form,
          responses: this.setResponses(index),
        };
      } else {
        this.form = { ...this.restForm, form: "" };
      }
    },
    setResponses(index, data = "") {
      const { responses } = this.restForm;
      return responses.map((t, i) => {
        return i == index
          ? {
              ...t,
              file: data ? data.url : null,
              fileName: data ? data.name : null,
            }
          : t;
      });
    },
    //新增图片事件
    addUpload(res) {
      this.form.imgUrl = [...this.form.imgUrl, res.url];
    },
    //删除图片事件
    removeUpload(file) {
      const index = this.form.imgUrl.indexOf(file);
      if (index > -1) {
        this.form.imgUrl.splice(index, 1);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const form = {
            ...this.form,
            file: this.form.file[0]?.url || null,
            fileName: this.form.file[0]?.name || null,
            picturePath: this.form.imgUrl?.join(",") || null,
          };
          this.loadingForm = true;
          updateNodeRecord(form).then((res) => {
            this.loadingForm = false;
            this.open = false;
            this.$emit("refresh");
          });
        }
      });
    },
    removeFileBtn(row) {
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return removeFileByRecordId(row.id);
        })
        .then(() => {
          this.$emit("refresh");
          this.$modal.msgSuccess("删除成功");
        });
    },
    handlePreview(row) {
      window.open(this.ensureFullUrl(row.file));
    },

    tableArr(config) {
      this.spanArr = [];
      let pos = 0;
      const { keys } = config; // 从配置参数中获取 keys 数组

      this.tableData.forEach((item, index) => {
        if (index === 0) {
          this.spanArr.push(1);
          pos = 0;
        } else {
          // 判断当前行与上一行的指定属性是否都相同
          const isSame = keys.every(
            (key) => item[key] === this.tableData[index - 1][key]
          );
          if (isSame) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = index;
          }
        }
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.diagnosis-offline-container {
  ::v-deep .el-table__row {

  }
}
</style>
