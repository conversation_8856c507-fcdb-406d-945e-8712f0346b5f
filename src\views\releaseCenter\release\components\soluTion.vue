<template>
    <div class="soluTionPage">
        <el-form :disabled="detailFlag" ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item label="解决方案名称" prop="solutionTitle">
                <el-input v-model="form.solutionTitle" placeholder="请输入解决方案名称" style="width: 100%" />
            </el-form-item>

            <div style="display: flex;">
                <el-form-item class="uploadItem" label="解决方案图片" prop="solutionTitleImage">
                    <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                        :http-request="((event) => uploadApiFtn(event, 'solutionTitleImage'))" action="#"
                        :show-file-list="false">
                        <img v-if="form.solutionTitleImage" :src="ensureFullUrl(form.solutionTitleImage)" class="appImage" />
                        <i v-else class="el-icon-plus avatar-uploader-icon appIcon"></i>
                        <div slot="tip" style="font-size: 12px; color: #9ea5b6">
                            支持扩展名：.jpg .img .png
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="简介" prop="solutionContent" style="flex: 1;">
                    <el-input v-model="form.solutionContent" placeholder="请输入简介" style="width: 100%" type="textarea"
                        :rows="8" />
                </el-form-item>
            </div>

            <el-form-item label="模块分类" prop="solutionSource">
                <el-select v-model="form.solutionSource" multiple placeholder="请选择模块分类" style="width: 100%" clearable>
                    <el-option v-for="dict in dict.type.solution_classify" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="场景名称" prop="applyScene">
                <el-input v-model="form.applyScene" placeholder="请输入场景名称" style="width: 100%" />
            </el-form-item>
            <el-form-item label="场景类别" prop="solutionCategory">
                <el-select v-model="form.solutionCategory" multiple placeholder="请选择场景类别" style="width: 100%" clearable>
                    <el-option v-for="dict in dict.type.solution_model" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="行业" prop="industrialSubstitutionIdJson">
              <el-cascader v-model="form.industrialSubstitutionIdJson" :options="industrys" :props="{
                        children: 'children',
                        label: 'vueName',
                        value: 'id',
                        multiple: true,
                    }" placeholder="请选择行业" style="width:100%" clearable>
              </el-cascader>
            </el-form-item>

<!--            <el-form-item label="所属行业" prop="solutionIndustry">
                <el-cascader v-model="form.solutionIndustry" :options="industryTypes" :props="{
                    children: 'childrenList',
                    label: 'name',
                    value: 'industryCode',
                }" placeholder="请选择所属行业" style="width:100%" clearable>
                </el-cascader>
            </el-form-item>-->
            <el-form-item label="方案描述" prop="schemeDescribe">
                <el-input v-model="form.schemeDescribe" placeholder="请输入方案描述" style="width: 100%" type="textarea"
                    :rows="5" />
            </el-form-item>

            <div style="display: flex;">
                <el-form-item class="uploadItem" label="业务结构图片" prop="businessImage">
                    <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                        :http-request="((event) => uploadApiFtn(event, 'businessImage'))" action="#"
                        :show-file-list="false">
                        <img v-if="form.businessImage" :src="ensureFullUrl(form.businessImage)" class="miniImage" />
                        <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
                        <div slot="tip" style="font-size: 12px; color: #9ea5b6">
                            支持扩展名：.jpg .img .png
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="业务结构描述" prop="businessDescribe" style="flex: 1;">
                    <el-input v-model="form.businessDescribe" placeholder="请输入业务结构描述" style="width: 100%" type="textarea"
                        :rows="6" />
                </el-form-item>
            </div>
            <div style="display: flex;">
                <el-form-item class="uploadItem" label="业务场景图片" prop="sceneImage">
                    <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                        :http-request="((event) => uploadApiFtn(event, 'sceneImage'))" action="#" :show-file-list="false">
                        <img v-if="form.sceneImage" :src="ensureFullUrl(form.sceneImage)" class="miniImage" />
                        <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
                        <div slot="tip" style="font-size: 12px; color: #9ea5b6">
                            支持扩展名：.jpg .img .png
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="业务场景描述" prop="sceneDescribe" style="flex: 1;">
                    <el-input v-model="form.sceneDescribe" placeholder="请输入业务场景描述" style="width: 100%" type="textarea"
                        :rows="6" />
                </el-form-item>
            </div>

            <div class="tagsTilte" style="margin-top: 30px;margin-bottom: 15px;">应用案例</div>
            <div class="dynaCard" style="padding-top: 24px;">
                <el-form-item label="应用案例名称" prop="caseTitle">
                    <el-input v-model="form.caseTitle" placeholder="请输入应用案例名称" style="width: 100%" />
                </el-form-item>
                <div style="display: flex;">
                    <el-form-item class="uploadItem" label="应用案例图片" prop="caseImage">
                        <el-upload class="avatar-uploader" accept="image/*" :before-upload="beforeUploadImage"
                            :http-request="((event) => uploadApiFtn(event, 'caseImage'))" action="#"
                            :show-file-list="false">
                            <img v-if="form.caseImage" :src="ensureFullUrl(form.caseImage)" class="miniImage" />
                            <i v-else class="el-icon-plus avatar-uploader-icon miniIcon"></i>
                            <div slot="tip" style="font-size: 12px; color: #9ea5b6">
                                支持扩展名：.jpg .img .png
                            </div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="应用案例描述" prop="caseDescribe" style="flex: 1;">
                        <el-input v-model="form.caseDescribe" placeholder="请输入应用案例描述" style="width: 100%" type="textarea"
                            :rows="6" />
                    </el-form-item>
                </div>
            </div>

            <el-form-item label="联系电话" prop="servicePhone">
                <el-input v-model="form.servicePhone" placeholder="请输入联系电话" style="width: 100%" />
            </el-form-item>

        </el-form>
    </div>
</template>

<script>
import { getInfo, uploadApi } from "@/api/release/index.js";
import {getSysIndustry, getSysIndustryType} from "@/api/release/indApp";


export default {
    name: "soluTionPage",
    dicts: [
        "areas_of_expertise", 'school_tags',
        'solution_classify',
        'solution_model'
    ],
    props: {},
    data() {
        var validateURL = (rule, value, callback) => {
            const urlRegex = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
            const reg = /^(\d{3,4}-)?\d{7,8}$/;
            if (!urlRegex.test(value) && !reg.test(value)) {
                callback(new Error('请输入正确的电话号码'));
            } else {
                callback();
            }
        };
        return {
            form: {
                industrialSubstitutionIdJson: undefined,
                solutionSource:[],
                solutionCategory:[]
            },
            rules: {
                solutionTitle: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                solutionTitleImage: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                industrialSubstitutionIdJson: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                solutionSource: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                solutionCategory: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                solutionIndustry: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                solutionContent: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                schemeDescribe: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                businessImage: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                businessDescribe: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                sceneImage: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                sceneDescribe: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                caseTitle: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                caseImage: [{ required: true, message: "必填项不能为空", trigger: "change" }],
                caseDescribe: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                applyScene: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
                servicePhone: [
                    { required: true, message: "必填项不能为空", trigger: "blur" },
                    { validator: validateURL, trigger: "blur" }
                ],
            },
            detailFlag:
                this.$route.query.pageType == "detail" ||
                this.$route.query.pageType == "check" ||
                false,
            files: [],
            industrys: [],
            industryTypes: [],
        };
    },

    created() {
        this.getSysIndustryFtn(); //行业
        const { flowInstanceId } = this.$route.query;
        flowInstanceId && this.getFormDataFtn(flowInstanceId);
    },

    methods: {

        getSysIndustryFtn() {
          getSysIndustry().then(res => {
            this.industrys = res.data;
          });
        },
        /*getSysIndustryTypeFtn() {
            getSysIndustryType().then(res => {
                this.industryTypes = res.data;
            });
        },*/

        getFormDataFtn(flowInstanceId) {
            getInfo({ flowInstanceId }).then(res => {
                const { params } = res.data;
                const { province, city, district } = params;
                this.form = {
                    ...params,
                  industrialSubstitutionIdJson: JSON.parse(params.industrialSubstitutionIdJson),
                  citys: [province || undefined, city || undefined, district || undefined].filter(v => v),
                };
            })
        },

        uploadApiFtn(event, key) {
            let fileData = new FormData();
            fileData.append("file", event.file);
            uploadApi(fileData).then((res) => {
                this.form = { ...this.form, [key]: res.data.url };
                this.$refs.form.clearValidate([key]);
            });
        },

        beforeUploadImage(file) {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            if (["jpg", "img", "png"].indexOf(fileType) == -1) {
                this.$message.error("请上传后缀为.jpg .img .png格式的图片文件");
                return false;
            }
        },

        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    if (this.form.industrialSubstitutionIdJson) {
                      const uniqueElements = [...new Set(this.form.industrialSubstitutionIdJson.map(subArray => subArray[0]))];
                      this.form.industrialSubstitutionIds = uniqueElements.join(',');
                    }
                    const params = {
                        ...this.form,
                    }
                    this.$emit("submitFtn", params, (res) => {
                        // 相应结束后的其他逻辑
                    });
                }
            });
        },
    },
};
</script>

<style lang="scss">
.soluTionPage {
    width: 55%;

    .uploadItem {
        .el-form-item__content {
            line-height: normal;
        }
    }

    .dynaCard {
        /* width: 100%; */
        background: #f6f8fc;
        border-radius: 5px;
        padding: 12px 24px;
        margin-bottom: 20px;

        .dynaCardHead {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            margin-top: 12px;
            margin-left: 22px;

            .hintTitleSamil {
                font-weight: bold;
                font-size: 15px;
                color: #0d162a;

                &:before {
                    content: "";
                    display: inline-block;
                    width: 4px;
                    height: 10px;
                    background: #6fc342;
                    border-radius: 0px;
                    margin-right: 6px;
                }
            }
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        text-align: center;
    }

    .avatar-uploader {
        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
    }

    .appIcon {
        width: 160px;
        height: 160px;
        line-height: 160px;
    }

    .appImage {
        width: 160px;
        height: 160px;
    }

    .miniIcon {
        width: 120px;
        height: 120px;
        line-height: 120px;
    }

    .miniImage {
        width: 120px;
        height: 120px;
    }
}
</style>
