<template>
    <div class="app-container tagManageList">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"
            @submit.native.prevent>
            <el-form-item label="网关编号" prop="gatewaySign">
                <el-input v-model="queryParams.gatewaySign" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>

            <el-form-item label="车间选择" prop="uniqueFlag">
                <el-cascader style="width: 250px;" placeholder="车间选择" v-model="queryParams.uniqueFlag"
                    @change="(val) => { handleBaseValue(val, 'queryParams') }" clearable
                    :props="{ ...defaultPickTableProps }" :options="workArrs">
                    <template slot-scope="{ node, data }">
                        <span>{{ data.tname }}</span>
                    </template>
                </el-cascader>
            </el-form-item>
            <el-form-item label="标签名称" prop="labelId">
                <el-select v-model="queryParams.labelId" placeholder="请选择" clearable>
                    <el-option class="tabSelect" v-for="val in manageData" :key="val.id" :value="val.id"
                        :label="val.labelName">
                        <el-tag :style="{ backgroundColor: val.labelColour, color: val.labelNameColour }" effect="dark">{{
                            val.labelName }}</el-tag>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:post:add']">新增</el-button>
            </el-col>
        </el-row>

        <el-table class="tableDemo" v-loading="loading" :data="postList" style="width: 100%" :show-header="false">
            <el-table-column prop="name" label="密级" header-align="center" show-overflow-tooltip width="300">
                <template slot-scope="scope">
                    <div class="gateway">
                        <img src="@/assets/images/login-background.jpg" style="width: 67px;height: 67px;" />
                        <div class="gateway-text">
                            <div class="gateway-text-title">{{ scope.row.gatewayAlias }}</div>
                            <div class="gateway-text-remark">{{ '网关编号：' + scope.row.gatewaySign }}</div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="workshopName" label="标签" width="200" align="left">
                <template slot-scope="scope">
                    <el-tag v-for="val in scope.row.labelInfoDTOS" :key="val.id"
                        :style="{ backgroundColor: val.labelColour, color: val.labelNameColour }" effect="dark">{{
                            val.labelName }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="workshopName" label="车间" width="200" align="left"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="databaseType" label="密级名称" width="150" align="left">
                <template slot-scope="scope">
                    <div class="facility"><span>设备数：</span><span>{{ scope.row.deviceNum ? scope.row.deviceNum : 0 }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="databaseIp" label="密级描述" header-align="center" width="150">
                <template slot-scope="scope">
                    <el-button type="primary" plain @click="relevanc(scope.row)">关联设备</el-button>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" header-align="center" width="300">
                <template slot-scope="scope">
                    <el-button type="text" @click="edit(scope.row.id)">修改网关</el-button>
                    <el-button type="text" @click="setRestartFuc(scope.row.id)">重启网关</el-button>
                    <el-dropdown @command="(command) => handleCommand(command, scope.row)">
                        <el-button type="text" style="margin-left: 12px;">更多操作<i
                                class="el-icon-arrow-down el-icon--right"></i></el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="delete" style="color: red;">删除网关</el-dropdown-item>
                            <el-dropdown-item command="drive">安装驱动</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.limit"
            @pagination="getList" />

        <!-- 添加或修改岗位对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="网关别名" prop="gatewayAlias">
                    <el-input v-model="form.gatewayAlias" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="网关标识" prop="gatewaySign">
                    <el-input v-model="form.gatewaySign" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="IP主机名" prop="gatewayIp">
                    <el-input v-model="form.gatewayIp" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="端口" prop="gatewayPort">
                    <el-input v-model="form.gatewayPort" placeholder="请输入" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="标签" prop="labelId">
                    <el-select multiple v-model="form.labelId" placeholder="请选择" clearable style="width: 90%;">
                        <el-option v-for="val in manageData" :key="val.id" :value="val.id" :label="val.labelName">
                            <el-tag :style="{ backgroundColor: val.labelColour, color: val.labelNameColour }"
                                effect="dark">{{ val.labelName }}</el-tag>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="用户名" prop="gatewayUsername">
                    <el-input v-model="form.gatewayUsername" placeholder="请输入用户名" style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="gatewayPwd">
                    <el-input type="password" show-password  auto-complete="new-password" v-model="form.gatewayPwd" placeholder="请输入密码"
                        style="width: 90%;"></el-input>
                </el-form-item>
                <el-form-item label="车间" prop="uniqueFlag">
                    <el-cascader @change="(val) => { handleBaseValue(val, 'form') }" style="width: 90%;" placeholder="请选择"
                        v-model="form.uniqueFlag" clearable :props="{ ...defaultPickTableProps }" :options="workArrs">
                        <template #default="{ node, data }">
                            <span>{{ data.tname }}</span>
                        </template>
                    </el-cascader>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm" :loading="smiBtnFlag">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>

        <!-- <div class="drawerClass" style="height:100%">
                <el-drawer :with-header="false" ref="detailDrawer" :modal="false" :visible.sync="detailDrawerVisible"  size="100%" :show-close="false" :destroy-on-close="true">
                    <template #header>
                        <div class="drawer-header">
                            <el-button link type="primary">
                                返回
                            </el-button>
                            <span class="title">{{ '安装驱动' }}</span>
                        </div>
                    </template>
                </el-drawer>
            </div>  -->

    </div>
</template>
  
<script>
import { getFlagTree, gatewayList, getManageDict, addManage, editManage, getManageData, deleteManage,restart } from "@/api/gatewayManage/list/index.js";
import { extractTree } from '@/utils/index'
import { encrypt, decrypt } from "@/utils/jsencrypt";


export default {
    name: "tagManageList",
    dicts: ['sys_normal_disable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            showSearch: true,
            total: 0,
            postList: [],
            title: "",
            open: false,
            queryParams: {
                page: 1, //page
                limit: 10, //limit
                gatewaySign: undefined,
                labelId: undefined,
                uniqueFlag: undefined
            },
            form: {
                gatewayAlias: '',
                gatewaySign: '',
                gatewayIp: '',
                gatewayPort: '',
                gatewayUsername: '',
                gatewayPwd: '',
                uniqueFlag: undefined,
                labelId: [],
            },
            rules: {
                gatewayAlias: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                gatewaySign: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                gatewayIp: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                gatewayPort: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                labelId: [
                    { required: false, message: "请选择", trigger: "change" }
                ],
                gatewayUsername: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                gatewayPwd: [
                    { required: true, message: "请输入", trigger: "blur" }
                ],
                uniqueFlag: [
                    { required: true, message: "请选择", trigger: "change" }
                ],
            },
            workArrs: [],
            workArrsFlat: [],
            manageData: [],
            defaultPickTableProps: {
                value: 'uniqueFlag',
                children: 'childrenList',
                label: 'tname'
            },
            textDdata: [],
            smiBtnFlag: false,
            detailDrawerVisible:false,
 
        };
    },
    created() {
        this.getList();
        getFlagTree().then(res => {
            this.workArrs = res.data;
            this.workArrsFlat = extractTree(res.data, 'childrenList', ['uniqueFlag', 'id']);
            // this.textDdata = res.data.map((org) => this.mapTree(org));
        })
        getManageDict().then(res => {
            this.manageData = res.data;
        })
    },
    methods: {

        relevanc(rows){
            this.$router.push({ path: `/gatewayManage/facility`, query: { gatewayId:rows.id,uniqueFlag:rows.uniqueFlag} })
        },

        mapTree(org) {
            const haveChildren = Array.isArray(org.childrenList) && org.childrenList.length > 0;
            return {
                id: org.id - 0,
                tname: org.tname,
                childrenList: haveChildren ? org.childrenList.map(i => this.mapTree(i)) : undefined,
            }
        },

        handleBaseValue(val, key) {
            if (val) {
                const lastId = val[val.length - 1];
                this[key] = {
                    ...this[key],
                    uniqueFlag: lastId
                }
            }
        },

        getList() {
            this.loading = true;
            const params = { ...this.queryParams };
            gatewayList(params).then(response => {
                const { list, total } = response.data
                this.postList = list;
                this.total = total;
            }).finally(() => this.loading = false)
        },

        cancel() {
            this.open = false;
            this.reset();
        },

        reset() {
            this.form = {
                gatewayAlias: '',
                gatewaySign: '',
                gatewayIp: '',
                gatewayPort: '',
                gatewayUsername: '',
                gatewayPwd: '',
                uniqueFlag: undefined,
                labelId: [],
            };
            this.resetForm("form");
        },

        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },

        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },

        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加岗位";
        },

        handleUpdate(row) {
            this.reset();
            this.form = {
                id: row.id,
                labelName: row.labelName,
                description: row.description,
                labelColour: row.labelColour,
                labelNameColour: row.labelNameColour,
            };
            this.open = true;
            this.title = "修改岗位";
        },

        submitForm: function () {
            this.$refs["form"].validate(valid => {
                const parmes = {
                    ...this.form,
                    gatewayPwd: encrypt(this.form.gatewayPwd)
                }
                if (valid) {
                    this.smiBtnFlag = true;
                    if (parmes.id) {
                        editManage(parmes).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => this.smiBtnFlag = false);
                    } else {
                        addManage(parmes).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList()
                        }).finally(() => this.smiBtnFlag = false);
                    }
                }
            });
        },

        edit(id) {
            getManageData(id).then(res => {
                this.form = {
                    ...res.data,
                    gatewayPwd: decrypt(res.data.gatewayPwd)
                };
                this.open = true;
            })
        },

        setRestartFuc(id) {
            this.loading=true
            restart({id}).then(res=>{
                this.$modal.msgSuccess("重启成功！");
            }).finally(()=>this.loading=false)
        },

        handleCommand(val, row) {
            if (val == 'delete') {
                this.deleteBatchHandle(row.id)
            }
            else {
                this.setDriveFuc(row.id)
            }
        },

        deleteBatchHandle(id) {
            this.$modal.confirm('是否确认数据项？').then(function () {
                return deleteManage(id);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功！");
            }).catch(() => { });
        },

        setDriveFuc(id) {
	        // this.detailDrawerVisible = true;
            this.$router.push({ path: `list/drawer`, query: { id } })
         }
    }
};
</script>



<style lang="scss" scoped>
.tabSelect {
    text-align: center !important;
}

.tagManageList {
    min-height: calc(100vh - 170px);
    position: relative;
    overflow: hidden;

    .drawerClass {
        background-color: red;
        ::v-deep .el-drawer__wrapper {
            height: 100%;
            position: absolute !important;
            overflow: hidden;
            .el-drawer__header {
                height: 40px;
            }
            .drawer-header {
                .el-button {
                    padding: 0;
                    margin-right: 10px;
                    line-height: 22px;
                }
                .title {
                    line-height: 22px;
                    display: inline-block;
                    font-size: 16px;
                    margin: 0;
                    vertical-align: middle;
                    padding-left: 20px;
                    font-weight: bold;
                    border-left: 1px solid #DBDFE9;
                }
            }

            .el-drawer__body {
                padding: 0;
            }
        }
    }

    .gateway {
        display: flex;
        align-items: center;

        .img {
            width: 68px;
            height: 68px;
        }

        .gateway-text {
            margin-left: 14px;

            .gateway-text-title {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #181F2D;
                margin-bottom: 6px;
            }

            .gateway-text-remark {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #515A6E;
            }
        }
    }

    .facility {
        span:nth-child(1) {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #181F2D;
        }

        span:nth-child(2) {
            font-family: HelveticaNeue-Bold;
            font-size: 18px;
            color: #181F2D;
        }
    }

    .tableDemo {
        margin-top: 12px;

        :deep(.el-tag) {
            margin: 0px 2px 2px 2px;

        }
    }

}
</style>