<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <div class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button id="gwButton"
                     type="goon"
                     icon="el-icon-circle-plus-outline"
                     v-if="true"
                     @click="deviceAdd"
          >新增</el-button
          >
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="true"
              :disabled="single"
              @click="handleEdit"
          >编辑</el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="true"
              @click.native="handleDel()"
              :disabled="multiple"
          >删除</el-button
          >
          <el-button
              type="check"
              icon="el-icon-download"
              @click="exportExcel"
          >导出</el-button
          >
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"></i>
        </div>
      </div>

    </el-card>
    <basic-container>
      <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          @selection-change="selectionChange"
          @on-load="getList"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @size-change="sizeChange"
          @current-change="currentChange"
          @row-update="handleUpdate"
          @row-save="handleSave"
          @row-del="handleDel"
      >
        <template slot="header">
          <IconTitle class="selfTitle" title="设备折旧" imgUrl="yunwei" />
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button type="text" @click="handleEdit(scope.row)">
            <i class="icon-bianji" style="font-size: 13px"></i>编辑
          </el-button
          >
        </template>
      </avue-crud>
    </basic-container>

    <!--  新增和修改  -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="50%" >

      <div>
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceId">
                <el-select
                    placeholder="请选择设备名称"
                    v-model="form.deviceId"
                    clearable
                    filterable
                    :disabled="showDis"
                    style="width: 100%"
                    @change="getChange(form.deviceId)"
                >
                  <el-option
                      :label="item.deviceName"
                      :value="item.id"
                      :key="item.id"
                      v-for="item in accountList"
                  ></el-option>
                </el-select>

              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="购置金额" prop="purchaseAmount">
                <el-input
                    v-model="form.purchaseAmount"
                    placeholder="请输入购置金额"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="折旧方法" prop="depreciationMethod">
                <el-select
                    placeholder="请选择设备折旧方法"
                    v-model="form.depreciationMethod"
                    clearable
                    style="width: 100%"
                >
                  <el-option
                      :label="item.label"
                      :value="item.value"
                      :key="item.id"
                      v-for="item in dict.type.depreciation_method"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="净残率" prop="residualRate">
                <el-input v-model="form.residualRate" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g, '$1')"  placeholder="请输入净残率(0.05)" maxlength="6"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                  label="购置日期" prop="purchaseDate">
                <el-date-picker
                    style="width: 100%"
                    v-model="form.purchaseDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择购置日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                  label="保修日期" prop="warrantyDate">
                <el-date-picker
                    style="width: 100%"
                    v-model="form.warrantyDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择保修日期">
                </el-date-picker>

              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                  label="投产日期" prop="introductionDate">
                <el-date-picker
                    style="width: 100%"
                    v-model="form.introductionDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择投产日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                  label="预计报废日期" prop="expectedScrapDate">
                <el-date-picker
                    style="width: 100%"
                    v-model="form.expectedScrapDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择预计报废日期">
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="开启折旧日期" prop="depreciationDate">
                <el-date-picker
                    style="width: 100%"
                    v-model="form.depreciationDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择开启折旧日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item style="float: right" v-if="title == '新增'">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="dialogFormVisible = false">返回</el-button>
              </el-form-item>
              <el-form-item style="float: right" v-if="title == '修改'">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="dialogFormVisible = false">返回</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  getAllAccountList
} from "@/api/ems/equipment/account";
import {fetchList,getObj,getDeviceById,addObj,putObj,putDepreciation,delObj} from "@/api/ems/equipment/depreciation";
import {tableOption} from '@/const/crud/ems/equipment/depreciation'
import { mapGetters } from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/icon-title/index.vue";
import {
  getDepreciationMethod,
} from "@/api/ems/equipment/account";

export default {
  name: 'emsdevicedepreciation',
  components: {
    IconTitle,
  },
  dicts: ['depreciation_method'],
  data() {
    return {
      title: '',
      tableData: [],
      //设备列表
      showDis: false,
      accountList:[],
      depreciationMethodSelect: [], //折旧方法
      searchForm: {}, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      form:{
        id: 0,
        deviceId: '',
        depreciationDate: '',
        depreciationMethod: '',
        monthCount: '',
        originalValue: '',
        netWorth: '',
        expectedScrapDate: null,
        purchaseDate: null,
        introductionDate: null,
        warrantyDate: null,
        monthDepreciation: '',
        accumulatedDepreciation: '',
        residualRate: '',
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: '',
        remark: '',
        delFlag: '',
        tenantId: ''
      },
      rules: {
        deviceId: [
          {required: true, message: '设备名称不能为空', trigger: 'blur'}
        ],
        depreciationDate: [
          {required: true, message: '折旧年月不能为空', trigger: 'blur'}
        ],
        depreciationMethod: [
          {required: true, message: '折旧方法不能为空', trigger: 'blur'}
        ],
        deviceName: [
          {required: true, message: '折旧方法不能为空', trigger: 'blur'}
        ],
        netWorth: [
          {required: true, message: '净值不能为空', trigger: 'blur'}
        ],
        monthDepreciation: [
          {required: true, message: '当前折旧不能为空', trigger: 'blur'}
        ],
        accumulatedDepreciation: [
          {required: true, message: '累计折旧不能为空', trigger: 'blur'}
        ],
        introductionDate: [
          {required: true, message: '请选择投产日期', trigger: 'blur'}
        ],
        purchaseAmount: [
          {required: true, message: '请输入购置金额', trigger: 'blur'}
        ],
        expectedScrapDate: [
          {required: true, message: '预计报废日期', trigger: 'blur'}
        ],
        residualRate: [
          {required: true, message: '净残率不能为空', trigger: 'blur'}
        ],
      },
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],
      dialogFormVisible: false,
    };
  },
  computed: {
    ...mapGetters(["permissions","theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsdevicedepreciation_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsdevicedepreciation_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsdevicedepreciation_edit,false),
      };
    },
  },
  mounted() {
    this.initElement();
    // this.changeThme();
  },
  methods: {


    getAllAccountList() {
      getAllAccountList().then(res => {
        this.accountList = res.data;
      });
    },

    getSelect() {
      getDepreciationMethod().then((res) => {
        this.depreciationMethodSelect = res.data;
      });
    },

    getChange(id) {
      this.showList(id);
    },


    showList(id) {
      getDeviceById(id).then(res =>{
        if (this.title == '新增') {
          this.form = res.data;
          this.form.id = null;
        } else {
          this.form = res.data;
        }
      })
    },

    // 新增
    deviceAdd() {
      this.title = "新增";
      this.reset();
      this.getSelect();
      this.getAllAccountList()
      this.showDis = false;
      this.dialogFormVisible = true;
    },


    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList=list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },

    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },

    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          },params,this.searchForm)).then((response) => {
        this.tableData = response.data.records;
        this.page.total = response.data.total;
        this.tableLoading = false;
      }).catch(() => {
        this.tableLoading = false;
      });
    },

    // 清空数据
    reset() {
      this.form = {
        id: null,
        dataName: null,
        documentEncryptionLevel: null,
        dataCategoryId: null,
        dataCategoryIdCopy: null,
        dataCategoryName: null,
        fileIdArray: null,
        remark: null
      };
      this.imgArrayTem = [];
    },

    //编辑
    handleEdit(row){
      this.title = "修改";
      this.reset();
      this.dialogFormVisible = true;
      this.getSelect();
      this.getAllAccountList();
      this.showDis = true;
      this.editId = row.id || this.selectionList[0].id;
      getObj(this.editId).then(res =>{
        this.form = res.data;

      })
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            addObj(this.form).then((data) => {
              if (data.code !== 200) {
                this.$message.success(data.msg);
              } else {
                this.$message.success("添加成功");
                this.dialogFormVisible = false;
                this.getList(this.page);
              }
            });
          } else {
            putDepreciation(this.form).then((res) => {
              if (res.code !== 200) {
                this.$message.success(data.msg);
              } else {
                this.$message.success("修改成功");
                this.$parent.listFlag = true;
                // this.form = res.data;
                this.dialogFormVisible = false;
                this.getList(this.page);
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return delObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 更新
    handleUpdate: function (row,  index,done, loading) {
      putObj(row)
          .then((data) => {
            this.$message.success("修改成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 保存
    handleSave: function (row, done, loading) {
      addObj(row)
          .then((data) => {
            this.$message.success("添加成功");
            done();
            this.getList(this.page);
          })
          .catch(() => {
            loading();
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsdevicedepreciation/export",
          this.searchForm,
          "设备折旧.xlsx"
      );
    },
    // 改变主题颜色
    // changeThme(){
    //   //"#02b980"
    //   document.getElementById("gwButton").style.backgroundColor=this.theme;
    // },
  },
};
</script>

<style lang="scss" scoped>

@import "@/styles/ems/avue.scss";
.el-button--goon.is-active,
.el-button--goon:active {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon:focus,
.el-button--goon:hover {
  background: #02b980;
  border-color: #02b980;
  color: #fff;
}

.el-button--goon {
  color: #FFF;
  background-color: #02b980;
  border-color: #02b980;
}

.drawerStyle {
  ::v-deep .el-drawer__header {
    background-color: #f2f2f5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 20px;
  }
}
.clearfix {
    height: 40px;
    position: relative;
    .btn-box {
        position: absolute;
        top: 0;
        left: 0;
    }
    .icon-box {
        position: absolute;
        right: 0;
        top: 0;
        height: 40px;
    }
}
</style>
