<template>
  <div class="account-detail">
    <div class="tab-box">
      <div
        :class="['item', index === tabIndex ? 'active' : '']"
        v-for="(item, index) in tabArr"
        @click="changeTab(index)"
      >
        <i :class="item.imgUrl"></i>
        <span>{{ item.text }}</span>
      </div>
    </div>
    <div class="con-box">
      <Basic v-if="tabIndex === 0"/>
      <Repair v-if="tabIndex === 1" />
      <Maintain v-if="tabIndex === 2" />
      <Inspection v-if="tabIndex === 3" />
      <Allocation v-if="tabIndex === 4" />
         <SparePart v-if="tabIndex === 5" />
      <Metering v-if="tabIndex === 6" />
      <Event v-if="tabIndex === 7" />
      <SpecialType v-if="tabIndex === 8" />
    </div>
    <div class="back-btn">
      <el-button @click="goBack()">返回</el-button>
    </div>
  </div>
</template>
<script>
import IconTitle from "@/components/icon-title/index.vue";
import Basic from "./basic.vue";
import Repair from "./repair.vue";
import Maintain from "./maintain.vue";
import Inspection from "./inspection.vue";
// import Allocation from "./allocation.vue";
import Event from "./event.vue";
// import SpecialType from "./specialType.vue";
// import SparePart from "./sparePart.vue";
// import Metering from "./metering.vue";

export default {
  name: "account-detail",
  components: {
    IconTitle,
    Basic,
    Repair,
    Maintain,
    Inspection,
    // Allocation,
    // Event,
    // SpecialType,
    // SparePart,
    // Metering
  },

  data() {
    return {
      tabArr: [
        { imgUrl: "icon-ziliao", text: "基本信息" },
        { imgUrl: "icon-guzhangweihu", text: "维修" },
        { imgUrl: "icon-gwfenlei", text: "保养" },
        { imgUrl: "icon-xunjian", text: "巡检" },
        // { imgUrl: "icon-tiaobo", text: "调拨" },
        // { imgUrl: "icon-beijian-line-", text: "备件" },
        // { imgUrl: "icon-jiliangtu", text: "计量" },
        // { imgUrl: "icon-shijian", text: "事件" },
        // { imgUrl: "icon-shebeizhuangtai", text: "特种设备" },
      ],
      tabIndex: 0,
    };
  },
  methods: {
    changeTab(index) {
      this.tabIndex = index;
    },
    goBack() {
      this.$router.go(-1);
    }
  },
};
</script>
<style lang="scss" >
@import "@/styles/color.scss";
.account-detail {
  width: 100%;
  margin-bottom: 50px;
  .tab-box {
    width: 100%;
    background: #fff;
    padding: 10px 15px;
    height: 55px;
    box-sizing: border-box;
    display: flex;
    border-radius: 4px;
    top: 115px;
    z-index: 10;
    .item {
      padding: 3px 5px;
      cursor: pointer;
      margin-right: 30px;
      border-radius: 4px;
      color: #666;
      display: flex;
      align-items: center;
      font-size: 15px;
      i {
        margin-right: 5px;
      }
    }
    .item.active {
      background: $theme;
      color: #fff;
    }
  }
  .con-box {
    width: 100%;
    //各个组件公共样式
    .table-box {
      margin-top: 10px;
      background: #fff;
      padding: 10px 15px;
      border-radius: 4px;
      box-sizing: border-box;
      .echarts-box {
        width: 100%;
        margin-top: 10px;
        .echarts-item {
          padding: 10px 15px;
          background: #fff;
          height: 180px;
          box-shadow: 0px 0px 7px 0px #eff2f5;
          .item-title {
            display: flex;
            align-items: center;
            i {
              color: #c9c184;
              margin-right: 5px;
            }
            span {
              font-size: 14px;
            }
            p {
              margin-left: 10px;
              padding: 0 5px;
              border: 1px solid #ccc;
              border-radius: 15px;
              font-weight: bold;
            }
          }
        }
      }
      .el-table {
        margin-top: 10px;
      }
    }
    .box-card {
      margin-top: 20px;
      .btn-box {
        margin-bottom: 20px;
      }
    }
  }
  .back-btn {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
