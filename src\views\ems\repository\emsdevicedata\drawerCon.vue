<template>
  <div class="drawer-box">
    <div class="info-box">
      <div class="info-item">
        <span class="labelS">资料编号：</span>
        <span class="contentS">{{ drawerData.dataNo }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">资料名称：</span>
        <span class="contentS">{{ drawerData.dataName }}</span>
      </div>
      <div class="info-item">
        <span class="labelS">资料类别：</span>
        <span class="contentS">{{ drawerData.categoryName }}</span>
      </div>
      <div class="info-item">
        <img class="xgfj" :src="require('@/assets/imagesAssets/xgfj.png')">
      </div>
    </div>
    <div class="tab-box">
      <IconTitle title="附件列表" imgUrl="yunwei" class="fjlb"/>
      <div class="fj" v-for="(i,index) in fileArray" :key="index">
        <img v-if="i.type === 'pdf'" src="@/assets/svg/pdf.svg" class="fjtp">
        <img v-if="i.type === 'ppt'" src="@/assets/svg/PPT.svg" class="fjtp">
        <img v-if="i.type === 'img' || i.type === 'jpg' || i.type === 'jpeg' || i.type === 'png'" src="@/assets/svg/img_tp.svg" class="fjtp">
        <img v-if="i.type === 'docx'" src="@/assets/svg/doc-excel.svg" class="fjtp">
        <div class="parameter">
          <div class="topParameter">
            <span class="fjName">{{ i.original }}</span>&nbsp;&nbsp;
            <span class="fjSize">[{{ i.fileSize }}]</span>
          </div>
          <div class="bottomParameter">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-unlock"
                @click="selectFile(i)"
            ><span class="ck" >查看</span>
            </el-button>
            <el-button
                type="text"
                size="small"
                icon="el-icon-download"
                @click="download(i, index)"
            ><span class="ck">下载</span>
            </el-button>
            <span class="newData">{{ i.createTime }}</span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="预览" :visible.sync="viewVisible" width="30%" height="30%" :modal="false">
      <pdf
          :src="url"
          :page="pdfPage"
          @num-pages="pdfPageCount = $event"
          @page-loaded="pdfPage = $event"
      ></pdf>
      <!-- 上下翻页 -->
      <button @click="previousPage" style="float: left;">上一页</button>
      <button @click="nextPage" style="float: right">下一页</button>
    </el-dialog>
  </div>
</template>
<script>
import IconTitle from "@/components/ems/icon-title/index.vue";
import pdf from 'vue-pdf'
export default {
  name: "drawerCon",
  components: {
    IconTitle,
    pdf
  },
  data() {
    return {
      viewVisible: false,
      url: '',
      pdfPage: 1,
      pdfPageCount: 1,
      // 下载文件
      searchForm: {
        fileName: ''
      },
    };
  },
  props: {
    drawerData: {
      type: Object
    },
    fileArray: {
      type: Array
    }
  },
  methods: {
    deviceSelectionChange() {
    },

    // 查看功能
    selectFile(i) {
      this.viewVisible = true;
      this.url = i.url;
    },
    // 上一页
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pdfPageCount;
      this.pdfPage = p;
    },
    // 下一页
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pdfPageCount ? p + 1 : 1;
      this.pdfPage = p;
    },

    // 下载功能
    download: function (i, index) {
      this.downBlobFile(
          "/admin/sys-file/" + i.bucketName + "/" + i.fileName,
          this.searchForm,
          i.fileName
      );
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/ems/mixin.scss";
@import "@/styles/color.scss";

::v-deep .el-drawer__body {
  overflow: auto;
}

.drawer-box {
  .info-box {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #f2f2f5;
    margin-bottom: 20px;


  }

  .info-item {
    display: flex;
    margin-bottom: 10px;

    .xgfj {
      float: left;
      margin-top: -140px;
      margin-left: 465px;
      margin-bottom: -30px;
    }

    .labelSMS {
      width: 15%;
      color: rgba(193, 200, 210, 100);
      font-size: 12px;
      font-family: SourceHanSansSC-bold;
      font-weight: bold;
    }

    .labelS {
      width: 15%;
      color: #888;
      font-size: 12px;
    }

    .contentS {
      font-weight: bold;
      color: #101010;
      display: inline-block;
      font-size: 12px;
      margin-top: auto;
    }

    .content-status {
      margin-right: 140px;
      font-size: 12px;
      display: flex;


    }
  }

  .tab-box {
    box-shadow: 0px 7px 12px 0px rgba(239, 239, 239, 100);
    line-height: 20px;
    border-radius: 10px;
    color: rgba(16, 16, 16, 100);
    font-size: 12px;
    margin: 20px;
    height: 500px;
    font-family: Roboto;

    .fjlb {
      padding: 20px 0 0 20px;
    }

    .fj {
      display: flex;
      margin: 10px 20px 0 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #EBEDF3;

      .fjtp {
        width: 50px;
        height: 50px;
        background-color: rgba(255, 255, 255, 100);
      }

      .parameter {
        display: flex;
        flex-direction: column;
        margin-left: 20px;

        .topParameter {
          .fjName {
            width: 48px;
            height: 21px;
            color: rgba(96, 98, 102, 100);
            font-size: 12px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
          }

          .fjSize {
            width: 50px;
            height: 21px;
            color: rgba(21, 132, 252, 100);
            font-size: 12px;
            text-align: left;
            font-family: SourceHanSansSC-bold;
          }
        }

        .bottomParameter {
          margin-top: 5px;

          .el-icon-unlock {
            color: rgba(38, 174, 97, 100);
            width: 18px;
            height: 18px;
          }

          .el-icon-download {
            margin-left: 20px;
            color: rgba(38, 174, 97, 100);
            width: 18px;
            height: 18px;
          }

          .ck {
            width: 80px;
            height: 18px;
            color: rgba(38, 174, 97, 100);
            font-size: 12px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
          }

          .newData {
            width: 103px;
            height: 21px;
            color: rgba(174, 174, 178, 100);
            font-size: 13px;
            text-align: left;
            font-family: SourceHanSansSC-regular;
            margin-left: 314px;
          }
        }

      }
    }
  }
}

</style>
