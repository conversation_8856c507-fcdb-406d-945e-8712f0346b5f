import echarts from "echarts"

/**
 * @param {*} mycharts
 * @name 报修工单月曲线
 */
function RepairworkOrder(mycharts, userData, xData, yData ,lastXData,lastYData) {
  console.log("lastXData>>>",JSON.stringify(lastXData))
  console.log("lastYData>>>",JSON.stringify(lastYData))
  const myChart = mycharts
  var newtitle = {}
  if (userData.length === 0) {
    newtitle = {
      text: '',
      x: 'center',
      y: 'center',
      textStyle: {
        color: '#9a9a9a',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  } else {
    newtitle = {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: {
        color: 'rgba(206, 145, 120,0)',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  }

  var option = {
    backgroundColor: '#ffffff',
    title: newtitle,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        saveAsImage: { show: true }
      }
    },
    legend: {
      data: ['本月', '上月']
    },
    grid: {
      x: '3%',
      x2: '1%',
    },
    xAxis: [
      {
        type: 'category',
        data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31],
        axisPointer: {
          type: ''
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 250,
        interval: 50,
        axisLine: {
          show: false  //不显示坐标轴轴线
        },
        axisTick: {
          show: false  //不显示坐标轴刻度
        }
      },
      {
        type: 'value',
        name: '',
        min: 0,
        max: 25,
        interval: 5,
        axisLabel: {
          formatter: '{value} °C',
          show: false
        },

        axisLine: {
          show: false  //不显示坐标轴轴线
        },
        axisTick: {
          show: false  //不显示坐标轴刻度
        },

        //设置网格线颜色
        splitLine: {
          show: true,
          lineStyle: {
            color: ['#e6e6e6'],
            width: 1,
            type: 'dotted '
          }
        }
      }
    ],
    series: [
      {
        name: '本月',
        type: 'bar',
        data: yData,
        barWidth: 30,
        itemStyle: {
          normal: {
            //这里是重点
            color: '#67df9c'
          }
        }

      },
      {
        name: '上月',
        type: 'line',
        yAxisIndex: 1,
        data: lastYData,
        itemStyle: {
          normal: {
            //这里是重点
            color: '#4797f4'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}
/**
* @param {*} mycharts
* @name 访问用户浏览器/操作系统
*/
function Usersreach(mycharts, userData, userColor,userTitle) {
  const myChart = mycharts
  var newtitle = {}
  if (userData.length === 0) {
    newtitle = {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: {
        color: '#9a9a9a',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  } else {
    newtitle = {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: {
        color: 'rgba(206, 145, 120,0)',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  }

  var option = {
    title: newtitle,
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: '10',
    },
    series: [
      {
        name: userTitle,
        type: 'pie',
        radius: '50%',
        itemStyle: {
          normal: {
          color: function(params) {
          //自定义颜色
          var colorList = userColor;
              return colorList[params.dataIndex]
              }
          }
        },
        data: userData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}

/**
* @param {*} mycharts
* @name 设备使用状态
*/
function Servicestatus(mycharts, userData, userColor,userTitle) {
  const myChart = mycharts
  var newtitle = {}
  if (userData.length === 0) {
    newtitle = {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: {
        color: '#9a9a9a',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  } else {
    newtitle = {
      text: '设备使用状态',
      left: 'center',
      textStyle: {//主标题的属性
                      color: '#87888A',//颜色
                      fontSize: 14,//大小
                      fontWeight:500,//
                  },
    }
  }

  var option = option = {
    title: newtitle,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      data: [
        'Direct',
        'Marketing',
        'Search Engine',
        'Email',
        'Union Ads',
        'Video Ads',
        'Baidu',
        'Google',
        'Bing',
        'Others'
      ]
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        selectedMode: 'single',
        radius: [0, '50%'],
        label: {
          position: 'inner',
          fontSize: 12,
          show:false
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1548, name: '保养' },
          { value: 775, name: '在用' },
          { value: 455, name: '停用' },
          { value: 679, name: '其他' }
        ]
      },
      {
        name: '设备状态',
        type: 'pie',
        radius: ['56%', '60%'],
        labelLine: {
          length: 30
        },
        label: {
          formatter:'{b|{b}：}{c}台  {per|{d}%}  ',
          // backgroundColor: '#F6F8FC',
          // borderColor: '#8C8D8E',
          // borderWidth: 1,
          // borderRadius: 4,
          rich: {
            a: {
              color: '#87888a',
              lineHeight: 22,
              align: 'center'
            },
            hr: {
              borderColor: '#87888a',
              width: '100%',
              borderWidth: 1,
              height: 0
            },
            b: {
              color: '#87888a',
              fontSize: 12,
              // fontWeight: 'bold',
              lineHeight: 33
            },
            per: {
              color: '#87888a',
              // backgroundColor: '#4C5058',
              padding: [3, 4],
              borderRadius: 4
            }
          }
        },
        data: [
          { value: 1548, name: '保养' },
          { value: 775, name: '在用' },
          { value: 455, name: '停用' },
          { value: 679, name: '其他' }
        ]
      }
    ]
  }
  myChart.setOption(option)
}

/**
* @param {*} mycharts
* @name 设备完好率
*/
function Servicerice(mycharts, userData, userColor,userTitle) {
  const myChart = mycharts
  var newtitle = {}
  if (userData.length === 0) {
    newtitle = {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: {
        color: '#9a9a9a',
        fontWeight: 'normal',
        fontSize: 16
      }
    }
  } else {
    newtitle = [{
      text: '设备完好率',
      x: 'center',
      top: '55%',
      textStyle: {
          color: '#525252',
          fontSize: 20,
          fontWeight: '100',
      }
  }, {
      text: ((userData.normalCount / userData.sum) * 100).toFixed(2),
      x: 'center',
      top: '35%',
      textStyle: {
          fontSize: '38',
          color: '#333333',
          fontFamily: 'Lato',
          foontWeight: '600',
      },
  }]
  }

  var option = {
    title:newtitle ,
    polar: {
        radius: ['80%', '90%'],
        center: ['50%', '50%'],
    },
    angleAxis: {
        max: 100,
        show: false,
    },
    radiusAxis: {
        type: 'category',
        show: true,
        axisLabel: {
            show: false,
        },
        axisLine: {
            show: false,

        },
        axisTick: {
            show: false
        },
    },
    series: [
        {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 60,
            showBackground: true,
            backgroundStyle: {
                color: 'rgba(230, 234, 237, .3)',
            },
            data: [50],
            coordinateSystem: 'polar',

            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                        offset: 0,
                        color: '#aa9dfb'
                    }, {
                        offset: 1,
                        color: '#426cf0'
                    }]),
                }
            }

        }
    ]
  }
  myChart.setOption(option)
}
function myWork(mycharts){
  const myChart = mycharts;
  var option = {
    xAxis: {
      type: 'category',
      data: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      axisLine: {
        show: false  //不显示坐标轴轴线
      },
      axisTick: {
        show: false  //不显示坐标轴刻度
      },
    },
    grid:{
      top:15,
      bottom:20,left:25,right:10
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false  //不显示坐标轴轴线
      },
      axisTick: {
        show: false  //不显示坐标轴刻度
      },
              splitLine:{
                lineStyle:{
                  type:'dashed',
                  color: ['#DCDCDC']
                }
               }
    },
    series: [
      {
        data: [0, 15, 5, 20, 5,15,20],
        type: 'line',
        symbol:"circle",
        symbolSize:10,
       itemStyle: {

            color: "#8FCD4E",
            borderColor:'#fff',
            borderWidth:'2'

    },
          lineStyle:{
              type:'dashed',
              color:'#52B7F5'
          }

      }
    ]
  }
  myChart.setOption(option)

}

export {
  RepairworkOrder,
  Usersreach,
  Servicestatus,
  Servicerice,
  myWork
}
