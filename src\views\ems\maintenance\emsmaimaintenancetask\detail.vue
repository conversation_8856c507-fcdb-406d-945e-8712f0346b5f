<template>
    <div>
        <el-row :gutter="20">
<!--            <el-col :span="16">-->
            <el-col >
                <el-card shadow="always" class="box-card">
                    <div class="tableTitle"><span>任务详情</span></div>
                    <div class="devTitle"><span>{{form.planName}}</span></div>
                    <div>
                        <div class="tableStyle">
                            <div class="labelS">任务编号</div>
                            <div class="contentS">{{form.taskNum}}</div>
                            <div class="labelS">保养类型</div>
                            <div class="contentS">日</div>
                        </div>
                        <div class="tableStyle">
                            <div class="labelS">计划名称</div>
                            <div class="contentS">{{form.planName}}</div>

                        </div>
                        <div class="tableStyle">
                            <div class="labelS">任务开始时间</div>
                            <div class="contentS">{{form.planBeginTime}}</div>
                            <div class="labelS">任务结束时间</div>
                            <div class="contentS">{{form.planEndTime}}</div>
                        </div>
                        <div class="tableStyle">
                            <div class="labelS">任务状态</div>
                            <div class="contentS">
                                <span class="nocomplate" :style="taskStatusStyle">{{form.statusName}}</span>
                            </div>

                        </div>
                        <div class="tableStyle">
                            <div class="labelS">备注</div>
                            <div class="contentS">
                                {{form.planRemark}}
                            </div>
                        </div>
                        <div class="tableStyle">
                            <div class="labelS">负责人</div>
                            <div class="contentS">
                                {{form.liableUserName}}
                            </div>
                        </div>
                    </div>
                </el-card>
                <el-card shadow="always" class="box-card">
                    <IconTitle title="保养项目" imgUrl="yunwei"></IconTitle>
                    <el-table v-loading="device.loading" :data="deviceList"
                              :span-method="objectSpanMethod">
                        <el-table-column
                                label="序号"
                                width="70px">
                            <template slot-scope="scope">
                                {{scope.$index+1}}
                            </template>
                        </el-table-column>
                        <el-table-column label="id" align="center" prop="id" v-if="false"/>
                        <el-table-column label="模块" align="center" prop="standardName"/>
                        <el-table-column label="项目" align="center" prop="itemsName"/>
                        <el-table-column label="编号" align="center" prop="itemsNum"/>
                        <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                        <el-table-column label="设备名称" align="center" prop="deviceName"/>
                        <el-table-column label="巡检时间" align="center" prop="recordInspectorTime"/>
                        <el-table-column label="巡检人" align="center" prop="recordInspector"/>
                        <el-table-column label="检验方法及说明" align="center" prop="methodBenchmark"/>
                        <el-table-column label="检验项" align="center" prop="itemsName"/>
                        <el-table-column label="点检值" align="center" prop="resultsInspectValue"/>
                        <el-table-column label="是否异常" align="center">
                            <template slot-scope="scope">
                                          <span>{{
                                            scope.row.resultsIsAbnormal == 0
                                              ? "正常"
                                              : scope.row.resultsIsAbnormal == 1
                                              ? "异常": ""
                                          }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" align="center" prop="resultsRemark"/>
<!--                        <el-table-column label="参考照片" align="center" prop="referencePicture"/>-->
<!--                        <el-table-column label="巡检照片" align="center" prop="resultsImageList"/>-->
                    </el-table>

                    <pagination
                            v-show="deviceQueryParams.total>0"
                            :total="deviceQueryParams.total"
                            :page.sync="deviceQueryParams.pageNum"
                            :limit.sync="deviceQueryParams.pageSize"
                            @pagination="getDeviceList"
                    />
                </el-card>
            </el-col>
<!--            //保养时间轴-->
<!--            <el-col :span="8">-->
<!--                <div class="icon-style">-->
<!--                    <div class="con-left" id="scrollBox">-->
<!--                        <el-card shadow="always" class="box-card">-->
<!--                            <div class="tableTitle">保养时间轴</div>-->
<!--                            <div class="vertical">-->
<!--                                <el-card shadow="always" class="box-card " v-for="item in 1" :key="item">-->
<!--                                    <div>-->
<!--                                        <div class="headerStyle">-->
<!--                                            <span class="dottedS"></span>-->
<!--                                            <i class="el-icon-date" style="color: #0ccb82"></i>-->
<!--                                            <span style="color: #0ccb82">任务节点：创建任务</span>-->
<!--                                            <i class="el-icon-arrow-up status"></i>-->
<!--                                        </div>-->
<!--                                        <div class="contentStyle">-->
<!--                                            <div class="boxContent">-->
<!--                                                <span class="desc">保养任务已创建</span>-->
<!--                                                <span class="status">-->
<!--                      <i class="el-icon-success"></i>-->
<!--                      正常-->
<!--                    </span>-->
<!--                                            </div>-->
<!--                                            <div>-->
<!--                                                <span class="label">处理人：</span>-->
<!--                                                <span class="content">系统自动操作</span>-->
<!--                                            </div>-->
<!--                                            <div>-->
<!--                                                <span class="label">执行时间：</span>-->
<!--                                                <span class="content"> 2022/03/11 00:00:00</span>-->
<!--                                            </div>-->
<!--                                            <div class="line"></div>-->
<!--                                            <div>-->
<!--                                                <span></span>-->
<!--                                                <span class="label timeTotal">用时：</span>-->
<!--                                                <span>1分</span>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->

<!--                                </el-card>-->
<!--                            </div>-->
<!--                        </el-card>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </el-col>-->
        </el-row>
        <el-card shadow="always" class="info-btn-box">
            <el-button @click="goBack">返回</el-button>
        </el-card>
    </div>
</template>
<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import {taskFetchList, taskGetObj, taskGetCheckResults_TaskId}
        from "@/api/ems/maintenance/emsmaimaintenancetask";
    import { getUser } from "@/api/system/user"
    export default {
        name: "detailTaskIndex",
        components: {
            IconTitle,
        },
        props: {
            id: {
                type: String,
            },
        },
        data() {
            return {
                deviceList: [],
                device: {
                    loading: false,
                },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },


                taskStatusStyle: null,
                form: {
                    id: null,
                    planName: null,
                    taskNum: null,
                    planBeginTime: null,
                    planEndTime: null,
                    planLiableUserId: null,
                    planRemark: null,
                    status: null,
                    statusName: null,
                    liableUserId: null,
                    liableUserName:null,

                },
            };
        },
        mounted() {
            if (this.id > 0) {
                taskGetObj(this.id).then((res) => {
                    this.form = res.data
                    if (this.form.liableUserId!=null){
                        getUser(this.form.liableUserId).then(res =>{
                            this.form.liableUserName=res.data.userName
                        })
                    }
                    var taskStatus = ["未开始", "执行中", "待核验", "已完成", "已过期"]
                    var taskStatusColor = [
                        "#002aff",
                        "#00f5e2",
                        "#F7CA60",
                        "#40f500",
                        "#ff0000"]
                    this.form.statusName = taskStatus[Number(this.form.status)]
                    this.taskStatusStyle = "background-color:" + taskStatusColor[Number(this.form.status)]
                    this.getDeviceList();
                });

            }
        },
        methods: {
            getDeviceList() {
                this.device.loading = true;
                taskGetCheckResults_TaskId(Object.assign(
                    {
                        current: this.deviceQueryParams.pageNum,
                        size: this.deviceQueryParams.pageSize,
                    },
                    {id: this.id}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    this.deviceQueryParams.total = response.data.total;
                    this.device.loading = false;
                });
            },
            objectSpanMethod({row, column, rowIndex, columnIndex}) {
                if (columnIndex === 1 || columnIndex === 2
                    || columnIndex === 3 || columnIndex === 4 ||
                    columnIndex === 0) {
                    if (rowIndex % 2 === 0) {
                        return {
                            rowspan: 2,
                            colspan: 1
                        };
                    } else {
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss" scoped>
    @import "@/styles/color.scss";

    .info-btn-box {
        width: 100%;
        text-align: center;
    }

    .headerStyle {
        margin-bottom: 20px;

    }

    .tableTitle {
        color: #333;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;

    }

    .smallTitle {
        color: rgba(153, 153, 153, 100);
        font-size: 12px;
        margin-left: 10px;
    }

    .devTitle {
        color: #262626;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .box-card {
        margin-bottom: 20px;

        .el-card__body {
            padding-top: 10px;
        }

        .labelS {
            //display: inline-block;
            flex: 0 0 150px;
            //height: 40px;
            // margin-right: 10px;
            text-align: left;
            color: #606266;
            padding: 10px;
            border: 1px solid rgba(236, 240, 244, 100);
            margin-bottom: -1px;
        }

        .contentS {
            border: 1px solid rgba(236, 240, 244, 100);
            // height: 40px;
            color: #606266;
            width: 100%;
            margin-left: -1px;
            margin-bottom: -1px;
            padding: 10px;
            // margin: 10px 0;
            // width: calc(100% - 120px);
            // display: inline-block;
        }

        .tableStyle {
            display: flex;
        }

        .number {
            font-weight: bold;
            margin-top: 5px;
            font-size: 16px;
        }

        .nocomplate {
            color: white;
            background-color: #E83672;
            padding: 3px 5px;
            border-radius: 3px;
            font-size: 12px;
        }

        .complate {
            background-color: #78BF34;
        }
    }

    .con-left {

        font-size: 12px;
        position: relative;

        .headerStyle {
            .dottedS {
                width: 10px;
                height: 10px;
                display: inline-block;
                border-radius: 50%;
                background-color: #666;
                position: absolute;
                left: -6px;
            }

            color: #0ccb82;

            i {
                margin-right: 25px;
            }
        }

        ::v-deep.vertical {
            border-left: 2px dashed #EDEEF2;
            position: relative;
            padding-left: 10px;

            .el-card__body {
                padding: 10px 0 10px 10px;
            }

            .box-card {
                box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, 0.12);

            }
        }

        .contentStyle {
            padding: 10px;

            .line {
                border: 1px solid rgba(236, 240, 244, 100);
                margin: 15px 0;
            }
        }

        .status {
            float: right;
            margin-right: 10px;

            i {
                color: #0ccb82;
                margin-right: 5px;
            }
        }

        .boxContent {
            background-color: #F6F6FC;
            font-size: 14px;
            height: 50px;
            line-height: 50px;
            margin: 10px 0 15px;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                height: 50px;
                width: 5px;
                background-color: #DEDEE4;

            }

            .desc {
                margin-left: 10px;
            }

        }

        .label {
            color: #999;
            margin-right: 10px;
            display: inline-block;
            margin-bottom: 10px;
            width: 70px;
        }

        .content {
            color: #101010;
        }

        .timeTotal {
            margin-right: 20px;
        }
    }

</style>
