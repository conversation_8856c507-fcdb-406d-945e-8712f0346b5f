<template>
  <div class="maintenance">
    <div class="table-box">
      <IconTitle title="基本信息" imgUrl="yunwei">
      </IconTitle>
      <div class="echarts-box">
        <el-row :gutter="10">
          <el-col :span="6">
            <div class="situation">
              <!--      今日完成情况        -->
              <div class="echarts-item" style="height: 100px; margin-bottom: 10px">
                <div class="item-title">
                  <i class="icon-ziliao"></i>
                  <span style="font-weight: 600; color: #847f8c">今日完成情况</span>
                </div>
                <div style="display: flex; margin-left: 15px">
                  <span style="font-weight: 600; color: #3f3365;font-size: 14px; margin-top: 20px">{{ checkingTodayCompletionData.num }} / {{ checkingTodayCompletionData.allNum }}</span>
                  <span style="color: #b4aeb1;margin: 20px 0 0 5px">次</span>
                  <!--<ball style="margin: -25px 0 0 80px" :checkingTodayCompletionData="checkingTodayCompletionData"/>-->
                  <statistical style="margin: -25px 0 0 90px" :checkingTodayCompletionData="checkingTodayCompletionData"/>
                </div>
              </div>

              <!--      历史未完成        -->
              <div class="echarts-item" style="height: 100px">
                <div class="item-title">
                  <i class="icon-ziliao"></i>
                  <span style="font-weight: 600; color: #847f8c">历史未完成</span>
                </div>
                <div style="display: flex">
                  <div style="display: flex; flex-direction: column; margin: 10px 0 0 20px">
                    <span style="font-weight: 600; margin-bottom: 10px">今年</span>
                    <div>
                      <span style="font-weight: 600; font-size: 18px; color: #7c90c2">{{ historyUnfinishedData.num }}</span>
                      <span style="color: #b4aeb1; margin-left: 5px">次</span>
                    </div>
                  </div>

                  <div style="display: flex; flex-direction: column; margin: 10px 0 0 130px">
                    <span style="font-weight: 600; margin-bottom: 10px">总数</span>
                    <div>
                      <span style="font-weight: 600; font-size: 18px; color: #676767">{{ historyUnfinishedData.allNum }}</span>
                      <span style="color: #b4aeb1; margin-left: 5px">次</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="echarts-item" style="height: 210px">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">季度统计</span>
              </div>
              <div>
                <transverse style="margin: -30px 0 0 -20px"/>
              </div>
            </div>
          </el-col>
          <el-col :span="10"
          >
            <div class="echarts-item" style="height: 210px; padding: 10px 0px;">
              <div class="item-title">
                <i class="icon-ziliao"></i>
                <span style="font-weight: 600; color: #847f8c">周统计</span>
              </div>
              <div class="weeks">
                <div class="total">
                  <div class="complete">
                    <span style="margin: 10px;">完成数(次)</span>
                    <span style="color: #b47b81; font-size: 18px; font-weight: 600; margin: 0 15px;">{{ weekCountData.accomplishNum }}</span>
                  </div>
                  <div class="unfinished">
                    <span style="margin: 10px;">未完成(次)</span>
                    <span style="color: #333333; font-size: 16px; font-weight: 600; margin: 0 15px;">{{ weekCountData.unfinishedNum }}</span>
                  </div>
                </div>
                <div>
                  <vertical style="margin-left: 20px" :weekCountData="weekCountData"/>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div>
      <el-row>
        <el-col :span="12">
          <div class="table-box" style="height: 344px; margin-right: 5px">
            <IconTitle title="任务完成情况" imgUrl="yunwei"></IconTitle>
            <completion/>
          </div>

        </el-col>

        <el-col :span="12">
          <div class="table-box" style="height: 344px; margin-left: 5px">
            <IconTitle title="负责人完成情况" imgUrl="yunwei"/>
            <head-completion/>
          </div>
        </el-col>
      </el-row>

    </div>

  </div>
</template>

<script>
import {getWeekCountList,getCheckingTodayCompletion,getCheckingHistoryUnfinished} from "@/api/ems/statistical/insperction";
import IconTitle from "@/components/ems/icon-title/index.vue";
import ball from "./echarts/ball";
import statistical from "./echarts/statistical";
import transverse from "./echarts/transverse";
import vertical from "./echarts/vertical";
import completion from "./echarts/completion";
import headCompletion from "./echarts/headCompletion";
export default {
  name: "inspection",
  components: {
    IconTitle,
    ball,
    statistical,
    transverse,
    vertical,
    completion,
    headCompletion
  },
  data(){
    return{
      weekCountData: {},
      checkingTodayCompletionData: {},
      historyUnfinishedData: []
    }
  },
  created() {
    this.getWeekCountList();
    this.getCheckingTodayCompletion();
    this.getCheckingHistoryUnfinished();
  },
  methods: {

    getCheckingTodayCompletion() {
      getCheckingTodayCompletion().then(res => {
        this.checkingTodayCompletionData = res.data.data;
      });
    },

    getCheckingHistoryUnfinished() {
      getCheckingHistoryUnfinished().then(res => {
        this.historyUnfinishedData = res.data.data;
      });
    },


    getWeekCountList() {
      getWeekCountList().then(res => {
        this.weekCountData = res.data.data;
      });
    }
  }
}
</script>

<style scoped lang="less">
.maintenance{
  font-size: 12px;
  .table-box{
    .echarts-box{
      .echarts-item{
        .weeks{
          width: 100%;
          height: 150px;
          background-color: #FEFAF9;
          margin-top: 10px;
          display: flex;

          .total{

            .complete{
              display: flex;
              flex-direction: column;

            }
            .unfinished{
              display: flex;
              flex-direction: column;
              margin-top: 20px;
            }

          }

        }
      }
    }
  }
}
</style>
