<template>
  <div class="ranking">
    <el-row>
      <el-row>
        <el-col style="background-color: #ffffff" :span="11">
          <div class="rank">
            <span style="color:#666666; margin-top: 8px; margin-left: 8px; font-weight: 600;font-size: 18px">任务/月</span>
            <span style="color:#666666; margin-top: 8px; margin-right: 8px; font-weight: 600; font-size: 18px">月视图</span>
          </div>
          <div style="margin-right: 10px; width: 580px; height: 580px">
            <rank-echarts/>
          </div>
        </el-col>
        <el-col style="background-color: #ffffff ; margin-left: 10px"  :span="12">
          <div class="device_rank" style="height:582px;overflow-y:auto">
            <span style="color:#666666; font-weight: 600; margin-top: 8px; font-weight: 600; text-align: center; display:block;font-size: 18px">设备维修占比排行</span>
            <div class="ph" v-if="rankingData.repairExceptionsVos.length > 0" v-for="item in rankingData.repairExceptionsVos">
              <img :src="headIMG(item.coverImg)" alt="">
              <div class="text">
                <div style="display: flex; justify-content: space-between;  margin-bottom: 15px">
                  <sapn>{{ item.deviceName }} ({{ ((Number(item.num) / allNum) * 100).toFixed(2) }}%)</sapn>
                  <span style="margin-right: 50px">{{ Number(item.num) }}次</span>
                </div>
                <el-progress :percentage="Number(item.num)" :format="format" style="width: 580px;" color="#F49A19"></el-progress>
              </div>
            </div>
            <div v-if="rankingData.repairExceptionsVos.length === 0" style="color: #bfbfbf; font-size: 18px; text-align: center;line-height: 500px;">暂无数据</div>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import {
  getRankingList
} from "@/api/ems/statistical/maintain"
import rankEcharts from "./echarts/rankEcharts";
export default {
  name: "ranking",
  data(){
    return{
      rankingData: [],
      allNum: 0
    };
  },
  components: {
    rankEcharts
  },
  created() {
    this.getRankingList();
  },
  methods: {

    getRankingList() {
      getRankingList().then(res => {
        this.rankingData = res.data.data;
        console.log("1111>>>>>>>>>" , JSON.stringify(this.rankingData.repairExceptionsVos.length))
        // let repair = [];
        if (this.rankingData.repairExceptionsVos.length > 0) {
          let a = 0;
          let repair = this.rankingData.repairExceptionsVos ;
          repair.forEach(function (item) {
            a += Number(item.num);
          });
          this.allNum = a;
        }

      });
    },
    headIMG(img) {
      const imgData = require('@/assets/imagesAssets/kong.png')
      return img != null ? img.url : imgData
    },

    format(percentage) {
      return ``;
    }
  }
}
</script>

<style lang="less" scoped>
  .ranking{
    background-color: #f6f6f6;
    width: 100%;
    height: 100%;
    padding: 10px;
    font-size: 12px;

    .device_rank{
      margin-left: 20px;
      .ph{
        width: 100%;
        margin-top: 20px;
        display: flex;
        .text{
          display: flex;
          flex-direction: column;
        }
        img{
          width: 50px;
          height: 50px;
          margin-right: 15px;
        }
      }

    }

    .rank{
      display: flex;
      justify-content: space-between;

    }
  }
</style>
