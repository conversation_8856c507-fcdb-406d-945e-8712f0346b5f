<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="巡检类型" prop="inspectionType">
      <el-input v-model="dataForm.inspectionType" placeholder="巡检类型"></el-input>
    </el-form-item>
    <el-form-item label="巡检项编号" prop="itemsNum">
      <el-input v-model="dataForm.itemsNum" placeholder="巡检项编号"></el-input>
    </el-form-item>
    <el-form-item label="项目" prop="itemsName">
      <el-input v-model="dataForm.itemsName" placeholder="项目"></el-input>
    </el-form-item>
    <el-form-item label="方法及基准" prop="methodBenchmark">
      <el-input v-model="dataForm.methodBenchmark" placeholder="方法及基准"></el-input>
    </el-form-item>
    <el-form-item label="类型" prop="type">
      <el-input v-model="dataForm.type" placeholder="类型"></el-input>
    </el-form-item>
    <el-form-item label="下限" prop="lowerLimit">
      <el-input v-model="dataForm.lowerLimit" placeholder="下限"></el-input>
    </el-form-item>
    <el-form-item label="上限" prop="upperLimit">
      <el-input v-model="dataForm.upperLimit" placeholder="上限"></el-input>
    </el-form-item>
    <el-form-item label="可选项" prop="optional">
      <el-input v-model="dataForm.optional" placeholder="可选项"></el-input>
    </el-form-item>
    <el-form-item label="正常选项" prop="normalOption">
      <el-input v-model="dataForm.normalOption" placeholder="正常选项"></el-input>
    </el-form-item>
    <el-form-item label="参考图片" prop="referencePicture">
      <el-input v-model="dataForm.referencePicture" placeholder="参考图片"></el-input>
    </el-form-item>
    <el-form-item label="标准Id" prop="standardId">
      <el-input v-model="dataForm.standardId" placeholder="标准Id"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createBy">
      <el-input v-model="dataForm.createBy" placeholder="创建者"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updateBy">
      <el-input v-model="dataForm.updateBy" placeholder="更新者"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    <el-form-item label="删除标志(0正常 1删除)" prop="delFlag">
      <el-input v-model="dataForm.delFlag" placeholder="删除标志(0正常 1删除)"></el-input>
    </el-form-item>
    <el-form-item label="租户Id" prop="tenantId">
      <el-input v-model="dataForm.tenantId" placeholder="租户Id"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {
    checkFetchList,
    checkDelObj,
    checkGetObj,
    checkAddObj,
    checkPutObj
  } from '@/api/ems/inspection/emsinsinspectitems';

    export default {
    data () {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          id: 0,
          inspectionType: '',
          itemsNum: '',
          itemsName: '',
          methodBenchmark: '',
          type: '',
          lowerLimit: '',
          upperLimit: '',
          optional: '',
          normalOption: '',
          referencePicture: '',
          standardId: '',
          createBy: '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          remark: '',
          delFlag: '',
          tenantId: ''
        },
        dataRule: {
          inspectionType: [
            { required: true, message: '巡检类型不能为空', trigger: 'blur' }
          ],
          itemsNum: [
            { required: true, message: '巡检项编号不能为空', trigger: 'blur' }
          ],
          itemsName: [
            { required: true, message: '项目不能为空', trigger: 'blur' }
          ],
          methodBenchmark: [
            { required: true, message: '方法及基准不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '类型不能为空', trigger: 'blur' }
          ],
          lowerLimit: [
            { required: true, message: '下限不能为空', trigger: 'blur' }
          ],
          upperLimit: [
            { required: true, message: '上限不能为空', trigger: 'blur' }
          ],
          optional: [
            { required: true, message: '可选项不能为空', trigger: 'blur' }
          ],
          normalOption: [
            { required: true, message: '正常选项不能为空', trigger: 'blur' }
          ],
          referencePicture: [
            { required: true, message: '参考图片不能为空', trigger: 'blur' }
          ],
          standardId: [
            { required: true, message: '标准Id不能为空', trigger: 'blur' }
          ],
          createBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          delFlag: [
            { required: true, message: '删除标志(0正常 1删除)不能为空', trigger: 'blur' }
          ],
          tenantId: [
            { required: true, message: '租户Id不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0;
        this.visible = true;
        this.canSubmit = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            checkGetObj(this.dataForm.id).then(response => {
                this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false;
            if (this.dataForm.id) {
              checkPutObj(this.dataForm).then(data => {
                    this.$notify.success('修改成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            } else {
              checkAddObj(this.dataForm).then(data => {
                    this.$notify.success('添加成功')
                    this.visible = false
                    this.$emit('refreshDataList')
                }).catch(() => {
                    this.canSubmit = true;
                });
            }
          }
        })
      }
    }
  }
</script>
