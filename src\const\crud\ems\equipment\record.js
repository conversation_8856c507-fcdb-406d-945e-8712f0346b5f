export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  'menu': true,//去掉操作栏
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "editBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": true,
  "gridBtn": false,
  "column": [
    // {
    //   "type": "input",
    //   "label": "主键",
    //   "prop": "id",
    //   "span": 12,
    //   hide:true
    // },
    {
      "type": "input",
      "label": "设备编号",
      "prop": "deviceNum",
      "span": 12,
      search: true
    },
    {
      "type": "input",
      "label": "设备名称",
      "prop": "deviceName",
      "span": 12,
      search: true
    },
    {
      "type": "input",
      "label": "开机时间",
      "prop": "startupTime",
      "span": 12

    }, {
      "type": "input",
      "label": "关机时间",
      "prop": "shutdownTime",
      "span": 12
    }, {
      "type": "input",
      "label": "运行时间(min)",
      "prop": "runDuration",
      "span": 12
    }, {
      "type": "input",
      "label": "异常停机时长(min)",
      "prop": "faultShutdownDuration",
      "span": 12
    }, {
      "type": "input",
      "label": "创建者",
      "prop": "createBy",
      "span": 12,
      hide: true
    }, {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12,
      hide: true
    }, {
      "type": "input",
      "label": "更新者",
      "prop": "updateBy",
      "span": 12,
      hide: true
    }, {
      "type": "input",
      "label": "更新时间",
      "prop": "updateTime",
      "span": 12,
      hide: true
    }, {
      "type": "input",
      "label": "备注",
      "prop": "remark",
      "span": 12,
      hide: true
    },]
}
