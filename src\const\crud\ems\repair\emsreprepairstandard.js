export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  'selection': true,
  "searchMenuSpan": 6,
  "addBtn": false,
  "refreshBtn": false,
  "columnBtn": false,
  "searchShowBtn": false,
  "searchIcon": false,
  "searchShow": true,
  "column": [
    {
      "type": "input",
      "label": "主键",
      "prop": "id",
      "span": 12,
      "hide": true,
      "addDisplay":false,
      "eddDisplay":false
    }, {
      "type": "input",
      "label": "标准编号",
      "prop": "standardNum",
      "span": 12,
      "search":true,
    }, {
      "type": "input",
      "label": "标准名称",
      "prop": "standardName",
      "span": 12,
      "search":true,
    },
    // {
    //   "type": "input",
    //   "label": "设备类别",
    //   "prop": "categoryId",
    //   "span": 12
    // },
    {
      "type": "input",
      "label": "检修周期",
      "prop": "inspectCycle",
      "span": 20
    },{
      "type": "input",
      "label": "作业内容",
      "prop": "jobContent",
      "span": 20
    }, {
      "type": "input",
      "label": "技术要求",
      "prop": "technicalRequirement",
      "span": 20
    },{
      "type": "input",
      "label": "安全要点",
      "prop": "safety",
      "span": 20
    },{
      "type": "input",
      "label": "质量要求",
      "prop": "qualityRequirements",
      "span": 20
    }]
}
