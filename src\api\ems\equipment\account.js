/*
 *    Copyright (c) 2018-2025, gewu All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: gewu
 */

import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/platform/emsdeviceaccount/page',
    method: 'get',
    params: query
  })
}
export function emsDevicefetchList(query) {
  return request({
    url: '/platform/emsdeviceaccount/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/platform/emsdeviceaccount',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/platform/emsdeviceaccount/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/platform/emsdeviceaccount/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/platform/emsdeviceaccount',
    method: 'put',
    data: obj
  })
}

export function getAccountList() {
  return request({
    url: '/platform/emsdeviceaccount/getAccountAndCategory',
    method: 'get'
  })
}
//获取未开启折旧的设备
export function getAllAccountList() {
  return request({
    url: '/platform/emsdeviceaccount/list',
    method: 'get'
  })
}

//得到设备使用状态
export function getSelectUseStatus(){
  return request({
    url: '/system/dict/data/type/device_use_status',
    method: 'get',
  })
 }


//得到折旧方法
export function getDepreciationMethod(){
  return request({
    url: '/system/dict/data/type/depreciation_method',
    method: 'get',
  })
}

 //得到设备使用状态
export function getSelectDeviceLevel(){
  return request({
    url: '/system/dict/data/type/device_level',
    method: 'get',
  })
 }
 //得到设备状态
export function getSelectDeviceStatus(){
  return request({
    url: '/system/dict/data/type/device_status',
    method: 'get',
  })
 }
 //得到单位
export function getSelectUnit(){
  return request({
    url: '/system/dict/data/type/unit',
    method: 'get',
  })
 }

//得到单位
export function getUpdateEcho(id){
  return request({
    url: '/platform/emsdeviceaccount/' + id,
    method: 'get',
  })
}

//得到最新点检
export function getLatestTally(id){
  return request({
    url: '/platform/emsdeviceaccount/latestTally/' + id,
    method: 'get',
  })
}

//设备台账详情巡检
export function getEquipmentInspectionById(id){
  return request({
    url: '/platform/emsdeviceaccount/equipmentInspection/' + id,
    method: 'get',
  })
}

//设备台账详情保养
export function getMaintenancePlanById(id){
  return request({
    url: '/platform/emsdeviceaccount/maintenancePlan/' + id,
    method: 'get',
  })
}

//得到父设备种类
export function geteEmsdevicecategory(query){
  return request({
    url: '/platform/emsdevicecategory/list',
    method: 'get',
    params: query
  })
}


//巡检情况
export function getInspectionSituation(id){
  return request({
    url: '/platform/emsdeviceaccount/inspectionSituation/' + id,
    method: 'get',
  })
}

//得到父设备
export function getAccountId(id){
  return request({
    url: '/platform/emsdeviceaccount/getAccountId/' + id,
    method: 'get'
  })
}


//分类统计
export function getClassifiedStatisticList(){
  return request({
    url: '/platform/deviceLedgerReport/classifiedStatisticList',
    method: 'get'
  })
}

//近五年保养次数对比
export function getRecentFiveYears(id){
  return request({
    url: '/platform/emsdeviceaccount/recentFiveYears/' + id,
    method: 'get'
  })
}

//通过设备id今年执行情况
export function getImplementationYear(id){
  return request({
    url: '/platform/emsdeviceaccount/implementationYear/' + id,
    method: 'get'
  })
}

//通过设备id故障等级
export function getFailureLevel(id){
  return request({
    url: '/platform/emsdeviceaccount/failureLevel/' + id,
    method: 'get'
  })
}

//通过设备id查询维保排名-保养
export function getMaintenanceRanking(query){
  return request({
    url: '/platform/emsdeviceaccount/maintenanceRanking',
    method: 'get',
    params: query
  })
}

//通过设备id查询维保排名-维修
export function getMaintainRanking(query){
  return request({
    url: '/platform/emsdeviceaccount/maintainRanking',
    method: 'get',
    params: query
  })
}

//通过设备id查询故障费用、次数和排名
export function getBasicInformation(id){
  return request({
    url: '/platform/emsdeviceaccount/basicInformation/' + id,
    method: 'get'
  })
}

//通过设备id查询故障类型分布
export function getFailureType(id){
  return request({
    url: '/platform/emsdeviceaccount/failureType/' + id,
    method: 'get'
  })
}


//维修方式
export function getSelectrepairMethod(){
  return request({
    url: '/system/dict/data/type/repair_method',
    method: 'get',
  })
}

//根据位置获取设备列表
export function getListByPosition(query){
  return request({
    url: '/platform/emsdeviceaccount/getListByPosition',
    method: 'get',
    params: query
  })
}


//根据位置获取设备列表
export function getListByCat(query){
  return request({
    url: '/platform/emsdeviceaccount/getListByCat',
    method: 'get',
    params: query
  })
}
//获取设备动态数据
export function getAllDynamicPro(query){
  return request({
    url: '/platform/emsdeviceaccount/getAllDynamicPro',
    method: 'get',
    params: query
  })
}

//获取设备动态数据
export function getDataPreview(query){
  return request({
    url: '/platform/emsdeviceaccount/getDataPreview',
    method: 'get',
    params: query
  })
}

//根据设备id集合查询设备，给信息模型用的
export function getDeviceListForInfoModelIdList(query){
  return request({
    url: '/platform/emsdeviceaccount/deviceListForInfoModelIdList/page',
    method: 'get',
    params: query
  })
}


