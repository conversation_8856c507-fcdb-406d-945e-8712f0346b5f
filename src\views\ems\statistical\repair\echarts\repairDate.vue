<template>
  <div id="repairDate" :style="{width: '100%', height: '240px'}"></div>
</template>


<script>
import echarts from "echarts"
import {
  getMaintenanceTimeList
} from "@/api/ems/statistical/maintain";

let maintenanceOption = {
  tooltip: {},
  grid: {
    left: '3%',
    right: '3%',
    bottom: '5%',
    top: '8%',
    containLabel: true
  },
  xAxis: {
    type: 'category',

    data: [],
    axisLabel: {
      interval:0,//代表显示所有x轴标签显示
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '处理时间',
      type: 'bar',
      color: '#63b2ee',
      stack: 'account',
      barWidth: 26,
      data: [],
      label: {
        show: true, //开启显示
        position: 'top', //在上方显示
        textStyle: { //数值样式
          color: '#63b2ee',
          fontSize: '12'
        }
      },
    },
  ]
};


export default {
  data() {

    return {
      maintenanceOption,
      maintenanceTime: []
    };
  },
  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      let repairDate = this.$echarts.init(document.getElementById('repairDate'))
      getMaintenanceTimeList().then(res => {
        this.maintenanceTime = res.data.data;
        if (this.maintenanceTime.length > 0) {
          let xData = [];
          let yData = [];
          this.maintenanceTime.forEach(item => {
            xData.push(item.name);
            yData.push(item.value)
          });
          maintenanceOption.xAxis.data = xData;
          maintenanceOption.series[0].data = yData;
          repairDate.setOption(this.maintenanceOption);
        } else {
          repairDate.showLoading({
            text: '暂无数据',
            showSpinner: false,    // 隐藏加载中的转圈动图
            textColor: '#9d9d9d',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '14px',
            fontFamily: 'Microsoft YaHei'
          });
        }
        // 绘制图表
        // repairDate.setOption(this.maintenanceOption);
      });



    }
  }
}

</script>
