<template>
    <div class="add-box">
        <el-form
                :model="form"
                :rules="rules"
                ref="ruleForm"
                label-width="140px"
                size="small"
                class="demo-ruleForm"
        >
            <div class="info-box">
                <IconTitle title="基础信息" imgUrl="yunwei"></IconTitle>
                <div class="info-from">
                    <el-form-item label="计划编号" prop="planNum">
                        <el-input v-model="form.planNum" maxlength="20":disabled="true" placeholder="无需填写自动生成"/>
                    </el-form-item>
                    <el-form-item label="计划名称" prop="planName">
                        <el-input v-model="form.planName" placeholder="请输入计划名称" maxlength="20"/>
                    </el-form-item>
                    <el-form-item label="所属部门" prop="deptId">
                        <treeselect
                                v-model="form.deptId"
                                :options="treeDeptData"
                                :normalizer="normalizer"
                                placeholder="所属部门"
                        />
                    </el-form-item>
                    <el-form-item label="负责人" prop="liableUserId">
                        <el-input v-model="form.liableUserName" placeholder="请选择负责人">
                            <el-button @click="liableUser()" resource="false" style="padding-right:10px" slot="suffix"
                                       type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="开始执行时间" prop="beginTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.beginTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择开始执行时间"
                                style="width: 100%;"
                                @blur="blurBeginTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束执行时间" prop="endTime">
                        <el-date-picker
                                clearable
                                size="small"
                                v-model="form.endTime"
                                type="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择结束执行时间"
                                style="width: 100%;"
                                @blur="blurEndTime"
                        ></el-date-picker>
                    </el-form-item>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="保养周期" prop="inspectCycle">
                                <el-input v-model="form.inspectCycle"
                                          placeholder="请输入保养周期"
                                          style="width: 150px"
                                          maxlength="2"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          @blur="blurInspectCycle"/>

                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="保养周期单位" prop="cycleUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.cycleUnit"-->
                                <!--                                placeholder="请选择巡检周期单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <el-input v-model="form.cycleUnitName"
                                          style="width: 50px;" :disabled="true" placeholder="日"/>
                                <!--                        </el-select>-->
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*保养周期：开始执行时间-到—结束执行时间中间循环的周期，如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，保养周期为1，代表在5天中，每天走一个循环</span>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="任务有效期" prop="effectiveTime">
                                <el-input v-model="form.effectiveTime"
                                          placeholder="请输入任务有效期"
                                          style="width: 150px"
                                          maxlength="2"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          @blur="blurEffectiveTime"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="任务有效期单位" prop="effectiveUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.effectiveUnit"-->
                                <!--                                placeholder="请选择任务有效期单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName"
                                          style="width: 50px;" :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*任务有效期：开始执行时间-到—结束执行时间中间循环的周期中有多长时间为有效的，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，保养周期为2，任务有效期为1，
                                代表在5天中，每两天走一个循环，在这两天中，只有前面一天才是任务执行期</span>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="提前提醒时间" prop="noticeTime">
                                <el-input v-model="form.noticeTime"
                                          placeholder="请输入提前提醒时间"
                                          style="width: 150px"
                                          maxlength="2"
                                          oninput="value=value.replace(/[^\d]/g,'')"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="提前提醒时间单位" prop="noticeUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.noticeUnit"-->
                                <!--                                placeholder="请选择提前提醒时间单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName" style="width: 50px;"
                                          :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*提前提醒时间：根据保养周期生成任务后，在移动端提前提醒执行人的时间，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，
                                保养周期为1,提前提醒时间为1，那么会在2000/2/1号提醒执行人注意保养待办任务</span>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="提前生成时间" prop="generateTime">
                                <el-input v-model="form.generateTime"
                                          placeholder="请输入提前生成时间"
                                          style="width: 150px"
                                          oninput="value=value.replace(/[^\d]/g,'')"
                                          maxlength="3"
                                          @blur="blurGenerateTime"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="7">
                            <el-form-item label="提前生成时间单位" prop="generateUnit">
                                <!--                        <el-select-->
                                <!--                                v-model="form.generateUnit"-->
                                <!--                                placeholder="请选择提前生成时间单位"-->
                                <!--                                size="small"-->
                                <!--                                clearable-->
                                <!--                                value-key="type"-->
                                <!--                        >-->
                                <!--                            <el-option label="日" :value="1"/>-->
                                <!--                        </el-select>-->
                                <el-input v-model="form.cycleUnitName" style="width: 50px;"
                                          :disabled="true" placeholder="日"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11" >
                            <span>*提前生成时间：开始执行时间-到—结束执行时间中间循环的周期中提前生成的时间，
                                如：开始时间2000/2/2号到2000/2/7,一共有5天时间执行计划，保养周期为1，
                                提前生成时间为1，那么会在2000/2/1号生成任务</span>
                        </el-col>
                    </el-row>
                    <el-form-item label="验收人" prop="auditUserId">
                        <el-input v-model="form.auditUserName" placeholder="请选择验收人">
                            <el-button @click="auditUser()" style="padding-right:10px" slot="suffix" type="text">选择
                            </el-button>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="是否启用" prop="enable">
                        <el-radio-group v-model="form.enable" @change="handleEnable">
                            <el-radio :label="0">不启用</el-radio>
                            <el-radio :label="1">启用</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="说明" prop="remark" style="width: 100%;">
                        <el-input type="textarea" v-model="form.remark" maxlength="100" placeholder="请输入说明"/>
                    </el-form-item>
                    <!--                    <el-form-item label="关联设备" prop="strategyId">-->
                    <!--                        <el-input v-model="form.strategyName" placeholder="请关联设备">-->
                    <!--                            <el-button @click="strategy()" resource="false" style="padding-right:10px" slot="suffix"-->
                    <!--                                       type="text">关联-->
                    <!--                            </el-button>-->
                    <!--                        </el-input>-->
                    <!--                    </el-form-item>-->
                </div>
                <!--用户弹框-->
                <el-dialog :title="user.title" :visible.sync="user.open" width="1000px" append-to-body>
                    <el-row :gutter="20">
                        <!--部门数据-->
                        <el-col :span="4" :xs="24">
                            <div class="head-container">
                                <div class="tree">
                                    <el-tree
                                            :data="treeDeptData"
                                            :props="defaultProps"
                                            :expand-on-click-node="false"
                                            :filter-node-method="filterNode"
                                            ref="tree"
                                            default-expand-all
                                            @node-click="handleNodeClick"
                                    />
                                </div>
                            </div>

                        </el-col>
                        <!--用户数据-->
                        <el-col :span="20" :xs="24">
                            <el-table v-loading="user.loading" :data="userList"
                                      @row-click="userRowClick">
                                <el-table-column label="用户编号" align="center" key="userId" prop="userId"/>
                                <el-table-column label="用户名称" align="center" key="username" prop="userName"/>
                                <el-table-column label="部门" align="center" key="dept.deptName" prop="dept.deptName"/>
                                <el-table-column label="角色" align="center" key="roleName" prop="roleName"/>
                                <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber"/>
                                <el-table-column label="状态" align="center" key="status">
                                    <template slot-scope="scope">
                                          <span>{{
                                            scope.row.status === '0'
                                              ? "正常"
                                              : scope.row.status === '1'
                                              ? "停用": ""
                                          }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="创建时间" align="center" prop="createTime"
                                                 width="160">
                                </el-table-column>
                            </el-table>

                            <pagination
                                    v-show="userQueryParams.total>0"
                                    :total="userQueryParams.total"
                                    :page.sync="userQueryParams.pageNum"
                                    :limit.sync="userQueryParams.pageSize"
                                    @pagination="getUserList"
                            />
                        </el-col>
                    </el-row>
                </el-dialog>

            </div>
            <!--            <div class="info-box">-->
            <!--                <IconTitle title="负责人" imgUrl="yunwei"></IconTitle>-->
            <!--                <el-table v-loading="liableUser.loading" :data="liableUserList">-->
            <!--                    <el-table-column label="id" align="center" prop="id" v-if="false"/>-->
            <!--                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>-->
            <!--                    <el-table-column label="设备名称" align="center" prop="deviceName"/>-->
            <!--                    <el-table-column label="品牌" align="center" prop="brandNewName"/>-->
            <!--                    <el-table-column label="规格型号" align="center" prop="specification"/>-->
            <!--                </el-table>-->
            <!--            </div>-->
            <div class="info-box">
                <IconTitle title="设备列表" imgUrl="yunwei"></IconTitle>
                <!--                @selection-change="handleSelectionChange"-->
                <el-button style="float: right"
                           type="primary"
                           icon="el-icon-plus"
                           size="mini"
                           @click="addDeviceId"
                >关联设备
                </el-button>
                <el-table v-loading="device.loading" :data="deviceList">
                    <el-table-column
                            label="序号"
                            width="50px">
                        <template slot-scope="scope">
                            {{scope.$index+1}}
                        </template>
                    </el-table-column>
                    <el-table-column label="id" align="center" prop="id" v-if="false"/>
                    <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                    <el-table-column label="设备名称" align="center" prop="deviceName"/>
                    <el-table-column label="品牌" align="center" prop="brandNewName"/>
                    <el-table-column label="规格型号" align="center" prop="specification"/>
                    <el-table-column label="保养项目" width="350px">
                        <template slot-scope="scope">
                            <span style="font-size: 5px">{{scope.row.maintenanceStandardName}}</span>
                            <el-button
                                    size="mini"
                                    type="text"
                                    v-if="scope.row.maiItemsListOnOFF==1"
                                    @click="spread(scope.row)"
                            >
                                <span style="color: #2d8cf0;margin-left: 5px">展开</span>
                            </el-button>
                            <el-button
                                    size="mini"
                                    type="text"
                                    @click="packUp(scope.row)"
                                    v-else-if="scope.row.maiItemsListOnOFF==2"
                            ><span style="color: #2d8cf0;margin-left: 5px">收起</span>
                            </el-button>
                            <div :style="scope.row.css" class="itemCss" style="height: 90px;overflow: auto">
                                <p v-for="(item,i) in scope.row.maiItemsList ">{{item}}</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100px" align="center" class-name="small-padding fixed-width">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="deviceListDele(scope.row)"
                            >删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-show="queryParams.total>0"
                        :total="queryParams.total"
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="deviceGetList"
                />

                <el-dialog :title="device.title" :visible.sync="device.open" width="800px" append-to-body>
                    <el-table v-loading="device.loading" :data="deviceNewList"
                              @selection-change="handleSelectionChange"
                    >
                        <el-table-column type="selection" align="center"/>
                        <el-table-column label="id" align="center" prop="id" v-if="false"/>
                        <el-table-column label="设备编号" align="center" prop="deviceNum"/>
                        <el-table-column label="设备名称" align="center" prop="deviceName"/>
                        <el-table-column label="品牌" align="center" prop="brandNewName"/>
                        <el-table-column label="规格型号" align="center" prop="specification"/>
                    </el-table>

                    <pagination
                            v-show="queryParamsDeviceList.total>0"
                            :total="queryParamsDeviceList.total"
                            :page.sync="queryParamsDeviceList.pageNum"
                            :limit.sync="queryParamsDeviceList.pageSize"
                            @pagination="deviceGetPlanIdList"
                    />
                    <div slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="submitDeiceList">确 定</el-button>
                        <el-button @click="cancelDeviceList">取 消</el-button>
                    </div>
                </el-dialog>
            </div>
            <div class="info-btn-box">
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
        </el-form>

    </div>

</template>
<script>
    import IconTitle from "@/components/icon-title/index.vue";
    import ImageUpload from "@/components/ems/ImageUpload/index.vue";
    import {fetchTree} from "@/api/admin/dept";
    import Treeselect from "@riophae/vue-treeselect";
    import "@riophae/vue-treeselect/dist/vue-treeselect.css";
    import {mapGetters} from "vuex";
    import {getUser} from "@/api/system/user";
    import { listUser } from '@/api/system/user'
    import jQuery from "jquery";
    import {planGetObj, planAddObj, planPutObj,
        deviceListAll,devicePlanIdList} from '@/api/ems/maintenance/emsmaimaintenanceplan'

    import user from "../../../../store/modules/user";

    export default {
        name: "AddIndex",
        components: {
            IconTitle,
            Treeselect,
            ImageUpload,
        },
        props: {
            id: {
                type: Number,
            },
        },
        data() {
            return {
                rowIndex :null,
                defaultProps: {
                    children: "children",
                    label: "label",
                },
                page: {
                    total: 0, // 总页数
                    currentPage: 1, // 当前页数
                    pageSize: 20, // 每页显示多少条,
                },
                // 用户数组
                userList: [],
                //用户数据分页
                userQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                    deptId: null,
                },
                user: {
                    title: "",
                    open: false,
                    loading: false,
                    type: null,
                },
                strategyList: [],
                strategyQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                strateg: {
                    title: "",
                    open: false,
                    loading: false,
                },
                deviceList: [],
                // device: {
                //     loading: false,
                // },
                deviceQueryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },

                list: [],
                treeData: [],
                loading: false,
                categoryList: [], //设备类别
                brandList: [], //设备品牌
                treeDeptData: [], //部门
                // 一天的毫秒数
                dayTime: 86400000,
                form: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    deviceId:[],
                },
                oldForm: {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    liableUserName: null,
                    auditUserId: null,
                    auditUserName: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    strategyName: null,
                    deviceId:[],
                },
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },
                queryParamsDeviceList: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0,
                },


                device: {
                    title: "",
                    open: false,
                    loading: false,
                    deviceForm: {
                        id: null,
                        standardNum: null,
                        standardName: null,
                        deptId: null,
                        requirement: null,
                        remark: null,
                        deviceId: [],
                    }
                },

                dataRule: {
                    // inspectionType: [
                    //     {required: true, message: '保养类型不能为空', trigger: 'blur'}
                    // ],
                },
                //所有的设备数据
                deviceNewList: [],
                coverImgTem: [],
                imgArrayTem: [],
                rules: {
                    planName: [
                        {required: true, message: '请输入计划名称', trigger: 'blur'}
                    ],
                    deptId: [
                        {required: true, message: '请选择部门', trigger: 'blur'}
                    ],
                    liableUserId:[
                        {required: true, message: '请选择负责人', trigger: 'blur'}
                    ],
                    beginTime:[
                        {required: true, message: '请选择开始执行时间', trigger: 'blur'}
                    ],
                    endTime:[
                        {required: true, message: '请选择结束执行时间', trigger: 'blur'}
                    ],
                    // inspectCycle:[
                    //     {required: true, message: '请输入保养周期', trigger: 'blur'}
                    // ],
                    // effectiveTime:[
                    //     {required: true, message: '请输入任务有效期', trigger: 'blur'}
                    // ],
                    auditUserId:[
                        {required: true, message: '请选择审核人', trigger: 'blur'}
                    ],
                },
                dialogVisible: false,

                deviceIdNewList:[],
            };
        },
        created() {
            this.getSelect();
        },
        computed: {
            ...mapGetters(["permissions", "theme"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
                    delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
                };
            },
        },
        mounted() {
            this.reset();
            if (this.id > 0) {
                planGetObj(this.id).then((res) => {
                    this.oldForm = res.data
                    this.oldForm.enable = parseInt(this.oldForm.enable);
                    this.oldForm.inspectSettings = parseInt(this.oldForm.inspectSettings);
                    if (this.oldForm.liableUserId!=null) {
                        getUser(this.oldForm.liableUserId).then(res => {
                            this.oldForm.liableUserName = res.data.userName
                            this.userList = [];
                            var o = {};
                            o.name = this.oldForm.liableUserName;
                            o.sex = "未知"
                            o.zs = "未知"
                            this.userList.push(o);
                            this.user.loading = false;
                        })
                    }
                    if (this.oldForm.auditUserId!=null){
                        getUser(this.oldForm.auditUserId).then(res =>{
                            this.oldForm.auditUserName=res.data.userName
                        })
                    }
                    if(this.oldForm.deviceIdList!=null){
                        this.deviceIdNewList = [];
                        this.deviceIdNewList=  this.oldForm.deviceIdList;
                        for (var i = 0; i <  this.deviceIdNewList.length; i++) {
                            this.device.deviceForm.deviceId.push( this.deviceIdNewList[i]);
                        }

                        deviceListAll(Object.assign(
                            {
                                current: this.queryParams.pageNum,
                                size: this.queryParams.pageSize,
                            },
                            {deviceId: this.device.deviceForm.deviceId}
                            )
                        ).then(response => {
                            this.deviceList = response.data.records;
                            for(let i=0;i<this.deviceList.length;i++){
                                if (this.deviceList[i].maiItemsList!=null){
                                    let maiItemsOldList =this.deviceList[i].maiItemsList;
                                    let onAndoffList =[];
                                    for(let j=0;j<maiItemsOldList.length;j++){
                                        let name = maiItemsOldList[j].itemsName+"("+maiItemsOldList[j].itemsNum+")"
                                        onAndoffList.push(name)
                                    }
                                    this.deviceList[i].maiItemsList=onAndoffList;

                                }
                                this.deviceList[i].css="display:none"
                                if (this.deviceList[i].maintenanceStandardName!=null){
                                    this.deviceList[i].maintenanceStandardName=this.deviceList[i].maintenanceStandardName+this.deviceList[i].maiItemsList.length+"项"
                                }
                            }
                            this.queryParams.total = response.data.total;
                            this.loading = false;
                            this.device.open = false;
                        });
                    }
                    this.form = this.oldForm;
                });
            }
        },
        methods: {
            deviceGetList() {
                this.device.loading = true;
                deviceListAll(Object.assign(
                    {
                        current: this.queryParams.pageNum,
                        size: this.queryParams.pageSize,
                    },
                    {deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    for(let i=0;i<this.deviceList.length;i++){
                        if (this.deviceList[i].maiItemsList!=null){
                            let maiItemsOldList =this.deviceList[i].maiItemsList;
                            let onAndoffList =[];
                            for(let j=0;j<maiItemsOldList.length;j++){
                                let name = maiItemsOldList[j].itemsName+"("+maiItemsOldList[j].itemsNum+")"
                                onAndoffList.push(name)
                            }
                            this.deviceList[i].maiItemsList=onAndoffList;

                        }
                        this.deviceList[i].css="display:none"
                        if (this.deviceList[i].maintenanceStandardName!=null){
                            this.deviceList[i].maintenanceStandardName=this.deviceList[i].maintenanceStandardName+this.deviceList[i].maiItemsList.length+"项"
                        }
                    }
                    this.queryParams.total = response.data.total;
                    this.device.loading = false;
                    this.device.open = false;
                });
            },
            deviceGetPlanIdList() {
                this.device.loading = true;
                devicePlanIdList(Object.assign(
                    {
                        current: this.queryParamsDeviceList.pageNum,
                        size: this.queryParamsDeviceList.pageSize,
                    },
                    {id: this.device.deviceForm.id, deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceNewList = response.data.records;
                    this.queryParamsDeviceList.total = response.data.total;
                    this.device.loading = false;
                });
            },
            submitDeiceList() {
                this.loading = true;
                this.ids = [...new Set(this.ids)];
                for (var i = 0; i < this.ids.length; i++) {
                    this.device.deviceForm.deviceId.push(this.ids[i]);
                }
                this.loading = false;
                this.device.open = false;
                deviceListAll(Object.assign(
                    {
                        current: this.queryParams.pageNum,
                        size: this.queryParams.pageSize,
                    },
                    {deviceId: this.device.deviceForm.deviceId}
                    )
                ).then(response => {
                    this.deviceList = response.data.records;
                    for(let i=0;i<this.deviceList.length;i++){
                        if (this.deviceList[i].maiItemsList!=null){
                            let maiItemsOldList =this.deviceList[i].maiItemsList;
                            console.log(this.deviceList[i].maiItemsList)
                            if (maiItemsOldList==null){
                                this.$message.error('未添加保养项目')
                                return
                            }

                            let onAndoffList =[];
                            for(let j=0;j<maiItemsOldList.length;j++){
                                let name = maiItemsOldList[j].itemsName+"("+maiItemsOldList[j].itemsNum+")"
                                onAndoffList.push(name)
                            }
                            this.deviceList[i].maiItemsList=onAndoffList;

                        }
                        this.deviceList[i].css="display:none"
                        if (this.deviceList[i].maintenanceStandardName!=null&&this.deviceList[i].maiItemsList!=null){
                            this.deviceList[i].maintenanceStandardName=this.deviceList[i].maintenanceStandardName+this.deviceList[i].maiItemsList.length+"项"
                        }
                    }
                    this.queryParams.total = response.data.total;
                    this.loading = false;
                    this.device.open = false;
                });
            },
            // 多选框选中数据+"
            handleSelectionChange(selection) {
                this.ids = selection.map((item) => item.id);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            cancelDeviceList() {
                this.device.title = "";
                this.device.open = false;
            },
            deviceListDele(row) {
                for (var i = 0; i < this.deviceList.length; i++) {
                    if (this.deviceList[i].id == row.id) {
                        this.deviceList.splice(i, 1)
                        this.queryParams.total = this.queryParams.total - 1;
                    }
                }
                for (var i = 0; i < this.device.deviceForm.deviceId.length; i++) {
                    if (this.device.deviceForm.deviceId[i] == row.id) {
                        this.device.deviceForm.deviceId.splice(i, 1)
                    }
                }
            },
            addDeviceId() {
                this.device.title = "设备台账";
                this.device.open = true;
                this.deviceGetPlanIdList();
            },
            packUp(row) {
                row.maiItemsListOnOFF= 1;
                row.css="display:none"
            },
            spread(row) {
                row.maiItemsListOnOFF= 2;
                row.css=""
            },
            liableUser() {
                this.user.title = "选择负责人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 1;
                this.getUserList()
            },
            auditUser() {
                this.user.title = "选择审核人";
                this.user.loading = true;
                this.user.open = true;
                this.user.type = 0;
                this.getUserList()
            },
            userRowClick(row, event, column) {
                if (this.user.type == 1) {
                    this.form.liableUserId = row.userId;
                    this.form.liableUserName = row.userName;
                } else if (this.user.type == 0) {
                    this.form.auditUserId = row.userId;
                    this.form.auditUserName = row.userName;
                }
                this.user.open = false;
            },
            // 筛选节点
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            // 节点单击事件
            handleNodeClick(data) {
                this.userQueryParams.deptId = data.id;
                this.getUserList();
            },
            getUserList() {
                listUser(Object.assign(
                    {
                        current: this.userQueryParams.pageNum,
                        size: this.userQueryParams.pageSize,
                    },
                    {deptId: this.userQueryParams.deptId}
                    )
                ).then(response => {
                    this.userList = response.rows;
                    this.userQueryParams.total = response.total;
                    this.user.loading = false;
                });
            },
            blurBeginTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('开始日期必须小于结束日期，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
                if (this.form.beginTime != null) {
                    const beginTime = this.form.beginTime;
                    const startTime = new Date(beginTime).getTime();
                    const time = new Date(new Date(new Date().toLocaleDateString()).getTime())
                    if (startTime < time) {
                        this.$message.error('开始日期必须大于当前时间，请重新选择！')
                        this.form.beginTime = null
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurEndTime() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    if (startTime > endTime) {
                        this.$message.error('结束日期必须大于开始日期，请重新选择！')
                        this.form.endTime = ''
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                }
            },
            blurInspectCycle() {
                if (this.form.endTime != null && this.form.beginTime != null) {
                    const startDate = this.form.beginTime
                    const endDate = this.form.endTime
                    const startTime = new Date(startDate).getTime()
                    const endTime = new Date(endDate).getTime()
                    const time = endTime - startTime;
                    const timeSum = this.dayTime * this.form.inspectCycle
                    if (time < timeSum) {
                        this.$message.error('保养周期不能大于总时间，请重新选择！')
                        this.form.inspectCycle = null;
                        this.form.effectiveTime = null;
                        this.form.generateTime = null;
                    }
                } else {
                    if (this.form.inspectCycle != null) {
                        this.$message.error('请先填写开始时间和结束时间！');
                        this.form.inspectCycle = null;
                    }

                }
            },
            blurGenerateTime() {
                if (this.form.generateTime != null && this.form.inspectCycle) {
                    if (this.form.inspectCycle < this.form.generateTime) {
                        this.$message.error('提前生成时间不能大于保养周期，请重新选择！')
                        this.form.generateTime = null;
                    }
                } else {
                    this.$message.error('请先填写保养周期！')
                    this.form.generateTime = null;
                }
            },
            blurEffectiveTime() {
                if (this.form.effectiveTime != null && this.form.inspectCycle) {
                    if (this.form.inspectCycle < this.form.effectiveTime) {
                        this.$message.error('任务有效期不能大于保养周期，请重新选择！')
                        this.form.effectiveTime = null;
                    }
                } else {
                    this.$message.error('请先填写保养周期！')
                    this.form.effectiveTime = null;
                }
            },
            handleEnable() {
                if (this.form.enable == 1) {
                    this.$confirm('是否确认选择启用！一旦启用提交后无法修改', '警告', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(res => {
                        this.form.enable = 1;
                        return;
                    }).catch(err => {
                        this.form.enable = 0;
                        return;
                    })
                }
            },
            strategy() {
                this.strateg.title = "关联保养路径";
                this.strateg.loading = true;
                this.strateg.open = true;
                this.strateg.type = 0;
                this.onAndoff = 1
                this.getStrategyList()
            },
            strategyRowClick(row, event, column) {
                this.form.strategyId = row.id;
                this.form.strategyName = row.strategyName;
                this.strateg.open = false;
                this.getDeviceList();
            },

            submitForm(formName) {
                const startDate = this.form.beginTime
                const endDate = this.form.endTime
                const startTime = new Date(startDate).getTime()
                const endTime = new Date(endDate).getTime()
                const time =new Date(new Date(new Date().toLocaleDateString()).getTime())
                if (startTime < time) {
                    this.$message.error('开始日期必须大于当前时间，请重新选择！')
                    this.form.beginTime = null
                    this.form.inspectCycle = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return
                }
                if (startTime > endTime) {
                    this.$message.error('结束日期必须大于开始日期，请重新选择！')
                    this.form.endTime = ''
                    this.form.inspectCycle = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return;
                }
                if(this.form.noticeTime==null){
                    this.form.noticeTime=0;
                }
                const timeSum = this.dayTime * this.form.inspectCycle
                const time1 = endTime - startTime;
                if (time1 < timeSum) {
                    this.$message.error('保养周期不能大于总时间，请重新选择！')
                    this.form.inspectCycle = null;
                    this.form.effectiveTime = null;
                    this.form.generateTime = null;
                    return;
                }
                if (this.form.inspectCycle==null){
                    this.form.inspectCycle=0;
                }
                if (this.form.generateTime != null && this.form.inspectCycle>0) {
                    if (this.form.inspectCycle < this.form.generateTime) {
                        this.$message.error('提前生成时间不能大于保养周期，请重新选择！')
                        this.form.generateTime = null;
                        return;
                    }
                }else {
                    this.form.generateTime=0;
                }
                if (this.form.effectiveTime != null && this.form.inspectCycle>0) {
                    if (this.form.inspectCycle < this.form.effectiveTime) {
                        this.$message.error('任务有效期不能大于保养周期，请重新选择！')
                        this.form.effectiveTime = null;
                        return;
                    }
                }else {
                    this.form.effectiveTime=0;
                }
                this.form.deviceId=this.device.deviceForm.deviceId
                if (this.form.deviceId.length==0){
                    this.$message.error("请添加设备");
                    return;
                }
                let data = JSON.parse(JSON.stringify(this.form));
                this.$refs[formName].validate((valid) => {
                    if (valid){
                        if (data.id) {
                            planPutObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("修改成功");
                            });
                        } else {
                            planAddObj(data).then((res) => {
                                this.reset();
                                this.$parent.listFlag = true;
                                this.$parent.addllistFlag = false;
                                this.$parent.detaillistFlag = false;
                                this.$message.success("新增成功");
                            });
                        }
                    }
                });
            },

            reset() {
                this.form = {
                    id: null,
                    planNum: null,
                    planName: null,
                    deptId: null,
                    liableUserId: null,
                    inspectCycle: null,
                    cycleUnit: null,
                    effectiveTime: null,
                    effectiveUnit: null,
                    generateTime: null,
                    generateUnit: null,
                    noticeTime: null,
                    noticeUnit: null,
                    beginTime: null,
                    endTime: null,
                    lastGenerateTime: null,
                    nextGenerateTime: null,
                    enable: 0,
                    inspectSettings: 0,
                    strategyId: null,
                    deviceId:[],
                };
                // //this.resetForm("form");
            },
            getSelect() {
                // remote("ins_inspect_items_type").then(response => {
                //     this.typeList = response.data;
                // });
                // fetchListTree("").then((res) => {
                //     this.categoryList = res.data ? res.data : [];
                // });
                // getBrandList().then((res) => {
                //     this.brandList = res.data;
                // });
                //部门
                fetchTree().then((response) => {
                    this.treeDeptData = response.data;
                });
            },
            normalizer(node) {
                if (node.children && !node.children.length) {
                    delete node.children;
                }
                return {
                    id: node.id,
                    label: node.label,
                    children: node.children,
                };
            },
            goBack() {
                this.$parent.listFlag = true;
                this.$parent.addllistFlag = false;
                this.$parent.detaillistFlag = false;
                //  this.$parent.refreshChange()
            },
        },
    };
</script>
<style lang="scss">
        .itemCss::-webkit-scrollbar {
            width : 8px;
            height: 7px;
            background-color: transparent;
        }
        .itemCss::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: hsla(220, 4%, 58%, .3);
        }
        .itemCss::-webkit-scrollbar-track {
            background-color: transparent;
        }
    /*#onAnd {*/
    /*    height: 90px;*/
    /*    overflow: auto;*/
    /*}*/

    .add-box {
        .el-dialog__body {
            height: 80vh;
        }

        .table-box {
            height: 100%;

            .table-big-box {
                overflow: auto;
                height: 80%;

            }

        }
    }
</style>

<style lang="scss" scoped>
    @import "@/styles/color.scss";
    @import "@/styles/ems/mixin.scss";

    .add-box {
        margin-bottom: 50px;

        .info-box {
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 10px 15px;
            overflow: hidden;

            .info-from {
                display: flex;
                flex-wrap: wrap;
                padding-top: 20px;
                position: relative;

                .el-form-item {
                    width: 50%;
                    padding-right: 10px;
                }
            }

            .info-from::before {
                position: absolute;
                top: 10px;
                height: 1px;
                content: "";
                left: -15px;
                right: -15px;
                display: block;
                background: #eff2f5;
            }

            .runTime {
                ::v-deep .el-form-item__content {
                    display: flex;

                    span {
                        display: inline-block;
                        margin: 0 10px;
                    }
                }
            }
        }

        .info-btn-box {
            width: 100%;
            text-align: center;
        }

        .user {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }
</style>
