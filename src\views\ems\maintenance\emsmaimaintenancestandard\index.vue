<!--
  -    Copyright (c) 2018-2025, gewu All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the gewucn.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: gewu
  -->
<template>
  <div v-if="listFlag" class="execution">
    <el-card class="box-card btn-search page-search">
      <div slot="header" class="clearfix">
        <div class="btn-box">
          <el-button
              type="info"
              icon="el-icon-refresh-left"
              @click="refreshChange()"
          ></el-button
          >
          <el-button
              id="gwButton"
              type="primary"
              icon="el-icon-circle-plus-outline"
              v-if="true"
              @click="toAdd()"
          >新增
          </el-button
          >
          <el-button
              type="success"
              icon="el-icon-edit"
              v-if="true"
              :disabled="single"
              @click="handleEdit(scope.row)"
          >编辑
          </el-button
          >
          <el-button
              type="danger"
              icon="el-icon-circle-close"
              v-if="true"
              @click.native="handleDel()"
              :disabled="single"
          >删除
          </el-button
          >
          <el-button
              type="check"
              icon="el-icon-download"
              @click="exportExcel"
          >导出
          </el-button
          >
        </div>
        <div class="icon-box">
          <i class="el-icon-search" @click="searchShow"></i>
          <i class="el-icon-refresh" @click="refreshChange"></i>
          <i class="el-icon-goods"></i>
          <i class="el-icon-setting" @click="columnShow"></i>
          <i class="icon-zuixiaohua"/>
        </div>
      </div>

    </el-card>
    <basic-container>
      <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          @selection-change="selectionChange"
          @on-load="getList"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @size-change="sizeChange"
          @current-change="currentChange"
          @row-del="handleDel"
          @cell-click="cellClick"
          :cell-style="cellStyle"
          @row-click="rowClick"
      >
        <template slot="header">
          <IconTitle class="selfTitle" title="保养标准" imgUrl="yunwei"/>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button type="text" @click="handleEdit(scope.row)">
            <i class="el-icon-edit"></i>编辑
          </el-button
          >
          <el-button type="text" @click="toDetail(scope.row)">
            <i class="el-icon-view"></i>查看
          </el-button
          >
        </template>
      </avue-crud>
    </basic-container>
    <!--        <a @click="detail=true">关联计划</a>-->
    <!--        <a @click="goDetail">详情</a>-->
    <el-drawer
        class="drawerStyle"
        title="保养标准"
        :show-close="false"
        :visible.sync="detail"
        direction="rtl"
        size="45%"

        append-to-body
    >
      <div>
        <img style="float: right;margin-right: 180px" :src="require('@/assets/imagesAssets/xgfj.png')">
        <div style="width: 66%">
          <span class="labelS">标准编号：</span>
          <span class="contentS">{{ rowCheck.standardNum }}</span>
        </div>

        <div style="width: 66%">
          <span class="labelS">标准名称：</span>
          <span class="contentS">{{ rowCheck.standardName }}</span>
        </div>
        <div style="width: 66%">
          <span class="labelS">所属部门：</span>
          <span class="contentS">{{ rowCheck.deptName }}</span>
        </div>

      </div>
      <div class="line">
      </div>
      <div>

        <el-card shadow="always" class="box-card">
          <div><span class="tableTitle">关联设备</span></div>
          <el-table v-loading="rowCheck.loading" :data="rowCheck.deviceList">
            <el-table-column label="id" align="center" prop="id" v-if="false"/>
            <el-table-column label="设备编号" align="center" prop="deviceNum"/>
            <el-table-column label="设备名称" align="center" prop="deviceName"/>
            <el-table-column label="品牌" align="center" prop="brandNewName"/>
            <el-table-column label="规格型号" align="center" prop="specification"/>
          </el-table>
          <pagination
              v-show="rowCheck.queryParams.total>0"
              :total="rowCheck.queryParams.total"
              :page.sync="rowCheck.queryParams.pageNum"
              :limit.sync="rowCheck.queryParams.pageSize"
              @pagination="deviceGetList"
          />
        </el-card>
      </div>
    </el-drawer>
  </div>
  <div v-else-if="addllistFlag">
    <IndexAdd :id='addEditId'/>
  </div>
  <div v-else-if="detaillistFlag">
    <IndexDetail :id='detailId'/>
  </div>
</template>
<script>
import {
  emsmaimaintenancestandardFetchList, emsmaimaintenancestandardAddObj,
  emsmaimaintenancestandardPutObj, emsmaimaintenancestandardDelObj
} from "@/api/ems/maintenance/emsmaimaintenancestandard";
import {tableOption} from '@/const/crud/ems/maintenance/emsmaimaintenancestandard';
import IndexAdd from "./indexAdd.vue";
import IndexDetail from "./detail.vue";
import {mapGetters} from "vuex";
import jQuery from "jquery";
import IconTitle from "@/components/icon-title/index.vue";
import DrawerCon from "../../equipment/account/drawerCon";
import {deviceList} from "@/api/ems/maintenance/emsmaimaintenancestandard";

export default {
  name: "criterion",
  components: {
    IconTitle,
    IndexAdd,
    IndexDetail,
  },
  data() {
    return {
      detail: false,

      tableData: [],
      searchForm: {}, // 查询参数
      single: true,  // 非单个禁用
      multiple: true, // 非多个禁用
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      listFlag: true,
      addEditId: null,
      detailId: null,
      detaillistFlag: false,
      addllistFlag: false,
      tableLoading: false,
      tableOption: tableOption,
      ids: [],
      selectionList: [],


      //以下为抽屉参数
      rowCheck: {
        standardNum: null,
        standardName: null,
        deptName: null,
        loading: false,
        deviceList: [],
        rowId: null,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          total: 0,
        },
      }
    }
  },
  mounted() {
    this.initElement();
  },
  computed: {
    ...mapGetters(["permissions", "theme"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_add, false),
        delBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_del, false),
        editBtn: this.vaildData(this.permissions.ems_emsinspectionstandard_edit, false),
      };
    },
  },
  methods: {
    goDetail() {
      this.$router.push({
        path: '/ems/inspection/criterion/detail'
      })
    },
    initElement() {
      var mediumb = document.createElement("b"); //思路一样引入中间元素
      jQuery(".avue-crud__tip").after(mediumb);
      jQuery(".selfTitle").after(jQuery(".avue-crud__tip"));
      jQuery(mediumb).after(jQuery(".selfTitle"));
      jQuery(mediumb).remove();
    },
    selectionChange(list) {
      this.selectionList = list
      this.single = list.length !== 1;
      this.multiple = !list.length;
      this.ids = list.map((item) => item.id);
    },
    toAdd() {
      this.addEditId = 0
      this.listFlag = false;
      this.addllistFlag = true;
    },
    toDetail(row) {
      this.detailId = row.id
      this.listFlag = false;
      this.detaillistFlag = true;
    },
    columnShow() {
      this.$refs.crud.$refs.dialogColumn.columnBox = !0;
    },
    // 搜索框显示与否
    searchShow() {
      this.$refs.crud.$refs.headerSearch.searchShow = !this.$refs.crud.$refs.headerSearch.searchShow;
    },


    cellStyle(data) {
      // 表格的第一列加上小手和padding为0
      if (data.columnIndex == 2) {
        return "color:#02b980;cursor:pointer";
      }
    },
    cellClick(row, column) {
      if (column.property === "standardNum") {
        this.toDetail(row);
      } else {
        return;
      }
    },
    rowClick(row, column) {

      if (column.property === "standardNum") {
        return;
      } else {
        this.detail = true;
        this.rowCheck.loading = true;
        this.rowCheck.standardNum = row.standardNum
        this.rowCheck.standardName = row.standardName
        this.rowCheck.deptName = row.deptName
        this.rowCheck.rowId = row.id;
        deviceList(Object.assign(
                {
                  current: this.rowCheck.queryParams.pageNum,
                  size: this.rowCheck.queryParams.pageSize,
                },
                {id: row.id}
            )
        ).then(response => {
          this.rowCheck.deviceList = response.data.records;
          this.rowCheck.queryParams.total = response.data.total;
          this.rowCheck.loading = false;
        });
      }
    },

    deviceGetList() {
      this.rowCheck.loading = true;
      deviceList(Object.assign(
              {
                current: this.rowCheck.queryParams.pageNum,
                size: this.rowCheck.queryParams.pageSize,
              },
              {id: this.rowCheck.rowId}
          )
      ).then(response => {
        this.rowCheck.deviceList = response.data.records;
        this.rowCheck.queryParams.total = response.data.total;
        this.rowCheck.loading = false;
      });
    },
    // 列表查询
    getList(page, params) {
      this.tableLoading = true;
      emsmaimaintenancestandardFetchList(
          Object.assign({
            current: page.currentPage,
            size: page.pageSize,
          }, params, this.searchForm)).then((response) => {
        this.tableData = response.data.records;
        this.page.total = response.data.total;
        this.tableLoading = false;
      })
          .catch(() => {
            this.tableLoading = false;
          });
    },
    //编辑
    handleEdit(row) {
      this.addEditId = row.id || this.selectionList[0].id;
      this.listFlag = false;
      this.addllistFlag = true;
      this.detaillistFlag = false;

    },
    // 删除
    handleDel: function (row, index) {
      this.$confirm("是否确认删除所选数据项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
          .then(() => {
            let id = "";
            if (row) {
              id = row.id;
            } else {
              id = this.ids;
            }
            return emsmaimaintenancestandardDelObj(id);
          })
          .then((data) => {
            this.$message.success("删除成功");
            this.getList(this.page);
          });
    },
    // 每页条数改变事件
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 当前页发生改变事件
    currentChange(current) {
      this.page.currentPage = current;
    },
    // 查询事件
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    // 刷新事件
    refreshChange() {
      this.getList(this.page);
    },
    // 导出excel
    exportExcel() {
      this.$download.getXlsx(
          process.env.VUE_APP_BASE_API + "/platform/emsmaimaintenancestandard/export",
          this.searchForm,
          "保养标准.xlsx"
      );
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/ems/avue.scss";
@import "@/styles/ems/public-styles.scss";

.labelS {
  display: inline-block;
  width: 100px;
  margin-right: 10px;
  text-align: right;
  color: #888888;
  font-size: 14px;
}

.contentS {
  font-weight: bold;
  color: #101010;
  margin: 10px 0;
  display: inline-block;
  font-size: 14px;
}

.line {
  border: 2px solid rgba(236, 240, 244, 100);
  margin: 30px 0;
}

::v-deep.drawerStyle {
  .el-drawer__header {
    background-color: #F2F2F5;
    padding: 20px 0 20px 20px;
    color: #101010;
    margin-bottom: 10px;
  }

  .el-card__body {
    padding: 0 20px 10px;
  }

  .box-card {
    box-shadow: 0px 7px 12px 0px rgba(152, 152, 152, .12);
    margin: 0 10px;

  }

  .tableTitle {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    display: inline-block;
  }
}

.sbtzxq {
  margin-top: -111px;
  margin-left: 300px;
}
</style>
