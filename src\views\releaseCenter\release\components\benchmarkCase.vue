<template>
  <div class="financialCommn" v-loading="submitDing">
    <el-form
      :disabled="detailFlag"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="案例名称" prop="caseName">
            <el-input
              v-model="form.caseName"
              placeholder="请输入案例名称"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="资源描述内容" prop="profiles">
            <el-input
              v-model="form.profiles"
              placeholder="请输入资源描述内容"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="行业" prop="industrialSubstitutionIdJson">
            <el-cascader
              v-model="form.industrialSubstitutionIdJson"
              :options="industrys"
              :props="{
                children: 'children',
                label: 'vueName',
                value: 'id',
                    multiple: true,
              }"
              placeholder="请选择行业"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>
<!--        <el-col :span="10">
          <el-form-item label="行业" prop="caseTypes">
            <el-cascader
              v-model="form.caseTypes"
              :options="industryTypes"
              :props="{
                children: 'childrenList',
                label: 'name',
                value: 'industryCode',
              }"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>-->
        <el-col :span="10">
          <el-form-item label="场景分类" prop="sceneTypes">
            <el-select
              v-model="form.sceneTypes"
              placeholder="请选择场景分类"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.scene"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item class="uploadItem" label="图片" prop="imgUrl">
            <UploadImage
              :disabled="detailFlag"
              :fileList="form.imgUrl"
              @addUpload="addUpload"
              @removeUpload="removeUpload"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="标签" prop="label">
            <el-select
              v-model="form.label"
              multiple
              placeholder="请选择标签"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in dict.type.supply_lable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="公司介绍" prop="companyInfo">
            <el-input
              v-model="form.companyInfo"
              placeholder="请输入公司介绍"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="案例背景" prop="caseScenario">
            <el-input
              v-model="form.caseScenario"
              placeholder="请输入案例背景"
              style="width: 100%"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="实施详情内容" prop="caseDetails">
            <editor
              :readOnly="detailFlag"
              v-model="form.caseDetails"
              :min-height="192"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="成功介绍" prop="successInfo">
            <editor
              :readOnly="detailFlag"
              v-model="form.successInfo"
              :min-height="192"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="是否热门" prop="popularStatus">
            <el-switch
              v-model="form.popularStatus"
              active-value="1"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="是否标杆" prop="isBenchmark">
            <el-switch
              v-model="form.isBenchmark"
              active-value="1"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="form.status"
              active-value="0"
              inactive-value="1"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="相关产品" prop="relatedProducts">
            <el-select
              v-model="form.relatedProducts"
              placeholder="请选择相关产品"
              multiple
              filterable
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in industrialList"
                :key="dict.industrialId"
                :label="dict.industrialName"
                :value="dict.industrialId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="相关解决方案" prop="relatedSolutions">
            <el-select
              filterable
              v-model="form.relatedSolutions"
              placeholder="请选择相关解决方案"
              multiple
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="dict in caseTypesList"
                :key="dict.providerId"
                :label="dict.providerName"
                :value="dict.providerId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import UploadImage from "@/components/UploadImage";
import { getCityData } from "@/api/release/financial";
import { getSysIndustryType } from "@/api/release/policy";
import { getInfo } from "@/api/release/index.js";
import {
  getAllProviderByName,
  getAllIndustrialByName,
} from "@/api/release/benchmakCase.js";
import { uploadApi } from "@/api/release/index.js";
import {getSysIndustry} from "@/api/release/indApp";
export default {
  name: "benchmarkCase",
  dicts: ["supply_lable", "scene"],
  props: {
    footerWidth: {
      type: String,
      default: "0px",
    },
  },
  components: {
    UploadImage,
  },
  data() {
    return {
      form: {
        caseName: undefined,
        profiles: undefined,
        industrialSubstitutionIdJson: undefined,
        caseTypes: undefined,
        sceneTypes: undefined,
        label: [],
        companyInfo: undefined,
        caseScenario: undefined,
        caseDetails: undefined,
        successInfo: undefined,
        popularStatus: "0",
        isBenchmark: "0",
        status: "0",
        relatedProducts: undefined,
        relatedSolutions: undefined,
        imgUrl: [],
      },
      rules: {
        caseName: [
          { required: true, message: "请输入案例名称", trigger: "blur" },
        ],
        profiles: [
          { required: true, message: "请输入案例简介", trigger: "blur" },
        ],
        caseDetails: [
          { required: true, message: "请输入案例详情", trigger: "blur" },
        ],
        successInfo: [
          { required: true, message: "请输入成功介绍", trigger: "blur" },
        ],
        caseTypes: [
          { required: true, message: "请选择案例类型", trigger: "blur" },
        ],
        sceneTypes: [
          { required: true, message: "请选择场景类型", trigger: "blur" },
        ],
        label: [{ required: true, message: "请选择标签", trigger: "blur" }],
        companyInfo: [
          { required: true, message: "请输入公司信息", trigger: "blur" },
        ],
        caseScenario: [
          { required: true, message: "请输入案例场景", trigger: "blur" },
        ],
        // relatedProducts: [
        //   { required: true, message: "请选择相关产品", trigger: "blur" },
        // ],
        // relatedSolutions: [
        //   { required: true, message: "请选择相关解决方案", trigger: "blur" },
        // ],
        imgUrl: [{ required: true, message: "请上传", trigger: "change" }],
        industrialSubstitutionIdJson: [{ required: true, message: "请选择行业", trigger: "change" }],
      },
      industryTypes: [],
      industrys: [],
      cityData: [],
      caseTypesList: [],
      industrialList: [],
      submitDing: false,
      detailFlag:
        this.$route.query.pageType == "detail" ||
        this.$route.query.pageType == "check" ||
        false,
    };
  },

  created() {
    this.getCityDataFtn();
    this.getSysIndustryFtn(); //行业
    this.getCase(); //案例
    this.getIndustrial(); //应用
    const { flowInstanceId } = this.$route.query;
    flowInstanceId && this.getFormDataFtn(flowInstanceId);
  },

  methods: {
    //详情接口回显
    getFormDataFtn(flowInstanceId) {
      this.submitDing = true;
      getInfo({ flowInstanceId }).then((res) => {
        const { params } = res.data;
        this.form = {
          ...params,
          label: params.label.split(","),
          industrialSubstitutionIdJson: JSON.parse(params.industrialSubstitutionIdJson),
          relatedSolutions:params.relatedSolutions? params.relatedSolutions
            .split(",")
            .map((str) => parseInt(str)):undefined,
          relatedProducts: params.relatedProducts?params.relatedProducts
            .split(",")
            .map((str) => parseInt(str)):undefined,
          imgUrl: params.caseImg.split(","),
        };
        this.submitDing = false;
      });
    },
    //新增图片事件
    addUpload(res) {
      this.form.imgUrl = [...this.form.imgUrl, res.url];
    },
    //删除图片事件
    removeUpload(file) {
      const index = this.form.imgUrl.indexOf(file);
      if (index > -1) {
        this.form.imgUrl.splice(index, 1);
      }
    },
    getCase() {
      getAllProviderByName().then((res) => {
        this.caseTypesList = res.data;
      });
    },
    getIndustrial() {
      getAllIndustrialByName().then((res) => {
        this.industrialList = res.data;
      });
    },
    getCityDataFtn() {
      getCityData().then((res) => {
        this.cityData = res.data;
      });
    },
    getSysIndustryFtn() {
      getSysIndustry().then(res => {
        this.industrys = res.data;
      });
    },
    // getSysIndustryTypeFtn() {
    //   getSysIndustryType().then((res) => {
    //     this.industryTypes = res.data;
    //   });
    // },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // const caseImg = this.form.imgUrl.map((item) => {
          //   return item.url;
          // });
          if (this.form.industrialSubstitutionIdJson) {
            const uniqueElements = [...new Set(this.form.industrialSubstitutionIdJson.map(subArray => subArray[0]))];
            this.form.industrialSubstitutionIds = uniqueElements.join(',');
          }
          const params = {
            ...this.form,
            label: this.form.label.join(","),
            relatedSolutions: this.form.relatedSolutions.join(","),
            caseImg: this.form.imgUrl.join(","),
            relatedProducts: this.form.relatedProducts.join(","),
          };
          this.$emit("submitFtn", params, (res) => {
            // 相应结束后的其他逻辑
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" s>
.financialCommn {
  width: 80%;

  .uploadItem {
    .el-form-item__content {
      line-height: normal;
    }
  }

  .dynaCard {
    /* width: 100%; */
    background: #f6f8fc;
    border-radius: 5px;
    padding: 12px 24px;
    margin-bottom: 20px;

    .dynaCardHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: 12px;
      margin-left: 22px;

      .hintTitleSamil {
        font-weight: bold;
        font-size: 15px;
        color: #0d162a;

        &:before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 10px;
          background: #6fc342;
          border-radius: 0px;
          margin-right: 6px;
        }
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }

  .appIcon {
    width: 160px;
    height: 160px;
    line-height: 160px;
  }

  .appImage {
    width: 160px;
    height: 160px;
  }
}
</style>
