<template>
  <div id="fluctuations" :style="{width: '1250px', height: '300px'}"></div>
</template>

<script>
export default {
  data() {
    return {};
  },

  // 注意： 要在mounted生命周期函数中实例化echarts对象，确保dom元素已经挂载到页面中
  mounted() {
    this.drawLine();
  },
  methods: {

    drawLine() {

      // 基于准备好的dom，初始化echarts实例
      let fluctuations = this.$echarts.init(document.getElementById('fluctuations'))

      // 绘制图表
      fluctuations.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2017-12-15', '2018-06-15', '2018-12-15', '2019-06-15', '2019-12-15', '2020-06-15', '2020-12-15', '2021-06-15', '2021-12-15']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '折旧',
            type: 'line',
            stack: 'Total',
            color: '#5e90ed',
            data: [120, 132, 101, 134, 90, 230, 210, 100,150]
          },
          {
            name: '采购',
            type: 'line',
            stack: 'Total',
            color: '#69bbc4',
            data: [220, 182, 191, 234, 290, 330, 310, 50, 20]
          },
        ]
      });
    }
  }
}

</script>
