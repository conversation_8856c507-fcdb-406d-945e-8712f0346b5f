import { requestPlatForm } from "@/utils/requestBase";
import request from '@/utils/request'

//产业行业
export function getSysIndustry() {
  return request({
    url: '/system/sysIndustryKind/getSubstitutionTree',
    method: 'get',
  })
}

//行业
export function getSysIndustryType() {
    return request({
      url: '/system/sysIndustryType/tree',
      method: 'get',
    })
  }

//服务商
export function getAllCaseByName(query) {
    return requestPlatForm({
      url: '/industrialBackend/getAllCaseByName',
      method: 'get',
      params: query
    })
  }
//应用案例
  export function getAllProviderByName(query) {
    return requestPlatForm({
      url: '/industrialBackend/getAllProviderByName',
      method: 'get',
      params: query
    })
  }


//产业
export function getSysIndustryKind() {
  return request({
    url: '/system/front/sysIndustryKind/list',
    method: 'get',
  })
}
